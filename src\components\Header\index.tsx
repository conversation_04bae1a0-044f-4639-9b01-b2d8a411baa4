import { Flex, Image, Show, Box, Link, Button, Skeleton } from "@chakra-ui/react";
import * as React from "react";
import LangSwitcher from "components/LangSwitcher";
import NextLink from "next/link";
import { useRouter } from "next/router";
import ProfileMenu from "./ProfileMenu";
import BellNotification from "components/BellNotification";
import { NavbarDesktop, NavbarMobile } from "components/Navbar";
import FontResize from "pagesComponents/FontResize";
import { LANG_SWITCHER_ENABLED } from "config";
import { useSession } from "next-auth/react";
import NavbarLogedInMobile from "components/Navbar/LogedInMobile";
import { useTranslation } from "react-i18next";
import { getImageUrls } from "utils/strapi/helpers";
import { HeaderAndFooterDetails, Menu } from "utils/strapi/navbar";

interface Props {
	isMain?: boolean;
	notBlockPage?: boolean;
	maintenanceMode?: boolean;
	data:
		| {
				global: HeaderAndFooterDetails | null;
				menus: Menu[] | null;
		  }
		| undefined;
	fetchStatus: "idle" | "error" | "loading" | "success";
}
const Header = ({
	isMain = true,
	notBlockPage = true,
	maintenanceMode = false,
	data,
	fetchStatus,
}: Props) => {
	const { locale, pathname, push } = useRouter();
	const { status } = useSession();
	const { t } = useTranslation();
	if (fetchStatus === "loading") return <Skeleton w="99%" rounded="lg" margin={"auto"} h="5rem" />;
	if (fetchStatus === "success" && !data) {
		return <h1>error</h1>;
	}
	const { header_logo } = data?.global!;
	const headerMenu = data?.menus?.find((m) => m.slug === "navbarMenu")!;
	return (
		<>
			<Flex
				as="header"
				flexDirection={{ base: "row", md: "row" }}
				justifyContent="space-between"
				px={6}
				py={{ base: 2, md: 4 }}
				bg="brand.white.50"
				w="full"
				h={{ base: "auto", md: "98px" }}
				dir={"ltr"}
			>
				<Flex>
					<Link
						as={maintenanceMode ? Box : NextLink}
						href={maintenanceMode ? "" : header_logo.logo_url}
					>
						<Image
							src={
								maintenanceMode
									? "../assets/images/logo-en.png"
									: getImageUrls(header_logo.logo_img.url)
							} //"../assets/images/logo-en.png"
							alt="logo"
							maxW={{ base: "80%", md: "100%" }}
							maxH={"100%"}
							draggable="false"
						/>
					</Link>
				</Flex>
				{true &&
					notBlockPage && ( //it was isMain prop to handle to diffrent pre/post login headers
						<Flex alignItems="center">
							<Flex flexDirection={"row"} alignItems={"center"} gap={4}>
								{LANG_SWITCHER_ENABLED && (
									<Show above={"md"}>
										<LangSwitcher />
									</Show>
								)}
								<Show above={"md"}>
									<Box>
										<FontResize />
									</Box>
								</Show>

								{status === "unauthenticated" && pathname !== "/home" && pathname !== "/login" && (
									<Show above={"md"}>
										{/* <Link
											fontSize={{ base: "sm", md: "md" }}
											color={"brand.mainGold"}
											href={`/${locale}/login${
												pathname ? `?callbackUrl=${encodeURIComponent(pathname)}` : ""
											}`}
											onClick={() => {
												if (!LANG_SWITCHER_ENABLED)
													push("/login", undefined, { locale: "ar" }).then(() => {
														document.querySelector("html")?.setAttribute("dir", "rtl");
													});
												else push("/login");
											}}
										>
											{t("logIn")}
										</Link> */}
										<Button
											variant="primary"
											href={`/${locale}/login${
												pathname ? `?callbackUrl=${encodeURIComponent(pathname)}` : ""
											}`}
											as={NextLink}
											onClick={() => {
												if (!LANG_SWITCHER_ENABLED)
													push("/login", undefined, { locale: "ar" }).then(() => {
														document.querySelector("html")?.setAttribute("dir", "rtl");
													});
												else push("/login");
											}}
										>
											{t("logIn")}
										</Button>
									</Show>
								)}

								{status === "authenticated" && <BellNotification />}
								{true && (
									<Show below="md">
										{status === "authenticated" ? (
											<NavbarLogedInMobile headerMenu={headerMenu} />
										) : (
											<NavbarMobile isMain={isMain} headerMenu={headerMenu} />
										)}
									</Show>
								)}
								{status === "authenticated" && (
									<Show above="md">
										<Box>
											<ProfileMenu />
										</Box>
									</Show>
								)}
							</Flex>
						</Flex>
					)}
			</Flex>
			{isMain && (
				<Show above="md">
					<NavbarDesktop headerMenu={headerMenu} />
				</Show>
			)}
		</>
	);
};

export default Header;
