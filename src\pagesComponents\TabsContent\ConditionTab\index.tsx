import { Box, ListItem, OrderedList, Text, UnorderedList } from "@chakra-ui/react";
import React from "react";
import { useTranslation } from "react-i18next";

const ConditionTab = () => {
	const { t } = useTranslation("common");

	return (
		<Box>
			<Box mt={3}>
				<Text fontWeight="700" fontSize={"md"} color={"brand.mainGold"}>
					{t("eligibility-criteria")}
				</Text>
				<Text fontSize={"md"} py={2}>
					{t("eligibility-criteria-start")}:
				</Text>
				<OrderedList>
					<ListItem>
						<Text>
							<Text fontWeight="bold" as="span">
								{t("eligibility-criteria-householdIncome")}:{" "}
							</Text>
							{t("eligibility-criteria-householdIncome-text")}.
						</Text>
					</ListItem>

					<ListItem>
						<Text>
							<Text fontWeight="bold" as="span">
								{t("eligibility-criteria-householdAssets")}:{" "}
							</Text>
							{t("eligibility-criteria-householdAssets-text")}.
						</Text>
					</ListItem>

					<ListItem>
						<Text>
							<Text fontWeight="bold" as="span">
								{t("eligibility-criteria-citizenship")}:{" "}
							</Text>
							{t("eligibility-criteria-citizenship-text")}.
						</Text>
					</ListItem>

					<ListItem>
						<Text>
							<Text fontWeight="bold" as="span">
								{t("eligibility-criteria-residency")}:{" "}
							</Text>
							{t("eligibility-criteria-residency-text")}.
						</Text>
					</ListItem>

					<ListItem>
						<Text>
							<Text fontWeight="bold" as="span">
								{t("eligibility-criteria-age")}:{" "}
							</Text>
							{t("eligibility-criteria-age-text")}.
						</Text>
					</ListItem>
				</OrderedList>
				<Text fontSize={"md"} fontWeight="bold" py={2} pt={6}>
					{t("eligibility-criteria-householdIncome")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-householdIncome-desc")}:
				</Text>
				<UnorderedList>
					<ListItem>
						<Text>{t("eligibility-criteria-householdIncome-item1")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-householdIncome-item2")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-householdIncome-item3")}</Text>
					</ListItem>
				</UnorderedList>

				<Text fontSize={"md"} fontWeight="bold" py={2} pt={6}>
					{t("eligibility-criteria-householdAssets")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-householdAssets-desc")}:
				</Text>
				<UnorderedList>
					<ListItem>
						<Text>{t("eligibility-criteria-householdAssets-item1")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-householdAssets-item2")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-householdAssets-item3")}</Text>
					</ListItem>
				</UnorderedList>
				<Text fontSize={"md"} fontWeight="bold" py={2} pt={6}>
					{t("eligibility-criteria-residencyExemption")}
				</Text>
				<Text fontSize={"md"} py={2} pt={6}>
					{t("eligibility-criteria-residencyExemption-desc")}.
				</Text>

				<Text fontSize={"md"} fontWeight="bold" py={2} pt={6}>
					{t("eligibility-criteria-ageExemption")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-age-desc")}:
				</Text>
				<UnorderedList>
					<ListItem>
						<Text>{t("eligibility-criteria-age-item1")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-age-item2")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-age-item3")}</Text>
					</ListItem>
					<ListItem>
						<Text>{t("eligibility-criteria-age-item4")}</Text>
					</ListItem>
				</UnorderedList>

				{/* <Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec2")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec3")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec4")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec5")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec6")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec7")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("eligibility-criteria-dec8")}
				</Text> */}
			</Box>
		</Box>
	);
};

export default ConditionTab;
