import { RefObject } from "react";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";

const usePdfDownloader = () => {
	const downloadPdf = (ref: RefObject<HTMLDivElement>, filename: string) => {
		const input = ref.current;
		if (input != null) {
			const isMobile = screen.width < 768;
			const pageWidth = isMobile ? 210 : 297;
			const pageHeight = isMobile ? 297 : 420;
			const imgWidth = 170; // Reduced from 180 to allow for better margins
			const imgHeight = (imgWidth * input.offsetHeight) / input.offsetWidth;
			const x = (pageWidth - imgWidth) / 2;
			const y = 15; // Increased from 10 to provide more top margin

			const style = document.createElement("style");
			document.head.appendChild(style);

			// Enhanced styling rules for better readability
			style.sheet?.insertRule("body > div:last-child img { display: inline-block; }");
			style.sheet?.insertRule(
				"body > div p { font-size: 14pt !important; font-family: Arial, sans-serif !important; }"
			);
			style.sheet?.insertRule(
				"body div table tbody tr td { font-size: 14pt !important; font-family: Arial, sans-serif !important; }"
			);
			style.sheet?.insertRule(
				"body div table tbody tr td:first-child { font-weight: bold !important; }"
			);
			style.sheet?.insertRule(".chakra-text { font-size: 14pt !important; }");
			style.sheet?.insertRule(
				"h1, h2, h3, h4, h5, h6 { font-size: 16pt !important; font-weight: bold !important; }"
			);
			style.sheet?.insertRule(".myCase-close { display: none; }");
			style.sheet?.insertRule(".download-butn { display: none; }");

			html2canvas(input, {
				scale: 3, // Increased from 2 to 3 for better quality
				useCORS: true,
				allowTaint: true,
				logging: false,
			}).then((canvas) => {
				style.remove();
				const imgData = canvas.toDataURL("image/png", 1.0); // Changed from JPEG to PNG for better quality

				const pdf = new jsPDF("p", "mm", [pageWidth, pageHeight]);
				pdf.addImage(imgData, "PNG", x, y, imgWidth, imgHeight);

				pdf.save(filename);
			});
		}
	};

	return downloadPdf;
};

export default usePdfDownloader;
