import { Box } from "@chakra-ui/react";
import EducationInfoForm from "./EducationInfoForm";

function RequestDetailsForm({ formKey, members, setMembers, readOnly = false }) {
	const onSubmit = async (values: any, actions) => {
		console.log({ values, actions, formKey });
	};
	return (
		<Box>
			<EducationInfoForm
				onSubmit={onSubmit}
				members={members}
				setMembers={setMembers}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
