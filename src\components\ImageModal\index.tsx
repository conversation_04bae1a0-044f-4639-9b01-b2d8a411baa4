import React from "react";
import {
	<PERSON><PERSON>,
	ModalOverlay,
	ModalContent,
	ModalHeader,
	ModalBody,
	ModalCloseButton,
	Image,
	Link,
} from "@chakra-ui/react";

interface ImageModalProp {
	isOpen: boolean;
	imagePath: string;
	onClose: () => void;
	imageTitle?: string;
	width?: any;
}

export default function ImageModal({
	isOpen,
	imagePath,
	onClose,
	imageTitle,
	width,
}: ImageModalProp) {
	return (
		<>
			<Modal isOpen={isOpen} onClose={onClose}>
				<ModalOverlay />
				<ModalContent maxW={width}>
					<ModalHeader>{imageTitle}</ModalHeader>
					<ModalCloseButton />
					<ModalBody>
						<Link href={imagePath} target={"_blank"}>
							<Image src={imagePath} w={"100%"} alt={"infographics"} />
						</Link>
					</ModalBody>
				</ModalContent>
			</Modal>
		</>
	);
}
