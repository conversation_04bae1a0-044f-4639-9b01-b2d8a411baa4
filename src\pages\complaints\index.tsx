import {
	Box,
	Button,
	Flex,
	Link,
	Show,
	Text,
	GridItem,
	Grid,
	Modal,
	ModalBody,
	ModalCloseButton,
	Modal<PERSON>ontent,
	<PERSON>dalHeader,
	ModalOverlay,
	useDisclosure,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { createColumnHelper } from "@tanstack/react-table";
import TablePage from "pagesComponents/ApplyForComplaint/TablePage";
import React, { ReactElement, useEffect, useState } from "react";
import NextLink from "next/link";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import {
	addLocalLookups,
	getEmiratesIdFromToken,
	getFormattedDate,
	getLocalizedLookups,
} from "utils/helpers";
import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
import { MyComplaintsDetailModal } from "pagesComponents/DetailModal";
import { ChevronLeftIcon } from "@chakra-ui/icons";
import { useRouter } from "next/router";
import Select from "react-select";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import CustomedStatusPill from "components/CustomedStatusPill";
import _ from "lodash";
import {
	ComplaintTypeIcon,
	InquiryTypeIcon,
	SuggestionTypeIcon,
	ThankyouTypeIcon,
} from "components/Icons";
import ComplaintsBox from "pagesComponents/ComplaintsBox";
import Breadcrumbs from "components/Breadcrumbs";

const customStyles = {
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
	}),
	control: (base, state) => ({
		...base,
		borderRadius: "8px",
		// color: "white",
		display: "flex",
		borderColor: "#B08D44",
		innerWidth: "100%",
		height: "100%",
		outerHeight: "100%",
		"&:hover": {
			boxShadow: "0 0 0 1px #B08D44",
		},
		color: "red",
		border: state.isFocused ? 0 : "1px solid #DDE1E6",
		// This line disable the blue border
		boxShadow: state.isFocused ? "0 0 0 1px #B08D44" : 0,
		// border: isDisabled ? "2px solid #C1C7CD" : "1px solid #DDE1E6",
		// color: state.isDisabled ? "red" : "white",
	}),
	option: (provided, state) => ({
		...provided,
		display: "flex",
		color: "#001841",
		zIndex: 999,
		"&:hover": {
			background: "#DDE1E6",
		},
		backgroundColor: state.isSelected ? "#DDE1E6" : "white",
	}),
	menu: (styles, state) => ({
		...styles,
		marginBottom: "1px",
		// zIndex: 9999,
	}),

	multiValue: (styles) => ({
		...styles,
		background: "#B08D44",
		borderRadius: "16px",
		color: "white",
		"&:hover": {
			background: "#B08D44",
		},
	}),
	menuList: (provided, state) => ({
		...provided,
		paddingTop: "-1px",
		paddingBottom: "-4px",
		marginTop: "-4px",
		boxShadow: "2px 4px 10px 2px #DDE1E6",
	}),
	menuPortal: (provided) => ({
		...provided,
		left: "unset",
		right: "unset",
	}),
};

function Complaints(props: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["common", "tables", "personalInfo", "forms"]);
	const [complaintType, setComplaintType] = useState(-1);
	const [toggle, setToggle] = useState(false);
	const router = useRouter();
	const { locale } = router;
	const allStatus = {
		label: t("allStatus"),
		value: "",
	};

	const InProgressActiveObj = {
		value: "1",
		label: t("inProgress"),
	};

	const ResolvedProblemSolvedObj = {
		value: "5",
		label: t("problemSolved"),
	};
	const appealStatus = {
		value: "2",
		label: t("closed"),
	};
	const reopenStatus = {
		value: "662410002",
		label: t("Reopened"),
	};
	const InformationProvidedStatus = {
		value: "1000",
		label: t("InformationProvided"),
	};
	let lookupArr = [
		allStatus,
		InProgressActiveObj,
		ResolvedProblemSolvedObj,
		reopenStatus,
		InformationProvidedStatus,
	];

	const [selectedStatus, setSelectedStatus] = React.useState<any>("");
	const [status, setStatus] = React.useState<any>("");
	const [inquiryType, setInquiryType] = React.useState<any>("");
	const { isOpen, onOpen, onClose } = useDisclosure();
	//const [statusLookup, setStatusLookup] = useState<Array<ICrmLookupLocalized>>(lookupArr);

	const columnHelper = createColumnHelper<ComplaintForm>();
	const columns = [
		columnHelper.accessor("TicketNumber", {
			cell: (info) => (
				<Text color="brand.blue.300" textDecoration="underline" fontSize="sm" cursor={"pointer"}>
					{info.getValue()}
				</Text>
			),
			header: `${t("tables:complaintNumber")}`,
		}),

		columnHelper.accessor("CaseType", {
			cell: (info) => {
				return (
					<Text fontSize="sm" cursor={"pointer"}>
						{t(`personalInfo:${_.camelCase(info.getValue().toString().toLowerCase())}`)}
					</Text>
				);
			},
			header: `${t("tables:complaintRequestType")}`,
		}),
		columnHelper.accessor("CreatedOn", {
			cell: (info) => getFormattedDate(info.getValue(), "dd MMMM yyyy", locale),
			header: `${t("tables:raisedAt")}`,
			meta: {},
		}),

		columnHelper.accessor("Status", {
			cell: (info) => {
				return (
					<CustomedStatusPill
						status={info.getValue()}
						AppealStatus={info.row.original.IsReopen}
						complaintId={info.row.original.Id}
						ModifiedOn={info.row.original.ModifiedOn}
					/>
				);
			},
			header: `${t("tables:status")}`,
		}),
	];

	useEffect(() => {
		setStatus("");
		setSelectedStatus("");
	}, [locale]);

	useEffect(() => {
		if (complaintType !== -1) {
			if (!props.isAuth) {
				onOpen();
			} else {
				router.push(`${locale}/complaints/apply-for-complain?complaintType=${complaintType}`);
			}
		}
	}, [complaintType, toggle]);
	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("navbar-complaints"),
			id: "complaints",
			link: "#",
			isCurrentPage: true,
		},
	];
	return (
		<Box pt={{ base: 4, md: 9 }} px={{ base: 2, md: 8 }} pb={8} w="100%" minH={"50vh"}>
			{breadcrumbsData.length > 0 && (
				<Box display={{ base: "none", md: "block" }}>
					<Breadcrumbs data={breadcrumbsData} />
				</Box>
			)}
			<Flex
				w="100%"
				py={{ base: 0, md: 4 }}
				mb={{ base: 2, md: 0 }}
				flexDirection={{ base: "column", md: "row" }}
				bg={{ base: "unset", md: "brand.white.50" }}
			>
				<Flex w={"100%"}>
					<Show below={"md"}>
						<Link
							as={NextLink}
							href={"/"}
							display={"flex"}
							alignItems={"center"}
							me={"-24px"}
							zIndex={2}
						>
							<ChevronLeftIcon
								h={"24px"}
								w={"24px"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
						</Link>
					</Show>

					<Text
						fontSize={{ base: "lg", md: "h4" }}
						fontWeight="medium"
						w={"100%"}
						textAlign={{ base: "center", md: "initial" }}
					>
						{t("complaints")}
					</Text>
				</Flex>

				{/* <Show above="md">
					<Button
						as={NextLink}
						ms={4}
						height={12}
						w={64}
						variant="primary"
						href={"/complaints/apply-for-complain"}
					>
						{t("newComplaint")}
					</Button>
				</Show> */}
			</Flex>
			<Grid
				// rowGap={{ base: 6, md: 6 }}
				my={6}
				columnGap={{ base: 4, md: 8, lg: 8 }}
				rowGap="8"
				templateColumns="repeat(4, 1fr)"
				templateRows="auto"
			>
				<GridItem colSpan={{ base: 4, sm: 2, md: 2, lg: 2, xl: 1 }}>
					<ComplaintsBox
						mainText={t("complaint")}
						handleViewDetails={() => {
							setInquiryType("complaint");
							setComplaintType(1);
							setToggle((prevStat) => !prevStat);
						}}
						subMainText={t("complaintSubText")}
						icon={
							<ComplaintTypeIcon w={"62px"} h={"38px"} color={"#A4A5A6"} className="add-icon" />
						}
						handleExample={undefined}
						detailsButton={t("proceed")}
						exampleButton={""}
					/>
				</GridItem>
				<GridItem colSpan={{ base: 4, sm: 2, md: 2, lg: 2, xl: 1 }}>
					<ComplaintsBox
						mainText={t("inquiry")}
						subMainText={t("inquirySubText")}
						handleViewDetails={() => {
							setInquiryType("inquiry");
							setComplaintType(2);
							setToggle((prevStat) => !prevStat);
						}}
						icon={<InquiryTypeIcon w={"60px"} h={"60px"} color={"#A4A5A6"} className="add-icon" />}
						handleExample={undefined}
						detailsButton={t("proceed")}
						exampleButton={""}
					/>
				</GridItem>
				<GridItem colSpan={{ base: 4, sm: 2, md: 2, lg: 2, xl: 1 }}>
					<ComplaintsBox
						mainText={t("suggestion")}
						subMainText={t("suggestionSubText")}
						handleViewDetails={() => {
							setInquiryType("suggestion");
							setComplaintType(662410000);
							setToggle((prevStat) => !prevStat);
						}}
						icon={
							<SuggestionTypeIcon w={"60px"} h={"60px"} color={"#A4A5A6"} className="add-icon" />
						}
						handleExample={undefined}
						detailsButton={t("proceed")}
						exampleButton={""}
					/>
				</GridItem>
				<GridItem colSpan={{ base: 4, sm: 2, md: 2, lg: 2, xl: 1 }}>
					<ComplaintsBox
						mainText={t("thankYou")}
						subMainText={t("thankYouSubText")}
						handleViewDetails={() => {
							setInquiryType("thankYouTitle");
							setComplaintType(3);
							setToggle((prevStat) => !prevStat);
						}}
						icon={<ThankyouTypeIcon w={"60px"} h={"60px"} color={"#A4A5A6"} className="add-icon" />}
						handleExample={undefined}
						detailsButton={t("proceed")}
						exampleButton={""}
					/>
				</GridItem>
			</Grid>

			{props.isAuth && (
				<>
					<Flex flexDirection={{ base: "column", md: "row" }}>
						<Text
							fontSize={{ base: "lg", md: "h4" }}
							fontWeight="medium"
							w={"100%"}
							mb={8}
							textAlign={{ base: "center", md: "initial" }}
						>
							{t("myInquiriesSuggestions")}
						</Text>
						<Flex
							mt={{ base: 4, md: 0 }}
							width={{ base: "100%", md: "40%" }}
							height={12}
							justifyContent="space-between"
						>
							<Box w={{ base: "55%", md: "100%" }} minW="100px">
								<Select
									value={status}
									className={"mycases-dropdown"}
									menuPosition={"fixed"}
									isDisabled={false}
									options={lookupArr}
									styles={customStyles}
									onChange={(e: any) => {
										setSelectedStatus(e.value);
										setStatus(e);
									}}
									placeholder={`${t("allStatus")}`}
									components={{
										IndicatorSeparator: () => null,
									}}
									isSearchable={false}
								/>
							</Box>
						</Flex>
					</Flex>

					<TablePage
						columns={columns}
						data={props.requests.filter(
							(i) => !selectedStatus || (selectedStatus && String(i.Status) === selectedStatus)
						)}
						Modal={MyComplaintsDetailModal({
							statusLookup: props.masterData?.CaseStatus,
							contactEmiratesId: props.emiratesId,
							contact: props.userDetails!,
						})}
						type={"complaints"}
					/>
				</>
			)}
			<Modal isOpen={isOpen} onClose={onClose} size={"xl"}>
				<ModalOverlay />
				<ModalContent w={{ base: "50%", md: "30%" }} maxW="95%">
					<ModalHeader>
						{locale === "ar"
							? t(inquiryType) + " " + t("newInquirie")
							: t("newInquirie") + " " + t(inquiryType)}
					</ModalHeader>
					<ModalCloseButton />
					<ModalBody>
						<Box>
							<Text my={5}>{t("complaintAuthText")}</Text>
							<Flex
								flexDirection={"row"}
								alignItems={"center"}
								justifyContent={"space-between"}
								my={6}
							>
								<Button
									variant="primary"
									onClick={() => {
										router.push(
											`${locale}/complaints/apply-for-complain?complaintType=${complaintType}`
										);
									}}
								>
									<Text as="span">{t("continue", { ns: "common" })}</Text>
								</Button>
								<Button
									variant="secondary"
									onClick={() => {
										router.push(
											`/${locale}/login?callbackUrl=/complaints/apply-for-complain?complaintType=${complaintType}`
										);
									}}
								>
									<Text as="span">{t("logIn", { ns: "common" })}</Text>
								</Button>
							</Flex>
						</Box>
					</ModalBody>
				</ModalContent>
			</Modal>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	let emiratesId = await getEmiratesIdFromToken(ctx.req);

	let requests: any = [];
	let userDetails: any = null;
	let isAuth = false;

	if (emiratesId) {
		requests = (await BackendServices.getComplaint(emiratesId))?.Data || [];
		requests.sort((a, b) => new Date(b.CreatedOn).getTime() - new Date(a.CreatedOn).getTime());
		const profile = await BackendServices.retrieveContact(emiratesId);
		userDetails = profile.Data;
		isAuth = true;
	} else {
		emiratesId = "";
	}

	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);

	return {
		props: {
			emiratesId,
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "personalInfo"])),
			requests,
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			userDetails,
			isAuth,
		},
	};
}

Complaints.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default Complaints;
