import { ICaseDetails, IEducationCase } from "interfaces/SocialAidForm.interface";
import { useEffect, useState } from "react";
import { mapSocialAidFormToEducationMembers } from "utils/helpers";

const useEducationMembers = (
	initialData: ICaseDetails | undefined,
	isEducationStep = false,
	isEligibleForTopup = true
) => {
	const [members, setMembers] = useState<IEducationCase[]>(
		mapSocialAidFormToEducationMembers(initialData)
	);
	const [proceedDisabled, setProceedDisabled] = useState(true);

	useEffect(() => {
		// If not eligible for topup, always enable the proceed button
		if (!isEligibleForTopup) {
			setProceedDisabled(false);
			return;
		}

		// Otherwise, use the original logic
		setProceedDisabled(
			isEducationStep &&
				members.length > 0 &&
				members.some((member) => !member.IsCompletedFromPortal)
		);
	}, [members, isEducationStep, isEligibleForTopup]);
	return {
		educationMembers: members,
		setEducationyMembers: setMembers,
		educationStepDisabled: proceedDisabled,
	};
};

export default useEducationMembers;
