import {
	Box,
	Button,
	Text,
	useDisclosure,
	Flex,
	AlertDialog,
	AlertDialogOverlay,
	AlertDialogContent,
	AlertDialogHeader,
	AlertDialogBody,
	AlertDialog<PERSON>ooter,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>u<PERSON>ist,
	<PERSON>u<PERSON><PERSON>,
	MenuDivider,
	Grid<PERSON>tem,
	Grid,
} from "@chakra-ui/react";
import { ChevronDownIcon } from "@chakra-ui/icons";
import { ButtonArrowIcon } from "components/Icons";
import {
	ADD_REASON_NOMINATED_CASES,
	EDIT_REASON_NOMINATED_CASES,
	INFLATION_TYPE,
	SOCIALAID_TEMPLATE_ID,
	STATUS_SLUG_MAPPING,
	STOP_REASON_NOMINATED_CASES,
} from "config";
import { ICrmMasterData, ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useMemo, useRef, useState } from "react";
import { modifyRequest } from "services/frontend";
import useAppToast from "hooks/useAppToast";
import { INFLATION_TEMPLATE_ID, INFLATION_TEMPLATE_ID_2 } from "config";
import FormField from "components/Form/FormField";
import { Form, Formik, FormikProps } from "formik";
import { Portal } from "@chakra-ui/react";

interface StatusPillProps {
	status?: any;
	customText?: string;
	customStatus?: "pending" | "submitted" | "requestApproved";
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
	reasonsLookupInflation?: ICrmMasterData<ICrmLookupLocalized>["ReasonsToEditInflation"];
	reasonsLookupSWP?: ICrmMasterData<ICrmLookupLocalized>["ReasonsToEditSwp"];
	onClick?: any;
	caseId?: string;
	requestName?: string;
	eligibleForAppeal?: boolean;
	eligibleForRefund?: boolean;
	eligibleForEdit?: boolean;
	templateId?: string;
	isNominatedInflationCase?: boolean;
}

const StatusPill = ({
	status,
	customText,
	customStatus,
	statusLookup,
	reasonsLookupInflation,
	reasonsLookupSWP,
	eligibleForAppeal,
	eligibleForEdit,
	caseId,
	requestName,
	onClick = () => {},
	templateId,
	isNominatedInflationCase,
}: StatusPillProps) => {
	const { t } = useTranslation();
	let bgColor;
	let textColor;
	const { isOpen, onOpen, onClose } = useDisclosure();
	const formikRef = useRef<FormikProps<any>>(null);
	const cancelRef = useRef<any>();
	const [callingApi, setCallingApi] = useState(false);
	const [reopenReason, setReopenReason] = useState("");
	const toast = useAppToast();
	const { query, isReady, replace, push } = useRouter();
	const [isLoading, setLoading] = useState(false);
	const router = useRouter();
	const locale = router.locale;
	const handleRefund = (caseId: string) => {
		router.push(`/my-cases/apply-to-refund?caseId=${caseId}`);
	};
	const lkpStatusObject = statusLookup?.find((i) => i.value === (status?.Key || ""));
	var isSWPCase = false;
	if (templateId === SOCIALAID_TEMPLATE_ID) {
		isSWPCase = true;
	}

	if (!isSWPCase && isNominatedInflationCase) {
		const validReasons = [
			EDIT_REASON_NOMINATED_CASES,
			STOP_REASON_NOMINATED_CASES,
			ADD_REASON_NOMINATED_CASES,
		];
		reasonsLookupInflation = reasonsLookupInflation?.filter((x) => validReasons.includes(x.value));
	}
	const lkpStatus = STATUS_SLUG_MAPPING[lkpStatusObject?.value || ""];
	const refundStatus = STATUS_SLUG_MAPPING[status || ""];
	const statusSlug = refundStatus || customStatus || lkpStatus || status?.Value || "draft";

	if (statusSlug.startsWith("pending")) {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "submitted") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "inProgress") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "requestApproved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "resolved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "requestRejected") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "draft" || statusSlug === "temporaryQueue") {
		bgColor = "#DDE1E6";
		textColor = "#697077";
	} else if (statusSlug === "annualReviewUpdate") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "PendingRefundApproved" || statusSlug === "PendingRefundNotApproved") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else {
		bgColor = "#f5f5f5";
		textColor = "#697077";
	}

	if (refundStatus == "PendingPayment") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (refundStatus == "OverduePayment") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (refundStatus == "PendingConfirmation") {
	} else if (refundStatus == "PaymentCompleted") {
	} else if (refundStatus == "PaymentAccumulated") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (refundStatus == "Stopped") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	}
	const initialValues = useMemo(
		() => ({
			ReasonToEdit: "",
		}),
		["", locale]
	);
	if (statusSlug === "draft") {
		return (
			<Button
				rightIcon={
					<ButtonArrowIcon
						w={"24px"}
						h={"24px"}
						transform={locale === "ar" ? "scale(-1, 1)" : ""}
						marginInlineStart={1}
					/>
				}
				variant={"outline"}
				minHeight={"0.2rem"}
				h={"2rem"}
				px={"1px !important"}
				onClick={onClick}
			>
				{t("proceed")}
			</Button>
		);
	}

	if (
		(statusSlug === "PendingRefundApproved" || statusSlug === "PendingRefundNotApproved") &&
		false
	) {
		// Eligible/approved cases and  Ineligible/rejected  cases

		return (
			<Flex
				alignItems={"center"}
				flexDir={{
					base: "column",
					md: "row",
				}}
				gap={4}
			>
				<Box
					bgColor="#FFF0F0"
					textColor="#C42828"
					px={2}
					borderRadius={"5px"}
					py="1"
					w={"fit-content"}
					borderWidth={"1px"}
					borderColor="#C42828"
					h={"fit-content"}
					onClick={onClick}
				>
					<Text
						fontSize={["sm", "sm", "sm"]}
						fontWeight={500}
						color="#C42828"
						p={-2}
						w={"100%"}
						textAlign={"center"}
					>
						{statusSlug === "PendingRefundApproved"
							? t(`PendingRefundApproved`)
							: t(`PendingRefundNotApproved`)}
					</Text>
				</Box>

				<Button
					w={"full"}
					rightIcon={
						<ButtonArrowIcon
							w={"24px"}
							h={"24px"}
							transform={locale === "ar" ? "scale(-1, 1)" : ""}
							marginInlineStart={1}
						/>
					}
					p={"0 !important"}
					onClick={(e) => {
						e.stopPropagation();
						handleRefund(caseId || "");
					}}
					variant={"outline"}
				>
					{t("refund")}
				</Button>
			</Flex>
		);
	}
	const handleEditClick = async (
		e,
		isHousingEducationTopup = false,
		isHousingTopup = false,
		isEducationTopup = false
	) => {
		e.stopPropagation();
		setLoading(true);
		setCallingApi(true);
		toast({
			title: t("pleaseWaitLoadDraft", { ns: "common" }),
			status: "info",
		});
		try {
			const data = await modifyRequest({
				UpdateType: "CREATE",
				reOpenReason: reopenReason,
				ParentCaseId: caseId || undefined,
				CaseDetails: {},
				CaseType:
					templateId === INFLATION_TEMPLATE_ID_2 || templateId === INFLATION_TEMPLATE_ID
						? INFLATION_TYPE
						: 2,
				//CaseType: 2,
			});

			if (data.Data && data?.IsSuccess) {
				// toast({
				// 	title: t("pleaseWaitLoadDraft", { ns: "common" }),
				// 	status: "info",
				// });

				if (data?.Data.IdCase) {
					if (templateId === INFLATION_TEMPLATE_ID_2 || templateId === INFLATION_TEMPLATE_ID) {
						push(
							`/${locale}/smart-services/inflation-service/apply-inflation?requestId=${data?.Data.IdCase}&editCase=true`
						);
					} else {
						if (isHousingEducationTopup) {
							push(
								`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&editCase=true&housingEducationTopup=true`
							);
						} else if (isHousingTopup) {
							push(
								`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&editCase=true&housingTopup=true`
							);
						} else if (isEducationTopup) {
							push(
								`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&editCase=true&educationTopup=true`
							);
						} else {
							push(
								`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&editCase=true`
							);
						}
					}
				}
			} else {
				toast({
					title: t("common:genericErrorTitle"),
					description: t("common:genericErrorDescription"),
					status: "error",
				});
				setCallingApi(false);
				setLoading(false);
			}
		} catch (error) {
			console.error("API error:", error);
			setCallingApi(false);
			setLoading(false);
			// Handle the error as needed
		}
	};

	const handleReopen = (e) => {
		if (reopenReason !== "") {
			handleEditClick(e, false);
		}
	};
	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}

		let reopenReasonsList = "";
		if (firstArg && firstArg?.length > 0) {
			firstArg.forEach((element) => {
				reopenReasonsList += element.value + ",";
			});
			reopenReasonsList = reopenReasonsList.slice(0, reopenReasonsList.length - 1);
			setReopenReason(reopenReasonsList);
		} else {
			setReopenReason(reopenReasonsList);
		}
	};
	const onSubmit = () => {};
	const handleAppealClick = (e) => {
		e.stopPropagation();
		onOpen();
	};

	return (
		<Flex
			alignItems={"center"}
			flexDir={{
				base: "column",
				md: "row",
			}}
			gap={4}
			w="100%"
		>
			<Box w={"50%"}>
				<Box
					bg={bgColor}
					px={2}
					borderRadius={"5px"}
					py="1"
					w={"fit-content"}
					borderWidth={"1px"}
					borderColor={textColor}
					h={"fit-content"}
					onClick={onClick}
					_hover={{ cursor: "pointer" }}
				>
					<Text
						fontSize={["sm", "sm", "sm"]}
						fontWeight={500}
						color={textColor}
						p={-2}
						w={"100%"}
						textAlign={"center"}
					>
						{customText ||
							lkpStatusObject?.label ||
							status?.Value ||
							t(`caseStatus.${statusSlug}`) ||
							refundStatus}
					</Text>
				</Box>
			</Box>
			<Box w={"50%"}>
				{/* {eligibleForAppeal && false && (
					<Button
						w="100%"
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
								marginInlineStart={1}
							/>
						}
						p={"0 !important"}
						onClick={(e) => {
							handleAppealClick(e);
						}}
						variant={"outline"}
					>
						{t("appeal")}
					</Button>
				)} */}
			</Box>
			<Box w={"80%"}>
				{eligibleForEdit && (
					<Menu>
						<MenuButton
							as={Button}
							variant="primary"
							h={12}
							rightIcon={<ChevronDownIcon />}
							onClick={(e) => e.stopPropagation()}
							w="100%"
						>
							{t("actions")}
						</MenuButton>
						<Portal>
							<MenuList>
								<MenuItem
									icon={<ButtonArrowIcon w={"20px"} h={"20px"} />}
									onClick={(e) => {
										e.stopPropagation();
										onOpen();
									}}
								>
									{t("edit")}
								</MenuItem>
								<MenuDivider />
								{!(
									templateId === INFLATION_TEMPLATE_ID_2 || templateId === INFLATION_TEMPLATE_ID
								) && (
									<>
										<MenuItem
											icon={<ButtonArrowIcon w={"20px"} h={"20px"} />}
											onClick={(e) => {
												e.stopPropagation();
												handleEditClick(e, false, true, false);
											}}
										>
											{t("applyForHousingAllowance", { ns: "common" })}
										</MenuItem>
										<MenuItem
											icon={<ButtonArrowIcon w={"20px"} h={"20px"} />}
											onClick={(e) => {
												e.stopPropagation();
												handleEditClick(e, false, false, true);
											}}
										>
											{t("applyForAcademicExcellenceAllowance", { ns: "common" })}
										</MenuItem>
										<MenuItem
											icon={<ButtonArrowIcon w={"20px"} h={"20px"} />}
											onClick={(e) => {
												e.stopPropagation();
												handleEditClick(e, true); // Existing: Housing+Education Topup
											}}
										>
											{t("applyForHousingEducationTopup", { ns: "common" })}
										</MenuItem>
									</>
								)}
							</MenuList>
						</Portal>
					</Menu>
				)}
			</Box>
			<AlertDialog isOpen={isOpen} leastDestructiveRef={cancelRef} onClose={onClose} isCentered>
				<AlertDialogOverlay>
					<AlertDialogContent>
						<AlertDialogHeader fontSize="lg" fontWeight="bold">
							{t("edit")}
						</AlertDialogHeader>

						<AlertDialogBody>
							{/* <Text>{t("reOpenConfirmation")}</Text> */}
							{/* <Text mb="3">{t("editReason")}</Text> */}
							<Formik
								enableReinitialize
								initialValues={initialValues}
								onSubmit={onSubmit}
								innerRef={formikRef}
							>
								{(formik) => (
									<>
										<Form
											onSubmit={(e) => {
												e.preventDefault();
												formik.handleSubmit(e);
											}}
											onChange={(e) => {
												e.preventDefault();
											}}
										>
											<Grid
												rowGap={{ base: 6, md: 6 }}
												columnGap={6}
												templateColumns="repeat(2, 1fr)"
												templateRows="auto"
											>
												{
													<GridItem colSpan={{ base: 2, md: 2 }}>
														<FormField
															type="selectableTags"
															value={formik.values["ReasonToEdit"]}
															isRequired={true}
															isMulti={true}
															name="ReasonToEdit"
															label={t("reasonToEdit")}
															isDisabled={false}
															placeholder={t("placeholder", { ns: "common" })}
															options={isSWPCase ? reasonsLookupSWP : reasonsLookupInflation}
															touched={formik.touched["ReasonToEdit"]}
															error={formik.errors["ReasonToEdit"]}
															onChange={(firstArg) => {
																handleChangeEvent("text", firstArg, "ReasonToEdit", formik);
															}}
														/>
													</GridItem>
												}
											</Grid>
										</Form>
									</>
								)}
							</Formik>
							{/* <Input
								type="text"
								value={reopenReason}
								onChange={(e) => setReopenReason(e.target.value)}
							/> */}
						</AlertDialogBody>

						<AlertDialogFooter>
							<Button ref={cancelRef} disabled={isLoading} variant={"secondary"} onClick={onClose}>
								{t("cancel")}
							</Button>
							<Button
								colorScheme="red"
								variant={"primary"}
								disabled={reopenReason === ""}
								isLoading={isLoading}
								onClick={handleReopen}
								ml={3}
							>
								{t("edit")}
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialogOverlay>
			</AlertDialog>
			{/* <Modal isOpen={isOpen} isCentered onClose={onClose} size={"xl"}>
				<ModalOverlay />
				<ModalContent w="fit" maxW="95%">
					<ModalHeader>{t("appeal")}</ModalHeader>
					<ModalCloseButton />
					<ModalBody p={0} maxHeight="80vh" overflowY="auto">
						<ApplyForAppeal
							caseId={caseId || ""}
							requestName={requestName || ""}
							onClose={onClose}
						></ApplyForAppeal>
					</ModalBody>
				</ModalContent>
			</Modal> */}
		</Flex>
	);
};

export default StatusPill;
