import React from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>over<PERSON>ontent,
	Divider,
	Text,
	Center,
	Box,
	VStack,
} from "@chakra-ui/react";
import { BellIcon } from "components/Icons";
import { ICrmNotification } from "interfaces/CrmNotification.interface";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

interface Props {
	notifications: ICrmNotification[];
	notificationCount: number;
	setNotificationsOpen: (open: boolean) => void;
}

const NotificationDesktop = ({ notificationCount, notifications, setNotificationsOpen }: Props) => {
	const { t } = useTranslation();
	const { locale } = useRouter();

	return (
		<>
			<Popover
				onOpen={() => setNotificationsOpen(true)}
				onClose={() => setNotificationsOpen(false)}
				direction={locale === "ar" ? "rtl" : "ltr"}
			>
				<PopoverTrigger>
					<Button
						pos="relative"
						bg={"brand.white.100"}
						borderRadius={"20px"}
						w={"40px"}
						height={"40px"}
					>
						{!!notificationCount && (
							<Center
								bg="brand.red.300"
								h="16px"
								w="16px"
								borderRadius="50%"
								pos="absolute"
								ms="22px"
								mt="6px"
								overflow={"hidden"}
							>
								<Text color="brand.white.50" fontSize="xs">
									{notificationCount > 9 ? "9+" : notificationCount}
								</Text>
							</Center>
						)}
						<BellIcon h={"20px"} />
					</Button>
				</PopoverTrigger>
				<PopoverContent
					borderRadius="0"
					w="250px"
					maxH={"25vh"}
					overflow={"auto"}
					px="0.5"
					py="5"
					me="32"
					boxShadow="0px 7px 9px rgba(0, 0, 0, 0.12)"
					zIndex={100}
				>
					<VStack divider={<Divider bg="brand.fieldBorder" />}>
						{notifications.map((item) => {
							return (
								<Box key={item.Id} w={"full"} textAlign={"left"}>
									<Text fontWeight="500" fontSize="md" color="brand.notificationTextColor">
										{locale === "en" ? item.TitleEn : item.TitleAr}
									</Text>
									<Text mt={2} fontWeight="400" color="brand.gray.400" fontSize="sm">
										{locale === "en" ? item.NotificationEn : item.NotificationAr}
									</Text>
								</Box>
							);
						})}
						{notificationCount === 0 && (
							<Box w="full">
								<Center>
									<Text fontWeight="500" fontSize="md" color="brand.notificationTextColor">
										{t("noNotifications")}
									</Text>
								</Center>
							</Box>
						)}
					</VStack>
				</PopoverContent>
			</Popover>
		</>
	);
};

export default NotificationDesktop;
