import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	PreferredPhoneNumber: "",
	EmiratesID: "",
	caseID: "",
	IDNBackNumber: "",
	alternativeNumber: "",
	address: "",
	AlternativeEmail: "",
	FirstName: "",
	LastName: "",
};

const getValidationSchema = () => {
	return Yup.object({
		PreferredPhoneNumber: Yup.string().notRequired().nullable(),
		IDNBackNumber: Yup.string().notRequired().nullable(),
		EmiratesID: Yup.string().notRequired().nullable(),
		alternativeNumber: Yup.string()
			.required()
			.label("thisField")
			.matches(/^05(\d){8}$/, "uaeMobileNumberError"),
		AlternativeEmail: Yup.string().email("wrongEmailAddress").required().label("thisField"),
		address: Yup.string().notRequired().label("thisField"),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
