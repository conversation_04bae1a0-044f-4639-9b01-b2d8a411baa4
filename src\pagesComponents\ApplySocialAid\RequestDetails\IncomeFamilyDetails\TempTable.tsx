import {
	Table,
	TableContainer,
	Tbody,
	Tr,
	Text,
	Thead,
	Th,
	Flex,
	Show,
	VStack,
	Box,
} from "@chakra-ui/react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

function TempTable({ tableBody, headers, mb = 10, sourceOfTranslation = "tables" }) {
	const { t } = useTranslation(["forms", "table", "common"]);
	const { locale } = useRouter();

	return (
		<>
			<Show above="md">
				<TableContainer
					mb={mb}
					rounded={"lg"}
					border="1px"
					borderBottom={"0px"}
					borderColor="#BBBCBD"
				>
					<Table variant="simple">
						<Thead>
							<Tr fontWeight="medium" mb={4}>
								{headers.map((header, idx) => {
									return (
										<Th
											color="brand.tableTextColor"
											key={idx}
											textTransform="none"
											lineHeight="150%"
											fontSize="0.9375rem"
											fontWeight="bold"
											letterSpacing="unset"
											py={4}
											px={4}
											borderColor="brand.tableBorderColor"
											width={header.width || "unset"}
										>
											{t(header.id)}
										</Th>
									);
								})}
							</Tr>
						</Thead>
						<Tbody>
							{tableBody.length > 0 ? (
								tableBody?.map((member, memberRow) => {
									return (
										<Tr key={member.Id}>
											{headers.map((header, idx) => {
												return (
													<Th
														key={idx}
														textTransform="none"
														fontFamily="Helvetica Neue"
														color="brand.familyTableRowColor"
														fontWeight="normal"
														fontSize="0.9375rem"
														letterSpacing="unset"
														lineHeight="150%"
														p={4}
														borderColor="brand.tableBorderColor"
													>
														<Flex alignItems={"center"} gap={2}>
															{header.id === "FullName" ? (
																<Text>
																	{locale === "ar" ? member["FullNameArabic"] : member["FullName"]}
																</Text>
															) : header.id === "StatusCode" ? (
																<Text>{t(member[header.id])}</Text>
															) : (
																<Text>{member[header.id]}</Text>
															)}
														</Flex>
													</Th>
												);
											})}
										</Tr>
									);
								})
							) : (
								<Tr>
									<Th colSpan={4}>
										<Text textAlign={"center"}>{t("noDataFound")}</Text>
									</Th>
								</Tr>
							)}
						</Tbody>
					</Table>
				</TableContainer>
			</Show>
			<Show below="md">
				<VStack align={"start"} my={5}>
					{tableBody.length > 0 ? (
						tableBody.map((member, memberRow) => {
							return (
								<Box borderBottom="1px solid #BBBCBD" key={memberRow} w={"100%"}>
									{headers.map((header, idx) => {
										return (
											<Flex key={idx} w="full" py={4} justifyContent={"space-between"}>
												<VStack flex={2} align={"start"}>
													<Text fontWeight={"bold"}>{t(header.id)}</Text>
													{header.id === "FullName" ? (
														<Text>
															{locale === "ar" ? member["FullNameArabic"] : member["FullName"]}
														</Text>
													) : header.id === "StatusCode" ? (
														<Text>{t(member[header.id])}</Text>
													) : (
														<Text>{member[header.id]}</Text>
													)}
												</VStack>
											</Flex>
										);
									})}
								</Box>
							);
						})
					) : (
						<Text pb={5} borderBottom="1px solid #BBBCBD" w={"100%"}>
							{t("noDataFound")}
						</Text>
					)}
				</VStack>
			</Show>
		</>
	);
}

export default TempTable;
