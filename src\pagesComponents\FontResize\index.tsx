import {
	extendTheme,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	PopoverBody,
	But<PERSON>,
} from "@chakra-ui/react";
import { AccessibilityIcon } from "components/Icons";
import { useAppContext } from "context/AppContext";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { themeObject } from "theme";
import FontResizeComp from "./FontResizeComp";

function FontResize() {
	const { setUsedTheme } = useAppContext();
	const [currentSize, setCurrentSize] = useState(16);
	const [showResizer, setshowResizer] = useState(false);
	const { t } = useTranslation();
	const maxSize = 20;
	const minSize = 12;
	const stepSize = 1;
	function decreaseSize(newValue = null) {
		let themeObjectCopy = JSON.parse(JSON.stringify(themeObject));
		let newSize = newValue ? newValue : currentSize;
		if (newSize > minSize) {
			newSize -= stepSize;
		}
		themeObjectCopy.styles.global.body.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		themeObjectCopy.styles.global.html.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		setCurrentSize(newSize);
		setUsedTheme(extendTheme(themeObjectCopy));
	}
	function increaseSize(newValue = null) {
		let themeObjectCopy = JSON.parse(JSON.stringify(themeObject));
		let newSize = newValue ? newValue : currentSize;
		if (newSize < maxSize) {
			newSize += stepSize;
		}
		themeObjectCopy.styles.global.body.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		themeObjectCopy.styles.global.html.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		setCurrentSize(newSize);
		setUsedTheme(extendTheme(themeObjectCopy));
	}
	return (
		// <Flex
		// 	justifyContent="space-between"
		// 	alignItems="center"
		// 	height="40px"
		// 	width="8.625rem"
		// 	zIndex={50000}
		// >
		// 	<Button
		// 		bg={"brand.gray.100"}
		// 		borderRadius="25px"
		// 		onClick={() => decreaseSize()}
		// 		p="5px!important"
		// 		height="2.25rem"
		// 		width="4rem"
		// 		background="brand.mainGold"
		// 		// @ts-ignore
		// 		_hover={""}
		// 		disabled={currentSize <= minSize}
		// 	>
		// 		<Text fontWeight="bold" fontSize="20px" color="white">
		// 			A-
		// 		</Text>
		// 	</Button>
		// 	<Button
		// 		bg={"brand.gray.100"}
		// 		borderRadius="25px"
		// 		p="5px!important"
		// 		onClick={() => increaseSize()}
		// 		height="2.25rem"
		// 		width="4rem"
		// 		background="brand.mainGold"
		// 		// @ts-ignore
		// 		_hover={""}
		// 		disabled={currentSize >= maxSize}
		// 	>
		// 		<Text fontWeight="bold" fontSize="20px" color="white">
		// 			A+
		// 		</Text>
		// 	</Button>
		// </Flex>
		<Box>
			<Popover onOpen={() => setshowResizer(true)} onClose={() => setshowResizer(false)}>
				<PopoverTrigger>
					<Button pos="relative">
						<AccessibilityIcon w={"40px"} h={"40px"} />
					</Button>
				</PopoverTrigger>
				<PopoverContent
					borderRadius="0"
					zIndex={10000}
					w="250px"
					px={5}
					py={5}
					me="32"
					boxShadow="0px 7px 9px rgba(0, 0, 0, 0.12) !important"
				>
					<PopoverBody>
						<FontResizeComp />
						{/* <Box textAlign={"left"}>
							<Text fontSize={"md"} color={"brand.textColor"}>
								{t("textResizer")}
							</Text>
							<Text fontSize={"sm"} color={"brand.textSecondaryColor"} py={4}>
								{t("textResizerDesc")}
							</Text>
							<Flex justifyContent={"space-between"} alignItems={"center"}>
								<Text
									cursor={"pointer"}
									fontWeight="bold"
									fontSize="20px"
									color="black"
									onClick={() => decreaseSize()}
								>
									A-
								</Text>
								<Text
									cursor={"pointer"}
									fontWeight="bold"
									fontSize="20px"
									color="black"
									onClick={() => increaseSize()}
								>
									A+
								</Text>
							</Flex>
							<Slider
								width="100%"
								id="slider"
								defaultValue={16}
								value={currentSize}
								step={1}
								min={12}
								max={20}
								colorScheme="gray"
								onChange={(v: any) => (v > currentSize ? increaseSize(v) : decreaseSize(v))}
							>
								<SliderMark value={12} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 12 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={13} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 13 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={14} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 14 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={15} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 15 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={16} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 16 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={17} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 17 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={18} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 18 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={19} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 19 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderMark value={20} mt="1" ml="-2.5" fontSize="sm">
									<Box
										h={3.5}
										w={1}
										bg={currentSize >= 20 ? "gray" : "#CDD0D4"}
										position={"relative"}
										top={"-11px"}
										zIndex={100}
										borderRadius={"10px"}
									></Box>
								</SliderMark>
								<SliderTrack>
									<SliderFilledTrack />
								</SliderTrack>
							</Slider>
						</Box> */}
					</PopoverBody>
				</PopoverContent>
			</Popover>
		</Box>
	);
}
export default FontResize;
