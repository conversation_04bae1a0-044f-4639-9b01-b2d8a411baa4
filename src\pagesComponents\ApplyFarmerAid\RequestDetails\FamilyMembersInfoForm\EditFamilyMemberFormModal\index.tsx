import {
	Grid,
	Grid<PERSON>tem,
	Modal,
	ModalBody,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	<PERSON>ton,
	Di<PERSON>r,
	<PERSON>dalFooter,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { CloseIcon, TrashIcon } from "components/Icons";
import ModalDialog from "components/ModalDialog";
import { useFormContext } from "context/FormContext";
import { FieldArray, Form, Formik, FormikProps } from "formik";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useRef } from "react";
import { useMemo, useState } from "react";
import { formatAmount } from "utils/formatters";
import { getLookupLabel, getLookupItem } from "utils/helpers";
import * as functions from "./functions";
import { AddIcon } from "@chakra-ui/icons";

interface Props {
	onClose: any;
	member: IFamilyMember | null;
	onEditMember: (member: IFamilyMember) => void;
}

function EditFamilyMemberForm({ onClose, member, onEditMember }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();
	const [isModalShow, setIsModalShow] = useState({
		incomes: false,
		pensions: false,
		tradeLicenses: false,
		RentalIncomes: false,
	});
	const [incomeID, setIncomeID] = useState("");
	const [pensionID, setPensionID] = useState("");
	const [tradeLicenseID, setTradeLicenseID] = useState("");
	const [rentalIncomeID, setRentalIncomeID] = useState("");
	const formikRef = useRef<FormikProps<any>>(null);

	const handleDeleteIncomeModal = (id, type) => {
		if (type === "incomes") {
			setIncomeID(id);
		} else if (type === "pensions") {
			setPensionID(id);
		} else if (type === "tradeLicenses") {
			setTradeLicenseID(id);
		} else if (type === "RentalIncomes") {
			setRentalIncomeID(id);
		}
		handleShowHideModal(true, type);
	};
	const handleDeleteIncome = (remove, type) => {
		if (type === "incomes") {
			remove(incomeID);
		} else if (type === "pensions") {
			remove(pensionID);
		} else if (type === "tradeLicenses") {
			remove(tradeLicenseID);
		} else if (type === "RentalIncomes") {
			remove(rentalIncomeID);
		}
		handleShowHideModal(false, type);
	};

	const handleShowHideModal = (hideOrShow, type) => {
		setIsModalShow((prev) => ({ ...prev, [type]: hideOrShow }));
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};

	const initialValues = useMemo(
		() => ({
			IsFamilyMemberReceiveTradeLicense:
				member?.IsFamilyMemberReceiveTradeLicense === true ? "yes" : "no",
			tradeLicenses: functions.getListDefault(member?.ListTradeLicenseDetails || null).map((i) => ({
				IncomeAmount: i.IncomeAmount || "",
			})),

			IsFamilyMemberReceivePensionIncome:
				member?.IsFamilyMemberReceivePensionIncome === true ? "yes" : "no",
			pensions: functions.getListDefault(member?.ListPensionDetails || null).map((i) => ({
				pensionType: getLookupItem(lookups, "PensionType", i.PensionType),
				pensionAuthority: getLookupItem(lookups, "PensionAuthority", i.PensionAuthority),
				pensionAmount: i.IncomeAmount || "",
			})),

			IsFamilyMemberContributeToIncome:
				member?.IsFamilyMemberContributeToIncome === true ? "yes" : "no",

			incomes: functions.getListDefault(member?.ListIncomeSourceDetails || null).map((i) => ({
				incomeSource: getLookupItem(lookups, "IncomeTypes", i.IncomeSource),
				incomeAmount: i.IncomeAmount || "",
				companyName: i.CompanyName || "",
			})),

			IsFamilyMemberReceiveRentalIncome:
				member?.IsFamilyMemberReceiveRentalIncome === true ? "yes" : "no",

			RentalIncomes: functions.getListDefault(member?.ListRentalDetails || null).map((i) => ({
				rentalSource: getLookupItem(lookups, "rentalSource", i.RentalSource),
				RentAmount: i.IncomeAmount || "",
				ContractNo: i.ContractNumber,
				ContractStartDate: i.ContractStartDate,
				ContractEndDate: i.ContractEndDate,
			})),
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[member?.Id, locale]
	);

	const onSubmit = (...props) => {
		member &&
			onEditMember({
				...member,
				ListTradeLicenseDetails: props[0].tradeLicenses.map((i) => ({
					IncomeAmount: i.IncomeAmount,
				})),
				IsFamilyMemberReceiveTradeLicense: props[0].IsFamilyMemberReceiveTradeLicense === "yes",
				ListPensionDetails: props[0].pensions.map((i) => ({
					PensionType: i.pensionType.value,
					PensionAuthority: i.pensionAuthority.value,
					IncomeAmount: i.pensionAmount,
				})),
				IsFamilyMemberReceivePensionIncome: props[0].IsFamilyMemberReceivePensionIncome === "yes",

				IsFamilyMemberContributeToIncome: props[0].IsFamilyMemberContributeToIncome === "yes",
				ListIncomeSourceDetails: props[0].incomes.map((i) => ({
					IncomeSource: i.incomeSource.value,
					IncomeAmount: i.incomeAmount,
					CompanyName: i.companyName,
				})),

				IsFamilyMemberReceiveRentalIncome: props[0].IsFamilyMemberReceiveRentalIncome === "yes",
				ListRentalDetails: props[0].RentalIncomes.map((i) => ({
					RentalSource: i.rentalSource.value,
					IncomeAmount: i.RentAmount,
					ContractNumber: i.ContractNo,
					ContractStartDate: i.ContractStartDate,
					ContractEndDate: i.ContractEndDate,
				})),

				IsInformationUpdated: true,
			});
	};

	return (
		<>
			<Modal
				isOpen={!!member}
				onClose={onClose}
				size={{ base: "full", md: "6xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{member?.Fullname} -{" "}
									{getLookupLabel(lookups, "FamilyRelationship", member?.Relationship)}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
					>
						{(formik) => (
							<>
								<ModalBody>
									<Form
										onSubmit={(e) => {
											e.preventDefault();
											formik.handleSubmit(e);
										}}
										onChange={(e) => {
											e.preventDefault();
											functions.onChange(e, formik);
										}}
									>
										<Grid
											rowGap={{ base: 6, md: 6 }}
											columnGap={6}
											templateColumns="repeat(2, 1fr)"
											templateRows="auto"
										>
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("doesFamilyMemberContribute")}
													tooltip={t("doesFamilyMemberContributeTooltip")}
													name="IsFamilyMemberContributeToIncome"
													value={formik.values["IsFamilyMemberContributeToIncome"]}
													options={lookups.Boolean}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"IsFamilyMemberContributeToIncome",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["IsFamilyMemberContributeToIncome"] === "yes" && (
												<FieldArray name="incomes">
													{({ remove, push }) => (
														<>
															<ModalDialog
																isModalShow={isModalShow.incomes}
																handleOnClick={() => {
																	handleDeleteIncome(remove, "incomes");
																}}
																handleOnClose={() => {
																	handleShowHideModal(false, "incomes");
																}}
																confirmText={t("confirm", { ns: "common" })}
																cancelText={t("cancel", { ns: "common" })}
																imgSrc="/assets/images/deleteIcon.png"
																headerTitle={t("deleteIncome")}
																confirmationMessage={t("deleteIncomeText")}
																undoneAction={t("deleteCantBeUndone")}
															/>
															{formik.values.incomes.length > 0 &&
																formik.values.incomes.map((item, index) => (
																	<React.Fragment key={index}>
																		{formik.values.incomes.length > 1 && (
																			<GridItem colSpan={{ base: 2, md: 2 }}>
																				{index < formik.values.incomes.length &&
																					index > 0 &&
																					formik.values.incomes.length > 1 && <Divider mb={6} />}
																				<Flex gap={4}>
																					<GridItem colSpan={{ base: 2, md: 2 }}>
																						<Text fontWeight="bold">
																							{t("income")}{" "}
																							{formik.values.incomes.length > 1 && index + 1}
																						</Text>
																					</GridItem>

																					<Flex
																						alignItems="center"
																						border="1px"
																						borderColor="brand.gray.250"
																						borderRadius="0.42rem"
																						px={1.5}
																						cursor="pointer"
																						onClick={() =>
																							handleDeleteIncomeModal(index, "incomes")
																						}
																					>
																						<TrashIcon
																							color="brand.gray.400"
																							h="12px"
																							w="10px"
																							transform="scale(1.1,1.1)"
																						/>
																					</Flex>
																				</Flex>
																			</GridItem>
																		)}
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={item.incomeSource}
																				options={lookups.IncomeTypes}
																				isRequired={true}
																				name={`incomes.${index}.incomeSource`}
																				label={t("incomeSource")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`incomes.${index}.incomeSource`]}
																				touched={formik.touched[`incomes.${index}.incomeSource`]}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						`incomes.${index}.incomeSource`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.incomeAmount}
																				isRequired={true}
																				customFormat={formatAmount}
																				name={`incomes.${index}.incomeAmount`}
																				label={t("incomeAmount")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`incomes.${index}.incomeAmount`]}
																				touched={formik.touched[`incomes.${index}.incomeAmount`]}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`incomes.${index}.incomeAmount`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.companyName}
																				isRequired={false}
																				name={`incomes.${index}.companyName`}
																				label={t("companyName")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`incomes.${index}.companyName`]}
																				touched={formik.touched[`incomes.${index}.companyName`]}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`incomes.${index}.companyName`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	</React.Fragment>
																))}
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<Button
																	variant="outline"
																	p="2px"
																	onClick={() =>
																		push({ incomeSource: "", incomeAmount: "", companyName: "" })
																	}
																	w={{ base: "100%", md: "auto" }}
																>
																	<AddIcon mr={3} />
																	<Text fontWeight="bold">{t("addMoreIncome")}</Text>
																</Button>
															</GridItem>
														</>
													)}
												</FieldArray>
											)}

											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("doesFamilyMemberPension")}
													tooltip={t("doesFamilyMemberPensionTooltip")}
													name="IsFamilyMemberReceivePensionIncome"
													value={formik.values["IsFamilyMemberReceivePensionIncome"]}
													options={lookups.Boolean}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"IsFamilyMemberReceivePensionIncome",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["IsFamilyMemberReceivePensionIncome"] === "yes" && (
												<FieldArray name="pensions">
													{({ remove, push }) => (
														<>
															<ModalDialog
																isModalShow={isModalShow.pensions}
																handleOnClick={() => {
																	handleDeleteIncome(remove, "pensions");
																}}
																handleOnClose={() => {
																	handleShowHideModal(false, "pensions");
																}}
																confirmText={t("confirm", { ns: "common" })}
																cancelText={t("cancel", { ns: "common" })}
																imgSrc="/assets/images/deleteIcon.png"
																headerTitle={t("deletePension")}
																confirmationMessage={t("deletePensionText")}
																undoneAction={t("deleteCantBeUndone")}
															/>
															{formik.values.pensions.length > 0 &&
																formik.values.pensions.map((item, index) => (
																	<React.Fragment key={index}>
																		{formik.values.pensions.length > 1 && (
																			<GridItem colSpan={{ base: 2, md: 2 }}>
																				{index < formik.values.pensions.length &&
																					index > 0 &&
																					formik.values.pensions.length > 1 && <Divider mb={6} />}
																				<Flex gap={4}>
																					<GridItem colSpan={{ base: 2, md: 2 }}>
																						<Text fontWeight="bold">
																							{t("pension")}{" "}
																							{formik.values.pensions.length > 1 && index + 1}
																						</Text>
																					</GridItem>

																					<Flex
																						alignItems="center"
																						border="1px"
																						borderColor="brand.gray.250"
																						borderRadius="0.42rem"
																						px={1.5}
																						cursor="pointer"
																						onClick={() =>
																							handleDeleteIncomeModal(index, "pensions")
																						}
																					>
																						<TrashIcon
																							color="brand.gray.400"
																							h="12px"
																							w="10px"
																							transform="scale(1.1,1.1)"
																						/>
																					</Flex>
																				</Flex>
																			</GridItem>
																		)}
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={item.pensionType}
																				options={lookups.PensionType}
																				isRequired={true}
																				name={`pensions.${index}.pensionType`}
																				label={t("PensionType")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`pensions.${index}.pensionType`]}
																				touched={formik.touched[`pensions.${index}.pensionType`]}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						`pensions.${index}.pensionType`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={item.pensionAuthority}
																				options={lookups.PensionAuthority}
																				isRequired={true}
																				name={`pensions.${index}.pensionAuthority`}
																				label={t("PensionAuthority")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`pensions.${index}.pensionAuthority`]}
																				touched={
																					formik.touched[`pensions.${index}.pensionAuthority`]
																				}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						`pensions.${index}.pensionAuthority`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.pensionAmount}
																				isRequired={true}
																				customFormat={formatAmount}
																				name={`pensions.${index}.pensionAmount`}
																				label={t("pensionAmount")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`pensions.${index}.pensionAmount`]}
																				touched={formik.touched[`pensions.${index}.pensionAmount`]}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`pensions.${index}.pensionAmount`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	</React.Fragment>
																))}
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<Button
																	variant="outline"
																	p="2px"
																	onClick={() =>
																		push({
																			pensionAmount: "",
																			pensionType: "",
																			pensionAuthority: "",
																		})
																	}
																	w={{ base: "100%", md: "auto" }}
																>
																	<AddIcon mr={3} />
																	<Text fontWeight="bold">{t("addPension")}</Text>
																</Button>
															</GridItem>
														</>
													)}
												</FieldArray>
											)}
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("doesFamilyTradeLicense")}
													name="IsFamilyMemberReceiveTradeLicense"
													value={formik.values["IsFamilyMemberReceiveTradeLicense"]}
													options={lookups.Boolean}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"IsFamilyMemberReceiveTradeLicense",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["IsFamilyMemberReceiveTradeLicense"] === "yes" && (
												<FieldArray name="tradeLicenses">
													{({ remove, push }) => (
														<>
															<ModalDialog
																isModalShow={isModalShow.tradeLicenses}
																handleOnClick={() => {
																	handleDeleteIncome(remove, "tradeLicenses");
																}}
																handleOnClose={() => {
																	handleShowHideModal(false, "tradeLicenses");
																}}
																confirmText={t("confirm", { ns: "common" })}
																cancelText={t("cancel", { ns: "common" })}
																imgSrc="/assets/images/deleteIcon.png"
																headerTitle={t("deleteTradeLicense")}
																confirmationMessage={t("deleteTradeLicenseText")}
																undoneAction={t("deleteCantBeUndone")}
															/>
															{formik.values.tradeLicenses.length > 0 &&
																formik.values.tradeLicenses.map((item, index) => (
																	<React.Fragment key={index}>
																		{formik.values.tradeLicenses.length > 1 && (
																			<GridItem colSpan={{ base: 2, md: 2 }}>
																				{index < formik.values.tradeLicenses.length &&
																					index > 0 &&
																					formik.values.tradeLicenses.length > 1 && (
																						<Divider mb={6} />
																					)}
																				<Flex gap={4}>
																					<GridItem colSpan={{ base: 2, md: 2 }}>
																						<Text fontWeight="bold">
																							{t("tradeLicense")}{" "}
																							{formik.values.tradeLicenses.length > 1 && index + 1}
																						</Text>
																					</GridItem>

																					<Flex
																						alignItems="center"
																						border="1px"
																						borderColor="brand.gray.250"
																						borderRadius="0.42rem"
																						px={1.5}
																						cursor="pointer"
																						onClick={() =>
																							handleDeleteIncomeModal(index, "tradeLicenses")
																						}
																					>
																						<TrashIcon
																							color="brand.gray.400"
																							h="12px"
																							w="10px"
																							transform="scale(1.1,1.1)"
																						/>
																					</Flex>
																				</Flex>
																			</GridItem>
																		)}
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.IncomeAmount}
																				isRequired={true}
																				name={`tradeLicenses.${index}.IncomeAmount`}
																				customFormat={formatAmount}
																				label={t("tradeLicenseAmount")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`tradeLicenses.${index}.IncomeAmount`]}
																				touched={
																					formik.touched[`tradeLicenses.${index}.IncomeAmount`]
																				}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`tradeLicenses.${index}.IncomeAmount`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	</React.Fragment>
																))}
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<Button
																	variant="outline"
																	p="2px"
																	onClick={() => push({ tradeLicenses: "" })}
																	w={{ base: "100%", md: "auto" }}
																>
																	<AddIcon mr={3} />
																	<Text fontWeight="bold">{t("addTradeLicense")}</Text>
																</Button>
															</GridItem>
														</>
													)}
												</FieldArray>
											)}

											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("doesFamilyMemberRental")}
													name="IsFamilyMemberReceiveRentalIncome"
													value={formik.values["IsFamilyMemberReceiveRentalIncome"]}
													options={lookups.Boolean}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"IsFamilyMemberReceiveRentalIncome",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["IsFamilyMemberReceiveRentalIncome"] === "yes" && (
												<FieldArray name="RentalIncomes">
													{({ remove, push }) => (
														<>
															<ModalDialog
																isModalShow={isModalShow.RentalIncomes}
																handleOnClick={() => {
																	handleDeleteIncome(remove, "RentalIncomes");
																}}
																handleOnClose={() => {
																	handleShowHideModal(false, "RentalIncomes");
																}}
																confirmText={t("confirm", { ns: "common" })}
																cancelText={t("cancel", { ns: "common" })}
																imgSrc="/assets/images/deleteIcon.png"
																headerTitle={t("deleteRentalIncome")}
																confirmationMessage={t("deleteRentalIncomeText")}
																undoneAction={t("deleteCantBeUndone")}
															/>
															{formik.values.RentalIncomes.length > 0 &&
																formik.values.RentalIncomes.map((item, index) => (
																	<React.Fragment key={index}>
																		{formik.values.RentalIncomes.length > 1 && (
																			<GridItem colSpan={{ base: 2, md: 2 }}>
																				{index < formik.values.RentalIncomes.length &&
																					index > 0 &&
																					formik.values.RentalIncomes.length > 1 && (
																						<Divider mb={6} />
																					)}
																				<Flex gap={4}>
																					<GridItem colSpan={{ base: 2, md: 2 }}>
																						<Text fontWeight="bold">
																							{t("RentalIncome")}{" "}
																							{formik.values.RentalIncomes.length > 1 && index + 1}
																						</Text>
																					</GridItem>

																					<Flex
																						alignItems="center"
																						border="1px"
																						borderColor="brand.gray.250"
																						borderRadius="0.42rem"
																						px={1.5}
																						cursor="pointer"
																						onClick={() =>
																							handleDeleteIncomeModal(index, "RentalIncomes")
																						}
																					>
																						<TrashIcon
																							color="brand.gray.400"
																							h="12px"
																							w="10px"
																							transform="scale(1.1,1.1)"
																						/>
																					</Flex>
																				</Flex>
																			</GridItem>
																		)}
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.ContractNo}
																				isRequired={true}
																				name={`RentalIncomes.${index}.ContractNo`}
																				label={t("ContractNo")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`RentalIncomes.${index}.ContractNo`]}
																				touched={
																					formik.touched[`RentalIncomes.${index}.ContractNo`]
																				}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`RentalIncomes.${index}.ContractNo`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>

																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="datetime"
																				value={item.ContractStartDate}
																				isRequired={true}
																				name={`RentalIncomes.${index}.ContractStartDate`}
																				label={t("ContractStartDate")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={
																					formik.errors[`RentalIncomes.${index}.ContractStartDate`]
																				}
																				touched={
																					formik.touched[`RentalIncomes.${index}.ContractStartDate`]
																				}
																				handleChangeEvent={handleChangeEvent}
																				onChange={(firstArg: Date) => {
																					if (
																						formik.values.RentalIncomes?.[index]?.ContractEndDate
																					) {
																						formik.setFieldTouched(
																							`RentalIncomes.${index}.ContractEndDate`,
																							undefined,
																							true
																						);
																					}
																					handleChangeEvent(
																						"datetime",
																						firstArg.toISOString(),
																						`RentalIncomes.${index}.ContractStartDate`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="datetime"
																				value={item.ContractEndDate}
																				isRequired={true}
																				name={`RentalIncomes.${index}.ContractEndDate`}
																				label={t("ContractEndDate")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={
																					formik.errors[`RentalIncomes.${index}.ContractEndDate`]
																				}
																				touched={
																					formik.touched[`RentalIncomes.${index}.ContractEndDate`]
																				}
																				minDate={
																					item.ContractStartDate
																						? new Date(item.ContractStartDate).setDate(
																								new Date(item.ContractStartDate).getDate() + 1
																						  )
																						: new Date()
																				}
																				handleChangeEvent={handleChangeEvent}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"datetime",
																						firstArg.toISOString(),
																						`RentalIncomes.${index}.ContractEndDate`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>

																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={item.rentalSource}
																				options={lookups.rentalSource}
																				isRequired={true}
																				name={`RentalIncomes.${index}.rentalSource`}
																				label={t("rentalSource")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`RentalIncomes.${index}.rentalSource`]}
																				touched={
																					formik.touched[`RentalIncomes.${index}.rentalSource`]
																				}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						`RentalIncomes.${index}.rentalSource`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="text"
																				value={item.RentAmount}
																				isRequired={true}
																				name={`RentalIncomes.${index}.RentAmount`}
																				label={t("RentAmount")}
																				customFormat={formatAmount}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors[`RentalIncomes.${index}.RentAmount`]}
																				touched={
																					formik.touched[`RentalIncomes.${index}.RentAmount`]
																				}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"text",
																						firstArg,
																						`RentalIncomes.${index}.RentAmount`,
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	</React.Fragment>
																))}
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<Button
																	variant="outline"
																	p="2px"
																	onClick={() =>
																		push({
																			ContractNo: "",
																			ContractStartDate: "",
																			ContractEndDate: "",
																			rentalSource: "",
																			RentAmount: "",
																		})
																	}
																	w={{ base: "100%", md: "auto" }}
																>
																	<AddIcon mr={3} />
																	<Text fontWeight="bold">{t("addRentalIncome")}</Text>
																</Button>
															</GridItem>
														</>
													)}
												</FieldArray>
											)}
										</Grid>
									</Form>
								</ModalBody>
								<ModalFooter borderTop="1px solid #BBBCBD">
									<HStack w={"100%"} gap={2} my={4}>
										<Button variant="secondary" w={"100%"} onClick={onClose}>
											{t("common:cancel")}
										</Button>
										<Button
											variant="primary"
											w={"100%"}
											isDisabled={!formik.isValid}
											onClick={formik.submitForm}
										>
											{t("common:save")}
										</Button>
									</HStack>
								</ModalFooter>
							</>
						)}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditFamilyMemberForm;
