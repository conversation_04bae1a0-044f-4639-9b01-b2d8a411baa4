{"createNewUaePass": "إنشاء حساب جديد", "description": "هوية رقمية موحدة ومعتمدة لجميع المواطنين", "didntReceiveOtp": "لم تحصل على الرمز؟", "dontHaveUaePass": "ليس لديك هوية رقمية؟", "eidPlaceholder": "رقم الهوية الإماراتية مثال: ‎784-1990-1234567-8", "loginOr": "أو", "loginToAccess": "يرجى تسجيل الدخول من أجل الوصول للصفحة", "otpCancel": "الغاء", "otpNotification": "يرجى ملاحظة أنه سيتم إرسال رقم المرور المؤقت إلى رقم الهاتف المسجل في الهوية الإماراتية", "otpPlaceholder": "ادخل الرقم السري مثال: 0950986", "otpResentSuccessDescription": "لقد أرسلنا رمز التحقق إلى رقم هاتفك المحمول.", "otpResentSuccessTitle": "تم إعادة إرسال الرمز ", "otpSubmit": "تأكيد", "pleaseEnterValidEid": "يرجى ادخال رقم هوية إماراتية فعالة", "pleaseEnterValidOtp": "يُرجى إدخال رمز التحقق الصحيح للمتابعة", "requestOtp": "طلب رقم مرور مؤقت", "sendOtpAgain": "أنقر لإعادة الإرسال", "signInWithUaePass": "تسجيل الدخول عن طريق الهوية الرقمية", "title": "تسجيل الدخول", "validateOtpDescription": "رمز التحقق ارسل الى رقم الهاتف {{otpNumber}}", "validateOtpTitle": "ادخل رمز التحقق", "verificationCode": "<PERSON><PERSON><PERSON> التحقق", "Sign_In_UAE_PASS": "تسجيل الدخول بالهويه الرقميه"}