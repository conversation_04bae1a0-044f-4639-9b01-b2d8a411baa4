import { Box, Button, Flex, Progress, Text } from "@chakra-ui/react";
import { useSteps } from "chakra-ui-steps";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import React, { ReactElement, useId, useState, useEffect } from "react";
import Select from "react-select";
import { customStyles } from "components/Form/SelectableTagsField";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { getLookUp } from "pagesComponents/calculator/lookups";
import LowIncomeHousehold from "pagesComponents/calculator/LowIncomeHousehold";
import UnemployedHousehold from "pagesComponents/calculator/UnemployedHousehold";
import PoDOrHealthCase from "pagesComponents/calculator/PoDOrHealthCase";
import WomenInDFSCase from "pagesComponents/calculator/WomenInDifficultSituation";
import ChildInDFSForm from "pagesComponents/calculator/ChildInDFS";
import { ChevronRightIcon } from "@chakra-ui/icons";

function Calculator(props) {
	const { t } = useTranslation(["calculator", "common"]);
	const stepManager = useSteps({
		initialStep: 0,
	});
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-myAllowance"),
			id: "navbar-myAllowance",
			link: "#",
			isCurrentPage: true,
		},
	];
	const [reason, setReason] = useState<string>();
	const [selectedValue, setSelectedValue] = useState(null);
	const { locale, push, reload } = useRouter();
	const router = useRouter();

	const id = useId();
	const stepCounter = ["1", "2"].includes(reason || "") ? 3 : 2;
	const progressPercantage = ((stepManager.activeStep + 1) / stepCounter) * 100;

	useEffect(() => {
		setReason(undefined);
		stepManager.reset();
		setSelectedValue(null);
	}, []);

	return (
		<Box pt={"2rem"} px={{ base: "4", lg: "6.25rem" }} w="full">
			<Flex w="full" justifyContent={"center"} bg="#F5F5F5">
				<Box
					bg="white"
					pb={16}
					pt={8}
					my={{ base: "1rem", md: "2rem" }}
					px={{ base: "2rem", md: "5.5rem" }}
					shadow={"sm"}
					rounded="lg"
					maxW={{ base: "95%", md: "1000px" }}
					flex={1}
					mx={"1rem"}
				>
					<Box mb={4}>
						<Button
							color="brand.mainGold"
							px="0 !important"
							onClick={() => {
								router.push("/");
							}}
						>
							{t("common:navbar-home")}
						</Button>
						<Button
							color="brand.mainGold"
							px="0 !important"
							onClick={() => {
								setReason(undefined);
								stepManager.reset();
							}}
							leftIcon={
								<ChevronRightIcon
									boxSize={"6"}
									transform={"scale(1.2,1.2)" + (locale === "ar" ? " scale(-1,1)" : "")}
								/>
							}
						>
							{t("AllowanceCalculatorBreadCrums")}
						</Button>
					</Box>
					{!reason ? (
						<Box mb={4}>
							<Progress variant={"blank"} size="sm" rounded="lg" value={0} />
						</Box>
					) : (
						stepManager.activeStep !== stepCounter - 1 && (
							<Box mb={4}>
								<Text color="#707070" mb={1}>
									{t("page")} {stepManager.activeStep + 1} {t("outOf")} {stepCounter}
								</Text>
								<Progress
									variant={progressPercantage === 100 ? "success" : "onProgress"}
									size="sm"
									rounded="lg"
									value={progressPercantage}
								/>
							</Box>
						)
					)}
					{stepManager.activeStep === 0 && (
						<>
							<Text fontSize={"1.5rem"} fontWeight={"bold"}>
								{t("calculateYourAll")}
							</Text>
							<Box maxW="500px" mt={4}>
								<Text color="#1b1d21b8" mb={1} fontWeight={"500"}>
									{t("reasonForApplying")}
								</Text>
								<Select
									styles={customStyles}
									instanceId={id}
									menuPosition={"fixed"}
									isSearchable={false}
									isClearable={false}
									isMulti={false}
									placeholder={t("chooseAnOption")}
									components={{
										IndicatorSeparator: () => null,
									}}
									options={getLookUp("reason", locale)}
									onChange={(e: any) => {
										setReason(e?.value!);
										setSelectedValue(e);
									}}
									value={getLookUp("reason", locale).find((r) => r.value === reason)}
								/>
							</Box>
						</>
					)}
					{stepManager.activeStep === stepCounter - 1 && (
						<>
							<Box mb={4}>
								<Text color="#707070" mb={1}>
									{t("page")} {stepManager.activeStep + 1} {t("outOf")} {stepCounter}
								</Text>
								<Progress
									variant={progressPercantage === 100 ? "success" : "onProgress"}
									size="sm"
									rounded="lg"
									value={progressPercantage}
								/>
							</Box>
						</>
					)}

					{reason === "1" && <LowIncomeHousehold stepManager={stepManager} reason={reason} />}
					{reason === "2" && <UnemployedHousehold stepManager={stepManager} reason={reason} />}
					{reason === "3" && <PoDOrHealthCase stepManager={stepManager} reason={reason} />}
					{reason === "4" && <WomenInDFSCase stepManager={stepManager} reason={reason} />}
					{reason === "5" && <ChildInDFSForm stepManager={stepManager} reason={reason} />}
				</Box>
			</Flex>
		</Box>
	);
}

Calculator.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};

export async function getServerSideProps(ctx) {
	// return {
	// 	redirect: {
	// 		destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}smart-services`,
	// 		permanent: false,
	// 	},
	// };
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "calculator", "forms"])),
		},
	};
}
export default Calculator;
