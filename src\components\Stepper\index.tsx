import { Flex, Circle, Text, Box } from "@chakra-ui/react";
import { CheckIcon } from "components/Icons";
import React from "react";
import { useTranslation } from "react-i18next";

export default function Stepper({
	activeStep,
	steps,
	stepOnClick,
	subStepOnClick,
	activeSubIndex,
	service,
}) {
	let steptitleStyle = {
		size: "sm",
		color: "black",
		weight: "medium",
	};
	let stepsupTitleStyle = {
		size: "xs",
		color: "brand.gray.400",
		weight: "normal",
	};
	let subTitleWeight = 400;
	const renderTitleIcon = (currentIndex, activeStep) => {
		if (currentIndex < activeStep) {
			return (
				<Circle
					//cursor={"pointer"}
					size="24px"
					bg={"#3F8E50"}
					color="white"
					zIndex={1}
					onClick={() => {
						stepOnClick(activeStep);
					}}
				>
					<CheckIcon />
				</Circle>
			);
		} else if (currentIndex === activeStep) {
			return (
				<Circle size="24px" bg={"brand.mainGold"} color="white" zIndex={1}>
					<Text fontSize={"12px"} fontWeight={500}>
						{currentIndex + 1}
					</Text>
				</Circle>
			);
		} else {
			return (
				<Circle
					size="24px"
					borderWidth={"1px"}
					borderColor={"rgba(27,29,33,0.54)"}
					color="brand.textColor"
					zIndex={1}
				>
					<Text fontSize={"12px"} color={"rgba(27,29,33,0.54)"} fontWeight={500}>
						{currentIndex + 1}
					</Text>
				</Circle>
			);
		}
	};

	const renderSubTitleIcon = (currentSubIndex, activeSubStep, currentIndex, activeStep) => {
		if (currentSubIndex < activeSubStep || currentIndex < activeStep) {
			subTitleWeight = 400;
			return (
				<Circle
					//cursor={"pointer"}
					size="16px"
					bg={"#3F8E50"}
					color="white"
					zIndex={1}
					ml={"5px"}
					onClick={() => {
						subStepOnClick(activeSubStep);
					}}
				>
					<CheckIcon w={"9px"} h={"6px"} />
				</Circle>
			);
		} else if (currentSubIndex === activeSubStep) {
			subTitleWeight = 600;
			return (
				<Circle
					size="7px"
					bg="brand.mainGold"
					borderWidth={"1px"}
					borderColor={"brand.mainGold"}
					zIndex={1}
					ml={"9px"}
				></Circle>
			);
		} else {
			subTitleWeight = 400;
			return (
				<Circle
					size="7px"
					bg="brand.white"
					borderWidth={"1px"}
					borderColor={"rgba(27,29,33,0.54)"}
					zIndex={1}
					ml={"9px"}
				></Circle>
			);
		}
	};
	const { t } = useTranslation("forms");

	return (
		<Flex w={"100%"} flexDirection={"column"} marginInlineStart={{ md: "2", lg: "10" }}>
			<Text>{t("theService")}</Text>
			<Text mb={6} paddingInlineEnd={10} fontWeight={"normal"} mt={2} fontSize={"2xl"}>
				{service}
			</Text>
			{steps?.map((item, index) => {
				let isLast = index === steps.length - 1;
				let currentIndex = index;
				let dividerHeight = item.subSteps.length > 0 ? "32px" : "64px;";
				return (
					<React.Fragment key={index}>
						<Flex flexDirection={"row"} alignItems={"center"} mb={"-1px"} mt={index === 0 ? 0 : 6}>
							{renderTitleIcon(currentIndex, activeStep)}
							<Text
								fontSize={steptitleStyle.size}
								color={steptitleStyle.color}
								fontWeight={steptitleStyle.weight}
								ml={3.5}
								mb="4px"
							>
								{item.label}
							</Text>
						</Flex>
						{!isLast && (
							<Box
								borderLeft={"1px"}
								borderColor={"#1b1d2133"}
								h={dividerHeight}
								w={"100%"}
								ml={"12px"}
								position={"relative"}
								top={"-1px"}
							></Box>
						)}
						{item.subSteps?.map((subItem, subIndex) => {
							let isLastIndex = subIndex === item.subSteps.length - 1;
							return (
								<React.Fragment key={subIndex}>
									<Flex flexDirection={"row"} alignItems={"center"} mt={"0px"}>
										{renderSubTitleIcon(subIndex, activeSubIndex, index, activeStep)}
										<Text
											fontSize={stepsupTitleStyle.size}
											color={stepsupTitleStyle.color}
											fontWeight={subTitleWeight}
											ml={3.5}
										>
											{subItem}
										</Text>
									</Flex>
									{!isLastIndex && (
										<Box
											borderLeft={"1px"}
											borderColor={"#1b1d2133"}
											h={"32px"}
											w={"100%"}
											ml={"12px"}
											position={"relative"}
											top={"-1px"}
										></Box>
									)}
								</React.Fragment>
							);
						})}
					</React.Fragment>
				);
			})}
		</Flex>
	);
}
