import {
	Box,
	<PERSON>ton,
	Flex,
	Modal,
	<PERSON>dal<PERSON>ody,
	Modal<PERSON>lose<PERSON>utton,
	<PERSON>dal<PERSON>ontent,
	ModalHeader,
	ModalOverlay,
	Show,
	Text,
	useDisclosure,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { createColumnHelper } from "@tanstack/react-table";
import TablePage from "pagesComponents/TablePage";
import React, { ReactElement, useEffect } from "react";
import StatusPill from "components/StatusPill";
import NextLink from "next/link";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import {
	addLocalLookups,
	getEmiratesIdFromToken,
	getFormattedDate,
	getLocalizedLookups,
} from "utils/helpers";

import { ICrmRequest } from "interfaces/CrmRequest.interface";
import { MyCasesDetailModal } from "pagesComponents/DetailModal";
import { AddIcon } from "@chakra-ui/icons";
import { useRouter } from "next/router";
import Select from "react-select";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import { PENDING_STATUS_ID } from "config";
import useAppToast from "hooks/useAppToast";
import useCaseToast from "hooks/useCaseToast";
import { ButtonArrowIcon } from "components/Icons";
import Breadcrumbs from "components/Breadcrumbs";
const customStyles = {
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
	}),
	control: (base, state) => ({
		...base,
		borderRadius: "2px",
		// color: "white",
		display: "flex",
		borderColor: "#B08D44",
		innerWidth: "100%",
		height: "100%",
		outerHeight: "100%",
		"&:hover": {
			boxShadow: "0 0 0 1px #B08D44",
		},
		color: "red",
		border: state.isFocused ? 0 : "1px solid #DDE1E6",
		// This line disable the blue border
		boxShadow: state.isFocused ? "0 0 0 1px #B08D44" : 0,
		// border: isDisabled ? "2px solid #C1C7CD" : "1px solid #DDE1E6",
		// color: state.isDisabled ? "red" : "white",
	}),
	option: (provided, state) => ({
		...provided,
		display: "flex",
		color: "#001841",
		zIndex: 999,
		"&:hover": {
			background: "#DDE1E6",
		},
		backgroundColor: state.isSelected ? "#DDE1E6" : "white",
	}),
	menu: (styles, state) => ({
		...styles,
		marginBottom: "1px",
		// zIndex: 9999,
	}),

	multiValue: (styles) => ({
		...styles,
		background: "#B08D44",
		borderRadius: "16px",
		color: "white",
		"&:hover": {
			background: "#B08D44",
		},
	}),
	menuList: (provided, state) => ({
		...provided,
		paddingTop: "-1px",
		paddingBottom: "-4px",
		marginTop: "-4px",
		boxShadow: "2px 4px 10px 2px #DDE1E6",
	}),
	menuPortal: (provided) => ({
		...provided,
		left: "unset",
		right: "unset",
	}),
};

function MyCases(props: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["common", "tables", "smartservice"]);
	const { locale } = useRouter();
	const [selectedStatus, setSelectedStatus] = React.useState("");
	const [statusForSelectedOption, setStatusForSelectedOption] = React.useState<any>("");

	let allStatus = {
		label: t("allStatus"),
		value: "",
	};
	const [status, setStatus] = React.useState([allStatus, ...props.masterData?.CaseStatus]);
	const columnHelper = createColumnHelper<ICrmRequest>();
	const toast = useAppToast();

	const columns = [
		columnHelper.accessor("RequestName", {
			cell: (info) => (
				<Text color="brand.blue.300" textDecoration="underline" fontSize="sm" cursor={"pointer"}>
					{info.getValue()}
				</Text>
			),
			header: `${t("tables:requestNumber")}`,
		}),
		columnHelper.accessor("Template", {
			cell: (info) =>
				locale === "en" ? info.getValue().TemplateName : info.getValue().TemplateNameAr,
			header: `${t("tables:serviceType")}`,
		}),
		columnHelper.accessor("CreatedDate", {
			cell: (info) => getFormattedDate(info.getValue(), "dd MMMM yyyy", locale),
			header: `${t("tables:createdDate")}`,
			meta: {},
		}),
		columnHelper.accessor("Status", {
			cell: (info) => {
				return (
					<StatusPill
						status={info.getValue()}
						caseId={info.row?.original?.CaseId}
						requestName={info.row?.original?.RequestName}
						eligibleForAppeal={info.row.original?.EligibleForAppeal}
						eligibleForEdit={info.row.original?.EligibleForEdit}
						statusLookup={props.masterData?.CaseStatus || []}
						reasonsLookupInflation={props.masterData?.ReasonsToEditInflation || []}
						reasonsLookupSWP={props.masterData?.ReasonsToEditSwp || []}
						templateId={info.row?.original?.Template?.TemplateId}
						isNominatedInflationCase={info.row?.original?.IsNomiatedInflationCase}
					/>
				);
			},
			header: `${t("tables:status")}`,
		}),
	];
	const uCT = useCaseToast(props.requests, (req) => req.Status.Key === PENDING_STATUS_ID);
	const { isOpen, onOpen, onClose } = useDisclosure();

	const services = [
		{
			name: t("landingPageTitle"),
			link: "/smart-services/how-to-apply",
		},
		// {
		// 	name: t("farmerAidBreadTitle"),
		// 	link: "/smart-services/farmer-service",
		// },
		{
			name: t("applying-for-to-whom"),
			link: "/smart-services/to-whom-apply",
		},
		{
			name: t("Inflation"),
			link: "/smart-services/inflation-service",
		},
	];

	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-myCases"),
			id: "navbar-myCases",
			link: "#",
			isCurrentPage: true,
		},
	];
	useEffect(() => {
		setSelectedStatus("");
		setStatusForSelectedOption("");
	}, [locale]);
	let statusArray = [allStatus, ...props.masterData?.CaseStatus];

	return (
		<Box pt={{ base: 4, md: 9 }} px={8} pb={8} w="100%" minH={"50vh"}>
			<Box display={{ base: "none", md: "block" }}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Flex
				w="100%"
				py={{ base: 0, md: 4 }}
				mb={{ base: 2, md: 0 }}
				flexDirection={{ base: "column", md: "row" }}
				bg={{ base: "unset", md: "brand.white.50" }}
			>
				<Flex w={"100%"}>
					<Text fontSize={{ base: "2xl", md: "h4" }} fontWeight="medium" w={"100%"}>
						{t("myCases")}
					</Text>
				</Flex>
				<Flex
					mt={{ base: 4, md: 0 }}
					width={{ base: "100%", md: "40%" }}
					height={12}
					justifyContent="space-between"
				>
					<Box w={{ base: "60%", md: "100%" }} minW="100px">
						<Select
							className={"mycases-dropdown"}
							value={statusForSelectedOption}
							menuPosition={"fixed"}
							isDisabled={false}
							styles={customStyles}
							options={statusArray}
							onChange={(e: any) => {
								setSelectedStatus(e.value);
								setStatusForSelectedOption(e);
							}}
							placeholder={`${t("allStatus")}`}
							components={{
								IndicatorSeparator: () => null,
							}}
							isSearchable={false}
						/>
					</Box>

					<Show below="md">
						<Button
							w="40%"
							leftIcon={<AddIcon boxSize={3} />}
							ms={4}
							height={12}
							variant="primary"
							onClick={onOpen}
						>
							{t("newRequest")}
						</Button>
					</Show>
				</Flex>
				<Show above="md">
					<Box w="15%">
						<Button
							w="95%"
							onClick={onOpen}
							leftIcon={<AddIcon boxSize={3} />}
							ms={4}
							height={12}
							variant="primary"
						>
							{t("newRequest")}
						</Button>
					</Box>
				</Show>
			</Flex>
			<TablePage
				columns={columns}
				data={props.requests.filter(
					(i) => !selectedStatus || (selectedStatus && i.Status.Key === selectedStatus)
				)}
				Modal={MyCasesDetailModal({
					statusLookup: props.masterData?.CaseStatus || [],
				})}
				type={"allowance"}
				StatusArabic={props.masterData?.CaseStatus}
			/>

			<Modal isOpen={isOpen} onClose={onClose} size={"xl"}>
				<ModalOverlay />
				<ModalContent w="fit" maxW="95%">
					<ModalHeader>{t("newRequest")}</ModalHeader>
					<ModalCloseButton />
					<ModalBody>
						{services.map((service, idx) => (
							<Flex
								key={idx}
								border="1px solid #BBBCBD"
								rounded="base"
								p="1rem"
								justifyContent={"space-between"}
								alignItems={"center"}
								mb="4"
							>
								<Text as="h2" fontWeight={"bold"}>
									{service.name}
								</Text>
								<Button
									variant="outline"
									rightIcon={
										<ButtonArrowIcon
											w={"24px"}
											h={"24px"}
											transform={locale === "ar" ? "scale(-1, 1)" : ""}
										/>
									}
									as={NextLink}
									href={service.link}
									width={"fit-content"}
								>
									{t("startTheService", { ns: "smartservice" })}
								</Button>
							</Flex>
						))}
					</ModalBody>
				</ModalContent>
			</Modal>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const emiratesId = await getEmiratesIdFromToken(ctx.req);

	const requests = (await BackendServices.retrieveAllRequests(emiratesId))?.Data || [];

	requests.sort((a, b) => new Date(b.CreatedDate).getTime() - new Date(a.CreatedDate).getTime());
	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", [
				"common",
				"tables",
				"forms",
				"smartservice",
			])),
			requests,
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
		},
	};
}

MyCases.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default MyCases;
