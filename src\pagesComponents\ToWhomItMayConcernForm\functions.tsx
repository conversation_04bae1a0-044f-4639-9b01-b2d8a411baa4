import { EntityAddressed } from "config";
import * as Yup from "yup";

const getInitialValues = {
	email: "",
	mobileNo: "",
	entityAddressed: "",
	otherEntity: "",
	typeOfCertificate: "",
	otherEntityAddressed: "",
};

const getValidationSchema = () => {
	return Yup.object({
		mobileNo: Yup.string()
			.notRequired()
			.nullable()
			.matches(/^05(\d){8}$/, "uaeMobileNumberError"),
		email: Yup.string().email("wrongEmailAddress").required().label("thisField"),
		entityAddressed: Yup.object().required().label("thisField"),
		typeOfCertificate: Yup.object().required().label("thisField"),
		otherEntity: Yup.object().when("entityAddressed", {
			is: (entityAddressed) => {
				return entityAddressed?.value === EntityAddressed.Other;
			},
			then: Yup.object().required().label("thisField"),
			otherwise: Yup.object().required().label("thisField"),
		}),
		otherEntityAddressed: Yup.string().when("otherEntity", {
			is: (otherEntity) => {
				return otherEntity?.value === "f54116ec-22e4-ed11-8847-6045bd6a528f";
			},
			then: Yup.string().required().label("thisField"),
			otherwise: Yup.string().notRequired(),
		}),
	});
};

const onChange = (event: any, formikProps: any) => {
	//console.log("formikProps", formikProps);
};

export { getInitialValues, onChange, getValidationSchema };
