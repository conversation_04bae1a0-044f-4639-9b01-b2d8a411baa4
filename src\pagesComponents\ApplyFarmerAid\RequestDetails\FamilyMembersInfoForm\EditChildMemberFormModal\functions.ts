import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	IsFamilyMemberReceiveTradeLicense: "",
	tradeLicenses: {
		IncomeAmount: 0,
	},

	IsFamilyMemberReceivePensionIncome: "",
	pensions: {
		pensionType: "",
		pensionAuthority: "",
		pensionAmount: 0,
	},

	IsFamilyMemberContributeToIncome: "",
	incomes: {
		incomeSource: "",
		incomeAmount: 0,
		companyName: "",
	},

	IsFamilyMemberReceiveRentalIncome: "",
	RentalIncomes: {
		IncomeAmount: "",
		ContractNumber: "",
		ContractStartDate: "",
		ContractEndDate: "",
		RentalSource: "",
	},
};

const getValidationSchema = () => {
	return Yup.object({
		IsFamilyMemberReceiveTradeLicense: Yup.string().required(),
		tradeLicenses: Yup.array().when("IsFamilyMemberReceiveTradeLicense", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							IncomeAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
						})
						.required()
						.label("thisField")
				),
		}),
		IsFamilyMemberReceivePensionIncome: Yup.string().required(),
		pensions: Yup.array().when("IsFamilyMemberReceivePensionIncome", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							pensionAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							pensionType: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
							pensionAuthority: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
		IsFamilyMemberContributeToIncome: Yup.string().required(),
		incomes: Yup.array().when("IsFamilyMemberContributeToIncome", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							incomeSource: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
							incomeAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							companyName: Yup.string().notRequired().label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
		IsFamilyMemberReceiveRentalIncome: Yup.string().required().label("thisField"),
		RentalIncomes: Yup.array().when("IsFamilyMemberReceiveRentalIncome", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							ContractNo: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							ContractStartDate: Yup.date().required().label("thisField").typeError("mustBeDate"),
							ContractEndDate: Yup.date()
								.required()
								.label("thisField")
								.typeError("mustBeDate")
								.min(Yup.ref("ContractStartDate"), "endDateCantStartBeforeStartDate")
								.test("same_dates_test", "endDateCantStartBeforeStartDate", function (value) {
									if (JSON.stringify(this.parent.ContractStartDate) === JSON.stringify(value))
										return false;
									return true;
								}),
							RentAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							rentalSource: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string().required(),
								})
								.required()
								.label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};

const getListDefault = (arr) => (!arr || arr?.length === 0 ? [{}] : arr);

export { getInitialValues, onChange, getValidationSchema, getListDefault };
