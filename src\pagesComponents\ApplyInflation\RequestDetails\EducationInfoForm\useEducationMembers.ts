import { ICaseDetails, IEducationCase } from "interfaces/SocialAidForm.interface";
import { useEffect, useState } from "react";
import { mapSocialAidFormToEducationMembers } from "utils/helpers";

const useEducationMembers = (initialData: ICaseDetails | undefined, isEducationStep = false) => {
	const [members, setMembers] = useState<IEducationCase[]>(
		mapSocialAidFormToEducationMembers(initialData)
	);
	const [proceedDisabled, setProceedDisabled] = useState(true);

	useEffect(() => {
		setProceedDisabled(
			isEducationStep &&
				members.length > 0 &&
				members.some((member) => !member.IsCompletedFromPortal)
		);
	}, [members, isEducationStep]);
	return {
		educationMembers: members,
		setEducationyMembers: setMembers,
		educationStepDisabled: proceedDisabled,
	};
};

export default useEducationMembers;
