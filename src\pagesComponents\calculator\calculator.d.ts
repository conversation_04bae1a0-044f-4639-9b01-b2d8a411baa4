export interface LowIncomeCase {
	personalInformation: {
		gender?: string;
		ageGroup?: string;
		maritalStatus?: string;
		numberOfSpouses?: number;
		isSpousesPOD?: string;
		numberOfPODSpouses?: number;
		haveChildren?: string;
		numberOfChildren?: number;
		isChildrenPOD?: string;
		numberOfPODChildren?: number;
	};
	employment: {
		spouseEmployedOrRetired?: string;
		ageOfTheOldestEmployedFamilyMember?: string;
		totalIncome?: number;
	};
}

export interface UnemployedCase {
	personalInformation: {
		gender?: string;
		ageGroupShort?: string;
		maritalStatus?: string;
		numberOfSpouses?: number;
		isSpousesPOD?: string;
		numberOfPODSpouses?: number;
		haveChildren?: string;
		numberOfChildren?: number;
		isChildrenPOD?: string;
		numberOfPODChildren?: number;
	};
	employment: {
		spouseEmployedOrRetired?: string;
		registeredWithNafis?: string;
		nafisDate?: string;
		totalIncome?: number;
	};
}

export interface PODCase {
	personalInformation: {
		gender?: string;
		ageGroupPOD?: string;
		maritalStatus?: string;
		numberOfSpouses?: number;
		isSpousesPOD?: string;
		numberOfPODSpouses?: number;
		haveChildren?: string;
		numberOfChildren?: number;
		isChildrenPOD?: string;
		numberOfPODChildren?: number;
		isPod?: string;
		haveHealthDisablity?: string;
		healthDisablity?: string;
		totalIncome?: number;
	};
}

export interface WomenIDSCase {
	personalInformation: {
		currentSituation?: string;
		ageGroupWomen?: string;
		haveChildren?: string;
		childAttributes?: string;
		numberOfChildren?: number;
		isChildrenPOD?: string;
		numberOfPODChildren?: number;
		totalIncome?: number;
	};
}

export interface ChildInDFSCase {
	personalInformation: {
		currentChildSituation?: string;
		gender?: string;
		ageGroupChild?: string;
		qualifiedStudent?: string;
		haveSiblings?: string;
		numberOfSiblings?: number;
		isSiblingsPOD?: string;
		numberOfPODSiblings?: number;
		totalIncome?: number;
	};
}
