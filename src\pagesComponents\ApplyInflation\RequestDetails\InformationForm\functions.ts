import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	InflationCategory: "",
};

const getValidationSchema = (t) => {
	return Yup.object({
		InflationCategory: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required(),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("Formik Validation", event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
