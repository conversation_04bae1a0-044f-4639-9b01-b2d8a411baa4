import {
	<PERSON>,
	Drawer,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>tack,
	Button,
	Center,
	DrawerBody,
	Text,
	useDisclosure,
	VStack,
} from "@chakra-ui/react";
import { BellIcon, CloseIcon } from "components/Icons";
import { ICrmNotification } from "interfaces/CrmNotification.interface";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

interface Props {
	notifications: ICrmNotification[];
	notificationCount: number;
	setNotificationsOpen: (open: boolean) => void;
}

const NotificationMobile = ({ notificationCount, notifications, setNotificationsOpen }: Props) => {
	const { t } = useTranslation();
	const { locale } = useRouter();

	const { isOpen, onOpen, onClose } = useDisclosure();

	useEffect(() => {
		setNotificationsOpen(isOpen);
	}, [isOpen, setNotificationsOpen]);

	return (
		<>
			<Button pe={0} mx={{ base: 2, md: 5 }} onClick={onOpen}>
				{!!notificationCount && (
					<Center
						bg="brand.red.300"
						h="16px"
						w="16px"
						borderRadius="50%"
						pos="absolute"
						ms="22px"
						mt="6px"
						overflow={"hidden"}
					>
						<Text color="brand.white.50" fontSize="xs">
							{notificationCount > 9 ? "9+" : notificationCount}
						</Text>
					</Center>
				)}
				<BellIcon />
			</Button>
			<Drawer
				onClose={onClose}
				isOpen={isOpen}
				size={"full"}
				placement={locale === "en" ? "right" : "left"}
			>
				<DrawerOverlay />
				<DrawerContent>
					<DrawerHeader>
						<HStack justifyContent="space-between" mb="4" mt="4">
							{/* <Button onClick={onClose} px={0} size={"xs"}>
								<LeftArr
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
									w={"8px"}
									h={"100%"}
								/>
							</Button> */}

							<HStack
								fontWeight="500"
								fontSize="md"
								w="full"
								color="brand.notificationTextColor"
								justifyContent="center"
								me={"-20px"}
							>
								<Text>{t("notifications")}</Text>
								{!!notificationCount && (
									<Center bg="brand.red.300" borderRadius="50%" w="20px" h="20px" mb={4}>
										<Text dir={"ltr"} color="brand.white.50" fontSize="sm">
											{notificationCount > 9 ? "9+" : notificationCount}
										</Text>
									</Center>
								)}
							</HStack>
							<CloseIcon onClick={onClose} />
						</HStack>
					</DrawerHeader>
					<DrawerBody>
						<VStack gap={4}>
							{notifications.map((item, index) => {
								return (
									<Box key={index} w="full">
										<Text fontWeight="500" fontSize="md" color="brand.notificationTextColor">
											{locale === "en" ? item.TitleEn : item.TitleAr}
										</Text>
										<Text mt="2" fontWeight="400" color="brand.gray.400" fontSize="sm">
											{locale === "en" ? item.NotificationEn : item.NotificationAr}
										</Text>
									</Box>
								);
							})}
							{notificationCount === 0 && (
								<Box w="full">
									<Center>
										<Text fontWeight="500" fontSize="md" color="brand.notificationTextColor">
											{t("noNotifications")}
										</Text>
									</Center>
								</Box>
							)}
						</VStack>
					</DrawerBody>
				</DrawerContent>
			</Drawer>
		</>
	);
};

export default NotificationMobile;
