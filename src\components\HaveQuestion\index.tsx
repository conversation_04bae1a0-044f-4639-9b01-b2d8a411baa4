import React, { ReactElement } from "react";
import { Divider, Box, Text, HStack, VStack } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

interface HaveQuestionProp {
	Title: string;
	Description?: any;
	BoxIcon: ReactElement;
	LinkContent: ReactElement;
	extraMargin?: Boolean;
}

const HaveQuestion = ({
	Title,
	Description,
	BoxIcon,
	LinkContent,
	extraMargin = false,
}: HaveQuestionProp) => {
	const { t } = useTranslation("common");
	return (
		<Box bg="brand.white.50" w="full" p={4}>
			<VStack>
				{BoxIcon}
				<Text
					fontWeight="700"
					textAlign="center"
					fontSize="md2"
					pt={extraMargin ? "1.063rem" : "2"}
				>
					{Title}
				</Text>
				<Box fontSize="md" fontWeight="400">
					<Text textAlign="center" color="brand.textSecondaryColor">
						{Description}
					</Text>
					{/* <Text textAlign="center" color="#6B7280" mt={1}>
						{t("reachUs")}
					</Text> */}
				</Box>
				<Divider pt={3} />
				<HStack justifyContent="center">
					{/* <SendEmailIcon w="4" h="4" /> */}
					{/* <Link
						//href={`mailto:${CONTACT_US_EMAIL}`}
						//isExternal
						color="brand.mainGold"
						fontWeight={"medium"}
					>
						{t("Sendusanemail")}
					</Link> */}
					{LinkContent}
				</HStack>
			</VStack>
		</Box>
	);
};

export default HaveQuestion;
