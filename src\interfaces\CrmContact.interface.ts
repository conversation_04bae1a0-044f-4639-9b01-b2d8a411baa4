export interface ICrmContact {
	DateofBirth: Date;
	Age: number;
	EmiratesCode: number;
	EmploymentTypeCode: number;
	Nationality: Nationality;
	FullNameArabic?: string;
	UniversityEnrollmentStatus: UniversityEnrollmentStatus;
	FirstName: string;
	MiddleName: string;
	LastName: string;
	FirstNameArabic: string;
	MiddleNameArabic: string;
	LastNameArabic: string;
	EmiratesID: string;
	ICAEmail: string;
	ICAPhoneNumber: string;
	PreferredEmail: string;
	PreferredPhoneNumber: string;
	GenderCode: GenderCode;
	PassportNumber: string;
	gender?: string;
	// Occupation: string;
	CountryOfBirth: string;
	MaritalStatus: MaritalStatus;
	ContactId: string;
	Image?: string | null;
	Mother?: any;
	City: string;
	EmiratesIDExpiryDate?: Date;
	IsEmirates?: boolean;
	Area: string;
	IDNBackNumber: string;
	KhulasitQaidNo?: any;
	IsExistingBeneficiary: boolean;
	IsICADetailsUpdated: boolean;
	IsFamilyBookDetailsUpdated: boolean;
	EligibleHousing?: boolean;
	EligibleEducation?: boolean;
	IdCase?: string;
	EligibleInflation?: boolean;
}

interface Nationality {
	Id: string;
	Name: string;
}

interface UniversityEnrollmentStatus {
	Value?: any;
	Key: number;
}

interface GenderCode {
	Value?: any;
	Key: number;
}

interface MaritalStatus {
	MaritalStatusId: string;
	Name: string;
	NameAr?: any;
	Code: string;
}
