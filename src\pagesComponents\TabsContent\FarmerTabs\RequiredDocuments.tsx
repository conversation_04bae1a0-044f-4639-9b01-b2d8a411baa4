import { Box, ListItem, OrderedList, Text } from "@chakra-ui/react";
import { VerticalRectangle } from "components/Icons";
// import OrderedListGoldenNumber from "components/Lists/OrderedListGoldenNumber";

import React from "react";
import { useTranslation } from "react-i18next";

const RequiredDocuments = ({ topMargin = 8 }) => {
	const { t } = useTranslation("common");

	// const additionalEligibilityCriteria = [
	// 	t("additionalEligibilityCriteriaForProgramPoint1"),
	// 	t("additionalEligibilityCriteriaForProgramPoint2"),
	// 	t("additionalEligibilityCriteriaForProgramPoint3"),
	// ];
	// const assetsOwnedByFamilyPoints = [
	// 	t("assetsOwnedByFamilyPoint1"),
	// 	t("assetsOwnedByFamilyPoint2"),
	// 	t("assetsOwnedByFamilyPoint3"),
	// ];

	let documentslist = [
		t("farmerDocumentsRequired.1"),
		t("farmerDocumentsRequired.2"),
		t("farmerDocumentsRequired.3"),
		t("farmerDocumentsRequired.4"),
		t("farmerDocumentsRequired.5"),
	];
	// const familyIncomePoints = [t("familyIncomePoint1"), t("familyIncomePoint2")];

	return (
		<Box>
			<Box>
				<Text fontSize="2xl" fontWeight="bold">
					<VerticalRectangle color="brand.mainGold" w="6px" mr="10px" mb="4px" />
					{t("makeSureRequiredDocumentsReady")}
				</Text>
				<OrderedList>
					{documentslist?.map((val, idx) => (
						<ListItem
							key={idx}
							my={idx === 0 ? "2" : "5"}
							color="brand.mainGold"
							fontWeight="bold"
							fontSize="lg"
						>
							<Text ml="4px" color="brand.altTextColor" fontWeight="400">
								{t(val)}
							</Text>
						</ListItem>
					))}
				</OrderedList>
			</Box>
		</Box>
	);
};

export default RequiredDocuments;
