import {
	Box,
	Checkbox,
	CheckboxGroup,
	Grid,
	GridItem,
	ListItem,
	OrderedList,
	Stack,
	Text,
	UnorderedList,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState, useRef } from "react";
import { PODCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import { validationSchema } from "./functions";
import { formatAmount } from "utils/formatters";

export default function FirstStep({
	formData: { personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: PODCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();

	const formikRef: any = useRef();

	const toast = useAppToast();
	const empty = {
		gender: "",
		ageGroupPOD: "",
		maritalStatus: "",
		isSpousesPOD: "",
		numberOfPODSpouses: "",
		haveChildren: "",
		numberOfSpouses: "",
		numberOfChildren: "",
		isChildrenPOD: "",
		numberOfPODChildren: "",
		isPod: "",
		haveHealthDisablity: "",
		healthDisablity: "",
		totalIncome: "",
	};

	const lookups = localizedLookups(locale);
	const handleSelectChange = (fieldName, newValue) => {
		handleChange("selectableTags", newValue, fieldName, formikRef.current, formKey);
	};

	const updateDropdownValues = () => {
		let originalInitialValues = { ...empty };
		let temparr: string[] = [];
		Object.keys(personalInformation).forEach((key) => {
			if (key in lookups) {
				if (key === "healthDisablity") {
					const valuesArray = personalInformation[key]?.split(",");
					valuesArray?.forEach((Element) => {
						let indexOfItem = lookups[key].findIndex((val) => Element.indexOf(val.value) != -1) + 1;
						if (indexOfItem >= 0) temparr.push(indexOfItem.toString());
					});
					originalInitialValues[key] = JSON.stringify(temparr);
				} else {
					let indexOfItem = lookups[key].findIndex((val) => val.value === personalInformation[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];

					handleSelectChange(key, lookups[key][indexOfItem]);
				}
			} else {
				originalInitialValues[key] = personalInformation[key]
					? JSON.parse(JSON.stringify(personalInformation[key]))
					: personalInformation[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());
	useEffect(() => {
		setInitialValues(() => updateDropdownValues());
	}, [locale]);

	const { t } = useTranslation("calculator");

	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				if (formik) {
					formikRef.current = formik!;
					const {
						gender,
						maritalStatus,
						isSpousesPOD,
						haveChildren,
						isChildrenPOD,
						ageGroupPOD,
						haveHealthDisablity,
						healthDisablity,
						isPod,
					} = formik.values as any;
					const handleGenderChange = (value) => {
						formik.setFieldValue("gender", value);
						// formik.setFieldValue("maritalStatus", "");
						// formik.setFieldValue("ageGroupPOD", "");
						formik.resetForm();
					};
					const maleAndMarried = gender.value === "1" && maritalStatus.value === "2";
					const notSingle = maritalStatus.value !== "1";
					const isOlderThan17 = ageGroupPOD.value !== "1" && !!ageGroupPOD;

					const withPotentialChildren = !!maritalStatus && !!gender && notSingle && isOlderThan17;
					const youngerThan17AndNotPod = !isOlderThan17 && isPod !== "1" && !!ageGroupPOD;
					const notPODandNOTHealthDisablity = isPod === "0" && haveHealthDisablity === "0";
					const marriedFemaleAndDisabled = maritalStatus.value === "2";
					const enEligableConditions = [
						youngerThan17AndNotPod,
						notPODandNOTHealthDisablity,
						marriedFemaleAndDisabled,
					];
					const enEligble = enEligableConditions.some((t) => t === true);

					handleSetFormikState(
						{
							isLoading: formik.isSubmitting,
							isDisabled: !formik.isValid || formik.isSubmitting || enEligble,
						},
						formKey
					);
					return (
						<Grid
							rowGap={{ base: 6, md: 4 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
								{t("personalInformation")}
							</Text>

							{/* {!notPODandNOTHealthDisablity && ( */}
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="selectableTags"
										label={t("gender")}
										options={getLookUp("gender", locale)}
										placeholder={t("chooseAnOption")}
										name="gender"
										error={formik.errors.gender}
										value={formik.values.gender}
										onChange={(firstArg) => {
											handleGenderChange(firstArg.value);
											handleSelectChange("gender", firstArg);
										}}
									/>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="selectableTags"
										label={t("ageGroup")}
										options={getLookUp("ageGroupPOD", locale)}
										placeholder={t("chooseAnOption")}
										name="ageGroupPOD"
										error={formik.errors.ageGroupPOD}
										value={formik.values.ageGroupPOD}
										onChange={(firstArg) => {
											handleSelectChange("ageGroupPOD", firstArg);
											if (firstArg.value === "1" && isPod === "0")
												toast({
													status: "error",
													title: t("enEligble.under17AndNotPOD"),
												});
										}}
									/>
								</GridItem>
							</>
							{/* )} */}
							<GridItem colSpan={2} maxW="500px">
								<FormField
									type="radio"
									label={t("isPod")}
									options={getLookUp("boolean", locale)}
									placeholder={t("chooseAnOption")}
									name="isPod"
									error={formik.errors.isPod}
									value={formik.values.isPod}
									onChange={(firstArg) => {
										handleChange("radio", firstArg, "isPod", formik, formKey);
										const youngerThan17AndNotPod = firstArg === "0" && ageGroupPOD.value === "1";
										if (youngerThan17AndNotPod) {
											toast({
												status: "error",
												title: t("enEligble.under17AndNotPOD"),
											});
										} else if (firstArg === "0" && haveHealthDisablity === "8") {
											toast({
												status: "error",
												title: t("enEligble.notPodAndNotHealthDisable"),
											});
										}
									}}
								/>
							</GridItem>
							{isPod === "0" && (
								<>
									<GridItem colSpan={2} maxW="500px">
										<FormField
											type="radio"
											label={t("haveHealthDisablity")}
											options={getLookUp("boolean", locale)}
											placeholder={t("chooseAnOption")}
											name="haveHealthDisablity"
											error={formik.errors.haveHealthDisablity}
											value={formik.values.haveHealthDisablity}
											onChange={(firstArg) => {
												handleChange("radio", firstArg, "haveHealthDisablity", formik, formKey);
												const isNotPodAndNotHealthDisablilty = firstArg === "0" && isPod === "0";
												if (isNotPodAndNotHealthDisablilty) {
													toast({
														status: "error",
														title: t("enEligble.notPodAndNotHealthDisable"),
													});
												}
											}}
										/>
									</GridItem>
									{haveHealthDisablity === "1" && (
										<GridItem colSpan={2} maxW="500px">
											<Text mb={2} color="#1b1d21b8" fontWeight={500}>
												{t("healthDisablity")}
											</Text>
											<CheckboxGroup
												variant={"golden"}
												defaultValue={
													formik.values.healthDisablity
														? JSON.parse(formik.values.healthDisablity)
														: []
												}
												onChange={(firstArg) => {
													handleChange(
														"text",
														{ target: { value: JSON.stringify(firstArg) } },
														"healthDisablity",
														formik,
														formKey
													);
												}}
											>
												<Stack spacing={7} direction={["column"]}>
													{getLookUp("healthDisablity", locale).map((lookup, idx) => (
														<Checkbox key={idx} value={lookup.value}>
															{lookup.label}
														</Checkbox>
													))}
												</Stack>
											</CheckboxGroup>
										</GridItem>
									)}
								</>
							)}
							{/* {!notPODandNOTHealthDisablity && ( */}
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="selectableTags"
										label={t("maritalStatus")}
										options={getLookUp("maritalStatus", locale)}
										placeholder={t("chooseAnOption")}
										name="maritalStatus"
										error={formik.errors.maritalStatus}
										value={formik.values.maritalStatus}
										onChange={(firstArg) => {
											handleSelectChange("maritalStatus", firstArg);
											if (firstArg.value === "2") {
												toast({ status: "error", title: t("enEligble.marriedFemaleAndDisabled") });
											}
										}}
									/>
								</GridItem>
							</>
							{/* )} */}

							{!enEligble && (
								<>
									{maleAndMarried && isOlderThan17 && (
										<>
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="text"
													name="numberOfSpouses"
													placeholder={t("writeAnswerHere")}
													label={t("numberOfSpouses")}
													value={formik.values.numberOfSpouses}
													error={formik.errors.numberOfSpouses}
													onChange={(firstArg) => {
														handleChange("text", firstArg, "numberOfSpouses", formik, formKey);
													}}
												/>
											</GridItem>
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="radio"
													label={t("isSpousesPOD")}
													options={getLookUp("boolean", locale)}
													placeholder={t("chooseAnOption")}
													name="isSpousesPOD"
													error={formik.errors.isSpousesPOD}
													value={formik.values.isSpousesPOD}
													onChange={(firstArg) => {
														handleChange("radio", firstArg, "isSpousesPOD", formik, formKey);
													}}
												/>
											</GridItem>
											<GridItem colSpan={2}>
												<GrayBox>
													<Text>{t("mocdNote")}</Text>
												</GrayBox>
											</GridItem>

											{isSpousesPOD == "1" && (
												<GridItem colSpan={2} maxW="500px">
													<FormField
														type="text"
														name="numberOfPODSpouses"
														placeholder={t("writeAnswerHere")}
														label={t("numberOfPODSpouses")}
														value={formik.values.numberOfPODSpouses}
														error={formik.errors.numberOfPODSpouses}
														onChange={(firstArg) => {
															handleChange("text", firstArg, "numberOfPODSpouses", formik, formKey);
														}}
													/>
												</GridItem>
											)}
										</>
									)}
									{withPotentialChildren && (
										<>
											<GridItem colSpan={2}>
												<Text fontWeight={"semibold"} my={4}>
													{t("emiratiChilds")}
												</Text>
												<GrayBox>
													<Text>{t("childRules-1")}</Text>
													<OrderedList>
														<ListItem>
															<Text>{t("childRules-2")}</Text>
														</ListItem>
														<ListItem>
															<Text>{t("childRules-3")}</Text>
														</ListItem>
													</OrderedList>
												</GrayBox>
											</GridItem>
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="radio"
													label={t("haveChildren")}
													options={getLookUp("boolean", locale)}
													placeholder={t("chooseAnOption")}
													name="haveChildren"
													error={formik.errors.haveChildren}
													value={formik.values.haveChildren}
													onChange={(firstArg) => {
														handleChange("radio", firstArg, "haveChildren", formik, formKey);
													}}
												/>
											</GridItem>
											{haveChildren === "1" && (
												<>
													<GridItem colSpan={2} maxW="500px">
														<FormField
															type="text"
															name="numberOfChildren"
															placeholder={t("writeAnswerHere")}
															label={t("numberOfChildren")}
															value={formik.values.numberOfChildren}
															error={formik.errors.numberOfChildren}
															onChange={(firstArg) => {
																handleChange("text", firstArg, "numberOfChildren", formik, formKey);
															}}
														/>
													</GridItem>
													<GridItem colSpan={2} maxW="500px">
														<FormField
															type="radio"
															label={t("isChildrenPOD")}
															options={getLookUp("boolean", locale)}
															placeholder={t("chooseAnOption")}
															name="isChildrenPOD"
															error={formik.errors.isChildrenPOD}
															value={formik.values.isChildrenPOD}
															onChange={(firstArg) => {
																handleChange("radio", firstArg, "isChildrenPOD", formik, formKey);
															}}
														/>
													</GridItem>
													<GridItem colSpan={2}>
														<GrayBox>
															<Text>{t("mocdNote")}</Text>
														</GrayBox>
													</GridItem>
													{isChildrenPOD === "1" && (
														<GridItem colSpan={2} maxW="500px">
															<FormField
																type="text"
																name="numberOfPODChildren"
																placeholder={t("writeAnswerHere")}
																label={t("numberOfPODChildren")}
																value={formik.values.numberOfPODChildren}
																error={formik.errors.numberOfPODChildren}
																onChange={(firstArg) => {
																	handleChange(
																		"text",
																		firstArg,
																		"numberOfPODChildren",
																		formik,
																		formKey
																	);
																}}
															/>
														</GridItem>
													)}
												</>
											)}
										</>
									)}

									<GridItem colSpan={2}>
										<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
											{t("incomeInformation")}
										</Text>

										<GrayBox>
											<UnorderedList>
												<Text>{t("incomeRules1")}</Text>
												<UnorderedList>
													<ListItem>
														<Text color="#1B1D21">{t("incomeRules2")}</Text>
													</ListItem>
													<ListItem>
														<Text color="#1B1D21">{t("incomeRules3")}</Text>
													</ListItem>
													<ListItem>
														<Text color="#1B1D21">{t("incomeRules4")}</Text>
													</ListItem>
													<ListItem>
														<Text color="#1B1D21">{t("incomeRules5")}</Text>
													</ListItem>
													<ListItem>
														<Text color="#1B1D21">{t("incomeRules6")}</Text>
													</ListItem>
												</UnorderedList>
											</UnorderedList>
										</GrayBox>
									</GridItem>
									<GridItem colSpan={2} maxW="500px">
										<FormField
											type="text"
											label={t("totalIncome")}
											name="totalIncome"
											error={formik.errors.totalIncome}
											placeholder={t("writeAnswerHere")}
											customFormat={formatAmount}
											value={formik.values.totalIncome}
											onChange={(firstArg) => {
												handleChange("text", firstArg, "totalIncome", formik, formKey);
											}}
										/>
									</GridItem>
								</>
							)}
						</Grid>
					);
				}

				return null;
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};
