import { Box, Flex, Text, Tabs, Tab<PERSON>ist, <PERSON>b<PERSON>ane<PERSON>, Tab, TabPanel, VStack } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ReactElement } from "react";
import { useTranslation } from "next-i18next";
import ServiceDescription from "components/DLS/ServiceDescription";
import StarRating from "components/StarRating";
import Breadcrumbs from "components/Breadcrumbs";
import RequiredDocumentsTab from "pagesComponents/TabsContent/RequiredDocumentsTab";
import { InferGetServerSidePropsType } from "next";
import { getStrapiContent } from "services/strapi";
import { getImageUrls } from "utils/strapi/helpers";
import {
	getContactIdFromToken,
	getIsEmiratesIDExpiryDateFromToken,
	getIsEmiratesNationalityFromToken,
	getUserInflationEligibilityDetails,
} from "utils/helpers";
import { FarmerServiceContent } from "utils/strapi/farmerService";
import { BackendServices } from "services/backend";
function Inflation({
	content,
	isEidExp,
	isEmirates,
	userEligibleAge,
	eligibleInflation,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation();
	// let documentslist = [
	// 	t("documentsRequired.documentsRequiredPart1"),
	// 	t("documentsRequired.documentsRequiredPart2"),
	// 	t("documentsRequired.documentsRequiredPart3"),
	// 	t("documentsRequired.documentsRequiredPart4"),
	// 	t("documentsRequired.documentsRequiredPart5"),
	// 	t("documentsRequired.documentsRequiredPart6"),
	// ];
	// const steps = [
	// 	{
	// 		title: "الخطوة الاولى:",
	// 		body: String(t("signupSteps.step1title")),
	// 		Icon: FirstStepICon,
	// 	},
	// 	{
	// 		title: "الخطوة الثانية:",
	// 		body: String(t("signupSteps.step2title")),
	// 		Icon: SecondStepIcon,
	// 	},
	// 	{
	// 		title: "الخطوة الثالثة:",
	// 		body: String(t("signupSteps.step3title")),
	// 		Icon: ThirdStepIcon,
	// 	},
	// 	{
	// 		title: "الخطوة الرابعة:",
	// 		body: String(t("signupSteps.step4title")),
	// 		Icon: ForthStepIcon,
	// 	},
	// 	{
	// 		title: "الخطوة الرابعة:",
	// 		body: String(t("signupSteps.step4title")),
	// 		Icon: ForthStepIcon,
	// 	},
	// 	{
	// 		title: "الخطوة الرابعة:",
	// 		body: String(t("signupSteps.step4title")),
	// 		Icon: ForthStepIcon,
	// 	},
	// ];
	// const eligibilityUnder45Points = [
	// 	{ val: t("eligPoints.eligibilityUnder45Point1"), isSub: false },
	// 	{ val: t("eligPoints.eligibilityUnder45Point2"), isSub: false },
	// 	{ val: t("eligPoints.eligibilityUnder45Point3"), isSub: false },
	// 	{ val: t("eligPoints.eligibilityUnder45Point4"), isSub: false },
	// 	{ val: t("eligPoints.eligibilityUnder45Point5"), isSub: false },
	// 	{ val: t("eligPoints.eligibilityUnder45Point6"), isSub: false },
	// ];

	// const servicesDescList = [
	// 	{ title: t("nationaility"), body: t("nationailityDesc") },
	// 	{ title: "", body: t("nationailityDesc2") },
	// 	{ title: t("monthlyIncome"), body: t("monthlyIncomeDesc") },
	// ];
	// const Chevron = locale === "ar" ? ChevronLeftIcon : ChevronRightIcon;

	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "howToApply",
			link: "/smart-services",
			isCurrentPage: false,
		},
		{
			label: t("common:Inflation"),
			id: "howToApply",
			link: "#",
			isCurrentPage: true,
		},
	];

	return (
		<Box w="100%">
			<Box pos={"relative"}>
				<Flex
					w={{ base: "100%", md: "100%" }}
					minH={"23rem"}
					bgImage={getImageUrls(content.landing_image.url)}
					bgPosition={{ base: "100%", md: "100%" }}
					bgSize="cover"
					bgRepeat={"no-repeat"}
					bgPos={{ base: "top", md: "left" }}
					className="overlay"
					alignItems={"center"}
				>
					<VStack
						width={{ base: "100%", sm: "90%", md: "40%" }}
						marginInlineStart={{ base: 0, md: "6.5rem" }}
						alignItems={"start"}
						zIndex={10}
						px={{ base: "1rem" }}
						w="full"
					>
						<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
							<Breadcrumbs data={breadcrumbsData} isLight />
						</Box>
						<Text color={"brand.white.50"} fontSize={{ base: "2rem", lg: "2.5rem" }}>
							{content.page_header}
						</Text>
					</VStack>
				</Flex>
			</Box>
			<Box>
				<Tabs>
					<TabList px={{ base: "1rem", md: "3rem", lg: "6rem" }}>
						<Tab
							_selected={{
								color: "brand.textColor",
								fontWeight: "700",
								opacity: 1,
								borderColor: "brand.mainGold",
								borderBottomWidth: "4px",
							}}
							px={0}
							py={5}
							me={{ base: 10, md: 15 }}
							opacity={0.5}
							fontSize={{ base: "md", md: "xl" }}
						>
							{content.first_tab_header}
						</Tab>

						<Tab
							_selected={{
								color: "brand.textColor",
								opacity: 1,
								fontWeight: "700",
								borderColor: "brand.mainGold",
								borderBottomWidth: "4px",
							}}
							px={0}
							pb={2.5}
							me={{ base: 10, md: 15 }}
							opacity={0.5}
							fontSize={{ base: "md", md: "xl" }}
						>
							{content.second_tab_header}
						</Tab>
					</TabList>

					<TabPanels
						px={{ base: "1rem", md: "3rem", lg: "6rem" }}
						lineHeight="2.375rem"
						w="full"
						mt={4}
					>
						<TabPanel px={0} w="full">
							<ServiceDescription
								content={content}
								isEidExp={isEidExp}
								isEmirates={isEmirates}
								userEligibleAge={userEligibleAge}
								eligibleInflation={eligibleInflation}
							/>
						</TabPanel>

						<TabPanel px={0}>
							<RequiredDocumentsTab
								content={content}
								isEidExp={isEidExp}
								isEmirates={isEmirates}
								userEligibleAge={userEligibleAge}
								eligibleInflation={eligibleInflation}
							/>
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<Box px={{ base: "1rem", md: "3rem", lg: "6rem" }} pt={0} pb={10}>
				<StarRating mt="10" />
			</Box>
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	const content = await getStrapiContent("/inflation-service-page?populate=deep", ctx.locale);

	const expDate = await getIsEmiratesIDExpiryDateFromToken(ctx.req);

	const isEmiratesData = await getIsEmiratesNationalityFromToken(ctx.req);
	const isEmirates = isEmiratesData != undefined ? isEmiratesData : true;
	const ToDate = new Date();

	const inflationEligiblityData = await getUserInflationEligibilityDetails(ctx.req);
	let userEligibleAge = true;
	let eligibleInflation = true;
	if (inflationEligiblityData.Age !== undefined) {
		userEligibleAge = inflationEligiblityData?.Age >= 21 ? true : false;
	}
	if (inflationEligiblityData?.EligibleInflation !== undefined) {
		eligibleInflation = inflationEligiblityData?.EligibleInflation;
	}

	let isEidExp = false;
	if (expDate) isEidExp = new Date(expDate).getTime() < ToDate.getTime() ? true : false;
	const contactId = await getContactIdFromToken(ctx.req);
	if (contactId) {
		let inflationDataCheck = await BackendServices.checkInflationAllowanceEligibility(contactId);
		if (inflationDataCheck.IsSuccess && inflationDataCheck?.Data) {
			eligibleInflation = inflationDataCheck?.Data?.eligibleInflation;
		}
	}

	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "buttons", "forms", "tables"])),
			// Will be passed to the page component as props
			content: content.data as FarmerServiceContent,
			isEidExp,
			isEmirates,
			userEligibleAge,
			eligibleInflation,
		},
	};
}
Inflation.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default Inflation;
