import { useBreakpointValue, useToast } from "@chakra-ui/react";
import HybridToastNotification, {
	HybridToastNotificationProps,
} from "components/HybridToastNotification";
import React from "react";

const useHybridAppToast = (Component = HybridToastNotification) => {
	const toast = useToast();
	const containerWidth = useBreakpointValue({
		base: {
			minWidth: "90%",
			maxWidth: "90%",
		},
		md: {
			minWidth: "50%",
			maxWidth: "50%",
		},
	});

	return (options: HybridToastNotificationProps) =>
		toast({
			position: "top",
			render: ({ onClose }) => <Component {...options} onClose={onClose} />,
			containerStyle: containerWidth,
		});
};

export default useHybridAppToast;
