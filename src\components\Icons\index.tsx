import { createIcon } from "@chakra-ui/icons";

export const HomeIcon = createIcon({
	displayName: "HomeIcon",
	viewBox: "0 0 23 22",
	path: (
		<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M11.9999 0.503906C11.896 0.503906 11.7921 0.536845 11.7035 0.605258L0.663757 9.22012C0.455986 9.38482 0.417979 9.68634 0.580141 9.89664C0.744838 10.1044 1.04636 10.1424 1.25666 9.97772L1.92051 9.46083V22.0791C1.92051 22.3426 2.13588 22.5605 2.3994 22.5605H9.11899V13.9203H14.8808V22.5605H21.6004C21.8639 22.5605 22.0793 22.3426 22.0793 22.0791V9.46083L22.7431 9.97772C22.8344 10.0461 22.9332 10.0791 23.0396 10.0791C23.184 10.0791 23.3234 10.0157 23.4197 9.89664C23.5818 9.68634 23.5438 9.38482 23.3361 9.22012L12.2964 0.605258C12.2077 0.536845 12.1038 0.503906 11.9999 0.503906ZM16.7989 2.39918V2.9034L19.6798 5.15087V2.39918H16.7989Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const VerticalRectangle = createIcon({
	displayName: "VerticalRectangle",
	viewBox: "0 0 6 28",
	path: (
		<svg
			width="6"
			height="28"
			viewBox="0 0 6 28"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect width="6" height="28" fill="currentColor" />
		</svg>
	),
});

export const PersonCheckFill = createIcon({
	displayName: "PersonCheckFill",
	viewBox: "0 0 37 37",
	path: (
		<svg
			width="37"
			height="37"
			viewBox="0 0 37 37"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect width="37" height="37" rx="8" fill="#B08D44" />
			<g clip-path="url(#clip0_688_2420)">
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M28.8173 15.4326C28.8755 15.4907 28.9217 15.5597 28.9532 15.6356C28.9847 15.7115 29.0009 15.7929 29.0009 15.8751C29.0009 15.9573 28.9847 16.0388 28.9532 16.1147C28.9217 16.1906 28.8755 16.2596 28.8173 16.3176L25.0673 20.0676C25.0093 20.1258 24.9403 20.172 24.8644 20.2035C24.7884 20.235 24.707 20.2513 24.6248 20.2513C24.5426 20.2513 24.4612 20.235 24.3853 20.2035C24.3093 20.172 24.2404 20.1258 24.1823 20.0676L22.3073 18.1926C22.19 18.0753 22.124 17.9161 22.124 17.7501C22.124 17.5842 22.19 17.425 22.3073 17.3076C22.4247 17.1903 22.5838 17.1244 22.7498 17.1244C22.9158 17.1244 23.075 17.1903 23.1923 17.3076L24.6248 18.7414L27.9323 15.4326C27.9904 15.3744 28.0593 15.3283 28.1353 15.2967C28.2112 15.2652 28.2926 15.249 28.3748 15.249C28.457 15.249 28.5384 15.2652 28.6144 15.2967C28.6903 15.3283 28.7593 15.3744 28.8173 15.4326Z"
					fill="white"
				/>
				<path
					d="M10.25 26.5C10.25 26.5 9 26.5 9 25.25C9 24 10.25 20.25 16.5 20.25C22.75 20.25 24 24 24 25.25C24 26.5 22.75 26.5 22.75 26.5H10.25ZM16.5 19C17.4946 19 18.4484 18.6049 19.1517 17.9017C19.8549 17.1984 20.25 16.2446 20.25 15.25C20.25 14.2554 19.8549 13.3016 19.1517 12.5983C18.4484 11.8951 17.4946 11.5 16.5 11.5C15.5054 11.5 14.5516 11.8951 13.8483 12.5983C13.1451 13.3016 12.75 14.2554 12.75 15.25C12.75 16.2446 13.1451 17.1984 13.8483 17.9017C14.5516 18.6049 15.5054 19 16.5 19Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_688_2420">
					<rect width="20" height="20" fill="white" transform="translate(9 9)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const DocumentIcon2 = createIcon({
	displayName: "DocumentIcon2",
	viewBox: "0 0 37 37",
	path: (
		<svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="37" height="37" rx="8" fill="#B08D44" />
			<path
				d="M20.1163 9H13.5C12.837 9 12.2011 9.26339 11.7322 9.73223C11.2634 10.2011 11 10.837 11 11.5V26.5C11 27.163 11.2634 27.7989 11.7322 28.2678C12.2011 28.7366 12.837 29 13.5 29H23.5C24.163 29 24.7989 28.7366 25.2678 28.2678C25.7366 27.7989 26 27.163 26 26.5V14.8837C25.9999 14.5523 25.8682 14.2344 25.6337 14L21 9.36625C20.7656 9.13181 20.4477 9.00007 20.1163 9V9ZM20.375 13.375V10.875L24.125 14.625H21.625C21.2935 14.625 20.9755 14.4933 20.7411 14.2589C20.5067 14.0245 20.375 13.7065 20.375 13.375ZM17.25 16.8125C17.25 16.9356 17.2258 17.0575 17.1786 17.1713C17.1315 17.285 17.0625 17.3884 16.9754 17.4754C16.8884 17.5625 16.785 17.6315 16.6713 17.6786C16.5575 17.7258 16.4356 17.75 16.3125 17.75C16.1894 17.75 16.0675 17.7258 15.9537 17.6786C15.84 17.6315 15.7366 17.5625 15.6496 17.4754C15.5625 17.3884 15.4935 17.285 15.4464 17.1713C15.3992 17.0575 15.375 16.9356 15.375 16.8125C15.375 16.5639 15.4738 16.3254 15.6496 16.1496C15.8254 15.9738 16.0639 15.875 16.3125 15.875C16.5611 15.875 16.7996 15.9738 16.9754 16.1496C17.1512 16.3254 17.25 16.5639 17.25 16.8125ZM16.1737 18.74L17.8362 19.8475L20.1537 17.5287C20.2001 17.4822 20.26 17.4515 20.3249 17.441C20.3897 17.4304 20.4563 17.4406 20.515 17.47L22.875 18.6875V20.875C22.875 21.0408 22.8092 21.1997 22.6919 21.3169C22.5747 21.4342 22.4158 21.5 22.25 21.5H14.75C14.5842 21.5 14.4253 21.4342 14.3081 21.3169C14.1908 21.1997 14.125 21.0408 14.125 20.875V20.25C14.125 20.25 16.05 18.6575 16.1737 18.74ZM14.75 22.75H22.25C22.4158 22.75 22.5747 22.8158 22.6919 22.9331C22.8092 23.0503 22.875 23.2092 22.875 23.375C22.875 23.5408 22.8092 23.6997 22.6919 23.8169C22.5747 23.9342 22.4158 24 22.25 24H14.75C14.5842 24 14.4253 23.9342 14.3081 23.8169C14.1908 23.6997 14.125 23.5408 14.125 23.375C14.125 23.2092 14.1908 23.0503 14.3081 22.9331C14.4253 22.8158 14.5842 22.75 14.75 22.75ZM14.75 25.25H18.5C18.6658 25.25 18.8247 25.3158 18.9419 25.4331C19.0592 25.5503 19.125 25.7092 19.125 25.875C19.125 26.0408 19.0592 26.1997 18.9419 26.3169C18.8247 26.4342 18.6658 26.5 18.5 26.5H14.75C14.5842 26.5 14.4253 26.4342 14.3081 26.3169C14.1908 26.1997 14.125 26.0408 14.125 25.875C14.125 25.7092 14.1908 25.5503 14.3081 25.4331C14.4253 25.3158 14.5842 25.25 14.75 25.25Z"
				fill="white"
			/>
		</svg>
	),
});

export const CheckBox = createIcon({
	displayName: "CheckBox",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8ZM12.03 4.97C11.9586 4.89882 11.8735 4.84277 11.7799 4.80522C11.6863 4.76766 11.5861 4.74936 11.4853 4.75141C11.3845 4.75347 11.2851 4.77583 11.1932 4.81717C11.1012 4.85851 11.0185 4.91797 10.95 4.992L7.477 9.417L5.384 7.323C5.24183 7.19052 5.05378 7.1184 4.85948 7.12183C4.66518 7.12525 4.47979 7.20397 4.34238 7.34138C4.20497 7.47879 4.12625 7.66418 4.12283 7.85848C4.1194 8.05278 4.19152 8.24083 4.324 8.383L6.97 11.03C7.04128 11.1012 7.12616 11.1572 7.21958 11.1949C7.313 11.2325 7.41305 11.2509 7.51375 11.2491C7.61444 11.2472 7.71374 11.2251 7.8057 11.184C7.89766 11.1429 7.9804 11.0837 8.049 11.01L12.041 6.02C12.1771 5.8785 12.2523 5.68928 12.2504 5.49296C12.2485 5.29664 12.1698 5.10888 12.031 4.97H12.03Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const EnvelopeOpened = createIcon({
	displayName: "EnvelopeOpened",
	viewBox: "0 0 37 37",
	path: (
		<svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="37" height="37" rx="8" fill="#B08D44" />
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M17.5 20.5L14 18.5V12.5C14 12.1022 14.158 11.7206 14.4393 11.4393C14.7206 11.158 15.1022 11 15.5 11H22.5C22.8978 11 23.2794 11.158 23.5607 11.4393C23.842 11.7206 24 12.1022 24 12.5V18.5L20.5 20.5L19 19.75L17.5 20.5ZM12.059 14.635L13 14.133V17.886L11 16.713V16.4C11 16.0374 11.0985 15.6817 11.285 15.3708C11.4715 15.0599 11.7391 14.8056 12.059 14.635ZM27 16.713L25 17.886V14.133L25.941 14.635C26.2609 14.8056 26.5285 15.0599 26.715 15.3708C26.9015 15.6817 27 16.0374 27 16.4V16.713ZM27 17.873L21.307 21.21L27 24.372V17.873ZM19 21.072L26.941 25.484C26.833 25.917 26.5833 26.3015 26.2315 26.5762C25.8798 26.8509 25.4463 27.0001 25 27H13C12.5537 27.0001 12.1202 26.8509 11.7685 26.5762C11.4167 26.3015 11.167 25.917 11.059 25.484L19 21.072ZM11 24.372L16.693 21.21L11 17.873V24.373V24.372Z"
				fill="white"
			/>
		</svg>
	),
});
export const TaskDone = createIcon({
	displayName: "TaskDone",
	viewBox: "0 0 37 37",
	path: (
		<svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="37" height="37" rx="8" fill="#B08D44" />
			<g clip-path="url(#clip0_705_2380)">
				<path
					d="M21.5 9.625C21.5 9.45924 21.4342 9.30027 21.3169 9.18306C21.1997 9.06585 21.0408 9 20.875 9H17.125C16.9592 9 16.8003 9.06585 16.6831 9.18306C16.5658 9.30027 16.5 9.45924 16.5 9.625C16.5 9.79076 16.4342 9.94973 16.3169 10.0669C16.1997 10.1842 16.0408 10.25 15.875 10.25C15.7092 10.25 15.5503 10.3158 15.4331 10.4331C15.3158 10.5503 15.25 10.7092 15.25 10.875V11.5C15.25 11.6658 15.3158 11.8247 15.4331 11.9419C15.5503 12.0592 15.7092 12.125 15.875 12.125H22.125C22.2908 12.125 22.4497 12.0592 22.5669 11.9419C22.6842 11.8247 22.75 11.6658 22.75 11.5V10.875C22.75 10.7092 22.6842 10.5503 22.5669 10.4331C22.4497 10.3158 22.2908 10.25 22.125 10.25C21.9592 10.25 21.8003 10.1842 21.6831 10.0669C21.5658 9.94973 21.5 9.79076 21.5 9.625Z"
					fill="white"
				/>
				<path
					d="M14.1063 10.25H13.375C12.8777 10.25 12.4008 10.4475 12.0492 10.7992C11.6975 11.1508 11.5 11.6277 11.5 12.125V27.125C11.5 27.6223 11.6975 28.0992 12.0492 28.4508C12.4008 28.8025 12.8777 29 13.375 29H24.625C25.1223 29 25.5992 28.8025 25.9508 28.4508C26.3025 28.0992 26.5 27.6223 26.5 27.125V12.125C26.5 11.6277 26.3025 11.1508 25.9508 10.7992C25.5992 10.4475 25.1223 10.25 24.625 10.25H23.8938C23.9625 10.445 24 10.6562 24 10.875V11.5C24 11.9973 23.8025 12.4742 23.4508 12.8258C23.0992 13.1775 22.6223 13.375 22.125 13.375H15.875C15.3777 13.375 14.9008 13.1775 14.5492 12.8258C14.1975 12.4742 14 11.9973 14 11.5V10.875C14 10.6562 14.0375 10.445 14.1063 10.25ZM22.5675 18.8175L18.8175 22.5675C18.7594 22.6257 18.6905 22.6719 18.6145 22.7034C18.5386 22.7349 18.4572 22.7511 18.375 22.7511C18.2928 22.7511 18.2114 22.7349 18.1355 22.7034C18.0595 22.6719 17.9906 22.6257 17.9325 22.5675L16.0575 20.6925C15.9994 20.6344 15.9533 20.5654 15.9218 20.4895C15.8904 20.4136 15.8742 20.3322 15.8742 20.25C15.8742 20.1678 15.8904 20.0864 15.9218 20.0105C15.9533 19.9346 15.9994 19.8656 16.0575 19.8075C16.1749 19.6901 16.334 19.6242 16.5 19.6242C16.5822 19.6242 16.6636 19.6404 16.7395 19.6718C16.8154 19.7033 16.8844 19.7494 16.9425 19.8075L18.375 21.2413L21.6825 17.9325C21.7999 17.8151 21.959 17.7492 22.125 17.7492C22.291 17.7492 22.4501 17.8151 22.5675 17.9325C22.6849 18.0499 22.7508 18.209 22.7508 18.375C22.7508 18.541 22.6849 18.7001 22.5675 18.8175Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_705_2380">
					<rect width="20" height="20" fill="white" transform="translate(9 9)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const RightArrow = createIcon({
	displayName: "RightArrow",
	viewBox: "0 0 10 16",
	path: (
		<svg width="10" height="16" viewBox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M2.14181 0.324219L0.833008 1.6306L7.21677 7.99979L0.833008 14.3811L2.12724 15.6754L9.15691 8.64569L9.81738 7.99979L9.15691 7.35389L2.14181 0.324219Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const ChromeIcon = createIcon({
	displayName: "ChromeIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M16 7.99994C15.9998 9.95224 15.2858 11.8371 13.9924 13.2995C12.6991 14.762 10.9156 15.7011 8.97801 15.9399L10.88 8.84194C11.0223 8.3568 11.0395 7.84352 10.93 7.34994C10.8195 6.84763 10.5807 6.38252 10.237 5.99994H15.748C15.916 6.65332 16.0007 7.32532 16 7.99994ZM6.52224e-06 7.99994C-8.12892e-05 10.1091 0.832745 12.1329 2.31728 13.6311C3.80182 15.1294 5.81795 15.9807 7.92701 15.9999L9.35301 10.6789C9.124 10.7947 8.88099 10.8804 8.63001 10.9339C8.04739 11.0605 7.44021 11.0093 6.88701 10.7869C6.49364 10.6302 6.13811 10.3916 5.84401 10.0869L0.633007 4.87594C0.213934 5.86407 -0.00136611 6.92662 6.52224e-06 7.99994ZM5.00401 7.83294L1.10801 3.93594C1.86668 2.65052 2.9697 1.60288 4.29247 0.911387C5.61523 0.219889 7.10502 -0.0879087 8.59352 0.0227665C10.082 0.133442 11.5099 0.65818 12.7159 1.5377C13.9218 2.41721 14.8578 3.61646 15.418 4.99994H8.06601C7.63602 4.98961 7.2089 5.07251 6.81401 5.24294C6.30033 5.46225 5.85844 5.82119 5.5385 6.27901C5.21855 6.73683 5.03338 7.27518 5.00401 7.83294ZM8.00001 9.99994C8.53044 9.99994 9.03915 9.78923 9.41422 9.41415C9.78929 9.03908 10 8.53037 10 7.99994C10 7.46951 9.78929 6.9608 9.41422 6.58573C9.03915 6.21065 8.53044 5.99994 8.00001 5.99994C7.46957 5.99994 6.96087 6.21065 6.58579 6.58573C6.21072 6.9608 6.00001 7.46951 6.00001 7.99994C6.00001 8.53037 6.21072 9.03908 6.58579 9.41415C6.96087 9.78923 7.46957 9.99994 8.00001 9.99994Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const EdgeIcon = createIcon({
	displayName: "EdgeIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_1296_7502)">
				<path
					d="M9.48166 9.341C9.41266 9.403 9.31166 9.494 9.31166 9.65C9.31166 9.812 9.41866 9.975 9.61166 10.106C10.4887 10.719 12.1327 10.646 12.2037 10.644H12.2057C12.8727 10.644 13.5257 10.464 14.0997 10.125C14.677 9.78727 15.156 9.30454 15.4893 8.72465C15.8225 8.14475 15.9985 7.48784 15.9997 6.819C16.0177 5.503 15.5597 4.601 15.3337 4.155L15.2937 4.075C13.9627 1.487 11.1057 0 7.99966 0C6.35315 2.33785e-06 4.74675 0.508036 3.39966 1.45478C2.05257 2.40152 1.03041 3.74084 0.472656 5.29C1.48766 4.048 3.18266 3.262 4.99966 3.262C7.82966 3.262 10.0097 5.147 10.0097 8.059H10.0057V8.061C10.0057 8.399 9.83766 8.893 9.51866 9.305L9.52466 9.299C9.51102 9.31338 9.49667 9.32706 9.48166 9.34V9.341Z"
					fill="currentcolor"
				/>
				<path
					d="M0.00970727 7.75294C-0.0236534 9.00845 0.234074 10.2547 0.762707 11.3939C1.3546 12.6541 2.26509 13.7379 3.40419 14.5384C4.54329 15.3388 5.87156 15.8281 7.25771 15.9579C6.98598 15.8544 6.72339 15.7283 6.47271 15.5809H6.46271L6.34271 15.5059C5.73119 15.1261 5.20098 14.6289 4.78271 14.0429C4.30548 13.3898 3.97699 12.6401 3.8203 11.8465C3.6636 11.0529 3.6825 10.2347 3.87566 9.44917C4.06882 8.66366 4.43156 7.92994 4.93843 7.29953C5.4453 6.66911 6.084 6.15727 6.80971 5.79994L6.81971 5.79594L6.84471 5.78394C7.05271 5.68594 7.46471 5.49194 8.01171 5.49894C8.14071 5.49994 8.26871 5.51094 8.39571 5.53194C8.10178 5.25056 7.76699 5.01523 7.40271 4.83394L7.39271 4.82894C6.34771 4.28194 5.19871 4.26294 4.99971 4.26294C2.55971 4.26294 0.175707 5.89694 0.00970727 7.75294ZM10.2727 15.6649C10.3607 15.6379 10.4497 15.6109 10.5377 15.5809C10.4357 15.6129 10.3337 15.6409 10.2307 15.6669L10.2727 15.6649Z"
					fill="currentcolor"
				/>
				<path
					d="M10.2281 15.6669C10.33 15.6413 10.431 15.6126 10.5311 15.5809L10.6131 15.5559C12.3351 14.9606 13.803 13.7967 14.7751 12.2559C14.8033 12.2091 14.8154 12.1543 14.8095 12.1C14.8037 12.0457 14.7802 11.9948 14.7426 11.9551C14.7051 11.9154 14.6556 11.8891 14.6017 11.8802C14.5478 11.8713 14.4924 11.8804 14.4441 11.9059C14.2291 12.0179 14.0082 12.1159 13.7811 12.1999C13.0636 12.467 12.3038 12.6025 11.5381 12.5999C8.58115 12.5999 6.00615 10.5689 6.00615 7.95588C6.00815 7.82088 6.02315 7.68788 6.05215 7.55688C5.28374 8.32304 4.81493 9.33893 4.73056 10.4207C4.64618 11.5026 4.95181 12.5789 5.59215 13.4549L5.59515 13.4599C5.91015 13.9009 6.30215 14.2809 6.75315 14.5809H6.75615L6.90015 14.6709C7.77715 15.2209 8.62115 15.7489 10.2281 15.6669Z"
					fill="currentcolor"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1296_7502">
					<rect width="16" height="16" fill="white" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const SafariIcon = createIcon({
	displayName: "SafariIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_1296_7506)">
				<path
					d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM8.25 1.25V2.75C8.25 2.8163 8.22366 2.87989 8.17678 2.92678C8.12989 2.97366 8.0663 3 8 3C7.9337 3 7.87011 2.97366 7.82322 2.92678C7.77634 2.87989 7.75 2.8163 7.75 2.75V1.25C7.75 1.1837 7.77634 1.12011 7.82322 1.07322C7.87011 1.02634 7.9337 1 8 1C8.0663 1 8.12989 1.02634 8.17678 1.07322C8.22366 1.12011 8.25 1.1837 8.25 1.25ZM8.25 13.25V14.75C8.25 14.8163 8.22366 14.8799 8.17678 14.9268C8.12989 14.9737 8.0663 15 8 15C7.9337 15 7.87011 14.9737 7.82322 14.9268C7.77634 14.8799 7.75 14.8163 7.75 14.75V13.25C7.75 13.1837 7.77634 13.1201 7.82322 13.0732C7.87011 13.0263 7.9337 13 8 13C8.0663 13 8.12989 13.0263 8.17678 13.0732C8.22366 13.1201 8.25 13.1837 8.25 13.25ZM4.5 1.938C4.52842 1.92149 4.55982 1.91074 4.5924 1.90638C4.62499 1.90201 4.65811 1.90412 4.68987 1.91257C4.72164 1.92102 4.75143 1.93566 4.77753 1.95564C4.80363 1.97561 4.82554 2.00054 4.842 2.029L5.592 3.329C5.62515 3.38655 5.63408 3.45492 5.61683 3.51905C5.59958 3.58319 5.55755 3.63785 5.5 3.671C5.44245 3.70415 5.37408 3.71308 5.30995 3.69583C5.24581 3.67858 5.19115 3.63655 5.158 3.579L4.408 2.279C4.37505 2.22157 4.36623 2.1534 4.38348 2.08947C4.40073 2.02554 4.44263 1.97107 4.5 1.938ZM10.5 12.33C10.5574 12.297 10.6256 12.2882 10.6895 12.3055C10.7535 12.3227 10.8079 12.3646 10.841 12.422L11.591 13.721C11.6098 13.7494 11.6226 13.7814 11.6285 13.815C11.6345 13.8485 11.6336 13.8829 11.6258 13.9161C11.618 13.9493 11.6034 13.9805 11.5831 14.0078C11.5628 14.0352 11.5371 14.0581 11.5076 14.0752C11.4781 14.0923 11.4455 14.1031 11.4116 14.1071C11.3778 14.1111 11.3435 14.1081 11.3108 14.0983C11.2782 14.0886 11.2479 14.0723 11.2218 14.0504C11.1956 14.0285 11.1743 14.0015 11.159 13.971L10.409 12.671C10.3763 12.6138 10.3675 12.5459 10.3845 12.4823C10.4016 12.4186 10.4431 12.3642 10.5 12.331V12.33ZM2.28 4.408L3.578 5.158C3.63555 5.19115 3.67758 5.24581 3.69483 5.30995C3.71208 5.37408 3.70315 5.44245 3.67 5.5C3.63685 5.55755 3.58219 5.59958 3.51805 5.61683C3.45392 5.63408 3.38555 5.62515 3.328 5.592L2.029 4.842C1.97145 4.80885 1.92942 4.75419 1.91217 4.69005C1.89492 4.62592 1.90385 4.55755 1.937 4.5C1.97015 4.44245 2.02481 4.40042 2.08895 4.38317C2.15308 4.36592 2.22145 4.37485 2.279 4.408H2.28ZM12.672 10.408L13.971 11.158C13.9995 11.1744 14.0245 11.1963 14.0445 11.2224C14.0646 11.2484 14.0793 11.2782 14.0878 11.3099C14.0964 11.3417 14.0986 11.3748 14.0943 11.4074C14.0901 11.4401 14.0794 11.4715 14.063 11.5C14.0466 11.5285 14.0247 11.5535 13.9986 11.5735C13.9726 11.5936 13.9428 11.6083 13.9111 11.6168C13.8793 11.6254 13.8462 11.6276 13.8136 11.6233C13.7809 11.6191 13.7495 11.6084 13.721 11.592L12.421 10.842C12.3634 10.8088 12.3214 10.7542 12.3042 10.6901C12.2869 10.6259 12.2958 10.5576 12.329 10.5C12.3622 10.4424 12.4168 10.4004 12.4809 10.3832C12.5451 10.3659 12.6134 10.3748 12.671 10.408H12.672ZM1 8C1 7.9337 1.02634 7.87011 1.07322 7.82322C1.12011 7.77634 1.1837 7.75 1.25 7.75H2.75C2.8163 7.75 2.87989 7.77634 2.92678 7.82322C2.97366 7.87011 3 7.9337 3 8C3 8.0663 2.97366 8.12989 2.92678 8.17678C2.87989 8.22366 2.8163 8.25 2.75 8.25H1.25C1.1837 8.25 1.12011 8.22366 1.07322 8.17678C1.02634 8.12989 1 8.0663 1 8ZM13 8C13 7.9337 13.0263 7.87011 13.0732 7.82322C13.1201 7.77634 13.1837 7.75 13.25 7.75H14.75C14.8163 7.75 14.8799 7.77634 14.9268 7.82322C14.9737 7.87011 15 7.9337 15 8C15 8.0663 14.9737 8.12989 14.9268 8.17678C14.8799 8.22366 14.8163 8.25 14.75 8.25H13.25C13.1837 8.25 13.1201 8.22366 13.0732 8.17678C13.0263 8.12989 13 8.0663 13 8ZM2.03 11.159L3.328 10.409C3.38496 10.3804 3.4507 10.3746 3.51178 10.3929C3.57285 10.4111 3.62464 10.452 3.65657 10.5072C3.6885 10.5624 3.69815 10.6276 3.68354 10.6897C3.66894 10.7517 3.63118 10.8059 3.578 10.841L2.279 11.591C2.22204 11.6196 2.1563 11.6254 2.09522 11.6071C2.03415 11.5889 1.98236 11.548 1.95043 11.4928C1.9185 11.4376 1.90885 11.3724 1.92346 11.3103C1.93806 11.2483 1.97582 11.1941 2.029 11.159H2.03ZM12.422 5.159L13.721 4.409C13.7494 4.39233 13.7809 4.38144 13.8136 4.37698C13.8462 4.37251 13.8795 4.37455 13.9113 4.38298C13.9432 4.3914 13.9731 4.40605 13.9993 4.42608C14.0255 4.44611 14.0475 4.47112 14.0639 4.49967C14.0804 4.52822 14.0911 4.55975 14.0954 4.59245C14.0996 4.62514 14.0973 4.65835 14.0887 4.69017C14.0801 4.72199 14.0652 4.75179 14.045 4.77784C14.0248 4.8039 13.9997 4.8257 13.971 4.842L12.671 5.592C12.6134 5.62515 12.5451 5.63408 12.4809 5.61683C12.4168 5.59958 12.3622 5.55755 12.329 5.5C12.2958 5.44245 12.2869 5.37408 12.3042 5.30995C12.3214 5.24581 12.3634 5.19115 12.421 5.158L12.422 5.159ZM4.5 14.061C4.44263 14.0279 4.40073 13.9735 4.38348 13.9095C4.36623 13.8456 4.37505 13.7774 4.408 13.72L5.158 12.42C5.19115 12.3624 5.24581 12.3204 5.30995 12.3032C5.37408 12.2859 5.44245 12.2948 5.5 12.328C5.55755 12.3612 5.59958 12.4158 5.61683 12.4799C5.63408 12.5441 5.62515 12.6124 5.592 12.67L4.842 13.97C4.82554 13.9985 4.80363 14.0234 4.77753 14.0434C4.75143 14.0633 4.72164 14.078 4.68987 14.0864C4.65811 14.0949 4.62499 14.097 4.5924 14.0926C4.55982 14.0883 4.52842 14.0775 4.5 14.061ZM10.5 3.669C10.4715 3.65254 10.4466 3.63063 10.4266 3.60453C10.4067 3.57843 10.392 3.54864 10.3836 3.51687C10.3751 3.48511 10.373 3.45198 10.3774 3.4194C10.3817 3.38682 10.3925 3.35542 10.409 3.327L11.159 2.028C11.1743 1.99755 11.1956 1.97054 11.2218 1.94864C11.2479 1.92675 11.2782 1.91042 11.3108 1.90066C11.3435 1.8909 11.3778 1.88792 11.4116 1.8919C11.4455 1.89589 11.4781 1.90674 11.5076 1.92381C11.5371 1.94088 11.5628 1.9638 11.5831 1.99116C11.6034 2.01852 11.618 2.04974 11.6258 2.08291C11.6336 2.11608 11.6345 2.1505 11.6285 2.18405C11.6226 2.2176 11.6098 2.24957 11.591 2.278L10.841 3.578C10.8076 3.63503 10.753 3.67651 10.6891 3.69338C10.6252 3.71024 10.5572 3.70112 10.5 3.668V3.669ZM6.494 1.415L6.624 1.898C6.63254 1.92971 6.63474 1.9628 6.63049 1.99537C6.62624 2.02793 6.61562 2.05934 6.59923 2.0878C6.56613 2.14528 6.51155 2.18726 6.4475 2.2045C6.38345 2.22174 6.31518 2.21283 6.2577 2.17973C6.20022 2.14663 6.15824 2.09205 6.141 2.028L6.011 1.545C5.99376 1.48095 6.00267 1.41268 6.03577 1.3552C6.06887 1.29772 6.12345 1.25574 6.1875 1.2385C6.25155 1.22126 6.31982 1.23017 6.3773 1.26327C6.43478 1.29637 6.47676 1.35095 6.494 1.415ZM9.86 13.972L9.99 14.455C9.99854 14.4867 10.0007 14.5198 9.99649 14.5524C9.99224 14.5849 9.98162 14.6163 9.96523 14.6448C9.93213 14.7023 9.87755 14.7443 9.8135 14.7615C9.74945 14.7787 9.68118 14.7698 9.6237 14.7367C9.56622 14.7036 9.52424 14.649 9.507 14.585L9.377 14.102C9.35976 14.038 9.36867 13.9697 9.40177 13.9122C9.43487 13.8547 9.48945 13.8127 9.5535 13.7955C9.61755 13.7783 9.68582 13.7872 9.7433 13.8203C9.80078 13.8534 9.84276 13.908 9.86 13.972ZM3.05 3.05C3.07322 3.02672 3.10081 3.00825 3.13118 2.99564C3.16156 2.98304 3.19412 2.97655 3.227 2.97655C3.25988 2.97655 3.29244 2.98304 3.32282 2.99564C3.35319 3.00825 3.38078 3.02672 3.404 3.05L3.757 3.404C3.79958 3.45163 3.8223 3.51375 3.82052 3.57761C3.81873 3.64147 3.79256 3.70222 3.74739 3.74739C3.70222 3.79256 3.64147 3.81873 3.57761 3.82052C3.51375 3.8223 3.45163 3.79958 3.404 3.757L3.05 3.404C3.02672 3.38078 3.00825 3.35319 2.99564 3.32282C2.98304 3.29244 2.97655 3.25988 2.97655 3.227C2.97655 3.19412 2.98304 3.16156 2.99564 3.13118C3.00825 3.10081 3.02672 3.07322 3.05 3.05ZM12.243 12.243C12.2899 12.1963 12.3533 12.1701 12.4195 12.1701C12.4857 12.1701 12.5491 12.1963 12.596 12.243L12.95 12.596C12.9732 12.6192 12.9917 12.6468 13.0043 12.6772C13.0168 12.7076 13.0233 12.7401 13.0233 12.773C13.0233 12.8059 13.0168 12.8384 13.0043 12.8688C12.9917 12.8992 12.9732 12.9268 12.95 12.95C12.9268 12.9732 12.8992 12.9917 12.8688 13.0043C12.8384 13.0168 12.8059 13.0233 12.773 13.0233C12.7401 13.0233 12.7076 13.0168 12.6772 13.0043C12.6468 12.9917 12.6192 12.9732 12.596 12.95L12.243 12.596C12.1963 12.5491 12.1701 12.4857 12.1701 12.4195C12.1701 12.3533 12.1963 12.2899 12.243 12.243ZM1.545 6.01L2.028 6.14C2.09205 6.15724 2.14663 6.19922 2.17973 6.2567C2.21283 6.31418 2.22174 6.38245 2.2045 6.4465C2.18726 6.51055 2.14528 6.56513 2.0878 6.59823C2.03032 6.63133 1.96205 6.64024 1.898 6.623L1.415 6.493C1.38173 6.48599 1.35023 6.47226 1.32245 6.45266C1.29466 6.43306 1.27117 6.408 1.2534 6.37901C1.23563 6.35002 1.22396 6.3177 1.21911 6.28405C1.21426 6.25039 1.21632 6.2161 1.22518 6.18327C1.23403 6.15044 1.24949 6.11976 1.27061 6.09311C1.29173 6.06646 1.31806 6.0444 1.348 6.02827C1.37793 6.01215 1.41084 6.0023 1.44472 5.99933C1.47859 5.99636 1.51271 6.00033 1.545 6.011V6.01ZM14.102 9.375L14.585 9.505C14.649 9.52224 14.7036 9.56422 14.7367 9.6217C14.7698 9.67918 14.7787 9.74745 14.7615 9.8115C14.7443 9.87555 14.7023 9.93013 14.6448 9.96323C14.5873 9.99633 14.519 10.0052 14.455 9.988L13.972 9.858C13.9403 9.84946 13.9106 9.83476 13.8845 9.81474C13.8585 9.79472 13.8367 9.76977 13.8203 9.7413C13.8039 9.71284 13.7933 9.68143 13.789 9.64887C13.7848 9.6163 13.787 9.58321 13.7955 9.5515C13.804 9.51979 13.8187 9.49006 13.8388 9.46403C13.8588 9.438 13.8837 9.41616 13.9122 9.39977C13.9407 9.38338 13.9721 9.37276 14.0046 9.36851C14.0372 9.36426 14.0703 9.36646 14.102 9.375ZM1.239 9.811C1.22187 9.74709 1.23077 9.67899 1.26376 9.62163C1.29676 9.56427 1.35114 9.52233 1.415 9.505L1.898 9.375C1.92971 9.36646 1.9628 9.36426 1.99537 9.36851C2.02793 9.37276 2.05934 9.38338 2.0878 9.39977C2.11627 9.41616 2.14122 9.438 2.16124 9.46403C2.18127 9.49006 2.19596 9.51979 2.2045 9.5515C2.21304 9.58321 2.21524 9.6163 2.21099 9.64887C2.20674 9.68143 2.19612 9.71284 2.17973 9.7413C2.16334 9.76977 2.1415 9.79472 2.11547 9.81474C2.08944 9.83476 2.05971 9.84946 2.028 9.858L1.545 9.988C1.48095 10.0051 1.41273 9.99605 1.35535 9.96286C1.29797 9.92967 1.25611 9.87504 1.239 9.811ZM13.796 6.446C13.7789 6.38209 13.7878 6.31399 13.8208 6.25663C13.8538 6.19927 13.9081 6.15733 13.972 6.14L14.455 6.01C14.519 5.99276 14.5873 6.00167 14.6448 6.03477C14.7023 6.06787 14.7443 6.12245 14.7615 6.1865C14.7787 6.25055 14.7698 6.31882 14.7367 6.3763C14.7036 6.43378 14.649 6.47576 14.585 6.493L14.102 6.623C14.038 6.6401 13.9697 6.63105 13.9123 6.59786C13.855 6.56467 13.8131 6.51004 13.796 6.446ZM3.045 12.944C2.99772 12.8942 2.96903 12.8296 2.96375 12.7611C2.95847 12.6927 2.97692 12.6245 3.016 12.568L6.914 6.976C6.93085 6.95184 6.95184 6.93085 6.976 6.914L12.578 3.03C12.6312 2.9844 12.6996 2.96056 12.7696 2.96324C12.8396 2.96592 12.906 2.99493 12.9555 3.04446C13.0051 3.09399 13.0341 3.1604 13.0368 3.2304C13.0394 3.30039 13.0156 3.36882 12.97 3.422L9.086 9.024C9.06915 9.04816 9.04816 9.06915 9.024 9.086L3.432 12.984C3.37444 13.024 3.30465 13.0425 3.23483 13.0363C3.16501 13.0301 3.09959 12.9995 3.05 12.95L3.045 12.944ZM6.188 14.761C6.12414 14.7437 6.06976 14.7017 6.03676 14.6444C6.00377 14.587 5.99487 14.5189 6.012 14.455L6.141 13.972C6.15824 13.908 6.20022 13.8534 6.2577 13.8203C6.31518 13.7872 6.38345 13.7783 6.4475 13.7955C6.51155 13.8127 6.56613 13.8547 6.59923 13.9122C6.63233 13.9697 6.64124 14.038 6.624 14.102L6.494 14.585C6.47667 14.6489 6.43473 14.7032 6.37737 14.7362C6.32001 14.7692 6.25191 14.7781 6.188 14.761ZM9.553 2.204C9.48896 2.18689 9.43433 2.14503 9.40114 2.08765C9.36795 2.03027 9.3589 1.96205 9.376 1.898L9.506 1.415C9.51454 1.38329 9.52923 1.35356 9.54926 1.32753C9.56928 1.3015 9.59423 1.27966 9.6227 1.26327C9.65116 1.24688 9.68257 1.23626 9.71513 1.23201C9.7477 1.22776 9.78079 1.22996 9.8125 1.2385C9.84421 1.24704 9.87394 1.26173 9.89997 1.28176C9.926 1.30178 9.94784 1.32673 9.96423 1.3552C9.98062 1.38366 9.99124 1.41507 9.99549 1.44763C9.99974 1.4802 9.99754 1.51329 9.989 1.545L9.859 2.028C9.84167 2.09186 9.79973 2.14625 9.74237 2.17924C9.68501 2.21223 9.61691 2.22113 9.553 2.204Z"
					fill="currentcolor"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1296_7506">
					<rect width="16" height="16" fill="currentcolor" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const AppleIcon = createIcon({
	displayName: "AppleIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_1296_7493)">
				<path
					d="M11.1819 0.00800701C11.1479 -0.029993 9.92293 0.023007 8.85693 1.18001C7.79093 2.33601 7.95493 3.66201 7.97893 3.69601C8.00293 3.73001 9.49893 3.78301 10.4539 2.43801C11.4089 1.09301 11.2159 0.047007 11.1819 0.00800701ZM14.4959 11.741C14.4479 11.645 12.1709 10.507 12.3829 8.31901C12.5949 6.13001 14.0579 5.53001 14.0809 5.46501C14.1039 5.40001 13.4839 4.67501 12.8269 4.30801C12.3445 4.04926 11.8107 3.90102 11.2639 3.87401C11.1559 3.87101 10.7809 3.77901 10.0099 3.99001C9.50193 4.12901 8.35693 4.57901 8.04193 4.59701C7.72593 4.61501 6.78593 4.07501 5.77493 3.93201C5.12793 3.80701 4.44193 4.06301 3.95093 4.26001C3.46093 4.45601 2.52893 5.01401 1.87693 6.49701C1.22493 7.97901 1.56593 10.327 1.80993 11.057C2.05393 11.786 2.43493 12.981 3.08293 13.853C3.65893 14.837 4.42293 15.52 4.74193 15.752C5.06093 15.984 5.96093 16.138 6.58493 15.819C7.08693 15.511 7.99293 15.334 8.35093 15.347C8.70793 15.36 9.41193 15.501 10.1329 15.886C10.7039 16.083 11.2439 16.001 11.7849 15.781C12.3259 15.56 13.1089 14.722 14.0229 13.023C14.3699 12.233 14.5279 11.806 14.4959 11.741Z"
					fill="currentcolor"
				/>
				<path
					d="M11.1819 0.00800701C11.1479 -0.029993 9.92293 0.023007 8.85693 1.18001C7.79093 2.33601 7.95493 3.66201 7.97893 3.69601C8.00293 3.73001 9.49893 3.78301 10.4539 2.43801C11.4089 1.09301 11.2159 0.047007 11.1819 0.00800701ZM14.4959 11.741C14.4479 11.645 12.1709 10.507 12.3829 8.31901C12.5949 6.13001 14.0579 5.53001 14.0809 5.46501C14.1039 5.40001 13.4839 4.67501 12.8269 4.30801C12.3445 4.04926 11.8107 3.90102 11.2639 3.87401C11.1559 3.87101 10.7809 3.77901 10.0099 3.99001C9.50193 4.12901 8.35693 4.57901 8.04193 4.59701C7.72593 4.61501 6.78593 4.07501 5.77493 3.93201C5.12793 3.80701 4.44193 4.06301 3.95093 4.26001C3.46093 4.45601 2.52893 5.01401 1.87693 6.49701C1.22493 7.97901 1.56593 10.327 1.80993 11.057C2.05393 11.786 2.43493 12.981 3.08293 13.853C3.65893 14.837 4.42293 15.52 4.74193 15.752C5.06093 15.984 5.96093 16.138 6.58493 15.819C7.08693 15.511 7.99293 15.334 8.35093 15.347C8.70793 15.36 9.41193 15.501 10.1329 15.886C10.7039 16.083 11.2439 16.001 11.7849 15.781C12.3259 15.56 13.1089 14.722 14.0229 13.023C14.3699 12.233 14.5279 11.806 14.4959 11.741Z"
					fill="currentcolor"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1296_7493">
					<rect width="16" height="16" fill="currentcolor" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const AndroidIcon = createIcon({
	displayName: "AndroidIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg
			width="16"
			height="16"
			viewBox="0 0 16 16"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_1296_7496)">
				<path
					d="M10.2131 1.47096L10.9041 0.210963C10.9501 0.127963 10.9341 0.0639631 10.8561 0.0189631C10.7711 -0.0190369 10.7061 -3.68729e-05 10.6611 0.0769631L9.96112 1.34696C9.34452 1.07686 8.67828 0.938569 8.00512 0.940963C7.31712 0.940963 6.66512 1.07596 6.04912 1.34496L5.34912 0.0749631C5.30312 -3.68729e-05 5.23912 -0.0180369 5.15412 0.0199631C5.07612 0.0659631 5.06012 0.129963 5.10512 0.212963L5.79612 1.47196C5.11696 1.8062 4.53939 2.31576 4.12312 2.94796C3.71347 3.56105 3.49652 4.28262 3.50012 5.01996H12.5001C12.5001 4.26996 12.2921 3.57996 11.8771 2.94796C11.4627 2.31747 10.8885 1.80816 10.2131 1.47196V1.47096ZM6.22012 3.30296C6.18537 3.3384 6.14379 3.36642 6.09789 3.38533C6.052 3.40423 6.00275 3.41364 5.95312 3.41296C5.90402 3.41411 5.85523 3.4049 5.80991 3.38595C5.7646 3.367 5.72379 3.33873 5.69012 3.30296C5.65554 3.26834 5.62826 3.22714 5.60988 3.1818C5.5915 3.13646 5.5824 3.08789 5.58312 3.03896C5.58249 2.9899 5.59163 2.94121 5.61 2.89572C5.62837 2.85022 5.65561 2.80883 5.69012 2.77396C5.72382 2.73825 5.76465 2.71001 5.80995 2.69106C5.85525 2.67211 5.90403 2.66288 5.95312 2.66396C6.05612 2.66396 6.14612 2.70096 6.22012 2.77396C6.256 2.80806 6.28447 2.84919 6.30374 2.89479C6.32301 2.94038 6.33267 2.98946 6.33212 3.03896C6.33254 3.08829 6.32281 3.13718 6.30354 3.1826C6.28428 3.22801 6.25588 3.26898 6.22012 3.30296ZM10.3211 3.30296C10.2875 3.33855 10.2469 3.36672 10.2017 3.38566C10.1566 3.4046 10.108 3.4139 10.0591 3.41296C10.0093 3.41383 9.95986 3.40452 9.91377 3.38561C9.86769 3.36669 9.82596 3.33857 9.79112 3.30296C9.75526 3.26905 9.7268 3.2281 9.70753 3.18267C9.68826 3.13724 9.67858 3.08831 9.67912 3.03896C9.67912 2.93596 9.71612 2.84796 9.79112 2.77396C9.82599 2.7384 9.86773 2.71031 9.91381 2.6914C9.95988 2.67249 10.0093 2.66315 10.0591 2.66396C10.1631 2.66396 10.2491 2.70096 10.3211 2.77396C10.3558 2.80873 10.3831 2.8501 10.4015 2.89562C10.4199 2.94114 10.4289 2.98988 10.4281 3.03896C10.4281 3.14096 10.3931 3.22896 10.3211 3.30296ZM3.50012 11.77C3.50012 12.064 3.60412 12.314 3.81112 12.52C4.01912 12.724 4.27112 12.827 4.57112 12.827H5.32912L5.33912 15.009C5.33912 15.285 5.43612 15.519 5.63112 15.712C5.72223 15.8049 5.83125 15.8783 5.95157 15.9278C6.0719 15.9773 6.20102 16.0018 6.33112 16C6.4628 16.0025 6.59362 15.9783 6.71566 15.9288C6.8377 15.8793 6.94843 15.8055 7.04112 15.712C7.13563 15.6212 7.21036 15.512 7.26061 15.391C7.31086 15.27 7.33555 15.1399 7.33312 15.009V12.827H8.67612V15.009C8.67612 15.285 8.77312 15.519 8.96812 15.712C9.06078 15.8056 9.17149 15.8794 9.29355 15.9289C9.4156 15.9784 9.54644 16.0026 9.67812 16C9.8098 16.0025 9.94062 15.9783 10.0627 15.9288C10.1847 15.8793 10.2954 15.8055 10.3881 15.712C10.4826 15.6212 10.5574 15.512 10.6076 15.391C10.6579 15.27 10.6825 15.1399 10.6801 15.009V12.827H11.4401C11.7311 12.827 11.9801 12.724 12.1891 12.519C12.3961 12.314 12.5001 12.064 12.5001 11.769V5.36496H3.50012V11.769V11.77ZM13.9951 5.18296C13.8656 5.18075 13.7368 5.20416 13.6164 5.25187C13.4959 5.29958 13.386 5.37064 13.2931 5.46096C13.1987 5.54801 13.1238 5.65404 13.0733 5.7721C13.0228 5.89016 12.9979 6.01758 13.0001 6.14596V10.209C13.0001 10.48 13.0981 10.71 13.2931 10.899C13.3851 10.991 13.4947 11.0635 13.6153 11.1123C13.7359 11.1611 13.865 11.1851 13.9951 11.183C14.2751 11.183 14.5121 11.088 14.7071 10.899C14.8015 10.8108 14.8763 10.7039 14.9267 10.585C14.9772 10.4662 15.0022 10.3381 15.0001 10.209V6.14596C15.0024 6.01758 14.9774 5.89016 14.9269 5.7721C14.8764 5.65404 14.8015 5.54801 14.7071 5.46096C14.6126 5.36998 14.5011 5.29858 14.3789 5.25087C14.2567 5.20316 14.1263 5.18008 13.9951 5.18296ZM1.29312 5.46596C1.38688 5.37362 1.49819 5.301 1.62048 5.2524C1.74277 5.20379 1.87355 5.18018 2.00512 5.18296C2.27812 5.18296 2.51212 5.27696 2.70712 5.46596C2.80074 5.55256 2.87518 5.65781 2.92565 5.77493C2.97611 5.89205 3.00148 6.01844 3.00012 6.14596V10.209C3.00235 10.3376 2.97791 10.4654 2.92835 10.5841C2.87878 10.7029 2.80516 10.8101 2.71212 10.899C2.61954 10.9916 2.50918 11.0645 2.38767 11.1133C2.26616 11.1621 2.13603 11.1858 2.00512 11.183C1.87349 11.1856 1.74268 11.1618 1.62039 11.113C1.49811 11.0642 1.38683 10.9914 1.29312 10.899C1.19878 10.8108 1.12397 10.7039 1.0735 10.585C1.02304 10.4662 0.998039 10.3381 1.00012 10.209V6.14596C1.00012 5.88196 1.09812 5.65496 1.29312 5.46596Z"
					fill="currentcolor"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1296_7496">
					<rect width="16" height="16" fill="currentcolor" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ClockIcon = createIcon({
	displayName: "ClockIcon",
	viewBox: "0 0 30 32",
	path: (
		<svg width="30" height="32" viewBox="0 0 30 32" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M4.19885 0.880859C4.00882 0.880859 3.81878 0.949271 3.68196 1.08229L1.02909 3.76177H11.4011C11.7964 3.76177 12.1195 4.08483 12.1195 4.4801C12.1195 6.09539 13.3851 7.36101 15.0004 7.36101C16.6157 7.36101 17.8813 6.09539 17.8813 4.4801C17.8813 4.08483 18.2005 3.76177 18.5996 3.76177H28.9717L26.3188 1.08229C26.182 0.949271 25.9919 0.880859 25.8019 0.880859H4.19885ZM0.599609 5.19843V30.4007C0.599609 30.7998 0.922666 31.119 1.32174 31.119H28.679C29.0781 31.119 29.4011 30.7998 29.4011 30.4007V5.19843H19.2533C18.9189 7.266 17.1782 8.80147 15.0004 8.80147C12.8226 8.80147 11.0781 7.266 10.7474 5.19843H0.599609ZM15.0004 12.4007C18.5806 12.4007 21.4805 15.3006 21.4805 18.8809C21.4805 22.4611 18.5806 25.361 15.0004 25.361C11.4201 25.361 8.52022 22.4611 8.52022 18.8809C8.52022 15.3006 11.4201 12.4007 15.0004 12.4007ZM14.282 14.5595V18.5198L11.6938 20.4543L12.5489 21.6249L15.426 19.4662L15.7187 19.2381V14.5595H14.282Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const QuickLinksIcon = createIcon({
	displayName: "QuickLinksIcon",
	viewBox: "0 0 25 30",
	path: (
		<svg fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M24.0507 0C23.6554 0.0380068 23.3628 0.39147 23.4008 0.78674C23.435 1.18581 23.7922 1.47466 24.1875 1.44046H33.5486L27.7867 7.19848C28.2922 7.34291 28.6571 7.70777 28.8015 8.21326L34.5595 2.45144V11.8125C34.5557 12.0709 34.6926 12.3142 34.9168 12.4434C35.1411 12.5764 35.4185 12.5764 35.6427 12.4434C35.867 12.3142 36.0038 12.0709 36 11.8125V0H24.1875C24.1647 0 24.1419 0 24.1191 0C24.0963 0 24.0735 0 24.0507 0ZM1.44046 7.19848C0.646114 7.19848 0 7.8484 0 8.63894V34.5595C0 35.3539 0.646114 36 1.44046 36H27.3611C28.1516 36 28.8015 35.3539 28.8015 34.5595V8.63894C28.8015 8.49831 28.8053 8.4223 28.7331 8.28167L19.5089 17.5059C19.3644 17.6503 19.1554 17.7073 19.011 17.7073C18.7943 17.7073 18.6385 17.6503 18.4941 17.5059C18.209 17.2171 18.209 16.78 18.4941 16.4911L27.7183 7.26689C27.5777 7.19468 27.5017 7.19848 27.3611 7.19848H1.44046Z"
				fill="#D3D3D3"
			/>
		</svg>
	),
});

export const BellIcon = createIcon({
	displayName: "BellIcon",
	viewBox: "0 0 16 20",
	path: (
		<svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0.75 17C0.533333 17 0.354167 16.9292 0.2125 16.7875C0.0708333 16.6458 0 16.4667 0 16.25C0 16.0333 0.0708333 15.8542 0.2125 15.7125C0.354167 15.5708 0.533333 15.5 0.75 15.5H2V7.9C2 6.5 2.4125 5.24583 3.2375 4.1375C4.0625 3.02917 5.15 2.33333 6.5 2.05V1.5C6.5 1.08333 6.64583 0.729167 6.9375 0.4375C7.22917 0.145833 7.58333 0 8 0C8.41667 0 8.77083 0.145833 9.0625 0.4375C9.35417 0.729167 9.5 1.08333 9.5 1.5V2.05C10.85 2.33333 11.9375 3.02917 12.7625 4.1375C13.5875 5.24583 14 6.5 14 7.9V15.5H15.25C15.4667 15.5 15.6458 15.5708 15.7875 15.7125C15.9292 15.8542 16 16.0333 16 16.25C16 16.4667 15.9292 16.6458 15.7875 16.7875C15.6458 16.9292 15.4667 17 15.25 17H0.75ZM8 20C7.45 20 6.97917 19.8042 6.5875 19.4125C6.19583 19.0208 6 18.55 6 18H10C10 18.55 9.80417 19.0208 9.4125 19.4125C9.02083 19.8042 8.55 20 8 20Z"
				fill="#384155"
			/>
		</svg>
	),
});

export const ThreeVerticalDots = createIcon({
	displayName: "ThreeVerticalDots",
	viewBox: "0 0 4 14",
	path: (
		<svg
			width="4"
			height="14"
			viewBox="0 0 4 14"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M3.5 12C3.5 12.3978 3.34196 12.7794 3.06066 13.0607C2.77936 13.342 2.39782 13.5 2 13.5C1.60218 13.5 1.22064 13.342 0.93934 13.0607C0.658035 12.7794 0.5 12.3978 0.5 12C0.5 11.6022 0.658035 11.2206 0.93934 10.9393C1.22064 10.658 1.60218 10.5 2 10.5C2.39782 10.5 2.77936 10.658 3.06066 10.9393C3.34196 11.2206 3.5 11.6022 3.5 12ZM3.5 7C3.5 7.39782 3.34196 7.77936 3.06066 8.06066C2.77936 8.34196 2.39782 8.5 2 8.5C1.60218 8.5 1.22064 8.34196 0.93934 8.06066C0.658035 7.77936 0.5 7.39782 0.5 7C0.5 6.60218 0.658035 6.22064 0.93934 5.93934C1.22064 5.65804 1.60218 5.5 2 5.5C2.39782 5.5 2.77936 5.65804 3.06066 5.93934C3.34196 6.22064 3.5 6.60218 3.5 7ZM3.5 2C3.5 2.39782 3.34196 2.77936 3.06066 3.06066C2.77936 3.34196 2.39782 3.5 2 3.5C1.60218 3.5 1.22064 3.34196 0.93934 3.06066C0.658035 2.77936 0.5 2.39782 0.5 2C0.5 1.60218 0.658035 1.22064 0.93934 0.93934C1.22064 0.658035 1.60218 0.5 2 0.5C2.39782 0.5 2.77936 0.658035 3.06066 0.93934C3.34196 1.22064 3.5 1.60218 3.5 2Z"
				fill="currentcolor"
			/>
		</svg>
	),
});

export const PlusSquare = createIcon({
	displayName: "PlusSquare",
	viewBox: "0 0 17 16",
	path: (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width="17"
			height="16"
			viewBox="0 0 17 16"
			fill="currentColor"
		>
			<path
				d="M2.5 0C1.96957 0 1.46086 0.210714 1.08579 0.585786C0.710714 0.960859 0.5 1.46957 0.5 2L0.5 14C0.5 14.5304 0.710714 15.0391 1.08579 15.4142C1.46086 15.7893 1.96957 16 2.5 16H14.5C15.0304 16 15.5391 15.7893 15.9142 15.4142C16.2893 15.0391 16.5 14.5304 16.5 14V2C16.5 1.46957 16.2893 0.960859 15.9142 0.585786C15.5391 0.210714 15.0304 0 14.5 0L2.5 0ZM9 4.5V7.5H12C12.1326 7.5 12.2598 7.55268 12.3536 7.64645C12.4473 7.74021 12.5 7.86739 12.5 8C12.5 8.13261 12.4473 8.25979 12.3536 8.35355C12.2598 8.44732 12.1326 8.5 12 8.5H9V11.5C9 11.6326 8.94732 11.7598 8.85355 11.8536C8.75979 11.9473 8.63261 12 8.5 12C8.36739 12 8.24021 11.9473 8.14645 11.8536C8.05268 11.7598 8 11.6326 8 11.5V8.5H5C4.86739 8.5 4.74021 8.44732 4.64645 8.35355C4.55268 8.25979 4.5 8.13261 4.5 8C4.5 7.86739 4.55268 7.74021 4.64645 7.64645C4.74021 7.55268 4.86739 7.5 5 7.5H8V4.5C8 4.36739 8.05268 4.24021 8.14645 4.14645C8.24021 4.05268 8.36739 4 8.5 4C8.63261 4 8.75979 4.05268 8.85355 4.14645C8.94732 4.24021 9 4.36739 9 4.5Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const SaveIcon = createIcon({
	displayName: "SaveIcon",
	viewBox: "0 0 17 16",
	path: (
		<svg
			width="17"
			height="16"
			viewBox="0 0 17 16"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M9 1.5C9 1.10218 9.15804 0.720644 9.43934 0.43934C9.72064 0.158035 10.1022 0 10.5 0L14.5 0C15.0304 0 15.5391 0.210714 15.9142 0.585786C16.2893 0.960859 16.5 1.46957 16.5 2V14C16.5 14.5304 16.2893 15.0391 15.9142 15.4142C15.5391 15.7893 15.0304 16 14.5 16H2.5C1.96957 16 1.46086 15.7893 1.08579 15.4142C0.710714 15.0391 0.5 14.5304 0.5 14V2C0.5 1.46957 0.710714 0.960859 1.08579 0.585786C1.46086 0.210714 1.96957 0 2.5 0L8.5 0C8.186 0.418 8 0.937 8 1.5V7.5H6C5.90098 7.49982 5.80414 7.52905 5.72175 7.58398C5.63936 7.63891 5.57513 7.71706 5.53722 7.80854C5.4993 7.90001 5.4894 8.00068 5.50876 8.09779C5.52813 8.1949 5.57589 8.28407 5.646 8.354L8.146 10.854C8.19245 10.9006 8.24762 10.9375 8.30837 10.9627C8.36911 10.9879 8.43423 11.0009 8.5 11.0009C8.56577 11.0009 8.63089 10.9879 8.69163 10.9627C8.75238 10.9375 8.80755 10.9006 8.854 10.854L11.354 8.354C11.4241 8.28407 11.4719 8.1949 11.4912 8.09779C11.5106 8.00068 11.5007 7.90001 11.4628 7.80854C11.4249 7.71706 11.3606 7.63891 11.2783 7.58398C11.1959 7.52905 11.099 7.49982 11 7.5H9V1.5Z"
				fill="currentcolor"
			/>
		</svg>
	),
});

export const FingerPrint = createIcon({
	displayName: "FingerPrint",
	viewBox: "0 0 33 37",
	path: (
		<svg width="33" height="37" viewBox="0 0 33 37" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M26.8145 4.97554C26.6702 4.97554 26.5259 4.93948 26.3997 4.86735C22.9375 3.08212 19.944 2.32475 16.3555 2.32475C12.7851 2.32475 9.39495 3.17228 6.31137 4.86735C5.87859 5.10177 5.33761 4.93948 5.08515 4.50669C4.97269 4.2958 4.94746 4.04919 5.01491 3.81989C5.08235 3.59059 5.23707 3.3969 5.4458 3.28048C8.79987 1.45918 12.4785 0.521484 16.3555 0.521484C20.1965 0.521484 23.5506 1.36902 27.2292 3.26244C27.68 3.49687 27.8423 4.03785 27.6079 4.47063C27.5366 4.62095 27.4243 4.74809 27.2839 4.83742C27.1436 4.92674 26.9808 4.97462 26.8145 4.97554ZM1.00978 14.4427C0.843557 14.4442 0.680154 14.3997 0.537695 14.314C0.395237 14.2284 0.27929 14.1049 0.202718 13.9574C0.126145 13.8098 0.0919384 13.644 0.103893 13.4782C0.115847 13.3124 0.173495 13.1531 0.270441 13.0181C2.05567 10.4935 4.32778 8.50994 7.03268 7.12143C12.6949 4.20014 19.944 4.18211 25.6243 7.10339C28.3292 8.49191 30.6013 10.4575 32.3865 12.964C32.4557 13.0609 32.505 13.1706 32.5316 13.2866C32.5582 13.4027 32.5615 13.5229 32.5414 13.6402C32.5213 13.7576 32.4781 13.8698 32.4144 13.9704C32.3507 14.071 32.2677 14.1579 32.1702 14.2263C31.7554 14.5148 31.1964 14.4246 30.9079 14.0099C29.3287 11.7745 27.2311 9.95537 24.7948 8.7083C19.6194 6.0575 13.0015 6.0575 7.84414 8.72633C5.39171 9.98861 3.33599 11.7919 1.71305 14.064C1.56879 14.3164 1.2983 14.4427 1.00978 14.4427ZM12.2802 36.2081C12.1619 36.2095 12.0447 36.1862 11.936 36.1396C11.8273 36.093 11.7295 36.0242 11.649 35.9376C10.0802 34.3687 9.23266 33.3589 8.02447 31.1769C6.78022 28.9589 6.13104 26.254 6.13104 23.3508C6.13104 17.9951 10.7113 13.6312 16.3375 13.6312C21.9637 13.6312 26.544 17.9951 26.544 23.3508C26.544 23.8557 26.1473 24.2524 25.6423 24.2524C25.1374 24.2524 24.7407 23.8557 24.7407 23.3508C24.7407 18.9869 20.9719 15.4345 16.3375 15.4345C11.7031 15.4345 7.93431 18.9869 7.93431 23.3508C7.93431 25.9475 8.51135 28.3458 9.61134 30.2934C10.7654 32.3671 11.5589 33.2507 12.9474 34.6572C13.29 35.0179 13.29 35.5769 12.9474 35.9376C12.749 36.1179 12.5146 36.2081 12.2802 36.2081ZM25.2096 32.872C23.0637 32.872 21.1703 32.331 19.6194 31.2671C16.9326 29.4458 15.3277 26.4885 15.3277 23.3508C15.3277 22.8459 15.7244 22.4492 16.2293 22.4492C16.7342 22.4492 17.1309 22.8459 17.1309 23.3508C17.1309 25.8934 18.4293 28.2917 20.6293 29.7704C21.9096 30.636 23.4063 31.0507 25.2096 31.0507C25.6423 31.0507 26.3636 30.9966 27.085 30.8704C27.5718 30.7802 28.0407 31.1048 28.1308 31.6097C28.221 32.0966 27.8964 32.5655 27.3915 32.6556C26.3636 32.854 25.462 32.872 25.2096 32.872ZM21.585 36.5867C21.5129 36.5867 21.4227 36.5687 21.3506 36.5507C18.4834 35.7572 16.608 34.6933 14.6424 32.7638C13.3969 31.534 12.4092 30.068 11.7373 28.4516C11.0654 26.8353 10.7227 25.1012 10.7294 23.3508C10.7294 20.4295 13.2179 18.0492 16.2834 18.0492C19.349 18.0492 21.8375 20.4295 21.8375 23.3508C21.8375 25.2803 23.5145 26.8491 25.5882 26.8491C27.662 26.8491 29.339 25.2803 29.339 23.3508C29.339 16.5525 23.4784 11.0345 16.2654 11.0345C11.1441 11.0345 6.45563 13.8837 4.34581 18.3017C3.64254 19.7623 3.28189 21.4754 3.28189 23.3508C3.28189 24.7573 3.40812 26.9753 4.49008 29.8606C4.6704 30.3294 4.43598 30.8524 3.96713 31.0147C3.49828 31.195 2.97534 30.9425 2.81304 30.4917C1.94412 28.2111 1.49804 25.7913 1.49666 23.3508C1.49666 21.1869 1.91141 19.2213 2.72288 17.5082C5.12122 12.4771 10.4408 9.21321 16.2654 9.21321C24.4702 9.21321 31.1423 15.5427 31.1423 23.3328C31.1423 26.254 28.6538 28.6343 25.5882 28.6343C22.5227 28.6343 20.0342 26.254 20.0342 23.3328C20.0342 21.4033 18.3572 19.8344 16.2834 19.8344C14.2097 19.8344 12.5326 21.4033 12.5326 23.3328C12.5326 26.4163 13.7228 29.3016 15.9047 31.4655C17.6178 33.1605 19.2588 34.0982 21.8014 34.8015C22.2883 34.9277 22.5588 35.4327 22.4325 35.9015C22.3424 36.3162 21.9637 36.5867 21.585 36.5867Z"
				fill="black"
			/>
		</svg>
	),
});

export const PencilIcon = createIcon({
	displayName: "PencilIcon",
	viewBox: "0 0 20 17",
	path: (
		<svg
			width="20"
			height="17"
			viewBox="0 0 20 17"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M15.4117 0.155055C15.3034 0.0554605 15.1567 -0.000488281 15.0037 -0.000488281C14.8506 -0.000488281 14.7039 0.0554605 14.5957 0.155055L12.6947 1.90499L16.9733 5.84368L18.8742 4.0948C18.9279 4.04546 18.9706 3.98683 18.9996 3.92229C19.0287 3.85775 19.0437 3.78856 19.0437 3.71868C19.0437 3.6488 19.0287 3.57961 18.9996 3.51507C18.9706 3.45053 18.9279 3.3919 18.8742 3.34255L15.4117 0.155055ZM16.1573 6.59487L11.8787 2.65618L4.37663 9.56243H4.61555C4.7686 9.56243 4.91538 9.6184 5.02361 9.71803C5.13183 9.81766 5.19263 9.95278 5.19263 10.0937V10.6249H5.76972C5.92277 10.6249 6.06955 10.6809 6.17778 10.7805C6.286 10.8802 6.3468 11.0153 6.3468 11.1562V11.6874H6.92389C7.07694 11.6874 7.22372 11.7434 7.33195 11.843C7.44017 11.9427 7.50097 12.0778 7.50097 12.2187V12.7499H8.07806C8.23111 12.7499 8.3779 12.8059 8.48612 12.9055C8.59435 13.0052 8.65514 13.1403 8.65514 13.2812V13.5011L16.1573 6.59487ZM7.53791 14.5296C7.51362 14.4702 7.50111 14.4072 7.50097 14.3437V13.8124H6.92389C6.77084 13.8124 6.62405 13.7565 6.51583 13.6568C6.4076 13.5572 6.3468 13.4221 6.3468 13.2812V12.7499H5.76972C5.61666 12.7499 5.46988 12.694 5.36166 12.5943C5.25343 12.4947 5.19263 12.3596 5.19263 12.2187V11.6874H4.61555C4.46249 11.6874 4.31571 11.6315 4.20749 11.5318C4.09926 11.4322 4.03846 11.2971 4.03846 11.1562V10.6249H3.46138C3.39238 10.6248 3.32397 10.6133 3.2594 10.5909L3.0528 10.7801C2.9978 10.831 2.95461 10.8918 2.92584 10.9586L0.617498 16.2711C0.57552 16.3676 0.565244 16.4734 0.587944 16.5752C0.610643 16.6771 0.66532 16.7706 0.745197 16.8441C0.825073 16.9177 0.926635 16.968 1.03729 16.9889C1.14795 17.0098 1.26284 17.0003 1.36771 16.9617L7.13856 14.8367C7.21112 14.8102 7.27708 14.7704 7.33247 14.7198L7.53791 14.5307V14.5296Z"
				fill="currentcolor"
			/>
		</svg>
	),
});

export const TrashIcon = createIcon({
	displayName: "TrashIcon",
	viewBox: "0 0 17 17",
	path: (
		<svg
			width="17"
			height="17"
			viewBox="0 0 17 17"
			fill="currentcolor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M12.154 1.59375V2.65625H16.1936C16.3467 2.65625 16.4935 2.71222 16.6017 2.81185C16.7099 2.91148 16.7707 3.0466 16.7707 3.1875C16.7707 3.3284 16.7099 3.46352 16.6017 3.56315C16.4935 3.66278 16.3467 3.71875 16.1936 3.71875H15.5727L14.5882 15.045C14.5417 15.5775 14.2791 16.0744 13.8526 16.4367C13.4261 16.799 12.8671 17.0001 12.2867 17H5.09626C4.51593 17.0001 3.95686 16.799 3.53037 16.4367C3.10388 16.0744 2.84127 15.5775 2.79484 15.045L1.81033 3.71875H1.18939C1.03634 3.71875 0.889554 3.66278 0.781329 3.56315C0.673105 3.46352 0.612305 3.3284 0.612305 3.1875C0.612305 3.0466 0.673105 2.91148 0.781329 2.81185C0.889554 2.71222 1.03634 2.65625 1.18939 2.65625H5.22899V1.59375C5.22899 1.17106 5.41139 0.765685 5.73606 0.466799C6.06074 0.167912 6.50109 0 6.96025 0L10.4228 0C10.8819 0 11.3223 0.167912 11.6469 0.466799C11.9716 0.765685 12.154 1.17106 12.154 1.59375ZM6.38316 1.59375V2.65625H10.9998V1.59375C10.9998 1.45285 10.939 1.31773 10.8308 1.2181C10.7226 1.11847 10.5758 1.0625 10.4228 1.0625H6.96025C6.80719 1.0625 6.66041 1.11847 6.55218 1.2181C6.44396 1.31773 6.38316 1.45285 6.38316 1.59375ZM4.6519 5.34331L5.22899 14.3746C5.23207 14.4452 5.25042 14.5145 5.28296 14.5785C5.3155 14.6425 5.36157 14.6998 5.41849 14.7472C5.47541 14.7946 5.54203 14.8311 5.61445 14.8545C5.68687 14.8779 5.76363 14.8878 5.84024 14.8835C5.91685 14.8793 5.99178 14.861 6.06063 14.8298C6.12948 14.7986 6.19087 14.755 6.24121 14.7017C6.29155 14.6484 6.32983 14.5863 6.35381 14.5192C6.37779 14.4521 6.38698 14.3812 6.38085 14.3108L5.80377 5.27956C5.80069 5.20896 5.78234 5.13964 5.7498 5.07565C5.71726 5.01166 5.67118 4.95429 5.61426 4.90689C5.55734 4.8595 5.49072 4.82303 5.4183 4.79963C5.34589 4.77622 5.26913 4.76635 5.19251 4.77059C5.1159 4.77483 5.04098 4.7931 4.97213 4.82432C4.90328 4.85554 4.84189 4.8991 4.79154 4.95243C4.7412 5.00576 4.70292 5.0678 4.67894 5.13492C4.65497 5.20204 4.64577 5.27288 4.6519 5.34331ZM12.1886 4.78231C12.0359 4.77419 11.8859 4.82222 11.7716 4.91586C11.6574 5.00951 11.5882 5.14109 11.5792 5.28169L11.0022 14.3129C10.9972 14.4514 11.0512 14.5863 11.1527 14.6886C11.2542 14.7909 11.3952 14.8527 11.5454 14.8608C11.6957 14.8688 11.8435 14.8225 11.9572 14.7316C12.0709 14.6408 12.1415 14.5127 12.154 14.3746L12.7311 5.34331C12.7399 5.20271 12.6877 5.06463 12.586 4.95943C12.4843 4.85424 12.3414 4.79053 12.1886 4.78231ZM8.6915 4.78125C8.53845 4.78125 8.39166 4.83722 8.28344 4.93685C8.17522 5.03648 8.11442 5.1716 8.11442 5.3125V14.3438C8.11442 14.4846 8.17522 14.6198 8.28344 14.7194C8.39166 14.819 8.53845 14.875 8.6915 14.875C8.84455 14.875 8.99134 14.819 9.09956 14.7194C9.20779 14.6198 9.26859 14.4846 9.26859 14.3438V5.3125C9.26859 5.1716 9.20779 5.03648 9.09956 4.93685C8.99134 4.83722 8.84455 4.78125 8.6915 4.78125Z"
				fill="currentcolor"
			/>
		</svg>
	),
});

export const LeftArr = createIcon({
	displayName: "LeftArr",
	viewBox: "0 0 8 14",
	path: (
		<svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M7.35489 0.646894C7.40146 0.69334 7.4384 0.748515 7.46361 0.80926C7.48881 0.870005 7.50179 0.935127 7.50179 1.00089C7.50179 1.06666 7.48881 1.13178 7.46361 1.19253C7.4384 1.25327 7.40146 1.30845 7.35489 1.35489L1.70789 7.00089L7.35489 12.6469C7.44878 12.7408 7.50152 12.8681 7.50152 13.0009C7.50152 13.1337 7.44878 13.261 7.35489 13.3549C7.26101 13.4488 7.13367 13.5015 7.00089 13.5015C6.86812 13.5015 6.74078 13.4488 6.64689 13.3549L0.646894 7.35489C0.60033 7.30845 0.563387 7.25327 0.538181 7.19253C0.512974 7.13178 0.5 7.06666 0.5 7.00089C0.5 6.93513 0.512974 6.87001 0.538181 6.80926C0.563387 6.74852 0.60033 6.69334 0.646894 6.64689L6.64689 0.646894C6.69334 0.600331 6.74852 0.563388 6.80926 0.538181C6.87001 0.512975 6.93513 0.5 7.00089 0.5C7.06666 0.5 7.13178 0.512975 7.19253 0.538181C7.25327 0.563388 7.30845 0.600331 7.35489 0.646894Z"
				fill="#3E4C62"
				stroke="#3E4C62"
				strokeWidth="0.5"
			/>
		</svg>
	),
});

export const HoursClockIcon = createIcon({
	displayName: "HoursClockIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M8.00042 0.640625C3.93961 0.640625 0.640625 3.93961 0.640625 8.00042C0.640625 12.0612 3.93961 15.3602 8.00042 15.3602C10.3332 15.3602 12.4109 14.2724 13.7606 12.5764V15.041C13.7589 15.1558 13.8197 15.2639 13.9193 15.3214C14.019 15.3788 14.1423 15.3788 14.242 15.3214C14.3416 15.2639 14.4025 15.1558 14.4008 15.041V11.5207H10.8805C10.7656 11.519 10.6575 11.5798 10.6001 11.6795C10.541 11.7791 10.541 11.9024 10.6001 12.0021C10.6575 12.1018 10.7656 12.1626 10.8805 12.1609H13.2741C12.0443 13.7183 10.1423 14.72 8.00042 14.72C4.2859 14.72 1.28083 11.7149 1.28083 8.00042C1.28083 4.2859 4.2859 1.28083 8.00042 1.28083C11.715 1.28083 14.72 4.2859 14.72 8.00042C14.7183 8.11529 14.7791 8.2234 14.8788 8.28083C14.9785 8.33995 15.1018 8.33995 15.2014 8.28083C15.3011 8.2234 15.3619 8.11529 15.3602 8.00042C15.3602 3.93961 12.0612 0.640625 8.00042 0.640625ZM8.00042 1.92103C4.64231 1.92103 1.92103 4.64231 1.92103 8.00042C1.92103 11.3585 4.64231 14.0798 8.00042 14.0798C9.40414 14.0798 10.693 13.6001 11.7234 12.8011H10.8805C10.3501 12.8011 9.92103 12.3704 9.92103 11.8399C9.92103 11.3095 10.3501 10.8805 10.8805 10.8805H13.3552C13.818 10.0224 14.0798 9.04265 14.0798 8.00042C14.0798 4.64231 11.3585 1.92103 8.00042 1.92103ZM8.00042 2.88049C8.17779 2.88049 8.31968 3.02407 8.31968 3.19975V7.0984C8.59164 7.19637 8.80617 7.40921 8.90245 7.68117H11.5207C11.6964 7.68117 11.84 7.82306 11.84 8.00042C11.84 8.17779 11.6964 8.31968 11.5207 8.31968H8.90245C8.769 8.69299 8.41765 8.95988 8.00042 8.95988C7.47002 8.95988 7.04096 8.53083 7.04096 8.00042C7.04096 7.58319 7.30786 7.23184 7.68117 7.0984V3.19975C7.68117 3.02407 7.82306 2.88049 8.00042 2.88049Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const SandClockIcon = createIcon({
	displayName: "SandClockIcon",
	viewBox: "0 0 16 21",
	path: (
		<svg width="16" height="21" viewBox="0 0 16 21" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0.400391 0.412109V2.06781C0.400391 2.75149 0.93882 3.31067 1.59971 3.31067H2.39997V6.0891C2.39997 7.33415 2.99752 8.50274 4.00048 9.19298L5.67488 10.3441L4.00048 11.4952C2.99752 12.1855 2.39997 13.3541 2.39997 14.5991V17.3775H1.59971C0.93882 17.3775 0.400391 17.9367 0.400391 18.6204V20.2761H15.5989V18.6204C15.5989 17.9367 15.0604 17.3775 14.3995 17.3775H13.5993V14.5991C13.5993 13.3541 13.0017 12.1855 11.9988 11.4952L10.3244 10.3441L11.9988 9.19298C13.0017 8.50274 13.5993 7.33415 13.5993 6.0891V3.31067H14.3995C15.0604 3.31067 15.5989 2.75149 15.5989 2.06781V0.412109H0.400391ZM3.20022 3.31067H12.799V6.0891C12.799 7.05892 12.3303 7.95885 11.549 8.49401L9.37421 9.99462L8.87379 10.3441L9.37421 10.6936L11.549 12.1942C12.3303 12.7315 12.799 13.6293 12.799 14.5991V17.3775H3.20022V14.5991C3.20022 13.6293 3.66897 12.7315 4.45022 12.1942L6.62505 10.6936L7.12548 10.3441L6.62505 9.99462L4.45022 8.49401C3.66897 7.95885 3.20022 7.05892 3.20022 6.0891V3.31067ZM7.99963 12.7359C7.99963 12.7359 4.93586 13.8543 4.17362 16.5519H11.8362C11.0866 13.7997 7.99963 12.7359 7.99963 12.7359Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const HandShakeIcon = createIcon({
	displayName: "HandShakeIcon",
	viewBox: "0 0 20 20",
	path: (
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M13.6867 0.851562L13.4628 1.17462L9.90076 6.3372L6.81166 7.25148C6.40414 7.37394 6.05574 7.6421 5.82559 8.00106L3.26225 11.9622L4.88809 17.4753L7.85051 16.825C7.92441 16.8102 8.00042 16.7827 8.07432 16.7637C8.80068 16.5188 9.43412 16.0796 9.91343 15.4757L10.2872 14.9373L11.9869 16.1261L12.3121 16.3499L12.5507 16.0141L19.7741 5.53906L20.0127 5.21389L19.6748 4.98796L14.0245 1.07538L13.6867 0.851562ZM13.8999 1.9622L18.8999 5.42504L16.0874 9.50021C16.4295 8.34312 15.8826 6.9432 14.6748 6.11339C13.9823 5.6383 13.1778 5.4166 12.4008 5.48839C11.9405 5.53062 11.5266 5.67209 11.174 5.90013L13.8999 1.9622ZM15.5617 4.3503C15.3484 4.34818 15.1415 4.44742 15.0127 4.63746C14.8057 4.94151 14.8818 5.36592 15.1879 5.57496C15.492 5.784 15.9037 5.70376 16.1128 5.3997C16.3197 5.09776 16.2416 4.68602 15.9375 4.47487C15.8235 4.39675 15.6905 4.35241 15.5617 4.3503ZM10.1499 7.38873C10.0338 7.94405 10.1077 8.55638 10.3632 9.12648L10.6757 8.84987C11.0389 8.55427 11.5055 8.40013 11.9742 8.40013C12.5042 8.40013 13.0046 8.5965 13.3868 8.96389C14.0118 9.56144 14.1807 10.463 13.8619 11.2253C14.3898 11.181 14.8628 10.9951 15.2492 10.7016L12.1115 15.2371L10.7496 14.2996L12.9497 11.2126C13.3467 10.708 13.3003 9.98163 12.8378 9.53822C12.3818 9.10114 11.6744 9.07791 11.1867 9.47487L8.92525 11.3625L7.98775 10.8009L10.1499 7.38873ZM2.43666 12.0129L0 12.7625L2.01225 19.5762L4.46157 18.8266L2.43666 12.0129Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const DocumentIcon = createIcon({
	displayName: "DocumentIcon",
	viewBox: "0 0 34 34",
	path: (
		<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M3.32134 0.441406C1.73266 0.441406 0.44043 1.73364 0.44043 3.32232L0.451832 21.0943H0.463233C0.455632 21.1703 0.44043 21.2425 0.44043 21.3223C0.44043 22.911 1.73266 24.1994 3.32134 24.1994H11.9603V22.7628H3.32134C2.527 22.7628 1.88089 22.1129 1.88089 21.3223C1.88089 20.528 2.527 19.8819 3.32134 19.8819H11.9603V12.6796C11.9603 10.3004 13.8986 8.36201 16.2816 8.36201H20.5992V0.441406H3.32134ZM5.48012 4.04065H15.5595C15.9548 4.04065 16.2816 4.3637 16.2816 4.76277V6.19943C16.2816 6.5985 15.9548 6.92156 15.5595 6.92156H5.48012C5.08485 6.92156 4.7618 6.5985 4.7618 6.19943V4.76277C4.7618 4.3637 5.08485 4.04065 5.48012 4.04065ZM16.2816 9.80247C14.6892 9.80247 13.4007 11.0909 13.4007 12.6796L13.4121 30.4515H13.4235C13.4159 30.5276 13.4007 30.6036 13.4007 30.6796C13.4007 32.2683 14.6892 33.5605 16.2816 33.5605H33.5595V32.12H16.2816C15.4873 32.12 14.8412 31.4739 14.8412 30.6796C14.8412 29.889 15.4873 29.2391 16.2816 29.2391H33.5595V9.80247H16.2816ZM18.4404 13.4017H28.5198C28.9151 13.4017 29.2382 13.7248 29.2382 14.12V15.5605C29.2382 15.9558 28.9151 16.2826 28.5198 16.2826H18.4404C18.0452 16.2826 17.7183 15.9558 17.7183 15.5605V14.12C17.7183 13.7248 18.0452 13.4017 18.4404 13.4017Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const InfoIcon = createIcon({
	displayName: "InfoIcon",
	viewBox: "0 0 43 43",
	path: (
		<svg width="43" height="43" viewBox="0 0 43 43" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M21.4664 0.800781C9.97149 0.800781 0.650391 10.0697 0.650391 21.5002C0.650391 32.9307 9.97149 42.1996 21.4664 42.1996C32.9613 42.1996 42.2824 32.9307 42.2824 21.5002C42.2824 10.0697 32.9613 0.800781 21.4664 0.800781ZM21.4664 8.90097C22.9666 8.90097 24.1801 10.1077 24.1801 11.5995C24.1801 13.0912 22.9666 14.2979 21.4664 14.2979C19.9662 14.2979 18.7527 13.0912 18.7527 11.5995C18.7527 10.1077 19.9662 8.90097 21.4664 8.90097ZM25.0878 33.2015H17.845V31.401H19.6557V19.6996H17.845V17.8991H23.2771V31.401H25.0878V33.2015Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const UploadIcon = createIcon({
	displayName: "UploadIcon",
	viewBox: "0 0 18 11",
	path: (
		<svg width="18" height="11" viewBox="0 0 18 11" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M8.91068 8.00089C9.06667 8.00089 9.21626 7.94822 9.32656 7.85445C9.43686 7.76068 9.49882 7.6335 9.49882 7.50089V1.70789L12.0231 3.85489C12.0778 3.90138 12.1427 3.93826 12.2142 3.96342C12.2856 3.98858 12.3622 4.00153 12.4395 4.00153C12.5168 4.00153 12.5934 3.98858 12.6649 3.96342C12.7363 3.93826 12.8012 3.90138 12.8559 3.85489C12.9106 3.80841 12.954 3.75322 12.9836 3.69248C13.0132 3.63174 13.0284 3.56664 13.0284 3.50089C13.0284 3.43515 13.0132 3.37005 12.9836 3.30931C12.954 3.24857 12.9106 3.19338 12.8559 3.14689L9.32708 0.146894C9.27245 0.100331 9.20755 0.0633878 9.1361 0.0381813C9.06464 0.0129749 8.98804 0 8.91068 0C8.83332 0 8.75672 0.0129749 8.68527 0.0381813C8.61382 0.0633878 8.54891 0.100331 8.49428 0.146894L4.96546 3.14689C4.85502 3.24078 4.79298 3.36812 4.79298 3.50089C4.79298 3.63367 4.85502 3.76101 4.96546 3.85489C5.07589 3.94878 5.22568 4.00153 5.38186 4.00153C5.53804 4.00153 5.68782 3.94878 5.79826 3.85489L8.32254 1.70789V7.50089C8.32254 7.6335 8.38451 7.76068 8.49481 7.85445C8.6051 7.94822 8.7547 8.00089 8.91068 8.00089ZM0.676758 10.5009C0.676758 10.3683 0.738722 10.2411 0.849019 10.1473C0.959316 10.0536 1.10891 10.0009 1.2649 10.0009H16.5565C16.7125 10.0009 16.862 10.0536 16.9723 10.1473C17.0826 10.2411 17.1446 10.3683 17.1446 10.5009C17.1446 10.6335 17.0826 10.7607 16.9723 10.8544C16.862 10.9482 16.7125 11.0009 16.5565 11.0009H1.2649C1.10891 11.0009 0.959316 10.9482 0.849019 10.8544C0.738722 10.7607 0.676758 10.6335 0.676758 10.5009Z"
				fill="white"
			/>
		</svg>
	),
});

export const MenuIcon = createIcon({
	displayName: "MenuIcon",
	viewBox: "0 0 14 13",
	path: (
		<svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0.125 11.5C0.125 11.3342 0.190848 11.1753 0.308058 11.0581C0.425269 10.9408 0.58424 10.875 0.75 10.875H13.25C13.4158 10.875 13.5747 10.9408 13.6919 11.0581C13.8092 11.1753 13.875 11.3342 13.875 11.5C13.875 11.6658 13.8092 11.8247 13.6919 11.9419C13.5747 12.0592 13.4158 12.125 13.25 12.125H0.75C0.58424 12.125 0.425269 12.0592 0.308058 11.9419C0.190848 11.8247 0.125 11.6658 0.125 11.5ZM0.125 6.5C0.125 6.33424 0.190848 6.17527 0.308058 6.05806C0.425269 5.94085 0.58424 5.875 0.75 5.875H13.25C13.4158 5.875 13.5747 5.94085 13.6919 6.05806C13.8092 6.17527 13.875 6.33424 13.875 6.5C13.875 6.66576 13.8092 6.82473 13.6919 6.94194C13.5747 7.05915 13.4158 7.125 13.25 7.125H0.75C0.58424 7.125 0.425269 7.05915 0.308058 6.94194C0.190848 6.82473 0.125 6.66576 0.125 6.5ZM0.125 1.5C0.125 1.33424 0.190848 1.17527 0.308058 1.05806C0.425269 0.940848 0.58424 0.875 0.75 0.875H13.25C13.4158 0.875 13.5747 0.940848 13.6919 1.05806C13.8092 1.17527 13.875 1.33424 13.875 1.5C13.875 1.66576 13.8092 1.82473 13.6919 1.94194C13.5747 2.05915 13.4158 2.125 13.25 2.125H0.75C0.58424 2.125 0.425269 2.05915 0.308058 1.94194C0.190848 1.82473 0.125 1.66576 0.125 1.5Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const LockIcon = createIcon({
	displayName: "LockIcon",
	viewBox: "0 0 57 56",
	path: (
		<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="8.5" y="8" width="40" height="40" rx="20" fill="#ECC575" fillOpacity="0.25" />
			<path
				d="M28.5 21C29.0304 21 29.5391 21.2107 29.9142 21.5858C30.2893 21.9609 30.5 22.4696 30.5 23V27H26.5V23C26.5 22.4696 26.7107 21.9609 27.0858 21.5858C27.4609 21.2107 27.9696 21 28.5 21ZM31.5 27V23C31.5 22.2044 31.1839 21.4413 30.6213 20.8787C30.0587 20.3161 29.2956 20 28.5 20C27.7044 20 26.9413 20.3161 26.3787 20.8787C25.8161 21.4413 25.5 22.2044 25.5 23V27C24.9696 27 24.4609 27.2107 24.0858 27.5858C23.7107 27.9609 23.5 28.4696 23.5 29V34C23.5 34.5304 23.7107 35.0391 24.0858 35.4142C24.4609 35.7893 24.9696 36 25.5 36H31.5C32.0304 36 32.5391 35.7893 32.9142 35.4142C33.2893 35.0391 33.5 34.5304 33.5 34V29C33.5 28.4696 33.2893 27.9609 32.9142 27.5858C32.5391 27.2107 32.0304 27 31.5 27Z"
				fill="#B08D44"
			/>
			<rect
				x="4.5"
				y="4"
				width="48"
				height="48"
				rx="24"
				stroke="#FFFAF0"
				strokeOpacity="0.74"
				strokeWidth="8"
			/>
		</svg>
	),
});

export const LogoutIcon = createIcon({
	displayName: "LogoutIcon",
	viewBox: "0 0 12 12",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
			<g clipPath="url(#clip0_691_17240)">
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M4.5 9.375C4.5 9.47446 4.53951 9.56984 4.60984 9.64017C4.68016 9.71049 4.77554 9.75 4.875 9.75H10.875C10.9745 9.75 11.0698 9.71049 11.1402 9.64017C11.2105 9.56984 11.25 9.47446 11.25 9.375V2.625C11.25 2.52554 11.2105 2.43016 11.1402 2.35984C11.0698 2.28951 10.9745 2.25 10.875 2.25H4.875C4.77554 2.25 4.68016 2.28951 4.60984 2.35984C4.53951 2.43016 4.5 2.52554 4.5 2.625V4.125C4.5 4.22446 4.46049 4.31984 4.39016 4.39016C4.31984 4.46049 4.22446 4.5 4.125 4.5C4.02554 4.5 3.93016 4.46049 3.85984 4.39016C3.78951 4.31984 3.75 4.22446 3.75 4.125V2.625C3.75 2.32663 3.86853 2.04048 4.0795 1.8295C4.29048 1.61853 4.57663 1.5 4.875 1.5H10.875C11.1734 1.5 11.4595 1.61853 11.6705 1.8295C11.8815 2.04048 12 2.32663 12 2.625V9.375C12 9.67337 11.8815 9.95952 11.6705 10.1705C11.4595 10.3815 11.1734 10.5 10.875 10.5H4.875C4.57663 10.5 4.29048 10.3815 4.0795 10.1705C3.86853 9.95952 3.75 9.67337 3.75 9.375V7.875C3.75 7.77554 3.78951 7.68016 3.85984 7.60983C3.93016 7.53951 4.02554 7.5 4.125 7.5C4.22446 7.5 4.31984 7.53951 4.39016 7.60983C4.46049 7.68016 4.5 7.77554 4.5 7.875V9.375Z"
					fill="#3E4C62"
				/>
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M0.109194 6.26549C0.0742713 6.23065 0.0465642 6.18927 0.0276594 6.14371C0.00875454 6.09815 -0.000976563 6.04931 -0.000976562 5.99999C-0.000976563 5.95066 0.00875454 5.90182 0.0276594 5.85626C0.0465642 5.8107 0.0742713 5.76932 0.109194 5.73449L2.35919 3.48449C2.39406 3.44962 2.43545 3.42196 2.48101 3.40309C2.52656 3.38422 2.57539 3.37451 2.62469 3.37451C2.674 3.37451 2.72283 3.38422 2.76838 3.40309C2.81394 3.42196 2.85533 3.44962 2.89019 3.48449C2.92506 3.51935 2.95272 3.56074 2.97159 3.6063C2.99046 3.65185 3.00017 3.70068 3.00017 3.74999C3.00017 3.79929 2.99046 3.84812 2.97159 3.89367C2.95272 3.93923 2.92506 3.98062 2.89019 4.01549L1.27994 5.62499H7.87469C7.97415 5.62499 8.06953 5.66449 8.13986 5.73482C8.21019 5.80515 8.24969 5.90053 8.24969 5.99999C8.24969 6.09944 8.21019 6.19482 8.13986 6.26515C8.06953 6.33548 7.97415 6.37499 7.87469 6.37499H1.27994L2.89019 7.98449C2.92506 8.01935 2.95272 8.06074 2.97159 8.1063C2.99046 8.15185 3.00017 8.20068 3.00017 8.24999C3.00017 8.29929 2.99046 8.34812 2.97159 8.39367C2.95272 8.43923 2.92506 8.48062 2.89019 8.51549C2.85533 8.55035 2.81394 8.57801 2.76838 8.59688C2.72283 8.61575 2.674 8.62546 2.62469 8.62546C2.57539 8.62546 2.52656 8.61575 2.48101 8.59688C2.43545 8.57801 2.39406 8.55035 2.35919 8.51549L0.109194 6.26549Z"
					fill="#3E4C62"
				/>
			</g>
			<defs>
				<clipPath id="clip0_691_17240">
					<rect width="12" height="12" fill="white" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const CheckIcon = createIcon({
	displayName: "LogoutIcon",
	viewBox: "0 0 9 6",
	path: (
		<svg width="9" height="6" viewBox="0 0 9 6" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M8.40336 0.220242C8.47317 0.289879 8.52856 0.372606 8.56636 0.463683C8.60415 0.55476 8.6236 0.652398 8.6236 0.751005C8.6236 0.849612 8.60415 0.94725 8.56636 1.03833C8.52856 1.1294 8.47317 1.21213 8.40336 1.28177L3.90537 5.77976C3.83573 5.84957 3.75301 5.90496 3.66193 5.94275C3.57085 5.98055 3.47321 6 3.37461 6C3.276 6 3.17836 5.98055 3.08729 5.94275C2.99621 5.90496 2.91348 5.84957 2.84384 5.77976L0.594849 3.53076C0.525149 3.46106 0.469859 3.37832 0.432137 3.28725C0.394415 3.19618 0.375 3.09857 0.375 3C0.375 2.90143 0.394415 2.80382 0.432137 2.71275C0.469859 2.62168 0.525149 2.53894 0.594849 2.46924C0.66455 2.39954 0.747297 2.34425 0.838365 2.30652C0.929434 2.2688 1.02704 2.24939 1.12561 2.24939C1.22418 2.24939 1.32179 2.2688 1.41286 2.30652C1.50393 2.34425 1.58667 2.39954 1.65638 2.46924L3.37461 4.18897L7.34183 0.220242C7.41147 0.150428 7.4942 0.0950385 7.58528 0.0572457C7.67635 0.0194529 7.77399 0 7.8726 0C7.9712 0 8.06884 0.0194529 8.15992 0.0572457C8.251 0.0950385 8.33372 0.150428 8.40336 0.220242Z"
				fill="white"
			/>
		</svg>
	),
});

export const CompletedSuccessfully = createIcon({
	displayName: "CompletedSuccessfully",
	viewBox: "0 0 80 80",
	path: (
		<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="8" y="8" width="64" height="64" rx="28" fill="#E5F3EC" />
			<g clipPath="url(#clip0_691_17322)">
				<path
					d="M60 40C60 45.3043 57.8929 50.3914 54.1421 54.1421C50.3914 57.8929 45.3043 60 40 60C34.6957 60 29.6086 57.8929 25.8579 54.1421C22.1071 50.3914 20 45.3043 20 40C20 34.6957 22.1071 29.6086 25.8579 25.8579C29.6086 22.1071 34.6957 20 40 20C45.3043 20 50.3914 22.1071 54.1421 25.8579C57.8929 29.6086 60 34.6957 60 40ZM50.075 32.425C49.8964 32.247 49.6838 32.1069 49.4498 32.013C49.2159 31.9191 48.9654 31.8734 48.7133 31.8785C48.4613 31.8837 48.2128 31.9396 47.9829 32.0429C47.7529 32.1463 47.5462 32.2949 47.375 32.48L38.6925 43.5425L33.46 38.3075C33.1046 37.9763 32.6344 37.796 32.1487 37.8046C31.6629 37.8131 31.1995 38.0099 30.8559 38.3534C30.5124 38.697 30.3156 39.1604 30.3071 39.6462C30.2985 40.1319 30.4788 40.6021 30.81 40.9575L37.425 47.575C37.6032 47.7529 37.8154 47.893 38.049 47.9871C38.2825 48.0812 38.5326 48.1273 38.7844 48.1226C39.0361 48.118 39.2843 48.0627 39.5142 47.96C39.7441 47.8573 39.951 47.7094 40.1225 47.525L50.1025 35.05C50.4427 34.6962 50.6307 34.2232 50.626 33.7324C50.6213 33.2416 50.4244 32.7722 50.0775 32.425H50.075Z"
					fill="#1A804C"
				/>
			</g>
			<rect
				x="4"
				y="4"
				width="72"
				height="72"
				rx="32"
				stroke="#F1F9F5"
				strokeOpacity="0.74"
				strokeWidth="8"
			/>
			<defs>
				<clipPath id="clip0_691_17322">
					<rect width="40" height="40" fill="white" transform="translate(20 20)" />
				</clipPath>
			</defs>
		</svg>
	),
});
export const NotQualifiedIcon = createIcon({
	displayName: "NotQualifiedIcon",
	viewBox: "0 0 80 80",
	path: (
		<svg width="73" height="72" viewBox="0 0 73 72" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="4.5" y="4" width="64" height="64" rx="32" fill="#FEE4E2" />
			<g clip-path="url(#clip0_2313_53566)">
				<path
					d="M56.5 36C56.5 41.3043 54.3929 46.3914 50.6421 50.1421C46.8914 53.8929 41.8043 56 36.5 56C31.1957 56 26.1086 53.8929 22.3579 50.1421C18.6071 46.3914 16.5 41.3043 16.5 36C16.5 30.6957 18.6071 25.6086 22.3579 21.8579C26.1086 18.1071 31.1957 16 36.5 16C41.8043 16 46.8914 18.1071 50.6421 21.8579C54.3929 25.6086 56.5 30.6957 56.5 36ZM29.885 27.615C29.6503 27.3803 29.3319 27.2484 29 27.2484C28.6681 27.2484 28.3497 27.3803 28.115 27.615C27.8803 27.8497 27.7484 28.1681 27.7484 28.5C27.7484 28.8319 27.8803 29.1503 28.115 29.385L34.7325 36L28.115 42.615C27.9988 42.7312 27.9066 42.8692 27.8437 43.021C27.7808 43.1729 27.7484 43.3356 27.7484 43.5C27.7484 43.6644 27.7808 43.8271 27.8437 43.979C27.9066 44.1308 27.9988 44.2688 28.115 44.385C28.3497 44.6197 28.6681 44.7516 29 44.7516C29.1644 44.7516 29.3271 44.7192 29.479 44.6563C29.6308 44.5934 29.7688 44.5012 29.885 44.385L36.5 37.7675L43.115 44.385C43.2312 44.5012 43.3692 44.5934 43.521 44.6563C43.6729 44.7192 43.8356 44.7516 44 44.7516C44.1644 44.7516 44.3271 44.7192 44.479 44.6563C44.6308 44.5934 44.7688 44.5012 44.885 44.385C45.0012 44.2688 45.0934 44.1308 45.1563 43.979C45.2192 43.8271 45.2516 43.6644 45.2516 43.5C45.2516 43.3356 45.2192 43.1729 45.1563 43.021C45.0934 42.8692 45.0012 42.7312 44.885 42.615L38.2675 36L44.885 29.385C45.0012 29.2688 45.0934 29.1308 45.1563 28.979C45.2192 28.8271 45.2516 28.6644 45.2516 28.5C45.2516 28.3356 45.2192 28.1729 45.1563 28.021C45.0934 27.8692 45.0012 27.7312 44.885 27.615C44.7688 27.4988 44.6308 27.4066 44.479 27.3437C44.3271 27.2808 44.1644 27.2484 44 27.2484C43.8356 27.2484 43.6729 27.2808 43.521 27.3437C43.3692 27.4066 43.2312 27.4988 43.115 27.615L36.5 34.2325L29.885 27.615Z"
					fill="#D83731"
				/>
			</g>
			<rect x="4.5" y="4" width="64" height="64" rx="32" stroke="#FEF3F2" stroke-width="8" />
			<defs>
				<clipPath id="clip0_2313_53566">
					<rect width="40" height="40" fill="white" transform="translate(16.5 16)" />
				</clipPath>
			</defs>
		</svg>
	),
});
export const UpdateProfilePictureIcon = createIcon({
	displayName: "UpdateProfilePictureIcon",
	viewBox: "0 0 15 14",
	path: (
		<svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g clipPath="url(#clip0_862_13972)">
				<path
					d="M4 13.9992C4 13.9992 3.125 13.9992 3.125 13.1242C3.125 12.2492 4 9.62422 7.5 9.62422C11 9.62422 11.875 12.2492 11.875 13.1242C11.875 13.9992 11 13.9992 11 13.9992H4ZM7.5 8.79297C8.08016 8.79297 8.63656 8.5625 9.0468 8.15226C9.45703 7.74203 9.6875 7.18563 9.6875 6.60547C9.6875 6.02531 9.45703 5.46891 9.0468 5.05867C8.63656 4.64844 8.08016 4.41797 7.5 4.41797C6.91984 4.41797 6.36344 4.64844 5.9532 5.05867C5.54297 5.46891 5.3125 6.02531 5.3125 6.60547C5.3125 7.18563 5.54297 7.74203 5.9532 8.15226C6.36344 8.5625 6.91984 8.79297 7.5 8.79297Z"
					fill="#B08D44"
				/>
				<path
					d="M2.25 0.875C1.78587 0.875 1.34075 1.05937 1.01256 1.38756C0.684374 1.71575 0.5 2.16087 0.5 2.625L0.5 10.9375C0.5 11.2856 0.638281 11.6194 0.884422 11.8656C1.13056 12.1117 1.4644 12.25 1.8125 12.25H2.38388C2.56683 11.607 2.88482 11.0104 3.31663 10.5H1.375V2.625C1.375 2.39294 1.46719 2.17038 1.63128 2.00628C1.79538 1.84219 2.01794 1.75 2.25 1.75H12.75C12.9821 1.75 13.2046 1.84219 13.3687 2.00628C13.5328 2.17038 13.625 2.39294 13.625 2.625V10.5H11.6834C12.1681 11.0723 12.4621 11.7014 12.6161 12.25H13.1875C13.5356 12.25 13.8694 12.1117 14.1156 11.8656C14.3617 11.6194 14.5 11.2856 14.5 10.9375V2.625C14.5 2.16087 14.3156 1.71575 13.9874 1.38756C13.6592 1.05937 13.2141 0.875 12.75 0.875H2.25Z"
					fill="#B08D44"
				/>
			</g>
			<defs>
				<clipPath id="clip0_862_13972">
					<rect width="14" height="14" fill="white" transform="translate(0.5)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ProfilePictureIcon = createIcon({
	displayName: "ProfilePictureIcon",
	viewBox: "0 0 42 42",
	path: (
		<svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M3.5 42C3.5 42 0 42 0 38.5C0 35 3.5 24.5 21 24.5C38.5 24.5 42 35 42 38.5C42 42 38.5 42 38.5 42H3.5ZM21 21C23.7848 21 26.4555 19.8938 28.4246 17.9246C30.3938 15.9555 31.5 13.2848 31.5 10.5C31.5 7.71523 30.3938 5.04451 28.4246 3.07538C26.4555 1.10625 23.7848 0 21 0C18.2152 0 15.5445 1.10625 13.5754 3.07538C11.6062 5.04451 10.5 7.71523 10.5 10.5C10.5 13.2848 11.6062 15.9555 13.5754 17.9246C15.5445 19.8938 18.2152 21 21 21Z"
				fill="#A2A9B0"
			/>
		</svg>
	),
});

export const EmailIcon = createIcon({
	displayName: "EmailIcon",
	viewBox: "0 0 16 12",
	path: (
		<svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0.05 1.555C0.150818 1.11324 0.398655 0.718813 0.752922 0.436308C1.10719 0.153804 1.54688 -3.04944e-05 2 4.53414e-09H14C14.4531 -3.04944e-05 14.8928 0.153804 15.2471 0.436308C15.6013 0.718813 15.8492 1.11324 15.95 1.555L8 6.414L0.05 1.555ZM0 2.697V9.801L5.803 6.243L0 2.697ZM6.761 6.83L0.191 10.857C0.353327 11.1993 0.609527 11.4884 0.929782 11.6908C1.25004 11.8931 1.62117 12.0004 2 12H14C14.3788 12.0001 14.7498 11.8926 15.0698 11.6901C15.3899 11.4876 15.6459 11.1983 15.808 10.856L9.238 6.829L8 7.586L6.761 6.829V6.83ZM10.197 6.244L16 9.801V2.697L10.197 6.243V6.244Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const BarChartIcon = createIcon({
	displayName: "BarChartIcon",
	viewBox: "0 0 25 24",
	path: (
		<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g clipPath="url(#clip0_691_16896)">
				<path
					d="M17 3C17 2.60218 17.158 2.22064 17.4393 1.93934C17.7206 1.65804 18.1022 1.5 18.5 1.5H21.5C21.8978 1.5 22.2794 1.65804 22.5607 1.93934C22.842 2.22064 23 2.60218 23 3V21H23.75C23.9489 21 24.1397 21.079 24.2803 21.2197C24.421 21.3603 24.5 21.5511 24.5 21.75C24.5 21.9489 24.421 22.1397 24.2803 22.2803C24.1397 22.421 23.9489 22.5 23.75 22.5H1.25C1.05109 22.5 0.860322 22.421 0.71967 22.2803C0.579018 22.1397 0.5 21.9489 0.5 21.75C0.5 21.5511 0.579018 21.3603 0.71967 21.2197C0.860322 21.079 1.05109 21 1.25 21H2V16.5C2 16.1022 2.15804 15.7206 2.43934 15.4393C2.72064 15.158 3.10218 15 3.5 15H6.5C6.89782 15 7.27936 15.158 7.56066 15.4393C7.84196 15.7206 8 16.1022 8 16.5V21H9.5V10.5C9.5 10.1022 9.65804 9.72064 9.93934 9.43934C10.2206 9.15804 10.6022 9 11 9H14C14.3978 9 14.7794 9.15804 15.0607 9.43934C15.342 9.72064 15.5 10.1022 15.5 10.5V21H17V3Z"
					fill="#3E4C62"
				/>
			</g>
			<defs>
				<clipPath id="clip0_691_16896">
					<rect width="24" height="24" fill="white" transform="translate(0.5)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const UaeFlagIcon = createIcon({
	displayName: "UaeFlagIcon",
	viewBox: "0 0 17 16",
	path: (
		<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g clipPath="url(#clip0_691_20724)">
				<g clipPath="url(#clip1_691_20724)">
					<path d="M-2.40918 0H19.409V5.45455H-2.40918V0Z" fill="#00732F" />
					<path d="M-2.40918 5.45312H19.409V10.9077H-2.40918V5.45312Z" fill="white" />
					<path d="M-2.40918 10.9102H19.409V16.3647H-2.40918V10.9102Z" fill="black" />
					<path d="M-2.40918 0H5.09082V16.3636H-2.40918V0Z" fill="#FF0000" />
				</g>
			</g>
			<defs>
				<clipPath id="clip0_691_20724">
					<rect x="0.5" width="16" height="16" rx="8" fill="white" />
				</clipPath>
				<clipPath id="clip1_691_20724">
					<rect width="21.8182" height="16.3636" fill="white" transform="translate(-2.40918)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const TableSortIcon = createIcon({
	displayName: "TableSortIcon",
	viewBox: "0 0 6 8",
	path: (
		<svg width="6" height="8" viewBox="0 0 6 8" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0.111053 2.59293L2.24704 0.151862V0.152307C2.28884 0.10447 2.3404 0.06613 2.39824 0.0398605C2.45608 0.0135909 2.51888 0 2.5824 0C2.64593 0 2.70873 0.0135909 2.76657 0.0398605C2.82441 0.06613 2.87596 0.10447 2.91777 0.152307L5.05375 2.59293C5.11018 2.65732 5.14684 2.73662 5.15936 2.82132C5.17187 2.90602 5.15971 2.99253 5.12432 3.0705C5.08894 3.14846 5.03183 3.21457 4.95983 3.26092C4.88784 3.30726 4.80401 3.33187 4.71839 3.33179H0.446416C0.0638443 3.33179 -0.141025 2.88108 0.111053 2.59293ZM0.111053 5.40685L2.24704 7.84836V7.84792C2.28884 7.89575 2.3404 7.93409 2.39824 7.96036C2.45608 7.98663 2.51888 8.00022 2.5824 8.00022C2.64593 8.00022 2.70872 7.98663 2.76657 7.96036C2.82441 7.93409 2.87596 7.89575 2.91777 7.84792L5.05375 5.4073C5.11018 5.3429 5.14684 5.2636 5.15936 5.1789C5.17187 5.0942 5.15971 5.00769 5.12432 4.92972C5.08894 4.85176 5.03183 4.78565 4.95983 4.73931C4.88784 4.69296 4.80401 4.66836 4.71839 4.66843H0.446416C0.0638439 4.66843 -0.141026 5.11959 0.111053 5.40685Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const DetailsIcon = createIcon({
	displayName: "DetailsIcon",
	viewBox: "0 0 56 56",
	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="8" y="8" width="40" height="40" rx="20" fill="#ECC575" fillOpacity="0.25" />
			<g clipPath="url(#clip0_691_20213)">
				<path
					d="M32 20H24C23.4696 20 22.9609 20.2107 22.5858 20.5858C22.2107 20.9609 22 21.4696 22 22V34C22 34.5304 22.2107 35.0391 22.5858 35.4142C22.9609 35.7893 23.4696 36 24 36H32C32.5304 36 33.0391 35.7893 33.4142 35.4142C33.7893 35.0391 34 34.5304 34 34V22C34 21.4696 33.7893 20.9609 33.4142 20.5858C33.0391 20.2107 32.5304 20 32 20V20ZM25 24H31C31.1326 24 31.2598 24.0527 31.3536 24.1464C31.4473 24.2402 31.5 24.3674 31.5 24.5C31.5 24.6326 31.4473 24.7598 31.3536 24.8536C31.2598 24.9473 31.1326 25 31 25H25C24.8674 25 24.7402 24.9473 24.6464 24.8536C24.5527 24.7598 24.5 24.6326 24.5 24.5C24.5 24.3674 24.5527 24.2402 24.6464 24.1464C24.7402 24.0527 24.8674 24 25 24ZM24.5 26.5C24.5 26.3674 24.5527 26.2402 24.6464 26.1464C24.7402 26.0527 24.8674 26 25 26H31C31.1326 26 31.2598 26.0527 31.3536 26.1464C31.4473 26.2402 31.5 26.3674 31.5 26.5C31.5 26.6326 31.4473 26.7598 31.3536 26.8536C31.2598 26.9473 31.1326 27 31 27H25C24.8674 27 24.7402 26.9473 24.6464 26.8536C24.5527 26.7598 24.5 26.6326 24.5 26.5ZM25 28H31C31.1326 28 31.2598 28.0527 31.3536 28.1464C31.4473 28.2402 31.5 28.3674 31.5 28.5C31.5 28.6326 31.4473 28.7598 31.3536 28.8536C31.2598 28.9473 31.1326 29 31 29H25C24.8674 29 24.7402 28.9473 24.6464 28.8536C24.5527 28.7598 24.5 28.6326 24.5 28.5C24.5 28.3674 24.5527 28.2402 24.6464 28.1464C24.7402 28.0527 24.8674 28 25 28ZM25 30H28C28.1326 30 28.2598 30.0527 28.3536 30.1464C28.4473 30.2402 28.5 30.3674 28.5 30.5C28.5 30.6326 28.4473 30.7598 28.3536 30.8536C28.2598 30.9473 28.1326 31 28 31H25C24.8674 31 24.7402 30.9473 24.6464 30.8536C24.5527 30.7598 24.5 30.6326 24.5 30.5C24.5 30.3674 24.5527 30.2402 24.6464 30.1464C24.7402 30.0527 24.8674 30 25 30Z"
					fill="#B08D44"
				/>
			</g>
			<rect
				x="4"
				y="4"
				width="48"
				height="48"
				rx="24"
				stroke="#FFFAF0"
				strokeOpacity="0.74"
				strokeWidth="8"
			/>
			<defs>
				<clipPath id="clip0_691_20213">
					<rect width="16" height="16" fill="white" transform="translate(20 20)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const CloseIcon = createIcon({
	displayName: "CloseIcon",
	viewBox: "0 0 18 18",
	path: (
		<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M9.66988 8.99999L12.3494 11.6796L11.6796 12.3494L8.99999 9.66988L6.32043 12.3494L5.65053 11.6796L8.3301 8.99999L5.65053 6.32043L6.32043 5.65053L8.99999 8.3301L11.6796 5.65053L12.3494 6.32043L9.66988 8.99999Z"
				fill="black"
			/>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0 9C0 4.02944 4.02944 0 9 0C13.9706 0 18 4.02944 18 9C18 13.9706 13.9706 18 9 18C4.02944 18 0 13.9706 0 9ZM9 0.947368C4.55265 0.947368 0.947368 4.55265 0.947368 9C0.947368 13.4473 4.55265 17.0526 9 17.0526C13.4473 17.0526 17.0526 13.4473 17.0526 9C17.0526 4.55265 13.4473 0.947368 9 0.947368Z"
				fill="black"
			/>
		</svg>
	),
});

export const CloseIconDynamic = createIcon({
	displayName: "CloseIconDynamic",
	viewBox: "0 0 18 18",
	path: (
		<svg
			width="18"
			height="18"
			viewBox="0 0 18 18"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M9.66988 8.99999L12.3494 11.6796L11.6796 12.3494L8.99999 9.66988L6.32043 12.3494L5.65053 11.6796L8.3301 8.99999L5.65053 6.32043L6.32043 5.65053L8.99999 8.3301L11.6796 5.65053L12.3494 6.32043L9.66988 8.99999Z"
				fill="currentColor"
			/>
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M0 9C0 4.02944 4.02944 0 9 0C13.9706 0 18 4.02944 18 9C18 13.9706 13.9706 18 9 18C4.02944 18 0 13.9706 0 9ZM9 0.947368C4.55265 0.947368 0.947368 4.55265 0.947368 9C0.947368 13.4473 4.55265 17.0526 9 17.0526C13.4473 17.0526 17.0526 13.4473 17.0526 9C17.0526 4.55265 13.4473 0.947368 9 0.947368Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const DownloadIcon = createIcon({
	displayName: "DownloadIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0.5 9.90039C0.632608 9.90039 0.759785 9.95307 0.853553 10.0468C0.947322 10.1406 1 10.2678 1 10.4004V12.9004C1 13.1656 1.10536 13.42 1.29289 13.6075C1.48043 13.795 1.73478 13.9004 2 13.9004H14C14.2652 13.9004 14.5196 13.795 14.7071 13.6075C14.8946 13.42 15 13.1656 15 12.9004V10.4004C15 10.2678 15.0527 10.1406 15.1464 10.0468C15.2402 9.95307 15.3674 9.90039 15.5 9.90039C15.6326 9.90039 15.7598 9.95307 15.8536 10.0468C15.9473 10.1406 16 10.2678 16 10.4004V12.9004C16 13.4308 15.7893 13.9395 15.4142 14.3146C15.0391 14.6897 14.5304 14.9004 14 14.9004H2C1.46957 14.9004 0.960859 14.6897 0.585786 14.3146C0.210714 13.9395 0 13.4308 0 12.9004V10.4004C0 10.2678 0.0526784 10.1406 0.146447 10.0468C0.240215 9.95307 0.367392 9.90039 0.5 9.90039Z"
				fill="#B08D44"
			/>
			<path
				d="M7.64566 11.854C7.6921 11.9006 7.74728 11.9375 7.80802 11.9627C7.86877 11.9879 7.93389 12.0009 7.99966 12.0009C8.06542 12.0009 8.13054 11.9879 8.19129 11.9627C8.25203 11.9375 8.30721 11.9006 8.35366 11.854L11.3537 8.854C11.4475 8.76011 11.5003 8.63278 11.5003 8.5C11.5003 8.36722 11.4475 8.23989 11.3537 8.146C11.2598 8.05211 11.1324 7.99937 10.9997 7.99937C10.8669 7.99937 10.7395 8.05211 10.6457 8.146L8.49966 10.293V1.5C8.49966 1.36739 8.44698 1.24021 8.35321 1.14645C8.25944 1.05268 8.13226 1 7.99966 1C7.86705 1 7.73987 1.05268 7.6461 1.14645C7.55233 1.24021 7.49966 1.36739 7.49966 1.5V10.293L5.35366 8.146C5.25977 8.05211 5.13243 7.99937 4.99966 7.99937C4.86688 7.99937 4.73954 8.05211 4.64566 8.146C4.55177 8.23989 4.49902 8.36722 4.49902 8.5C4.49902 8.63278 4.55177 8.76011 4.64566 8.854L7.64566 11.854Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const QuestionsIcon = createIcon({
	displayName: "QuestionsIcon",
	viewBox: "0 0 56 56",

	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="8" y="8" width="40" height="40" rx="20" fill="#ECC575" fillOpacity="0.25" />
			<g clipPath="url(#clip0_1099_25469)">
				<path
					d="M20 22C20 21.4696 20.2107 20.9609 20.5858 20.5858C20.9609 20.2107 21.4696 20 22 20H34C34.5304 20 35.0391 20.2107 35.4142 20.5858C35.7893 20.9609 36 21.4696 36 22V30C36 30.5304 35.7893 31.0391 35.4142 31.4142C35.0391 31.7893 34.5304 32 34 32H31.5C31.3448 32 31.1916 32.0361 31.0528 32.1056C30.9139 32.175 30.7931 32.2758 30.7 32.4L28.8 34.933C28.7069 35.0572 28.5861 35.158 28.4472 35.2274C28.3084 35.2969 28.1552 35.333 28 35.333C27.8448 35.333 27.6916 35.2969 27.5528 35.2274C27.4139 35.158 27.2931 35.0572 27.2 34.933L25.3 32.4C25.2069 32.2758 25.0861 32.175 24.9472 32.1056C24.8084 32.0361 24.6552 32 24.5 32H22C21.4696 32 20.9609 31.7893 20.5858 31.4142C20.2107 31.0391 20 30.5304 20 30V22ZM27.194 24.766C27.1281 24.6676 27.052 24.5765 26.967 24.494C26.8334 24.3564 26.674 24.2463 26.498 24.17L26.49 24.166C26.2532 24.056 25.9951 23.9994 25.734 24C24.776 24 24 24.746 24 25.667C24 26.587 24.776 27.333 25.734 27.333C26.077 27.333 26.396 27.238 26.665 27.073C26.528 27.462 26.275 27.877 25.855 28.293C25.8154 28.3316 25.7842 28.3779 25.7632 28.4291C25.7422 28.4802 25.7319 28.5351 25.733 28.5904C25.734 28.6457 25.7463 28.7001 25.7692 28.7505C25.7921 28.8008 25.825 28.8459 25.866 28.883C26.039 29.043 26.313 29.038 26.48 28.873C27.814 27.544 27.85 26.115 27.421 25.167C27.358 25.0269 27.282 24.893 27.194 24.767V24.766ZM31 27.073C30.864 27.462 30.61 27.877 30.19 28.293C30.1505 28.3317 30.1193 28.378 30.0984 28.4292C30.0775 28.4804 30.0673 28.5353 30.0684 28.5906C30.0696 28.6459 30.082 28.7003 30.105 28.7506C30.1279 28.8009 30.161 28.846 30.202 28.883C30.374 29.043 30.648 29.038 30.815 28.873C32.149 27.544 32.185 26.115 31.757 25.167C31.6936 25.0268 31.6173 24.8929 31.529 24.767C31.4632 24.6683 31.3871 24.5768 31.302 24.494C31.1684 24.3564 31.009 24.2463 30.833 24.17L30.825 24.166C30.5885 24.0562 30.3308 23.9995 30.07 24C29.113 24 28.336 24.746 28.336 25.667C28.336 26.587 29.113 27.333 30.07 27.333C30.413 27.333 30.732 27.238 31.001 27.073H31Z"
					fill="#B08D44"
				/>
			</g>
			<rect
				x="4"
				y="4"
				width="48"
				height="48"
				rx="24"
				stroke="#FFFAF0"
				strokeOpacity="0.74"
				strokeWidth="8"
			/>
			<defs>
				<clipPath id="clip0_1099_25469">
					<rect width="16" height="16" fill="white" transform="translate(20 20)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const PhoneIcon = createIcon({
	displayName: "PhoneIcon",
	viewBox: "0 0 24 24",

	path: (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				fillRule="evenodd"
				clipRule="evenodd"
				d="M7.41406 6.38265C7.54529 6.25161 7.70286 6.14995 7.87634 6.08442C8.04982 6.01889 8.23525 5.99099 8.42033 6.00255C8.60541 6.0141 8.78593 6.06487 8.94991 6.15148C9.11389 6.23808 9.25759 6.35855 9.3715 6.5049L10.7177 8.23448C10.9644 8.55174 11.0514 8.96501 10.9539 9.35503L10.5437 10.9976C10.5225 11.0827 10.5236 11.1718 10.547 11.2563C10.5704 11.3408 10.6153 11.4178 10.6772 11.4799L12.5199 13.3227C12.582 13.3848 12.6592 13.4297 12.7438 13.4531C12.8284 13.4765 12.9177 13.4776 13.0029 13.4562L14.6446 13.046C14.837 12.9978 15.0379 12.9941 15.232 13.035C15.4261 13.076 15.6084 13.1605 15.765 13.2822L17.4945 14.6278C18.1162 15.1115 18.1732 16.0303 17.6167 16.5861L16.8413 17.3616C16.2863 17.9167 15.4568 18.1604 14.6836 17.8882C12.7045 17.1918 10.9077 16.0587 9.42625 14.573C7.94076 13.0917 6.80778 11.295 6.11135 9.31603C5.83986 8.54349 6.0836 7.71321 6.63858 7.15818L7.41406 6.38265Z"
				fill="#B08D44"
			/>
			<path
				d="M24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12Z"
				fill="#ECC575"
				fillOpacity="0.25"
			/>
		</svg>
	),
});

export const SendEmailIcon = createIcon({
	displayName: "SendEmailIcon",
	viewBox: "0 0 15 14",

	path: (
		<svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M14.4682 0.60008C14.5 0.520574 14.5078 0.433477 14.4906 0.349585C14.4734 0.265693 14.4319 0.188697 14.3714 0.128141C14.3108 0.0675853 14.2338 0.0261335 14.1499 0.00892444C14.066 -0.00828458 13.9789 -0.00049402 13.8994 0.0313303L1.1708 5.12296H1.16993L0.774429 5.28046C0.699521 5.31034 0.634329 5.36034 0.586056 5.42494C0.537782 5.48955 0.508305 5.56624 0.500881 5.64655C0.493457 5.72685 0.508375 5.80765 0.543987 5.88001C0.579599 5.95237 0.634518 6.01347 0.702679 6.05658L1.06143 6.28408L1.0623 6.28583L5.43293 9.06658L8.21368 13.4372L8.21543 13.439L8.44293 13.7977C8.48617 13.8656 8.54732 13.9203 8.61962 13.9556C8.69193 13.991 8.7726 14.0058 8.85275 13.9983C8.93289 13.9908 9.00942 13.9613 9.07389 13.9131C9.13837 13.8649 9.1883 13.7998 9.21818 13.7251L14.4682 0.60008V0.60008ZM12.8643 2.25383L6.30705 8.81108L6.11893 8.51533C6.08446 8.46106 6.03845 8.41505 5.98418 8.38058L5.68843 8.19246L12.2457 1.63521L13.2764 1.22308L12.8652 2.25383H12.8643Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const ExlamationMark = createIcon({
	displayName: "ExlamationMark",
	viewBox: "0 0 30 30",

	path: (
		<svg
			width="30"
			height="30"
			viewBox="0 0 30 30"
			fill="currentColor"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M15 0C6.71678 0 0 6.71678 0 15C0 23.2832 6.71678 30 15 30C23.2832 30 30 23.2832 30 15C30 6.71678 23.2832 0 15 0ZM15 5.86986C16.081 5.86986 16.9555 6.74432 16.9555 7.82534C16.9555 8.90636 16.081 9.78081 15 9.78081C13.919 9.78081 13.0445 8.90636 13.0445 7.82534C13.0445 6.74432 13.919 5.86986 15 5.86986ZM17.6096 23.4795H12.3904V22.1747H13.6952V13.6952H12.3904V12.3904H16.3048V22.1747H17.6096V23.4795Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const EmptyStar = createIcon({
	displayName: "EmptyStar",
	viewBox: "0 0 32 29",
	path: (
		<svg width="32" height="29" viewBox="0 0 32 29" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M16 2.14298L20.0059 9.73562L20.2354 10.1705L20.7199 10.2543L29.1788 11.7179L23.1957 17.8741L22.853 18.2267L22.923 18.7134L24.145 27.2106L16.4412 23.4226L16 23.2057L15.5588 23.4226L7.85505 27.2106L9.07703 18.7134L9.14702 18.2267L8.80432 17.8741L2.82119 11.7179L11.2801 10.2543L11.7646 10.1705L11.9941 9.73562L16 2.14298Z"
				stroke="#996516"
				strokeWidth="2"
			/>
		</svg>
	),
});

export const FilledStar = createIcon({
	displayName: "FilledStar",
	viewBox: "0 0 32 29",
	path: (
		<svg width="32" height="29" viewBox="0 0 32 29" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M16 0L20.2321 10.1751L31.2169 11.0557L22.8476 18.2249L25.4046 28.9443L16 23.2L6.59544 28.9443L9.15239 18.2249L0.783095 11.0557L11.7679 10.1751L16 0Z"
				fill="#996516"
			/>
		</svg>
	),
});

export const SearchIcon = createIcon({
	displayName: "SearchIcon",
	viewBox: "0 0 16 16",
	path: (
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M11.7419 10.3421C12.7102 9.02083 13.1439 7.38264 12.9562 5.7553C12.7685 4.12796 11.9733 2.63149 10.7297 1.56528C9.48604 0.499068 7.88567 -0.0582491 6.24876 0.00482408C4.61184 0.0678972 3.05911 0.746709 1.90119 1.90545C0.743273 3.0642 0.0655718 4.61742 0.00366997 6.25438C-0.0582319 7.89134 0.500231 9.49131 1.56733 10.7342C2.63443 11.9771 4.13147 12.7712 5.75894 12.9577C7.38641 13.1442 9.0243 12.7094 10.3449 11.7401H10.3439C10.3739 11.7801 10.4059 11.8181 10.4419 11.8551L14.2919 15.7051C14.4794 15.8928 14.7338 15.9983 14.9991 15.9983C15.2643 15.9984 15.5188 15.8932 15.7064 15.7056C15.8941 15.5181 15.9995 15.2638 15.9996 14.9985C15.9997 14.7332 15.8944 14.4788 15.7069 14.2911L11.8569 10.4411C11.8212 10.405 11.7827 10.3715 11.7419 10.3411V10.3421ZM11.9999 6.49815C11.9999 7.22042 11.8577 7.93562 11.5813 8.60291C11.3049 9.2702 10.8997 9.87651 10.389 10.3872C9.87829 10.898 9.27197 11.3031 8.60468 11.5795C7.93739 11.8559 7.22219 11.9981 6.49992 11.9981C5.77765 11.9981 5.06245 11.8559 4.39516 11.5795C3.72787 11.3031 3.12156 10.898 2.61083 10.3872C2.10011 9.87651 1.69498 9.2702 1.41858 8.60291C1.14218 7.93562 0.999921 7.22042 0.999921 6.49815C0.999921 5.03946 1.57938 3.64051 2.61083 2.60906C3.64228 1.57761 5.04123 0.998147 6.49992 0.998147C7.95861 0.998147 9.35756 1.57761 10.389 2.60906C11.4205 3.64051 11.9999 5.03946 11.9999 6.49815Z"
				fill="#697077"
			/>
		</svg>
	),
});

export const NoDataIcon = createIcon({
	displayName: "NoDataIcon",
	viewBox: "0 0 64 64",
	path: (
		<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="4" y="4" width="56" height="56" rx="28" fill="#E7EBF4" />
			<g clipPath="url(#clip0_1354_30765)">
				<path
					d="M35 20.75C35 20.5511 34.921 20.3603 34.7803 20.2197C34.6397 20.079 34.4489 20 34.25 20H29.75C29.5511 20 29.3603 20.079 29.2197 20.2197C29.079 20.3603 29 20.5511 29 20.75C29 20.9489 28.921 21.1397 28.7803 21.2803C28.6397 21.421 28.4489 21.5 28.25 21.5C28.0511 21.5 27.8603 21.579 27.7197 21.7197C27.579 21.8603 27.5 22.0511 27.5 22.25V23C27.5 23.1989 27.579 23.3897 27.7197 23.5303C27.8603 23.671 28.0511 23.75 28.25 23.75H35.75C35.9489 23.75 36.1397 23.671 36.2803 23.5303C36.421 23.3897 36.5 23.1989 36.5 23V22.25C36.5 22.0511 36.421 21.8603 36.2803 21.7197C36.1397 21.579 35.9489 21.5 35.75 21.5C35.5511 21.5 35.3603 21.421 35.2197 21.2803C35.079 21.1397 35 20.9489 35 20.75Z"
					fill="#A2A9B0"
				/>
				<path
					d="M26.1275 21.5H25.25C24.6533 21.5 24.081 21.7371 23.659 22.159C23.2371 22.581 23 23.1533 23 23.75V41.75C23 42.3467 23.2371 42.919 23.659 43.341C24.081 43.7629 24.6533 44 25.25 44H38.75C39.3467 44 39.919 43.7629 40.341 43.341C40.7629 42.919 41 42.3467 41 41.75V23.75C41 23.1533 40.7629 22.581 40.341 22.159C39.919 21.7371 39.3467 21.5 38.75 21.5H37.8725C37.955 21.734 38 21.9875 38 22.25V23C38 23.5967 37.7629 24.169 37.341 24.591C36.919 25.0129 36.3467 25.25 35.75 25.25H28.25C27.6533 25.25 27.081 25.0129 26.659 24.591C26.2371 24.169 26 23.5967 26 23V22.25C26 21.9875 26.045 21.734 26.1275 21.5ZM32 32.4395L33.719 30.719C33.8598 30.5782 34.0508 30.4991 34.25 30.4991C34.4492 30.4991 34.6402 30.5782 34.781 30.719C34.9218 30.8598 35.0009 31.0508 35.0009 31.25C35.0009 31.4492 34.9218 31.6402 34.781 31.781L33.0605 33.5L34.781 35.219C34.9218 35.3598 35.0009 35.5508 35.0009 35.75C35.0009 35.9492 34.9218 36.1402 34.781 36.281C34.6402 36.4218 34.4492 36.5009 34.25 36.5009C34.0508 36.5009 33.8598 36.4218 33.719 36.281L32 34.5605L30.281 36.281C30.1402 36.4218 29.9492 36.5009 29.75 36.5009C29.5508 36.5009 29.3598 36.4218 29.219 36.281C29.0782 36.1402 28.9991 35.9492 28.9991 35.75C28.9991 35.5508 29.0782 35.3598 29.219 35.219L30.9395 33.5L29.219 31.781C29.1493 31.7113 29.094 31.6285 29.0562 31.5374C29.0185 31.4463 28.9991 31.3486 28.9991 31.25C28.9991 31.1514 29.0185 31.0537 29.0562 30.9626C29.094 30.8715 29.1493 30.7887 29.219 30.719C29.2887 30.6493 29.3715 30.594 29.4626 30.5562C29.5537 30.5185 29.6514 30.4991 29.75 30.4991C29.8486 30.4991 29.9463 30.5185 30.0374 30.5562C30.1285 30.594 30.2113 30.6493 30.281 30.719L32 32.4395Z"
					fill="#A2A9B0"
				/>
			</g>
			<rect x="4" y="4" width="56" height="56" rx="28" stroke="#F7FAFF" strokeWidth="8" />
			<defs>
				<clipPath id="clip0_1354_30765">
					<rect width="24" height="24" fill="white" transform="translate(20 20)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const TooltipIcon = createIcon({
	displayName: "TooltipIcon",
	viewBox: "0 0 14 15",
	path: (
		<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M7 14.5C8.85652 14.5 10.637 13.7625 11.9497 12.4497C13.2625 11.137 14 9.35652 14 7.5C14 5.64348 13.2625 3.86301 11.9497 2.55025C10.637 1.2375 8.85652 0.5 7 0.5C5.14348 0.5 3.36301 1.2375 2.05025 2.55025C0.737498 3.86301 0 5.64348 0 7.5C0 9.35652 0.737498 11.137 2.05025 12.4497C3.36301 13.7625 5.14348 14.5 7 14.5ZM7.81375 6.2645L6.93875 10.3814C6.8775 10.6789 6.96413 10.8477 7.20475 10.8477C7.3745 10.8477 7.63088 10.7865 7.805 10.6325L7.728 10.9965C7.47687 11.2993 6.923 11.5197 6.44613 11.5197C5.831 11.5197 5.56938 11.1505 5.73913 10.3656L6.38487 7.33113C6.44088 7.07475 6.39013 6.982 6.13375 6.91988L5.73913 6.849L5.81087 6.51562L7.81462 6.2645H7.81375ZM7 5.3125C6.76794 5.3125 6.54538 5.22031 6.38128 5.05622C6.21719 4.89212 6.125 4.66956 6.125 4.4375C6.125 4.20544 6.21719 3.98288 6.38128 3.81878C6.54538 3.65469 6.76794 3.5625 7 3.5625C7.23206 3.5625 7.45462 3.65469 7.61872 3.81878C7.78281 3.98288 7.875 4.20544 7.875 4.4375C7.875 4.66956 7.78281 4.89212 7.61872 5.05622C7.45462 5.22031 7.23206 5.3125 7 5.3125Z"
				fill="#697077"
			/>
		</svg>
	),
});

export const ServiceFee = createIcon({
	displayName: "ServiceFee",
	viewBox: "0 0 43 41",
	path: (
		<svg width="43" height="41" viewBox="0 0 43 41" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M29.242 0.789062L28.772 1.46748L21.2916 12.3089L14.8045 14.2289C13.9487 14.4861 13.2171 15.0492 12.7337 15.803L7.35072 24.1214L10.765 35.6989L16.9861 34.3332C17.1413 34.3022 17.3009 34.2445 17.4561 34.2046C18.9814 33.6902 20.3117 32.768 21.3182 31.4998L22.103 30.3691L25.6725 32.8655L26.3554 33.3355L26.8564 32.6305L42.0256 10.6328L42.5266 9.94996L41.8171 9.47551L29.9514 1.25908L29.242 0.789062ZM29.6898 3.12141L40.1898 10.3934L34.2836 18.9512C35.0019 16.5213 33.8535 13.5815 31.3171 11.8389C29.8628 10.8412 28.1734 10.3756 26.5416 10.5264C25.575 10.6151 24.7059 10.9122 23.9654 11.391L29.6898 3.12141ZM33.1795 8.1364C32.7316 8.13197 32.2971 8.34037 32.0266 8.73944C31.5921 9.37796 31.7517 10.2692 32.3946 10.7082C33.0331 11.1472 33.8978 10.9787 34.3368 10.3402C34.7713 9.70608 34.6073 8.84143 33.9688 8.39802C33.7293 8.23395 33.45 8.14084 33.1795 8.1364ZM21.8148 14.5171C21.5709 15.6833 21.7261 16.9692 22.2627 18.1664L22.9189 17.5855C23.6816 16.9647 24.6615 16.641 25.6459 16.641C26.7589 16.641 27.8098 17.0534 28.6123 17.825C29.9248 19.0798 30.2796 20.9732 29.61 22.5739C30.7185 22.4808 31.7118 22.0906 32.5232 21.4742L25.9341 30.9987L23.0741 29.03L27.6945 22.5473C28.5281 21.4875 28.4305 19.9622 27.4595 19.031C26.5017 18.1132 25.0163 18.0644 23.992 18.898L19.243 22.8621L17.2743 21.6826L21.8148 14.5171ZM5.61698 24.2278L0.5 25.8019L4.72572 40.1109L9.8693 38.5367L5.61698 24.2278Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const ServiceTime = createIcon({
	displayName: "ServiceTime",
	viewBox: "0 0 33 32",
	path: (
		<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M32.5 16C32.5 20.2435 30.8143 24.3131 27.8137 27.3137C24.8131 30.3143 20.7435 32 16.5 32C12.2565 32 8.18687 30.3143 5.18629 27.3137C2.18571 24.3131 0.5 20.2435 0.5 16C0.5 11.7565 2.18571 7.68687 5.18629 4.68629C8.18687 1.68571 12.2565 0 16.5 0C20.7435 0 24.8131 1.68571 27.8137 4.68629C30.8143 7.68687 32.5 11.7565 32.5 16ZM16.5 7C16.5 6.73478 16.3946 6.48043 16.2071 6.29289C16.0196 6.10536 15.7652 6 15.5 6C15.2348 6 14.9804 6.10536 14.7929 6.29289C14.6054 6.48043 14.5 6.73478 14.5 7V18C14.5001 18.1763 14.5467 18.3494 14.6352 18.5018C14.7237 18.6542 14.8509 18.7806 15.004 18.868L22.004 22.868C22.2337 22.9921 22.5029 23.0215 22.754 22.9498C23.0051 22.8782 23.2181 22.7111 23.3477 22.4844C23.4772 22.2577 23.513 21.9893 23.4473 21.7366C23.3815 21.4839 23.2196 21.2669 22.996 21.132L16.5 17.42V7Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const ServiceApplications = createIcon({
	displayName: "ServiceApplications",
	viewBox: "0 0 36 36",
	path: (
		<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g clipPath="url(#clip0_1362_31408)">
				<path
					d="M15.8125 31.125C15.8125 31.125 13.625 31.125 13.625 28.9375C13.625 26.75 15.8125 20.1875 24.5625 20.1875C33.3125 20.1875 35.5 26.75 35.5 28.9375C35.5 31.125 33.3125 31.125 33.3125 31.125H15.8125ZM24.5625 18C26.303 18 27.9722 17.3086 29.2029 16.0779C30.4336 14.8472 31.125 13.178 31.125 11.4375C31.125 9.69702 30.4336 8.02782 29.2029 6.79711C27.9722 5.5664 26.303 4.875 24.5625 4.875C22.822 4.875 21.1528 5.5664 19.9221 6.79711C18.6914 8.02782 18 9.69702 18 11.4375C18 13.178 18.6914 14.8472 19.9221 16.0779C21.1528 17.3086 22.822 18 24.5625 18Z"
					fill="#B08D44"
				/>
				<path
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M11.91 31.1259C11.5857 30.443 11.424 29.6943 11.4375 28.9384C11.4375 25.9744 12.925 22.9228 15.6725 20.8009C14.3011 20.3784 12.8724 20.1718 11.4375 20.1884C2.6875 20.1884 0.5 26.7509 0.5 28.9384C0.5 31.1259 2.6875 31.1259 2.6875 31.1259H11.91Z"
					fill="#B08D44"
				/>
				<path
					d="M10.3438 18C11.7942 18 13.1852 17.4238 14.2107 16.3982C15.2363 15.3727 15.8125 13.9817 15.8125 12.5312C15.8125 11.0808 15.2363 9.68985 14.2107 8.66426C13.1852 7.63867 11.7942 7.0625 10.3438 7.0625C8.89335 7.0625 7.50235 7.63867 6.47676 8.66426C5.45117 9.68985 4.875 11.0808 4.875 12.5312C4.875 13.9817 5.45117 15.3727 6.47676 16.3982C7.50235 17.4238 8.89335 18 10.3438 18Z"
					fill="#B08D44"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31408">
					<rect width="35" height="35" fill="white" transform="translate(0.5 0.5)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ServiceRating = createIcon({
	displayName: "ServiceRating",
	viewBox: "0 0 152 40",
	path: (
		<svg
			width="152"
			height="40"
			viewBox="0 0 152 40"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<g clipPath="url(#clip0_1362_31415)">
				<path
					d="M6.51527 29.5895C6.03277 29.837 5.48527 29.4033 5.58277 28.8495L6.62027 22.937L2.21652 18.742C1.80527 18.3495 2.01902 17.632 2.57027 17.5545L8.69277 16.6845L11.4228 11.2758C11.669 10.7883 12.3353 10.7883 12.5815 11.2758L15.3115 16.6845L21.434 17.5545C21.9853 17.632 22.199 18.3495 21.7865 18.742L17.384 22.937L18.4215 28.8495C18.519 29.4033 17.9715 29.837 17.489 29.5895L12.0003 26.7695L6.51402 29.5895H6.51527Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip1_1362_31415)">
				<path
					d="M6.51527 29.5895C6.03277 29.837 5.48527 29.4033 5.58277 28.8495L6.62027 22.937L2.21652 18.742C1.80527 18.3495 2.01902 17.632 2.57027 17.5545L8.69277 16.6845L11.4228 11.2758C11.669 10.7883 12.3353 10.7883 12.5815 11.2758L15.3115 16.6845L21.434 17.5545C21.9853 17.632 22.199 18.3495 21.7865 18.742L17.384 22.937L18.4215 28.8495C18.519 29.4033 17.9715 29.837 17.489 29.5895L12.0003 26.7695L6.51402 29.5895H6.51527Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip2_1362_31415)">
				<path
					d="M38.5153 29.5895C38.0328 29.837 37.4853 29.4033 37.5828 28.8495L38.6203 22.937L34.2165 18.742C33.8053 18.3495 34.019 17.632 34.5703 17.5545L40.6928 16.6845L43.4228 11.2758C43.669 10.7883 44.3353 10.7883 44.5815 11.2758L47.3115 16.6845L53.434 17.5545C53.9853 17.632 54.199 18.3495 53.7865 18.742L49.384 22.937L50.4215 28.8495C50.519 29.4033 49.9715 29.837 49.489 29.5895L44.0003 26.7695L38.514 29.5895H38.5153Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip3_1362_31415)">
				<path
					d="M38.5153 29.5895C38.0328 29.837 37.4853 29.4033 37.5828 28.8495L38.6203 22.937L34.2165 18.742C33.8053 18.3495 34.019 17.632 34.5703 17.5545L40.6928 16.6845L43.4228 11.2758C43.669 10.7883 44.3353 10.7883 44.5815 11.2758L47.3115 16.6845L53.434 17.5545C53.9853 17.632 54.199 18.3495 53.7865 18.742L49.384 22.937L50.4215 28.8495C50.519 29.4033 49.9715 29.837 49.489 29.5895L44.0003 26.7695L38.514 29.5895H38.5153Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip4_1362_31415)">
				<path
					d="M70.5153 29.5895C70.0328 29.837 69.4853 29.4033 69.5828 28.8495L70.6203 22.937L66.2165 18.742C65.8053 18.3495 66.019 17.632 66.5703 17.5545L72.6928 16.6845L75.4228 11.2758C75.669 10.7883 76.3353 10.7883 76.5815 11.2758L79.3115 16.6845L85.434 17.5545C85.9853 17.632 86.199 18.3495 85.7865 18.742L81.384 22.937L82.4215 28.8495C82.519 29.4033 81.9715 29.837 81.489 29.5895L76.0003 26.7695L70.514 29.5895H70.5153Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip5_1362_31415)">
				<path
					d="M70.5153 29.5895C70.0328 29.837 69.4853 29.4033 69.5828 28.8495L70.6203 22.937L66.2165 18.742C65.8053 18.3495 66.019 17.632 66.5703 17.5545L72.6928 16.6845L75.4228 11.2758C75.669 10.7883 76.3353 10.7883 76.5815 11.2758L79.3115 16.6845L85.434 17.5545C85.9853 17.632 86.199 18.3495 85.7865 18.742L81.384 22.937L82.4215 28.8495C82.519 29.4033 81.9715 29.837 81.489 29.5895L76.0003 26.7695L70.514 29.5895H70.5153Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip6_1362_31415)">
				<path
					d="M102.515 29.5895C102.033 29.837 101.485 29.4033 101.583 28.8495L102.62 22.937L98.2165 18.742C97.8053 18.3495 98.019 17.632 98.5703 17.5545L104.693 16.6845L107.423 11.2758C107.669 10.7883 108.335 10.7883 108.582 11.2758L111.312 16.6845L117.434 17.5545C117.985 17.632 118.199 18.3495 117.787 18.742L113.384 22.937L114.422 28.8495C114.519 29.4033 113.972 29.837 113.489 29.5895L108 26.7695L102.514 29.5895H102.515Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip7_1362_31415)">
				<path
					d="M102.515 29.5895C102.033 29.837 101.485 29.4033 101.583 28.8495L102.62 22.937L98.2165 18.742C97.8053 18.3495 98.019 17.632 98.5703 17.5545L104.693 16.6845L107.423 11.2758C107.669 10.7883 108.335 10.7883 108.582 11.2758L111.312 16.6845L117.434 17.5545C117.985 17.632 118.199 18.3495 117.787 18.742L113.384 22.937L114.422 28.8495C114.519 29.4033 113.972 29.837 113.489 29.5895L108 26.7695L102.514 29.5895H102.515Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip8_1362_31415)">
				<path
					d="M134.515 29.5895C134.033 29.837 133.485 29.4033 133.583 28.8495L134.62 22.937L130.217 18.742C129.805 18.3495 130.019 17.632 130.57 17.5545L136.693 16.6845L139.423 11.2758C139.669 10.7883 140.335 10.7883 140.582 11.2758L143.312 16.6845L149.434 17.5545C149.985 17.632 150.199 18.3495 149.787 18.742L145.384 22.937L146.422 28.8495C146.519 29.4033 145.972 29.837 145.489 29.5895L140 26.7695L134.514 29.5895H134.515Z"
					fill="#B08D44"
				/>
			</g>
			<g clipPath="url(#clip9_1362_31415)">
				<path
					d="M134.515 29.5895C134.033 29.837 133.485 29.4033 133.583 28.8495L134.62 22.937L130.217 18.742C129.805 18.3495 130.019 17.632 130.57 17.5545L136.693 16.6845L139.423 11.2758C139.669 10.7883 140.335 10.7883 140.582 11.2758L143.312 16.6845L149.434 17.5545C149.985 17.632 150.199 18.3495 149.787 18.742L145.384 22.937L146.422 28.8495C146.519 29.4033 145.972 29.837 145.489 29.5895L140 26.7695L134.514 29.5895H134.515Z"
					fill="#B08D44"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(2 10.2852)" />
				</clipPath>
				<clipPath id="clip1_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(2 10.2852)" />
				</clipPath>
				<clipPath id="clip2_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(34 10.2852)" />
				</clipPath>
				<clipPath id="clip3_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(34 10.2852)" />
				</clipPath>
				<clipPath id="clip4_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(66 10.2852)" />
				</clipPath>
				<clipPath id="clip5_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(66 10.2852)" />
				</clipPath>
				<clipPath id="clip6_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(98 10.2852)" />
				</clipPath>
				<clipPath id="clip7_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(98 10.2852)" />
				</clipPath>
				<clipPath id="clip8_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(130 10.2852)" />
				</clipPath>
				<clipPath id="clip9_1362_31415">
					<rect width="20" height="20" fill="white" transform="translate(130 10.2852)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ServiceStepOne = createIcon({
	displayName: "ServiceStepOne",
	viewBox: "0 0 100 99",
	path: (
		<svg
			width="100"
			height="99"
			viewBox="0 0 100 99"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect x="0.5" width="99" height="99" rx="8" fill="#B08D44" />
			<g clipPath="url(#clip0_1362_31446)">
				<path
					d="M60.6177 32.3202C60.4126 32.1152 60.1344 32 59.8444 32C59.5544 32 59.2763 32.1152 59.0711 32.3202L55.4683 35.923L63.5774 44.0321L67.1802 40.4315C67.2821 40.3299 67.3629 40.2092 67.418 40.0763C67.4732 39.9434 67.5015 39.801 67.5015 39.6571C67.5015 39.5132 67.4732 39.3708 67.418 39.2379C67.3629 39.105 67.2821 38.9843 67.1802 38.8827L60.6177 32.3202ZM62.0308 45.5787L53.9218 37.4696L39.703 51.6884H40.1558C40.4459 51.6884 40.7241 51.8036 40.9292 52.0087C41.1343 52.2138 41.2496 52.492 41.2496 52.7821V53.8759H42.3433C42.6334 53.8759 42.9116 53.9911 43.1167 54.1962C43.3218 54.4013 43.4371 54.6795 43.4371 54.9696V56.0634H44.5308C44.8209 56.0634 45.0991 56.1786 45.3042 56.3837C45.5093 56.5888 45.6246 56.867 45.6246 57.1571V58.2509H46.7183C47.0084 58.2509 47.2866 58.3661 47.4917 58.5712C47.6968 58.7763 47.8121 59.0545 47.8121 59.3446V59.7974L62.0308 45.5787ZM45.6946 61.9149C45.6485 61.7925 45.6248 61.6629 45.6246 61.5321V60.4384H44.5308C44.2407 60.4384 43.9625 60.3231 43.7574 60.118C43.5523 59.9129 43.4371 59.6347 43.4371 59.3446V58.2509H42.3433C42.0532 58.2509 41.775 58.1356 41.5699 57.9305C41.3648 57.7254 41.2496 57.4472 41.2496 57.1571V56.0634H40.1558C39.8657 56.0634 39.5875 55.9481 39.3824 55.743C39.1773 55.5379 39.0621 55.2597 39.0621 54.9696V53.8759H37.9683C37.8376 53.8756 37.7079 53.8519 37.5855 53.8059L37.194 54.1952C37.0897 54.3002 37.0079 54.4252 36.9533 54.5627L32.5783 65.5002C32.4988 65.699 32.4793 65.9167 32.5223 66.1265C32.5653 66.3362 32.669 66.5287 32.8204 66.6801C32.9717 66.8315 33.1642 66.9351 33.374 66.9781C33.5837 67.0211 33.8014 67.0017 34.0002 66.9221L44.9377 62.5471C45.0752 62.4926 45.2002 62.4107 45.3052 62.3065L45.6946 61.9171V61.9149Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31446">
					<rect width="35" height="35" fill="white" transform="translate(32.5 32)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ServiceStepTwo = createIcon({
	displayName: "ServiceStepTwo",
	viewBox: "0 0 100 99",
	path: (
		<svg
			width="100"
			height="99"
			viewBox="0 0 100 99"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect x="0.5" width="99" height="99" rx="8" fill="#B08D44" />
			<g clipPath="url(#clip0_1362_31453)">
				<path
					d="M52.8284 32H41.25C40.0897 32 38.9769 32.4609 38.1564 33.2814C37.3359 34.1019 36.875 35.2147 36.875 36.375V62.625C36.875 63.7853 37.3359 64.8981 38.1564 65.7186C38.9769 66.5391 40.0897 67 41.25 67H58.75C59.9103 67 61.0231 66.5391 61.8436 65.7186C62.6641 64.8981 63.125 63.7853 63.125 62.625V42.2966C63.1249 41.7164 62.8943 41.1601 62.4841 40.75L54.375 32.6409C53.9649 32.2307 53.4086 32.0001 52.8284 32V32ZM53.2812 39.6562V35.2812L59.8438 41.8438H55.4688C54.8886 41.8438 54.3322 41.6133 53.922 41.203C53.5117 40.7928 53.2812 40.2364 53.2812 39.6562ZM51.0938 48.4062V56.7034L53.6006 54.1944C53.806 53.989 54.0846 53.8736 54.375 53.8736C54.6654 53.8736 54.944 53.989 55.1494 54.1944C55.3548 54.3998 55.4701 54.6783 55.4701 54.9688C55.4701 55.2592 55.3548 55.5377 55.1494 55.7431L50.7744 60.1181C50.6728 60.22 50.5521 60.3008 50.4192 60.3559C50.2863 60.4111 50.1439 60.4395 50 60.4395C49.8561 60.4395 49.7137 60.4111 49.5808 60.3559C49.4479 60.3008 49.3272 60.22 49.2256 60.1181L44.8506 55.7431C44.6452 55.5377 44.5299 55.2592 44.5299 54.9688C44.5299 54.6783 44.6452 54.3998 44.8506 54.1944C45.056 53.989 45.3346 53.8736 45.625 53.8736C45.9154 53.8736 46.194 53.989 46.3994 54.1944L48.9062 56.7034V48.4062C48.9062 48.1162 49.0215 47.838 49.2266 47.6329C49.4317 47.4277 49.7099 47.3125 50 47.3125C50.2901 47.3125 50.5683 47.4277 50.7734 47.6329C50.9785 47.838 51.0938 48.1162 51.0938 48.4062Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31453">
					<rect width="35" height="35" fill="white" transform="translate(32.5 32)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ServiceStepThree = createIcon({
	displayName: "ServiceStepThree",
	viewBox: "0 0 100 99",
	path: (
		<svg
			width="100"
			height="99"
			viewBox="0 0 100 99"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect x="0.5" width="99" height="99" rx="8" fill="#B08D44" />
			<g clipPath="url(#clip0_1362_31460)">
				<path
					d="M47.8125 37.4688C47.8125 37.1787 47.9277 36.9005 48.1329 36.6954C48.338 36.4902 48.6162 36.375 48.9062 36.375H64.2188C64.5088 36.375 64.787 36.4902 64.9921 36.6954C65.1973 36.9005 65.3125 37.1787 65.3125 37.4688V39.6562C65.3125 39.9463 65.1973 40.2245 64.9921 40.4296C64.787 40.6348 64.5088 40.75 64.2188 40.75H48.9062C48.6162 40.75 48.338 40.6348 48.1329 40.4296C47.9277 40.2245 47.8125 39.9463 47.8125 39.6562V37.4688ZM36.875 34.1875C35.7147 34.1875 34.6019 34.6484 33.7814 35.4689C32.9609 36.2894 32.5 37.4022 32.5 38.5625V42.9375C32.5 44.0978 32.9609 45.2106 33.7814 46.0311C34.6019 46.8516 35.7147 47.3125 36.875 47.3125H41.25C42.4103 47.3125 43.5231 46.8516 44.3436 46.0311C45.1641 45.2106 45.625 44.0978 45.625 42.9375V38.5625C45.625 37.4022 45.1641 36.2894 44.3436 35.4689C43.5231 34.6484 42.4103 34.1875 41.25 34.1875H36.875ZM36.875 51.6875C35.7147 51.6875 34.6019 52.1484 33.7814 52.9689C32.9609 53.7894 32.5 54.9022 32.5 56.0625V60.4375C32.5 61.5978 32.9609 62.7106 33.7814 63.5311C34.6019 64.3516 35.7147 64.8125 36.875 64.8125H41.25C42.4103 64.8125 43.5231 64.3516 44.3436 63.5311C45.1641 62.7106 45.625 61.5978 45.625 60.4375V56.0625C45.625 54.9022 45.1641 53.7894 44.3436 52.9689C43.5231 52.1484 42.4103 51.6875 41.25 51.6875H36.875ZM38.7431 43.7119C38.6415 43.8137 38.5208 43.8945 38.3879 43.9497C38.2551 44.0048 38.1126 44.0332 37.9688 44.0332C37.8249 44.0332 37.6824 44.0048 37.5496 43.9497C37.4167 43.8945 37.296 43.8137 37.1944 43.7119L35.0069 41.5244C34.9052 41.4227 34.8245 41.302 34.7695 41.1691C34.7144 41.0362 34.6861 40.8938 34.6861 40.75C34.6861 40.6062 34.7144 40.4638 34.7695 40.3309C34.8245 40.198 34.9052 40.0773 35.0069 39.9756C35.1086 39.8739 35.2293 39.7933 35.3622 39.7382C35.495 39.6832 35.6374 39.6549 35.7812 39.6549C35.9251 39.6549 36.0675 39.6832 36.2003 39.7382C36.3332 39.7933 36.4539 39.8739 36.5556 39.9756L37.9688 41.3909L41.5694 37.7881C41.6711 37.6864 41.7918 37.6058 41.9247 37.5507C42.0575 37.4957 42.1999 37.4674 42.3438 37.4674C42.4876 37.4674 42.63 37.4957 42.7628 37.5507C42.8957 37.6058 43.0164 37.6864 43.1181 37.7881C43.2198 37.8898 43.3005 38.0105 43.3555 38.1434C43.4106 38.2763 43.4389 38.4187 43.4389 38.5625C43.4389 38.7063 43.4106 38.8487 43.3555 38.9816C43.3005 39.1145 43.2198 39.2352 43.1181 39.3369L38.7431 43.7119ZM38.7431 61.2119C38.6415 61.3137 38.5208 61.3945 38.3879 61.4497C38.2551 61.5048 38.1126 61.5332 37.9688 61.5332C37.8249 61.5332 37.6824 61.5048 37.5496 61.4497C37.4167 61.3945 37.296 61.3137 37.1944 61.2119L35.0069 59.0244C34.8015 58.819 34.6861 58.5404 34.6861 58.25C34.6861 57.9596 34.8015 57.681 35.0069 57.4756C35.2123 57.2702 35.4908 57.1549 35.7812 57.1549C36.0717 57.1549 36.3502 57.2702 36.5556 57.4756L37.9688 58.8909L41.5694 55.2881C41.7748 55.0827 42.0533 54.9674 42.3438 54.9674C42.6342 54.9674 42.9127 55.0827 43.1181 55.2881C43.3235 55.4935 43.4389 55.7721 43.4389 56.0625C43.4389 56.3529 43.3235 56.6315 43.1181 56.8369L38.7431 61.2119ZM47.8125 54.9688C47.8125 54.6787 47.9277 54.4005 48.1329 54.1954C48.338 53.9902 48.6162 53.875 48.9062 53.875H64.2188C64.5088 53.875 64.787 53.9902 64.9921 54.1954C65.1973 54.4005 65.3125 54.6787 65.3125 54.9688V57.1562C65.3125 57.4463 65.1973 57.7245 64.9921 57.9296C64.787 58.1348 64.5088 58.25 64.2188 58.25H48.9062C48.6162 58.25 48.338 58.1348 48.1329 57.9296C47.9277 57.7245 47.8125 57.4463 47.8125 57.1562V54.9688ZM47.8125 44.0312C47.8125 43.7412 47.9277 43.463 48.1329 43.2579C48.338 43.0527 48.6162 42.9375 48.9062 42.9375H59.8438C60.1338 42.9375 60.412 43.0527 60.6171 43.2579C60.8223 43.463 60.9375 43.7412 60.9375 44.0312C60.9375 44.3213 60.8223 44.5995 60.6171 44.8046C60.412 45.0098 60.1338 45.125 59.8438 45.125H48.9062C48.6162 45.125 48.338 45.0098 48.1329 44.8046C47.9277 44.5995 47.8125 44.3213 47.8125 44.0312ZM47.8125 61.5312C47.8125 61.2412 47.9277 60.963 48.1329 60.7579C48.338 60.5527 48.6162 60.4375 48.9062 60.4375H59.8438C60.1338 60.4375 60.412 60.5527 60.6171 60.7579C60.8223 60.963 60.9375 61.2412 60.9375 61.5312C60.9375 61.8213 60.8223 62.0995 60.6171 62.3046C60.412 62.5098 60.1338 62.625 59.8438 62.625H48.9062C48.6162 62.625 48.338 62.5098 48.1329 62.3046C47.9277 62.0995 47.8125 61.8213 47.8125 61.5312Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31460">
					<rect width="35" height="35" fill="white" transform="translate(32.5 32)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ServiceStepFour = createIcon({
	displayName: "ServiceStepFour",
	viewBox: "0 0 100 99",
	path: (
		<svg
			width="100"
			height="99"
			viewBox="0 0 100 99"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect x="0.5" width="99" height="99" rx="8" fill="#B08D44" />
			<g clipPath="url(#clip0_1362_31453)">
				<path
					d="M52.8284 32H41.25C40.0897 32 38.9769 32.4609 38.1564 33.2814C37.3359 34.1019 36.875 35.2147 36.875 36.375V62.625C36.875 63.7853 37.3359 64.8981 38.1564 65.7186C38.9769 66.5391 40.0897 67 41.25 67H58.75C59.9103 67 61.0231 66.5391 61.8436 65.7186C62.6641 64.8981 63.125 63.7853 63.125 62.625V42.2966C63.1249 41.7164 62.8943 41.1601 62.4841 40.75L54.375 32.6409C53.9649 32.2307 53.4086 32.0001 52.8284 32V32ZM53.2812 39.6562V35.2812L59.8438 41.8438H55.4688C54.8886 41.8438 54.3322 41.6133 53.922 41.203C53.5117 40.7928 53.2812 40.2364 53.2812 39.6562ZM51.0938 48.4062V56.7034L53.6006 54.1944C53.806 53.989 54.0846 53.8736 54.375 53.8736C54.6654 53.8736 54.944 53.989 55.1494 54.1944C55.3548 54.3998 55.4701 54.6783 55.4701 54.9688C55.4701 55.2592 55.3548 55.5377 55.1494 55.7431L50.7744 60.1181C50.6728 60.22 50.5521 60.3008 50.4192 60.3559C50.2863 60.4111 50.1439 60.4395 50 60.4395C49.8561 60.4395 49.7137 60.4111 49.5808 60.3559C49.4479 60.3008 49.3272 60.22 49.2256 60.1181L44.8506 55.7431C44.6452 55.5377 44.5299 55.2592 44.5299 54.9688C44.5299 54.6783 44.6452 54.3998 44.8506 54.1944C45.056 53.989 45.3346 53.8736 45.625 53.8736C45.9154 53.8736 46.194 53.989 46.3994 54.1944L48.9062 56.7034V48.4062C48.9062 48.1162 49.0215 47.838 49.2266 47.6329C49.4317 47.4277 49.7099 47.3125 50 47.3125C50.2901 47.3125 50.5683 47.4277 50.7734 47.6329C50.9785 47.838 51.0938 48.1162 51.0938 48.4062Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31453">
					<rect width="35" height="35" fill="white" transform="translate(32.5 32)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const DividerLine = createIcon({
	displayName: "DividerLine",
	viewBox: "0 0 153 9",
	path: (
		<svg width="153" height="9" viewBox="0 0 153 9" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="4.5" cy="4.5" r="4" fill="#B08D44" />
			<path
				d="M14.5 3.5H13.5V5.5H14.5V3.5ZM138.5 5.5C139.052 5.5 139.5 5.05228 139.5 4.5C139.5 3.94772 139.052 3.5 138.5 3.5V5.5ZM14.5257 5.5C15.078 5.5 15.5257 5.05228 15.5257 4.5C15.5257 3.94772 15.078 3.5 14.5257 3.5V5.5ZM22.741 3.5C22.1887 3.5 21.741 3.94772 21.741 4.5C21.741 5.05228 22.1887 5.5 22.741 5.5V3.5ZM22.7923 5.5C23.3446 5.5 23.7923 5.05228 23.7923 4.5C23.7923 3.94772 23.3446 3.5 22.7923 3.5V5.5ZM31.0077 3.5C30.4554 3.5 30.0077 3.94772 30.0077 4.5C30.0077 5.05228 30.4554 5.5 31.0077 5.5V3.5ZM31.059 5.5C31.6113 5.5 32.059 5.05228 32.059 4.5C32.059 3.94772 31.6113 3.5 31.059 3.5V5.5ZM39.2743 3.5C38.722 3.5 38.2743 3.94772 38.2743 4.5C38.2743 5.05228 38.722 5.5 39.2743 5.5V3.5ZM39.3257 5.5C39.878 5.5 40.3257 5.05228 40.3257 4.5C40.3257 3.94772 39.878 3.5 39.3257 3.5V5.5ZM47.541 3.5C46.9887 3.5 46.541 3.94772 46.541 4.5C46.541 5.05228 46.9887 5.5 47.541 5.5V3.5ZM47.5923 5.5C48.1446 5.5 48.5923 5.05228 48.5923 4.5C48.5923 3.94772 48.1446 3.5 47.5923 3.5V5.5ZM55.8077 3.5C55.2554 3.5 54.8077 3.94772 54.8077 4.5C54.8077 5.05228 55.2554 5.5 55.8077 5.5V3.5ZM55.859 5.5C56.4113 5.5 56.859 5.05228 56.859 4.5C56.859 3.94772 56.4113 3.5 55.859 3.5V5.5ZM64.0743 3.5C63.522 3.5 63.0743 3.94772 63.0743 4.5C63.0743 5.05228 63.522 5.5 64.0743 5.5V3.5ZM64.1257 5.5C64.678 5.5 65.1257 5.05228 65.1257 4.5C65.1257 3.94772 64.678 3.5 64.1257 3.5V5.5ZM72.341 3.5C71.7887 3.5 71.341 3.94772 71.341 4.5C71.341 5.05228 71.7887 5.5 72.341 5.5V3.5ZM72.3923 5.5C72.9446 5.5 73.3923 5.05228 73.3923 4.5C73.3923 3.94772 72.9446 3.5 72.3923 3.5V5.5ZM80.6077 3.5C80.0554 3.5 79.6077 3.94772 79.6077 4.5C79.6077 5.05228 80.0554 5.5 80.6077 5.5V3.5ZM80.659 5.5C81.2113 5.5 81.659 5.05228 81.659 4.5C81.659 3.94772 81.2113 3.5 80.659 3.5V5.5ZM88.8743 3.5C88.322 3.5 87.8743 3.94772 87.8743 4.5C87.8743 5.05228 88.322 5.5 88.8743 5.5V3.5ZM88.9257 5.5C89.478 5.5 89.9257 5.05228 89.9257 4.5C89.9257 3.94772 89.478 3.5 88.9257 3.5V5.5ZM97.141 3.5C96.5887 3.5 96.141 3.94772 96.141 4.5C96.141 5.05228 96.5887 5.5 97.141 5.5V3.5ZM97.1923 5.5C97.7446 5.5 98.1923 5.05228 98.1923 4.5C98.1923 3.94772 97.7446 3.5 97.1923 3.5V5.5ZM105.408 3.5C104.855 3.5 104.408 3.94772 104.408 4.5C104.408 5.05228 104.855 5.5 105.408 5.5V3.5ZM105.459 5.5C106.011 5.5 106.459 5.05228 106.459 4.5C106.459 3.94772 106.011 3.5 105.459 3.5V5.5ZM113.674 3.5C113.122 3.5 112.674 3.94772 112.674 4.5C112.674 5.05228 113.122 5.5 113.674 5.5V3.5ZM113.726 5.5C114.278 5.5 114.726 5.05228 114.726 4.5C114.726 3.94772 114.278 3.5 113.726 3.5V5.5ZM121.941 3.5C121.389 3.5 120.941 3.94772 120.941 4.5C120.941 5.05228 121.389 5.5 121.941 5.5V3.5ZM121.992 5.5C122.545 5.5 122.992 5.05228 122.992 4.5C122.992 3.94772 122.545 3.5 121.992 3.5V5.5ZM130.208 3.5C129.655 3.5 129.208 3.94772 129.208 4.5C129.208 5.05228 129.655 5.5 130.208 5.5V3.5ZM130.259 5.5C130.811 5.5 131.259 5.05228 131.259 4.5C131.259 3.94772 130.811 3.5 130.259 3.5V5.5ZM138.474 3.5C137.922 3.5 137.474 3.94772 137.474 4.5C137.474 5.05228 137.922 5.5 138.474 5.5V3.5ZM14.5 5.5H14.5257V3.5H14.5V5.5ZM22.741 5.5H22.7923V3.5H22.741V5.5ZM31.0077 5.5H31.059V3.5H31.0077V5.5ZM39.2743 5.5H39.3257V3.5H39.2743V5.5ZM47.541 5.5H47.5923V3.5H47.541V5.5ZM55.8077 5.5H55.859V3.5H55.8077V5.5ZM64.0743 5.5H64.1257V3.5H64.0743V5.5ZM72.341 5.5H72.3923V3.5H72.341V5.5ZM80.6077 5.5H80.659V3.5H80.6077V5.5ZM88.8743 5.5H88.9257V3.5H88.8743V5.5ZM97.141 5.5H97.1923V3.5H97.141V5.5ZM105.408 5.5H105.459V3.5H105.408V5.5ZM113.674 5.5H113.726V3.5H113.674V5.5ZM121.941 5.5H121.992V3.5H121.941V5.5ZM130.208 5.5H130.259V3.5H130.208V5.5ZM138.474 5.5H138.5V3.5H138.474V5.5Z"
				fill="#B08D44"
			/>
			<circle cx="148.5" cy="4.5" r="4" fill="#B08D44" />
		</svg>
	),
});

export const CheckList = createIcon({
	displayName: "CheckList",
	viewBox: "0 0 16 16",
	path: (
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8ZM12.03 4.97C11.9586 4.89882 11.8735 4.84277 11.7799 4.80522C11.6863 4.76766 11.5861 4.74936 11.4853 4.75141C11.3845 4.75347 11.2851 4.77583 11.1932 4.81717C11.1012 4.85851 11.0185 4.91797 10.95 4.992L7.477 9.417L5.384 7.323C5.24183 7.19052 5.05378 7.1184 4.85948 7.12183C4.66518 7.12525 4.47979 7.20397 4.34238 7.34138C4.20497 7.47879 4.12625 7.66418 4.12283 7.85848C4.1194 8.05278 4.19152 8.24083 4.324 8.383L6.97 11.03C7.04128 11.1012 7.12616 11.1572 7.21958 11.1949C7.313 11.2325 7.41305 11.2509 7.51375 11.2491C7.61444 11.2472 7.71374 11.2251 7.8057 11.184C7.89766 11.1429 7.9804 11.0837 8.049 11.01L12.041 6.02C12.1771 5.8785 12.2523 5.68928 12.2504 5.49296C12.2485 5.29664 12.1698 5.10888 12.031 4.97H12.03Z"
				fill="#B08D44"
			/>
		</svg>
	),
});

export const EditIcon = createIcon({
	displayName: "EditIcon",
	viewBox: "0 0 56 56",
	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect x="8" y="8" width="40" height="40" rx="20" fill="#ECC575" fillOpacity="0.25" />
			<g clipPath="url(#clip0_1528_38143)">
				<path
					d="M32.8538 20.1464C32.76 20.0527 32.6329 20 32.5003 20C32.3677 20 32.2406 20.0527 32.1468 20.1464L30.4998 21.7934L34.2068 25.5004L35.8538 23.8544C35.9004 23.8079 35.9373 23.7528 35.9625 23.692C35.9877 23.6313 36.0007 23.5662 36.0007 23.5004C36.0007 23.4346 35.9877 23.3695 35.9625 23.3088C35.9373 23.248 35.9004 23.1928 35.8538 23.1464L32.8538 20.1464ZM33.4998 26.2074L29.7928 22.5004L23.2928 29.0004H23.4998C23.6324 29.0004 23.7596 29.0531 23.8534 29.1468C23.9471 29.2406 23.9998 29.3678 23.9998 29.5004V30.0004H24.4998C24.6324 30.0004 24.7596 30.0531 24.8534 30.1468C24.9471 30.2406 24.9998 30.3678 24.9998 30.5004V31.0004H25.4998C25.6324 31.0004 25.7596 31.0531 25.8534 31.1468C25.9471 31.2406 25.9998 31.3678 25.9998 31.5004V32.0004H26.4998C26.6324 32.0004 26.7596 32.0531 26.8534 32.1468C26.9471 32.2406 26.9998 32.3678 26.9998 32.5004V32.7074L33.4998 26.2074ZM26.0318 33.6754C26.0108 33.6194 25.9999 33.5602 25.9998 33.5004V33.0004H25.4998C25.3672 33.0004 25.24 32.9477 25.1463 32.8539C25.0525 32.7602 24.9998 32.633 24.9998 32.5004V32.0004H24.4998C24.3672 32.0004 24.24 31.9477 24.1463 31.8539C24.0525 31.7602 23.9998 31.633 23.9998 31.5004V31.0004H23.4998C23.3672 31.0004 23.24 30.9477 23.1463 30.8539C23.0525 30.7602 22.9998 30.633 22.9998 30.5004V30.0004H22.4998C22.44 30.0003 22.3808 29.9895 22.3248 29.9684L22.1458 30.1464C22.0982 30.1944 22.0607 30.2515 22.0358 30.3144L20.0358 35.3144C19.9994 35.4053 19.9905 35.5048 20.0102 35.6007C20.0299 35.6966 20.0772 35.7845 20.1464 35.8538C20.2157 35.923 20.3036 35.9703 20.3995 35.99C20.4954 36.0097 20.5949 36.0008 20.6858 35.9644L25.6858 33.9644C25.7487 33.9395 25.8058 33.902 25.8538 33.8544L26.0318 33.6764V33.6754Z"
					fill="#B08D44"
				/>
			</g>
			<rect
				x="4"
				y="4"
				width="48"
				height="48"
				rx="24"
				stroke="#FFFAF0"
				strokeOpacity="0.74"
				strokeWidth="8"
			/>
			<defs>
				<clipPath id="clip0_1528_38143">
					<rect width="16" height="16" fill="white" transform="translate(20 20)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const FinalStep = createIcon({
	displayName: "FinalStep",
	viewBox: "0 0 100 99",
	path: (
		<svg
			width="100"
			height="99"
			viewBox="0 0 100 99"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<rect x="0.5" width="99" height="99" rx="8" fill="#B08D44" />
			<g clip-path="url(#clip0_1362_31467)">
				<path
					d="M54.375 33.0938C54.375 32.8037 54.2598 32.5255 54.0546 32.3204C53.8495 32.1152 53.5713 32 53.2812 32H46.7188C46.4287 32 46.1505 32.1152 45.9454 32.3204C45.7402 32.5255 45.625 32.8037 45.625 33.0938C45.625 33.3838 45.5098 33.662 45.3046 33.8671C45.0995 34.0723 44.8213 34.1875 44.5312 34.1875C44.2412 34.1875 43.963 34.3027 43.7579 34.5079C43.5527 34.713 43.4375 34.9912 43.4375 35.2812V36.375C43.4375 36.6651 43.5527 36.9433 43.7579 37.1484C43.963 37.3535 44.2412 37.4688 44.5312 37.4688H55.4688C55.7588 37.4688 56.037 37.3535 56.2421 37.1484C56.4473 36.9433 56.5625 36.6651 56.5625 36.375V35.2812C56.5625 34.9912 56.4473 34.713 56.2421 34.5079C56.037 34.3027 55.7588 34.1875 55.4688 34.1875C55.1787 34.1875 54.9005 34.0723 54.6954 33.8671C54.4902 33.662 54.375 33.3838 54.375 33.0938Z"
					fill="white"
				/>
				<path
					d="M41.4359 34.1875H40.1562C39.286 34.1875 38.4514 34.5332 37.8361 35.1486C37.2207 35.7639 36.875 36.5985 36.875 37.4688V63.7188C36.875 64.589 37.2207 65.4236 37.8361 66.0389C38.4514 66.6543 39.286 67 40.1562 67H59.8438C60.714 67 61.5486 66.6543 62.1639 66.0389C62.7793 65.4236 63.125 64.589 63.125 63.7188V37.4688C63.125 36.5985 62.7793 35.7639 62.1639 35.1486C61.5486 34.5332 60.714 34.1875 59.8438 34.1875H58.5641C58.6844 34.5288 58.75 34.8984 58.75 35.2812V36.375C58.75 37.2452 58.4043 38.0798 57.7889 38.6952C57.1736 39.3105 56.339 39.6562 55.4688 39.6562H44.5312C43.661 39.6562 42.8264 39.3105 42.2111 38.6952C41.5957 38.0798 41.25 37.2452 41.25 36.375V35.2812C41.25 34.8984 41.3156 34.5288 41.4359 34.1875ZM56.2431 49.1806L49.6806 55.7431C49.579 55.845 49.4583 55.9258 49.3254 55.9809C49.1926 56.0361 49.0501 56.0645 48.9062 56.0645C48.7624 56.0645 48.6199 56.0361 48.4871 55.9809C48.3542 55.9258 48.2335 55.845 48.1319 55.7431L44.8506 52.4619C44.7489 52.3602 44.6683 52.2395 44.6132 52.1066C44.5582 51.9737 44.5299 51.8313 44.5299 51.6875C44.5299 51.5437 44.5582 51.4013 44.6132 51.2684C44.6683 51.1355 44.7489 51.0148 44.8506 50.9131C45.056 50.7077 45.3346 50.5924 45.625 50.5924C45.7688 50.5924 45.9112 50.6207 46.0441 50.6757C46.177 50.7308 46.2977 50.8114 46.3994 50.9131L48.9062 53.4222L54.6944 47.6319C54.8998 47.4265 55.1783 47.3111 55.4688 47.3111C55.7592 47.3111 56.0377 47.4265 56.2431 47.6319C56.4485 47.8373 56.5639 48.1158 56.5639 48.4062C56.5639 48.6967 56.4485 48.9752 56.2431 49.1806Z"
					fill="white"
				/>
			</g>
			<defs>
				<clipPath id="clip0_1362_31467">
					<rect width="35" height="35" fill="white" transform="translate(32.5 32)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const RequestIcon = createIcon({
	displayName: "RequestIcon",
	viewBox: "0 0 24 30",
	path: (
		<svg width="24" height="30" viewBox="0 0 24 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M14.4244 0H4.5C3.50544 0 2.55161 0.395088 1.84835 1.09835C1.14509 1.80161 0.75 2.75544 0.75 3.75V26.25C0.75 27.2446 1.14509 28.1984 1.84835 28.9016C2.55161 29.6049 3.50544 30 4.5 30H19.5C20.4946 30 21.4484 29.6049 22.1516 28.9016C22.8549 28.1984 23.25 27.2446 23.25 26.25V8.82562C23.2499 8.32838 23.0523 7.85155 22.7006 7.5L15.75 0.549375C15.3985 0.19772 14.9216 0.000106195 14.4244 0V0ZM14.8125 6.5625V2.8125L20.4375 8.4375H16.6875C16.1902 8.4375 15.7133 8.23996 15.3617 7.88832C15.01 7.53669 14.8125 7.05978 14.8125 6.5625ZM5.4375 16.875C5.18886 16.875 4.9504 16.7762 4.77459 16.6004C4.59877 16.4246 4.5 16.1861 4.5 15.9375C4.5 15.6889 4.59877 15.4504 4.77459 15.2746C4.9504 15.0988 5.18886 15 5.4375 15H18.5625C18.8111 15 19.0496 15.0988 19.2254 15.2746C19.4012 15.4504 19.5 15.6889 19.5 15.9375C19.5 16.1861 19.4012 16.4246 19.2254 16.6004C19.0496 16.7762 18.8111 16.875 18.5625 16.875H5.4375ZM4.5 19.6875C4.5 19.4389 4.59877 19.2004 4.77459 19.0246C4.9504 18.8488 5.18886 18.75 5.4375 18.75H18.5625C18.8111 18.75 19.0496 18.8488 19.2254 19.0246C19.4012 19.2004 19.5 19.4389 19.5 19.6875C19.5 19.9361 19.4012 20.1746 19.2254 20.3504C19.0496 20.5262 18.8111 20.625 18.5625 20.625H5.4375C5.18886 20.625 4.9504 20.5262 4.77459 20.3504C4.59877 20.1746 4.5 19.9361 4.5 19.6875ZM5.4375 24.375C5.18886 24.375 4.9504 24.2762 4.77459 24.1004C4.59877 23.9246 4.5 23.6861 4.5 23.4375C4.5 23.1889 4.59877 22.9504 4.77459 22.7746C4.9504 22.5988 5.18886 22.5 5.4375 22.5H12.9375C13.1861 22.5 13.4246 22.5988 13.6004 22.7746C13.7762 22.9504 13.875 23.1889 13.875 23.4375C13.875 23.6861 13.7762 23.9246 13.6004 24.1004C13.4246 24.2762 13.1861 24.375 12.9375 24.375H5.4375Z"
				fill="#3E4C62"
			/>
		</svg>
	),
});

export const ApplyIcon = createIcon({
	displayName: "ApplyIcon",
	viewBox: "0 0 48 48",
	path: (
		<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M3.40039 39.9992C2.96706 39.9992 2.60872 39.8576 2.32539 39.5742C2.04206 39.2909 1.90039 38.9326 1.90039 38.4992V35.2992C1.90039 34.1326 2.20039 33.0742 2.80039 32.1242C3.40039 31.1742 4.23372 30.4659 5.30039 29.9992C7.73372 28.9326 9.92539 28.1659 11.8754 27.6992C13.8254 27.2326 15.8337 26.9992 17.9004 26.9992C19.9671 26.9992 21.9671 27.2326 23.9004 27.6992C25.8337 28.1659 28.0171 28.9326 30.4504 29.9992C31.5171 30.4659 32.3587 31.1742 32.9754 32.1242C33.5921 33.0742 33.9004 34.1326 33.9004 35.2992V38.4992C33.9004 38.9326 33.7587 39.2909 33.4754 39.5742C33.1921 39.8576 32.8337 39.9992 32.4004 39.9992H3.40039ZM35.8004 39.9992C36.1337 39.9326 36.4004 39.7576 36.6004 39.4742C36.8004 39.1909 36.9004 38.8326 36.9004 38.3992V35.2992C36.9004 33.1992 36.3671 31.4742 35.3004 30.1242C34.2337 28.7742 32.8337 27.6826 31.1004 26.8492C33.4004 27.1159 35.5671 27.5076 37.6004 28.0242C39.6337 28.5409 41.2837 29.1326 42.5504 29.7992C43.6504 30.4326 44.5171 31.2159 45.1504 32.1492C45.7837 33.0826 46.1004 34.1326 46.1004 35.2992V38.4992C46.1004 38.9326 45.9587 39.2909 45.6754 39.5742C45.3921 39.8576 45.0337 39.9992 44.6004 39.9992H35.8004ZM17.9004 23.9492C15.7004 23.9492 13.9004 23.2492 12.5004 21.8492C11.1004 20.4492 10.4004 18.6492 10.4004 16.4492C10.4004 14.2492 11.1004 12.4492 12.5004 11.0492C13.9004 9.64922 15.7004 8.94922 17.9004 8.94922C20.1004 8.94922 21.9004 9.64922 23.3004 11.0492C24.7004 12.4492 25.4004 14.2492 25.4004 16.4492C25.4004 18.6492 24.7004 20.4492 23.3004 21.8492C21.9004 23.2492 20.1004 23.9492 17.9004 23.9492ZM35.9004 16.4492C35.9004 18.6492 35.2004 20.4492 33.8004 21.8492C32.4004 23.2492 30.6004 23.9492 28.4004 23.9492C28.0337 23.9492 27.6254 23.9242 27.1754 23.8742C26.7254 23.8242 26.3171 23.7326 25.9504 23.5992C26.7504 22.7659 27.3587 21.7409 27.7754 20.5242C28.1921 19.3076 28.4004 17.9492 28.4004 16.4492C28.4004 14.9492 28.1921 13.6242 27.7754 12.4742C27.3587 11.3242 26.7504 10.2659 25.9504 9.29922C26.3171 9.19922 26.7254 9.11589 27.1754 9.04922C27.6254 8.98255 28.0337 8.94922 28.4004 8.94922C30.6004 8.94922 32.4004 9.64922 33.8004 11.0492C35.2004 12.4492 35.9004 14.2492 35.9004 16.4492Z"
				fill="#E4D5B6"
			/>
			<path
				d="M3.40039 39.9992C2.96706 39.9992 2.60872 39.8576 2.32539 39.5742C2.04206 39.2909 1.90039 38.9326 1.90039 38.4992V35.2992C1.90039 34.1326 2.20039 33.0742 2.80039 32.1242C3.40039 31.1742 4.23372 30.4659 5.30039 29.9992C7.73372 28.9326 9.92539 28.1659 11.8754 27.6992C13.8254 27.2326 15.8337 26.9992 17.9004 26.9992C19.9671 26.9992 21.9671 27.2326 23.9004 27.6992C25.8337 28.1659 28.0171 28.9326 30.4504 29.9992C31.5171 30.4659 32.3587 31.1742 32.9754 32.1242C33.5921 33.0742 33.9004 34.1326 33.9004 35.2992V38.4992C33.9004 38.9326 33.7587 39.2909 33.4754 39.5742C33.1921 39.8576 32.8337 39.9992 32.4004 39.9992H3.40039ZM17.9004 23.9492C15.7004 23.9492 13.9004 23.2492 12.5004 21.8492C11.1004 20.4492 10.4004 18.6492 10.4004 16.4492C10.4004 14.2492 11.1004 12.4492 12.5004 11.0492C13.9004 9.64922 15.7004 8.94922 17.9004 8.94922C20.1004 8.94922 21.9004 9.64922 23.3004 11.0492C24.7004 12.4492 25.4004 14.2492 25.4004 16.4492C25.4004 18.6492 24.7004 20.4492 23.3004 21.8492C21.9004 23.2492 20.1004 23.9492 17.9004 23.9492Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const ErrorInput = createIcon({
	displayName: "ErrorInput",
	viewBox: "0 0 22 22",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22">
			<g id="alert-circle" transform="translate(-1 -1)">
				<circle
					id="Ellipse_903"
					data-name="Ellipse 903"
					cx="10"
					cy="10"
					r="10"
					transform="translate(2 2)"
					fill="none"
					stroke="#b52e29"
					strokeLinecap="square"
					strokeLinejoin="round"
					strokeWidth="2"
				></circle>
				<line
					id="Line_418"
					data-name="Line 418"
					y2="4"
					transform="translate(12 8)"
					fill="none"
					stroke="#b52e29"
					strokeLinecap="square"
					strokeLinejoin="round"
					strokeWidth="2"
				></line>
				<line
					id="Line_419"
					data-name="Line 419"
					x2="0.01"
					transform="translate(12 16)"
					fill="none"
					stroke="#b52e29"
					strokeLinecap="square"
					strokeLinejoin="round"
					strokeWidth="2"
				></line>
			</g>
		</svg>
	),
});

export const UaePass = createIcon({
	displayName: "UaePass",
	viewBox: "0 0 24 24",
	path: (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<g clip-path="url(#clip0_498_56506)">
				<path
					d="M11.2648 21.7613L11.2655 21.7604C11.2632 21.7593 11.2613 21.7582 11.2592 21.7569C11.2529 21.7529 11.246 21.7494 11.2395 21.7458C10.6656 21.4303 9.71247 20.5024 9.65183 20.4564C9.65118 20.4556 9.65054 20.4549 9.64989 20.4542C9.60521 20.4206 9.55666 20.3912 9.50164 20.3701C9.18407 20.2484 8.82379 20.4003 8.69858 20.7092C8.62951 20.8793 8.64829 21.0614 8.73075 21.2114L8.73853 21.2408C9.50589 22.2486 10.7241 22.7496 10.7241 22.7496C11.0217 22.8637 11.3592 22.7213 11.4766 22.4318C11.5774 22.1833 11.4822 21.9067 11.2648 21.7613Z"
					fill="#E82227"
				/>
				<path
					d="M6.47218 3.05935C10.1386 1.13101 13.8072 1.28484 17.4737 3.05999C17.8812 3.25729 18.2992 3.39412 18.5401 2.88357C18.7512 2.43629 18.4363 2.19231 18.0526 1.9989C16.1077 1.01827 14.048 0.494824 11.852 0.500418C9.73607 0.483207 7.76175 0.997618 5.91502 1.99202C5.52453 2.202 5.15648 2.45544 5.44963 2.94511C5.70887 3.37819 6.0955 3.25729 6.47218 3.05935Z"
					fill="#1D1D1A"
				/>
				<path
					d="M5.28414 19.1387C4.94286 19.1387 4.66504 19.4087 4.66504 19.7406C4.66504 20.0724 4.94286 20.3424 5.28414 20.3424C5.62564 20.3424 5.90324 20.0724 5.90324 19.7406C5.90324 19.4087 5.62564 19.1387 5.28414 19.1387Z"
					fill="#1D1D1A"
				/>
				<path
					d="M13.3415 9.01973C13.3415 8.68798 13.0639 8.41797 12.7224 8.41797C12.3812 8.41797 12.1035 8.68798 12.1035 9.01973C12.1035 9.35148 12.3812 9.62149 12.7224 9.62149C13.0639 9.62149 13.3415 9.35148 13.3415 9.01973Z"
					fill="#1D1D1A"
				/>
				<path
					d="M19.9941 17.7656C19.6526 17.7656 19.375 18.0356 19.375 18.3674C19.375 18.6991 19.6526 18.9692 19.9941 18.9692C20.3354 18.9692 20.613 18.6991 20.613 18.3674C20.613 18.0356 20.3354 17.7656 19.9941 17.7656Z"
					fill="#1D1D1A"
				/>
				<path
					d="M15.3366 15.3223C14.9952 15.3223 14.7178 15.5923 14.7178 15.924C14.7178 16.2558 14.9952 16.5258 15.3366 16.5258C15.6779 16.5258 15.9557 16.2558 15.9557 15.924C15.9557 15.5923 15.6779 15.3223 15.3366 15.3223Z"
					fill="#1D1D1A"
				/>
				<path
					d="M15.3366 15.3223C14.9952 15.3223 14.7178 15.5923 14.7178 15.924C14.7178 16.2558 14.9952 16.5258 15.3366 16.5258C15.6779 16.5258 15.9557 16.2558 15.9557 15.924C15.9557 15.5923 15.6779 15.3223 15.3366 15.3223Z"
					fill="#1D1D1A"
				/>
				<path
					d="M22.7959 11.3633C22.4543 11.3633 22.1768 11.6333 22.1768 11.9651C22.1768 12.297 22.4543 12.5668 22.7959 12.5668C23.1371 12.5668 23.415 12.297 23.415 11.9651C23.415 11.6333 23.1371 11.3633 22.7959 11.3633Z"
					fill="#1D1D1A"
				/>
				<path
					d="M22.4968 9.99433C22.4968 9.66258 22.2192 9.39258 21.8777 9.39258C21.5365 9.39258 21.2588 9.66258 21.2588 9.99433C21.2588 10.3263 21.5365 10.5961 21.8777 10.5961C22.2192 10.5961 22.4968 10.3263 22.4968 9.99433Z"
					fill="#1D1D1A"
				/>
				<path
					d="M4.03414 4.78751C4.37542 4.78751 4.65324 4.51771 4.65324 4.18575C4.65324 3.854 4.37542 3.58398 4.03414 3.58398C3.69286 3.58398 3.41504 3.854 3.41504 4.18575C3.41504 4.51771 3.69286 4.78751 4.03414 4.78751Z"
					fill="#1D1D1A"
				/>
				<path
					d="M1.74307 10.0762C1.51188 10.0762 1.31221 10.2016 1.206 10.3845L1.20385 10.3834C1.20147 10.3894 1.19909 10.3952 1.19672 10.4013C1.17492 10.4415 1.15744 10.4839 1.14556 10.5288C0.617989 11.8907 0.584961 13.3042 0.584961 13.3042C0.584961 13.6362 0.862562 13.9059 1.20385 13.9059C1.49634 13.9059 1.7407 13.7071 1.80503 13.4417L1.80611 13.4414C1.80611 13.4402 1.80611 13.4391 1.80632 13.438C1.81625 13.395 1.82295 13.3505 1.82295 13.3042C1.82295 13.2833 1.81863 13.2633 1.81647 13.2429C1.89892 11.8911 2.31187 10.915 2.31187 10.915C2.34404 10.8423 2.36217 10.762 2.36217 10.6779C2.36217 10.3462 2.08435 10.0762 1.74307 10.0762Z"
					fill="#1D1D1A"
				/>
				<path
					d="M8.87642 6.33897C3.7898 8.31292 2.47734 13.0358 3.85952 17.6541C3.86708 17.6946 3.87873 17.7335 3.89406 17.7706C3.98623 17.9905 4.20792 18.1464 4.46718 18.1464C4.50431 18.1464 4.54035 18.1418 4.57575 18.1358C4.65066 18.1229 4.72039 18.0958 4.78342 18.0592C4.96388 17.954 5.08606 17.7638 5.08606 17.5446C5.08606 17.5054 5.08152 17.4671 5.07419 17.4297C5.07203 17.4196 5.06857 17.4103 5.06598 17.4002C4.9438 16.9644 4.82659 16.5267 4.73744 16.0844C3.74856 11.1825 7.0623 6.97795 11.9248 6.97795C16.3911 6.97795 19.3944 9.69006 19.3944 13.7703C19.3944 15.3937 18.7291 16.2027 17.5488 16.1786C17.5468 16.1786 17.5451 16.1779 17.5432 16.1779L17.5384 16.1784C17.5156 16.1779 17.4924 16.1792 17.4698 16.1779C17.467 16.1805 17.4652 16.1834 17.4624 16.1859C17.1595 16.2249 16.9242 16.4751 16.9242 16.7797C16.9242 17.0568 17.1184 17.2876 17.3806 17.3577C17.3884 17.3609 17.3968 17.3638 17.4115 17.3677L17.4151 17.3683C17.4201 17.3692 17.4256 17.3696 17.4304 17.3705C17.4435 17.3723 17.459 17.3738 17.4782 17.3752C17.4998 17.3772 17.5209 17.3815 17.5432 17.3815C17.5512 17.3815 17.5585 17.3795 17.5665 17.3794C17.6915 17.383 17.8404 17.3815 17.8404 17.3815C19.7788 17.2322 20.4755 15.6621 20.4755 13.394C20.4755 7.73956 14.2802 4.24195 8.87642 6.33897Z"
					fill="#1D1D1A"
				/>
				<path
					d="M9.1648 4.72501C9.19057 4.72079 9.21364 4.71121 9.23861 4.70508C9.21288 4.71159 9.18787 4.71849 9.16211 4.72539L9.1648 4.72501Z"
					fill="#00AEEF"
				/>
				<path
					d="M9.65387 4.0221C9.65193 4.01221 9.64852 4.00295 9.64612 3.99327C9.58002 3.73531 9.34196 3.54297 9.05659 3.54297C9.01925 3.54297 8.98299 3.54749 8.94759 3.55373C8.90096 3.56685 8.85606 3.58106 8.8103 3.59461C7.81667 3.89 6.99315 4.29275 6.14825 4.78565C4.98389 5.46508 4.05697 6.23185 3.07889 7.45538C2.93921 7.63008 2.68989 7.85964 2.52519 8.1062C2.44229 8.19655 2.38833 8.31101 2.37365 8.43752C2.33955 8.60232 2.37862 8.76712 2.54138 8.9218C2.54721 8.92783 2.55368 8.93321 2.55973 8.93902C2.58153 8.95859 2.60506 8.97796 2.63118 8.99733C2.63399 8.99947 2.6368 9.00098 2.6396 9.00313C3.29777 9.47624 3.7159 8.58231 4.00925 8.21333C5.55204 6.2738 7.0428 5.26284 9.16172 4.71702C9.17619 4.71336 9.19022 4.70927 9.20489 4.70562C9.26258 4.6912 9.31778 4.67055 9.3679 4.6415C9.5455 4.53801 9.66578 4.35106 9.66578 4.13526C9.66578 4.09654 9.66125 4.05889 9.65387 4.0221Z"
					fill="#1D1D1A"
				/>
				<path
					d="M21.0765 7.6793C21.0472 7.6268 21.012 7.57796 20.9685 7.53644C18.7152 4.67179 15.7046 3.28604 12.0967 3.21289C11.9367 3.21504 11.5324 3.21289 11.4853 3.21289C11.1441 3.21289 10.8662 3.48289 10.8662 3.81465C10.8662 4.14662 11.1441 4.41641 11.4853 4.41641C14.6604 4.25204 17.8589 5.53043 20.0212 8.29182C20.0281 8.30236 20.0372 8.31204 20.0447 8.32194C20.1574 8.46801 20.3351 8.56397 20.5369 8.56397C20.8773 8.56397 21.1539 8.29482 21.1539 7.96415C21.1539 7.86066 21.1243 7.76428 21.0765 7.6793Z"
					fill="#1D1D1A"
				/>
				<path
					d="M17.9304 13.7714C17.9209 13.5819 17.8941 13.3921 17.8592 13.2051C17.5676 11.6475 16.6652 10.2385 15.4577 9.36523C15.5609 9.44785 15.6372 9.56144 15.6705 9.6916C15.6729 9.70194 15.6767 9.71141 15.6789 9.72172C15.6862 9.75962 15.6908 9.79872 15.6908 9.83874C15.6908 10.0621 15.5664 10.2557 15.3827 10.3627C15.3184 10.4001 15.2474 10.4276 15.171 10.4407C15.1352 10.447 15.0984 10.4515 15.0607 10.4515C14.935 10.4515 14.8189 10.4149 14.7207 10.3532C15.7113 11.1686 16.5484 12.5197 16.6963 13.8805C16.7438 14.317 16.7889 14.763 17.346 14.7451C17.9785 14.7251 17.9526 14.2185 17.9304 13.7714Z"
					fill="#1D1D1A"
				/>
				<path
					d="M15.1705 10.4413C15.247 10.428 15.3178 10.4006 15.3821 10.3632C15.566 10.2562 15.6903 10.0626 15.6903 9.83925C15.6903 9.79923 15.6858 9.76014 15.6782 9.72205C15.6761 9.71193 15.6724 9.70245 15.67 9.69214C15.6366 9.56197 15.5604 9.44837 15.457 9.36576C15.449 9.35995 15.441 9.35393 15.4331 9.34833C15.3288 9.27239 15.2004 9.22656 15.0601 9.22656C14.7124 9.22656 14.4297 9.5013 14.4297 9.83925C14.4297 9.87762 14.4342 9.9146 14.4413 9.95076C14.4491 9.99179 14.4608 10.0314 14.4765 10.0693C14.5076 10.1433 14.554 10.2094 14.6103 10.2657C14.6473 10.2941 14.6837 10.3235 14.72 10.3537C14.8182 10.4155 14.9346 10.4521 15.0601 10.4521C15.098 10.4521 15.1345 10.4475 15.1705 10.4413Z"
					fill="#1D1D1A"
				/>
				<path
					d="M14.8057 10.4243C14.7407 10.3706 14.6757 10.3182 14.6104 10.2676C14.6695 10.3266 14.7341 10.3794 14.8057 10.4243Z"
					fill="#00AEEF"
				/>
				<path
					d="M12.5884 11.05C12.5863 11.0496 12.5851 11.0492 12.583 11.0488C12.5851 11.0496 12.5871 11.05 12.5892 11.0508L12.5884 11.05Z"
					fill="#1D1D1A"
				/>
				<path
					d="M15.0797 20.9046C14.738 20.7845 14.3824 20.6905 14.0583 20.5354C11.443 19.2837 10.036 17.2357 9.90109 14.4057C9.8409 13.1404 10.6826 12.2525 11.859 12.2073L11.876 12.2015C11.9475 12.1901 12.0148 12.1667 12.0761 12.1337C12.2655 12.0325 12.3954 11.839 12.3954 11.6146C12.3954 11.3918 12.2672 11.1994 12.08 11.0974C11.9974 11.0527 11.9043 11.0251 11.8042 11.0216C11.797 11.0215 11.7903 11.0195 11.7832 11.0195C11.7694 11.0195 11.7562 11.0228 11.7426 11.0236C10.1681 11.1221 8.90014 12.2465 8.69334 13.7734C8.22794 17.2127 11.2274 21.3325 14.6966 22.0226C15.0917 22.1014 15.4492 22.0803 15.5708 21.6378C15.6776 21.2492 15.435 21.0293 15.0797 20.9046Z"
					fill="#1D1D1A"
				/>
				<path
					d="M15.098 13.5842C15.0922 13.5371 15.0801 13.4924 15.0639 13.449C14.9493 13.0095 14.7714 12.4949 14.5163 12.1461C14.4972 12.1162 14.4755 12.0882 14.4515 12.062C14.4474 12.0571 14.4437 12.0513 14.4396 12.0466L14.4361 12.044C14.3235 11.9295 14.1657 11.8574 13.9902 11.8574C13.6487 11.8574 13.3711 12.1274 13.3711 12.4592C13.3711 12.5224 13.3838 12.582 13.4027 12.6392L13.4003 12.6416C13.4003 12.6416 13.404 12.6494 13.4101 12.6633C13.418 12.6842 13.427 12.7042 13.4369 12.7238C13.5012 12.8723 13.6433 13.2217 13.8104 13.7619C13.8194 13.8174 13.8352 13.8706 13.8583 13.9201C13.9607 14.1403 14.1873 14.2944 14.4515 14.2944C14.5653 14.2944 14.6704 14.2636 14.7635 14.2137C14.9651 14.1059 15.1034 13.8998 15.1034 13.6605C15.1034 13.6348 15.1012 13.6094 15.098 13.5842Z"
					fill="#1D1D1A"
				/>
				<path
					d="M8.44727 18.7949C8.46957 18.8321 8.49034 18.8697 8.51265 18.9069C8.49303 18.8682 8.4715 18.831 8.44727 18.7949Z"
					fill="#E82227"
				/>
				<path
					d="M7.36133 19.3672C7.39017 19.4304 7.42518 19.4902 7.46594 19.5462C7.44286 19.5067 7.42017 19.4668 7.39941 19.4304C7.39633 19.4251 7.38133 19.4005 7.36133 19.3672Z"
					fill="#E82227"
				/>
				<path
					d="M10.4747 8.58594C10.3756 8.58594 10.1957 8.65113 10.1957 8.65113C7.64253 9.39639 5.91907 11.6896 5.94561 14.4708C5.99721 16.2105 6.76396 18.326 7.27188 19.2107C7.29541 19.2578 7.33643 19.3266 7.36169 19.3684C7.37291 19.3871 7.38133 19.4007 7.38327 19.4036C7.39472 19.4243 7.40745 19.4465 7.42041 19.4688C7.53265 19.624 7.71786 19.7263 7.92747 19.7263C8.03562 19.7263 8.13599 19.6969 8.22471 19.6493C8.41683 19.5467 8.54851 19.3505 8.54851 19.1225C8.54851 19.0275 8.52454 18.9391 8.48397 18.8593C8.47145 18.8385 8.45979 18.8174 8.44726 18.7965C7.58446 17.3623 7.14669 15.7795 7.20238 14.0261C7.26714 11.985 8.72638 10.3331 10.5932 9.77784C10.6303 9.77094 10.666 9.75999 10.7005 9.74665C10.7557 9.73156 10.9193 9.62896 10.9351 9.58593C11.0327 9.47965 11.0935 9.3411 11.0935 9.1877C11.0935 8.85595 10.816 8.58594 10.4747 8.58594Z"
					fill="#00AC75"
				/>
				<path
					d="M18.0131 18.7332C17.9289 18.6874 17.8344 18.6594 17.7323 18.6562C17.6973 18.6596 17.6623 18.6617 17.6275 18.6647C14.9748 18.8891 12.8196 17.1574 12.5898 14.5794C12.5501 14.1321 12.5645 13.6095 11.9335 13.6551C11.3517 13.6975 11.3468 14.2666 11.3768 14.7171C11.5704 17.6286 14.0878 19.8792 17.1397 19.911C17.3381 19.911 17.5391 19.8919 17.7401 19.8644C17.7796 19.859 17.8191 19.8536 17.8588 19.8474C17.9117 19.834 17.9622 19.8144 18.0092 19.7891C18.2023 19.686 18.3345 19.4888 18.3345 19.2599C18.3345 19.0328 18.2039 18.8369 18.0131 18.7332Z"
					fill="#1D1D1A"
				/>
			</g>
			<defs>
				<clipPath id="clip0_498_56506">
					<rect width="24" height="23" fill="white" transform="translate(0 0.5)" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const AccessibilityIcon = createIcon({
	displayName: "AccessibilityIcon",
	viewBox: "0 0 40 40",
	path: (
		<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle cx="20" cy="20" r="20" fill="#F2F2F2" />
			<path
				d="M20.0016 13.65C19.5003 13.65 19.0705 13.4715 18.7121 13.1145C18.3538 12.7575 18.1746 12.3283 18.1746 11.827C18.1746 11.3257 18.3531 10.8958 18.7101 10.5375C19.0672 10.1792 19.4963 10 19.9976 10C20.499 10 20.9288 10.1785 21.2871 10.5355C21.6455 10.8925 21.8246 11.3217 21.8246 11.823C21.8246 12.3243 21.6461 12.7542 21.2891 13.1125C20.9321 13.4708 20.503 13.65 20.0016 13.65ZM28.2496 15.925C27.3803 16.1013 26.4838 16.2495 25.5601 16.3697C24.6365 16.4899 23.6746 16.5917 22.6746 16.675V29.275C22.6746 29.475 22.6024 29.6458 22.4578 29.7875C22.3132 29.9292 22.134 30 21.9203 30C21.7065 30 21.5288 29.9281 21.3871 29.7844C21.2455 29.6406 21.1746 29.4625 21.1746 29.25V23.5H18.8246V29.275C18.8246 29.475 18.7524 29.6458 18.6078 29.7875C18.4632 29.9292 18.284 30 18.0703 30C17.8565 30 17.6788 29.9281 17.5371 29.7844C17.3955 29.6406 17.3246 29.4625 17.3246 29.25V16.675C16.3246 16.5917 15.3628 16.4899 14.4391 16.3697C13.5155 16.2495 12.619 16.1013 11.7496 15.925C11.5473 15.8765 11.3925 15.7644 11.2854 15.5886C11.1782 15.4129 11.1496 15.2167 11.1996 15C11.2496 14.8 11.3621 14.6417 11.5371 14.525C11.7121 14.4083 11.908 14.375 12.1246 14.425C13.408 14.7083 14.6913 14.9042 15.9746 15.0125C17.258 15.1208 18.5996 15.175 19.9996 15.175C21.3996 15.175 22.7413 15.1208 24.0246 15.0125C25.308 14.9042 26.5913 14.7083 27.8746 14.425C28.0913 14.375 28.2913 14.4042 28.4746 14.5125C28.658 14.6208 28.7746 14.7833 28.8246 15C28.8746 15.2 28.8413 15.3917 28.7246 15.575C28.608 15.7583 28.4496 15.875 28.2496 15.925Z"
				fill="#384155"
			/>
		</svg>
	),
});

export const ApplyFormerIcon = createIcon({
	displayName: "ApplyFormerIcon",
	viewBox: "0 0 48 48",
	path: (
		<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M30.0504 43.9996C29.2504 43.9996 28.567 43.7163 28.0004 43.1496C27.4337 42.5829 27.1504 41.8996 27.1504 41.0996V38.0496H32.9004V41.1496C32.9004 41.9496 32.6254 42.6246 32.0754 43.1746C31.5254 43.7246 30.8504 43.9996 30.0504 43.9996ZM18.0504 43.9996C17.2504 43.9996 16.567 43.7163 16.0004 43.1496C15.4337 42.5829 15.1504 41.8996 15.1504 41.0996V36.5496H2.85035C2.25035 36.5496 1.80035 36.2829 1.50035 35.7496C1.20035 35.2163 1.23368 34.6996 1.60035 34.1996L9.45035 22.8496H7.60035C7.00035 22.8496 6.55035 22.5829 6.25035 22.0496C5.95035 21.5163 5.98369 20.9996 6.35035 20.4996L16.7504 5.74961C17.0504 5.31628 17.467 5.09961 18.0004 5.09961C18.5337 5.09961 18.9504 5.31628 19.2504 5.74961L29.6504 20.4996C30.017 20.9996 30.0504 21.5163 29.7504 22.0496C29.4504 22.5829 29.0004 22.8496 28.4004 22.8496H26.6004L34.4504 34.1996C34.7504 34.6329 34.767 35.1329 34.5004 35.6996C34.2337 36.2663 33.8004 36.5496 33.2004 36.5496H20.9004V41.1496C20.9004 41.9496 20.6254 42.6246 20.0754 43.1746C19.5254 43.7246 18.8504 43.9996 18.0504 43.9996ZM35.8004 36.5496C35.967 36.2496 36.117 35.8163 36.2504 35.2496C36.3837 34.6829 36.2837 34.1663 35.9504 33.6996L29.3004 24.0996C30.167 23.9329 30.792 23.3496 31.1754 22.3496C31.5587 21.3496 31.467 20.4663 30.9004 19.6996L24.9504 11.1496L28.7504 5.74961C29.0504 5.31628 29.467 5.09961 30.0004 5.09961C30.5337 5.09961 30.9504 5.31628 31.2504 5.74961L41.6504 20.4996C42.017 20.9996 42.0504 21.5163 41.7504 22.0496C41.4504 22.5829 41.0004 22.8496 40.4004 22.8496H38.6004L46.4004 34.1996C46.767 34.6996 46.8004 35.2163 46.5004 35.7496C46.2004 36.2829 45.7504 36.5496 45.1504 36.5496H35.8004Z"
				fill="#B68A35"
			/>
			<path
				d="M30.0502 43.9996C29.2502 43.9996 28.5669 43.7163 28.0002 43.1496C27.4335 42.5829 27.1502 41.8996 27.1502 41.0996V38.0496H32.9002V41.1496C32.9002 41.9496 32.6252 42.6246 32.0752 43.1746C31.5252 43.7246 30.8502 43.9996 30.0502 43.9996ZM35.8002 36.5496C35.9669 36.2496 36.1169 35.8163 36.2502 35.2496C36.3835 34.6829 36.2835 34.1663 35.9502 33.6996L29.3002 24.0996C30.1669 23.9329 30.7919 23.3496 31.1752 22.3496C31.5585 21.3496 31.4669 20.4663 30.9002 19.6996L24.9502 11.1496L28.7502 5.74961C29.0502 5.31628 29.4669 5.09961 30.0002 5.09961C30.5335 5.09961 30.9502 5.31628 31.2502 5.74961L41.6502 20.4996C42.0169 20.9996 42.0502 21.5163 41.7502 22.0496C41.4502 22.5829 41.0002 22.8496 40.4002 22.8496H38.6002L46.4002 34.1996C46.7669 34.6996 46.8002 35.2163 46.5002 35.7496C46.2002 36.2829 45.7502 36.5496 45.1502 36.5496H35.8002Z"
				fill="#E4D5B6"
			/>
		</svg>
	),
});

export const ApplyToWhomIcon = createIcon({
	displayName: "ApplyToWhomIcon",
	viewBox: "0 0 48 48",
	path: (
		<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M34 31.5C32.3333 31.5 30.9167 30.9167 29.75 29.75C28.5833 28.5833 28 27.1667 28 25.5C28 23.8333 28.5833 22.4167 29.75 21.25C30.9167 20.0833 32.3333 19.5 34 19.5C35.6667 19.5 37.0833 20.0833 38.25 21.25C39.4167 22.4167 40 23.8333 40 25.5C40 27.1667 39.4167 28.5833 38.25 29.75C37.0833 30.9167 35.6667 31.5 34 31.5ZM19 39.7V42H9C8.175 42 7.46875 41.7062 6.88125 41.1188C6.29375 40.5312 6 39.825 6 39V9C6 8.175 6.29375 7.46875 6.88125 6.88125C7.46875 6.29375 8.175 6 9 6H39C39.825 6 40.5312 6.29375 41.1188 6.88125C41.7062 7.46875 42 8.175 42 9V21.35C41.2333 19.85 40.1333 18.6667 38.7 17.8C37.2667 16.9333 35.7 16.5 34 16.5V14H15.5C15.075 14 14.7188 14.1446 14.4313 14.4337C14.1438 14.7229 14 15.0813 14 15.5087C14 15.9362 14.1438 16.2917 14.4313 16.575C14.7188 16.8583 15.075 17 15.5 17H31.05C29.7833 17.4667 28.6583 18.1833 27.675 19.15C26.6917 20.1167 25.9667 21.2333 25.5 22.5H15.5C15.075 22.5 14.7188 22.6446 14.4313 22.9338C14.1438 23.2229 14 23.5813 14 24.0087C14 24.4362 14.1438 24.7917 14.4313 25.075C14.7188 25.3583 15.075 25.5 15.5 25.5H25C25 26.5333 25.1667 27.5167 25.5 28.45C25.8333 29.3833 26.3 30.2333 26.9 31H15.5C15.075 31 14.7188 31.1446 14.4313 31.4338C14.1438 31.7229 14 32.0813 14 32.5087C14 32.9363 14.1438 33.2917 14.4313 33.575C14.7188 33.8583 15.075 34 15.5 34H21.85C20.95 34.6667 20.25 35.5083 19.75 36.525C19.25 37.5417 19 38.6 19 39.7ZM23.5 45.5C23.075 45.5 22.7188 45.3563 22.4312 45.0688C22.1438 44.7812 22 44.425 22 44V39.7C22 39 22.1667 38.3417 22.5 37.725C22.8333 37.1083 23.3 36.6167 23.9 36.25C24.8333 35.6833 25.625 35.2667 26.275 35C26.925 34.7333 27.7833 34.4667 28.85 34.2C29.1722 34.1333 29.4945 34.125 29.8167 34.175C30.1389 34.225 30.4 34.3833 30.6 34.65L34 39.05L37.4 34.65C37.6083 34.3881 37.8687 34.231 38.1813 34.1786C38.4938 34.1262 38.8167 34.1333 39.15 34.2C40.2167 34.4667 41.0667 34.7333 41.7 35C42.3333 35.2667 43.1167 35.6833 44.05 36.25C44.65 36.6167 45.125 37.1083 45.475 37.725C45.825 38.3417 46 39 46 39.7V44C46 44.425 45.8563 44.7812 45.5688 45.0688C45.2812 45.3563 44.925 45.5 44.5 45.5H23.5Z"
				fill="#E4D5B6"
			/>
			<path
				d="M19 39.7V42H9C8.175 42 7.46875 41.7063 6.88125 41.1188C6.29375 40.5312 6 39.825 6 39V9C6 8.175 6.29375 7.46875 6.88125 6.88125C7.46875 6.29375 8.175 6 9 6H39C39.825 6 40.5312 6.29375 41.1188 6.88125C41.7063 7.46875 42 8.175 42 9V21.35C41.2333 19.85 40.1333 18.6667 38.7 17.8C37.2667 16.9333 35.7 16.5 34 16.5V14H15.5C15.075 14 14.7188 14.1446 14.4313 14.4337C14.1438 14.7229 14 15.0813 14 15.5087C14 15.9362 14.1438 16.2917 14.4313 16.575C14.7188 16.8583 15.075 17 15.5 17H31.05C29.7833 17.4667 28.6583 18.1833 27.675 19.15C26.6917 20.1167 25.9667 21.2333 25.5 22.5H15.5C15.075 22.5 14.7188 22.6446 14.4313 22.9338C14.1438 23.2229 14 23.5813 14 24.0087C14 24.4362 14.1438 24.7917 14.4313 25.075C14.7188 25.3583 15.075 25.5 15.5 25.5H25C25 26.5333 25.1667 27.5167 25.5 28.45C25.8333 29.3833 26.3 30.2333 26.9 31H15.5C15.075 31 14.7188 31.1446 14.4313 31.4338C14.1438 31.7229 14 32.0813 14 32.5087C14 32.9362 14.1438 33.2917 14.4313 33.575C14.7188 33.8583 15.075 34 15.5 34H21.85C20.95 34.6667 20.25 35.5083 19.75 36.525C19.25 37.5417 19 38.6 19 39.7Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const ButtonArrowIcon = createIcon({
	displayName: "ButtonArrowIcon",
	viewBox: "0 0 24 24",
	path: (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M17.125 12.7016H4.75C4.53333 12.7016 4.35417 12.6307 4.2125 12.4891C4.07083 12.3474 4 12.1682 4 11.9516C4 11.7349 4.07083 11.5557 4.2125 11.4141C4.35417 11.2724 4.53333 11.2016 4.75 11.2016H17.125L13.425 7.50156C13.275 7.35156 13.2042 7.17656 13.2125 6.97656C13.2208 6.77656 13.3 6.60156 13.45 6.45156C13.6 6.30156 13.775 6.22656 13.975 6.22656C14.175 6.22656 14.35 6.30156 14.5 6.45156L19.475 11.4266C19.5583 11.5099 19.6167 11.5932 19.65 11.6766C19.6833 11.7599 19.7 11.8516 19.7 11.9516C19.7 12.0516 19.6833 12.1432 19.65 12.2266C19.6167 12.3099 19.5583 12.3932 19.475 12.4766L14.525 17.4266C14.375 17.5766 14.2 17.6516 14 17.6516C13.8 17.6516 13.625 17.5766 13.475 17.4266C13.325 17.2766 13.25 17.0974 13.25 16.8891C13.25 16.6807 13.325 16.5016 13.475 16.3516L17.125 12.7016Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const ArticleIcon = createIcon({
	displayName: "ArticleIcon",
	viewBox: "0 0 24 24",
	path: (
		<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M7.675 17.025H13.05C13.2625 17.025 13.4406 16.9527 13.5844 16.8081C13.7281 16.6635 13.8 16.4844 13.8 16.2706C13.8 16.0569 13.7281 15.8792 13.5844 15.7375C13.4406 15.5958 13.2625 15.525 13.05 15.525H7.675C7.4625 15.525 7.28438 15.5973 7.14062 15.7419C6.99688 15.8865 6.925 16.0656 6.925 16.2794C6.925 16.4931 6.99688 16.6708 7.14062 16.8125C7.28438 16.9542 7.4625 17.025 7.675 17.025ZM7.675 12.75H16.325C16.5375 12.75 16.7156 12.6777 16.8594 12.5331C17.0031 12.3885 17.075 12.2094 17.075 11.9956C17.075 11.7819 17.0031 11.6042 16.8594 11.4625C16.7156 11.3208 16.5375 11.25 16.325 11.25H7.675C7.4625 11.25 7.28438 11.3223 7.14062 11.4669C6.99688 11.6115 6.925 11.7906 6.925 12.0044C6.925 12.2181 6.99688 12.3958 7.14062 12.5375C7.28438 12.6792 7.4625 12.75 7.675 12.75ZM7.675 8.475H16.325C16.5375 8.475 16.7156 8.40271 16.8594 8.25813C17.0031 8.11353 17.075 7.93436 17.075 7.72063C17.075 7.50688 17.0031 7.32917 16.8594 7.1875C16.7156 7.04583 16.5375 6.975 16.325 6.975H7.675C7.4625 6.975 7.28438 7.04729 7.14062 7.19187C6.99688 7.33647 6.925 7.51564 6.925 7.72937C6.925 7.94312 6.99688 8.12083 7.14062 8.2625C7.28438 8.40417 7.4625 8.475 7.675 8.475ZM4.5 21C4.1 21 3.75 20.85 3.45 20.55C3.15 20.25 3 19.9 3 19.5V4.5C3 4.1 3.15 3.75 3.45 3.45C3.75 3.15 4.1 3 4.5 3H19.5C19.9 3 20.25 3.15 20.55 3.45C20.85 3.75 21 4.1 21 4.5V19.5C21 19.9 20.85 20.25 20.55 20.55C20.25 20.85 19.9 21 19.5 21H4.5Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const PersonCheckIcon = createIcon({
	displayName: "Person check",
	viewBox: "0 0 24 24",
	path: (
		<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M2.75 19.9992C2.5375 19.9992 2.35938 19.9273 2.21563 19.7836C2.07187 19.6398 2 19.4617 2 19.2492V17.6492C2 17.0826 2.14167 16.5617 2.425 16.0867C2.70833 15.6117 3.13333 15.2492 3.7 14.9992C4.9 14.4659 6 14.0826 7 13.8492C8 13.6159 9 13.4992 10 13.4992C10.4833 13.4992 10.9958 13.5284 11.5375 13.5867C12.0792 13.6451 12.6333 13.7492 13.2 13.8992L11.975 15.1242C11.6417 15.0909 11.3125 15.0617 10.9875 15.0367C10.6625 15.0117 10.3333 14.9992 10 14.9992C9.03333 14.9992 8.15417 15.0867 7.3625 15.2617C6.57083 15.4367 5.55 15.7992 4.3 16.3492C4.01667 16.4826 3.8125 16.6742 3.6875 16.9242C3.5625 17.1742 3.5 17.4159 3.5 17.6492V18.4992H11.475L12.975 19.9992H2.75ZM15.6217 20.0992C15.5239 20.0992 15.4333 20.0826 15.35 20.0492C15.2667 20.0159 15.1833 19.9576 15.1 19.8742L12.625 17.3992C12.475 17.2492 12.4 17.0742 12.4 16.8742C12.4 16.6742 12.475 16.4992 12.625 16.3492C12.775 16.1992 12.95 16.1242 13.15 16.1242C13.35 16.1242 13.525 16.1992 13.675 16.3492L15.625 18.2992L20.425 13.4992C20.575 13.3492 20.75 13.2742 20.95 13.2742C21.15 13.2742 21.325 13.3492 21.475 13.4992C21.625 13.6492 21.7 13.8242 21.7 14.0242C21.7 14.2242 21.625 14.3992 21.475 14.5492L16.15 19.8742C16.0667 19.9576 15.9822 20.0159 15.8967 20.0492C15.8111 20.0826 15.7194 20.0992 15.6217 20.0992ZM10 11.9492C8.9 11.9492 8 11.5992 7.3 10.8992C6.6 10.1992 6.25 9.29922 6.25 8.19922C6.25 7.09922 6.6 6.19922 7.3 5.49922C8 4.79922 8.9 4.44922 10 4.44922C11.1 4.44922 12 4.79922 12.7 5.49922C13.4 6.19922 13.75 7.09922 13.75 8.19922C13.75 9.29922 13.4 10.1992 12.7 10.8992C12 11.5992 11.1 11.9492 10 11.9492ZM10 10.4492C10.65 10.4492 11.1875 10.2367 11.6125 9.81172C12.0375 9.38672 12.25 8.84922 12.25 8.19922C12.25 7.54922 12.0375 7.01172 11.6125 6.58672C11.1875 6.16172 10.65 5.94922 10 5.94922C9.35 5.94922 8.8125 6.16172 8.3875 6.58672C7.9625 7.01172 7.75 7.54922 7.75 8.19922C7.75 8.84922 7.9625 9.38672 8.3875 9.81172C8.8125 10.2367 9.35 10.4492 10 10.4492Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const RequirmentIcom = createIcon({
	displayName: "RequirmentIcom",
	viewBox: "0 0 24 24",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path
				d="M6.125 15.75C5.9125 15.75 5.73438 15.6777 5.59063 15.5331C5.44688 15.3885 5.375 15.2094 5.375 14.9956C5.375 14.7819 5.44688 14.6042 5.59063 14.4625C5.73438 14.3208 5.9125 14.25 6.125 14.25H8.65C8.8625 14.25 9.04063 14.3223 9.18438 14.4669C9.32813 14.6115 9.4 14.7906 9.4 15.0044C9.4 15.2181 9.32813 15.3958 9.18438 15.5375C9.04063 15.6792 8.8625 15.75 8.65 15.75H6.125ZM6.125 11.625C5.9125 11.625 5.73438 11.5527 5.59063 11.4081C5.44688 11.2635 5.375 11.0844 5.375 10.8706C5.375 10.6569 5.44688 10.4792 5.59063 10.3375C5.73438 10.1958 5.9125 10.125 6.125 10.125H18.15C18.3625 10.125 18.5406 10.1973 18.6844 10.3419C18.8281 10.4865 18.9 10.6656 18.9 10.8794C18.9 11.0931 18.8281 11.2708 18.6844 11.4125C18.5406 11.5542 18.3625 11.625 18.15 11.625H6.125ZM6.125 7.5C5.9125 7.5 5.73438 7.42771 5.59063 7.28313C5.44688 7.13853 5.375 6.95936 5.375 6.74563C5.375 6.53188 5.44688 6.35417 5.59063 6.2125C5.73438 6.07083 5.9125 6 6.125 6H18.15C18.3625 6 18.5406 6.07229 18.6844 6.21687C18.8281 6.36147 18.9 6.54064 18.9 6.75437C18.9 6.96812 18.8281 7.14583 18.6844 7.2875C18.5406 7.42917 18.3625 7.5 18.15 7.5H6.125ZM15.55 17.85L20.9 12.5C21.05 12.35 21.225 12.275 21.425 12.275C21.625 12.275 21.8033 12.35 21.9598 12.5C22.1033 12.65 22.175 12.8292 22.175 13.0375C22.175 13.2458 22.1 13.425 21.95 13.575L16.05 19.475C15.9 19.625 15.725 19.6958 15.525 19.6875C15.325 19.6792 15.15 19.6 15 19.45L11.925 16.325C11.775 16.175 11.7 16 11.7 15.8C11.7 15.6 11.7768 15.4217 11.9304 15.2652C12.0711 15.1217 12.2471 15.05 12.4583 15.05C12.6694 15.05 12.85 15.125 13 15.275L15.55 17.85ZM2.24563 7.5C2.03187 7.5 1.85417 7.42771 1.7125 7.28313C1.57083 7.13853 1.5 6.95936 1.5 6.74563C1.5 6.53188 1.57229 6.35417 1.71688 6.2125C1.86148 6.07083 2.04064 6 2.25438 6C2.46813 6 2.64583 6.07229 2.7875 6.21687C2.92917 6.36147 3 6.54064 3 6.75437C3 6.96812 2.92771 7.14583 2.78313 7.2875C2.63853 7.42917 2.45936 7.5 2.24563 7.5ZM2.24563 11.625C2.03187 11.625 1.85417 11.5527 1.7125 11.4081C1.57083 11.2635 1.5 11.0844 1.5 10.8706C1.5 10.6569 1.57229 10.4792 1.71688 10.3375C1.86148 10.1958 2.04064 10.125 2.25438 10.125C2.46813 10.125 2.64583 10.1973 2.7875 10.3419C2.92917 10.4865 3 10.6656 3 10.8794C3 11.0931 2.92771 11.2708 2.78313 11.4125C2.63853 11.5542 2.45936 11.625 2.24563 11.625ZM2.24563 15.75C2.03187 15.75 1.85417 15.6777 1.7125 15.5331C1.57083 15.3885 1.5 15.2094 1.5 14.9956C1.5 14.7819 1.57229 14.6042 1.71688 14.4625C1.86148 14.3208 2.04064 14.25 2.25438 14.25C2.46813 14.25 2.64583 14.3223 2.7875 14.4669C2.92917 14.6115 3 14.7906 3 15.0044C3 15.2181 2.92771 15.3958 2.78313 15.5375C2.63853 15.6792 2.45936 15.75 2.24563 15.75Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const PeopleGroupIcon = createIcon({
	displayName: "PeopleGroupIcon",
	viewBox: "0 0 24 24",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<g clip-path="url(#clip0_615_55459)">
				<path
					d="M0.75 18C0.5375 18 0.359375 17.9281 0.215625 17.7844C0.071875 17.6406 0 17.4625 0 17.25V16.675C0 16.0173 0.34375 15.4903 1.03125 15.0942C1.71875 14.6981 2.625 14.5 3.75 14.5C3.95 14.5 4.14583 14.5063 4.3375 14.5188C4.52917 14.5312 4.71667 14.55 4.9 14.575C4.76667 14.8583 4.66667 15.1491 4.6 15.4472C4.53333 15.7454 4.5 16.063 4.5 16.4V18H0.75ZM6.75 18C6.5375 18 6.35938 17.9281 6.21563 17.7844C6.07188 17.6406 6 17.4625 6 17.25V16.4C6 15.3167 6.55417 14.4375 7.6625 13.7625C8.77083 13.0875 10.2167 12.75 12 12.75C13.8 12.75 15.25 13.0875 16.35 13.7625C17.45 14.4375 18 15.3167 18 16.4V17.25C18 17.4625 17.9281 17.6406 17.7844 17.7844C17.6406 17.9281 17.4625 18 17.25 18H6.75ZM19.5 18V16.4C19.5 16.063 19.4667 15.7454 19.4 15.4472C19.3333 15.1491 19.2333 14.8561 19.1 14.5682C19.2833 14.5394 19.4708 14.5208 19.6625 14.5125C19.8542 14.5042 20.0507 14.5 20.2521 14.5C21.3673 14.5 22.2708 14.6981 22.9625 15.0942C23.6542 15.4903 24 16.0173 24 16.675V17.25C24 17.4625 23.9281 17.6406 23.7844 17.7844C23.6406 17.9281 23.4625 18 23.25 18H19.5ZM12 14.25C10.65 14.25 9.5625 14.4542 8.7375 14.8625C7.9125 15.2708 7.5 15.7833 7.5 16.4V16.5H16.5V16.375C16.5 15.775 16.0875 15.2708 15.2625 14.8625C14.4375 14.4542 13.35 14.25 12 14.25ZM3.74777 13.75C3.26592 13.75 2.85417 13.5784 2.5125 13.2353C2.17083 12.8921 2 12.4796 2 11.9978C2 11.5159 2.17158 11.1042 2.51472 10.7625C2.85786 10.4208 3.27036 10.25 3.75223 10.25C4.23408 10.25 4.64583 10.4216 4.9875 10.7647C5.32917 11.1079 5.5 11.5204 5.5 12.0022C5.5 12.4841 5.32842 12.8958 4.98527 13.2375C4.64214 13.5792 4.22964 13.75 3.74777 13.75ZM20.2478 13.75C19.7659 13.75 19.3542 13.5784 19.0125 13.2353C18.6708 12.8921 18.5 12.4796 18.5 11.9978C18.5 11.5159 18.6716 11.1042 19.0147 10.7625C19.3579 10.4208 19.7704 10.25 20.2522 10.25C20.7341 10.25 21.1458 10.4216 21.4875 10.7647C21.8292 11.1079 22 11.5204 22 12.0022C22 12.4841 21.8284 12.8958 21.4853 13.2375C21.1421 13.5792 20.7296 13.75 20.2478 13.75ZM12 12C11.1667 12 10.4583 11.7083 9.875 11.125C9.29167 10.5417 9 9.83333 9 9C9 8.16667 9.29167 7.45833 9.875 6.875C10.4583 6.29167 11.1667 6 12 6C12.8333 6 13.5417 6.29167 14.125 6.875C14.7083 7.45833 15 8.16667 15 9C15 9.83333 14.7083 10.5417 14.125 11.125C13.5417 11.7083 12.8333 12 12 12ZM12 7.5C11.575 7.5 11.2188 7.64375 10.9313 7.93125C10.6438 8.21875 10.5 8.575 10.5 9C10.5 9.425 10.6438 9.78125 10.9313 10.0687C11.2188 10.3563 11.575 10.5 12 10.5C12.425 10.5 12.7812 10.3563 13.0688 10.0687C13.3562 9.78125 13.5 9.425 13.5 9C13.5 8.575 13.3562 8.21875 13.0688 7.93125C12.7812 7.64375 12.425 7.5 12 7.5Z"
					fill="currentColor"
				/>
			</g>
			<defs>
				<clipPath id="clip0_615_55459">
					<rect width="24" height="24" fill="currentColor" />
				</clipPath>
			</defs>
		</svg>
	),
});

export const ApplyingTimeIcon = createIcon({
	displayName: "ApplyingTimeIcon",
	viewBox: "0 0 24 24",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path
				d="M17.275 22C15.9677 22 14.8534 21.5393 13.932 20.6179C13.0107 19.6966 12.55 18.5823 12.55 17.275C12.55 15.9678 13.0107 14.8534 13.932 13.9321C14.8534 13.0107 15.9677 12.55 17.275 12.55C18.5822 12.55 19.6966 13.0107 20.6179 13.9321C21.5393 14.8534 22 15.9678 22 17.275C22 18.5823 21.5393 19.6966 20.6179 20.6179C19.6966 21.5393 18.5822 22 17.275 22ZM17.55 17.2132V14.901C17.55 14.767 17.5025 14.6542 17.4075 14.5625C17.3125 14.4708 17.2017 14.425 17.075 14.425C16.9417 14.425 16.825 14.475 16.725 14.575C16.625 14.675 16.575 14.7917 16.575 14.925V17.275C16.575 17.3678 16.5917 17.4576 16.625 17.5446C16.6583 17.6315 16.7083 17.7167 16.775 17.8L18.375 19.45C18.475 19.55 18.5917 19.6 18.725 19.6C18.8583 19.6 18.975 19.55 19.075 19.45C19.175 19.3495 19.225 19.2322 19.225 19.0982C19.225 18.9641 19.175 18.8468 19.075 18.7463L17.55 17.2132ZM4.5 21C4.0875 21 3.73437 20.8531 3.44062 20.5594C3.14687 20.2656 3 19.9125 3 19.5V4.5C3 4.06667 3.14167 3.70833 3.425 3.425C3.70833 3.14167 4.06667 3 4.5 3H9.55C9.66667 2.41667 9.95417 1.9375 10.4125 1.5625C10.8708 1.1875 11.4 1 12 1C12.6 1 13.1292 1.1875 13.5875 1.5625C14.0458 1.9375 14.3333 2.41667 14.45 3H19.5C19.9333 3 20.2917 3.14167 20.575 3.425C20.8583 3.70833 21 4.06667 21 4.5V12.2C20.75 12.05 20.504 11.921 20.2621 11.813C20.0202 11.705 19.7661 11.609 19.5 11.525V4.5H18V6C18 6.2125 17.9281 6.39062 17.7844 6.53438C17.6406 6.67812 17.4625 6.75 17.25 6.75H6.75C6.5375 6.75 6.35937 6.67812 6.21562 6.53438C6.07187 6.39062 6 6.2125 6 6V4.5H4.5V19.5H11.5C11.5833 19.75 11.6833 19.9958 11.8 20.2375C11.9167 20.4792 12.0583 20.7333 12.225 21H4.5ZM12 4.5C12.2833 4.5 12.5208 4.40417 12.7125 4.2125C12.9042 4.02083 13 3.78333 13 3.5C13 3.21667 12.9042 2.97917 12.7125 2.7875C12.5208 2.59583 12.2833 2.5 12 2.5C11.7167 2.5 11.4792 2.59583 11.2875 2.7875C11.0958 2.97917 11 3.21667 11 3.5C11 3.78333 11.0958 4.02083 11.2875 4.2125C11.4792 4.40417 11.7167 4.5 12 4.5Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const RequestTimeIcon = createIcon({
	displayName: "RequestTimeIcon",
	viewBox: "0 0 24 24",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path
				d="M11 11.5C9.96667 11.5 9.08333 11.1333 8.35 10.4C7.61667 9.66667 7.25 8.78333 7.25 7.75C7.25 6.7 7.61667 5.8125 8.35 5.0875C9.08333 4.3625 9.96667 4 11 4C12.05 4 12.9375 4.3625 13.6625 5.0875C14.3875 5.8125 14.75 6.7 14.75 7.75C14.75 8.78333 14.3875 9.66667 13.6625 10.4C12.9375 11.1333 12.05 11.5 11 11.5ZM21.925 22.975L18.95 20C18.5667 20.2833 18.1708 20.4792 17.7625 20.5875C17.3542 20.6958 16.9333 20.75 16.5 20.75C15.3167 20.75 14.3125 20.3375 13.4875 19.5125C12.6625 18.6875 12.25 17.6833 12.25 16.5C12.25 15.3167 12.6625 14.3125 13.4875 13.4875C14.3125 12.6625 15.3167 12.25 16.5 12.25C17.6833 12.25 18.6875 12.6625 19.5125 13.4875C20.3375 14.3125 20.75 15.3167 20.75 16.5C20.75 16.9333 20.6958 17.3542 20.5875 17.7625C20.4792 18.1708 20.2833 18.5667 20 18.95L22.975 21.925C23.125 22.075 23.2 22.25 23.2 22.45C23.2 22.65 23.125 22.825 22.975 22.975C22.825 23.125 22.65 23.2 22.45 23.2C22.25 23.2 22.075 23.125 21.925 22.975ZM16.5 19.25C17.2833 19.25 17.9375 18.9875 18.4625 18.4625C18.9875 17.9375 19.25 17.2833 19.25 16.5C19.25 15.7167 18.9875 15.0625 18.4625 14.5375C17.9375 14.0125 17.2833 13.75 16.5 13.75C15.7167 13.75 15.0625 14.0125 14.5375 14.5375C14.0125 15.0625 13.75 15.7167 13.75 16.5C13.75 17.2833 14.0125 17.9375 14.5375 18.4625C15.0625 18.9875 15.7167 19.25 16.5 19.25ZM4.5 20C4.08333 20 3.72917 19.8542 3.4375 19.5625C3.14583 19.2708 3 18.9167 3 18.5V17.65C3 17.0333 3.14583 16.5083 3.4375 16.075C3.72917 15.6417 4.15 15.2833 4.7 15C5.48333 14.6167 6.50417 14.275 7.7625 13.975C9.02083 13.675 10.3 13.525 11.6 13.525C11.3167 13.9583 11.1042 14.425 10.9625 14.925C10.8208 15.425 10.75 15.9333 10.75 16.45C10.75 17.0833 10.8542 17.7042 11.0625 18.3125C11.2708 18.9208 11.5667 19.4833 11.95 20H4.5Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const FirstStepICon = createIcon({
	displayName: "FirstStepICon",
	viewBox: "0 0 36 36",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
			<path
				d="M6.75 31.5004C6.15 31.5004 5.625 31.2754 5.175 30.8254C4.725 30.3754 4.5 29.8504 4.5 29.2504V6.75039C4.5 6.15039 4.725 5.62539 5.175 5.17539C5.625 4.72539 6.15 4.50039 6.75 4.50039H14.5125C14.4625 3.57539 14.7875 2.74414 15.4875 2.00664C16.1875 1.26914 17.025 0.900391 18 0.900391C18.975 0.900391 19.8125 1.26914 20.5125 2.00664C21.2125 2.74414 21.5375 3.57539 21.4875 4.50039H29.25C29.85 4.50039 30.375 4.72539 30.825 5.17539C31.275 5.62539 31.5 6.15039 31.5 6.75039V29.2504C31.5 29.8504 31.275 30.3754 30.825 30.8254C30.375 31.2754 29.85 31.5004 29.25 31.5004H6.75ZM18 5.17539C18.325 5.17539 18.5938 5.06914 18.8063 4.85664C19.0188 4.64414 19.125 4.37539 19.125 4.05039C19.125 3.72539 19.0188 3.45664 18.8063 3.24414C18.5938 3.03164 18.325 2.92539 18 2.92539C17.675 2.92539 17.4063 3.03164 17.1938 3.24414C16.9813 3.45664 16.875 3.72539 16.875 4.05039C16.875 4.37539 16.9813 4.64414 17.1938 4.85664C17.4063 5.06914 17.675 5.17539 18 5.17539ZM18.075 20.9629C19.525 20.9629 20.75 20.4629 21.75 19.4629C22.75 18.4629 23.25 17.2379 23.25 15.7879C23.25 14.3379 22.75 13.1129 21.75 12.1129C20.75 11.1129 19.525 10.6129 18.075 10.6129C16.625 10.6129 15.4 11.1129 14.4 12.1129C13.4 13.1129 12.9 14.3379 12.9 15.7879C12.9 17.2379 13.4 18.4629 14.4 19.4629C15.4 20.4629 16.625 20.9629 18.075 20.9629ZM6.75 29.2504H29.25V27.8629C27.75 26.4629 26.05 25.3317 24.15 24.4692C22.25 23.6067 20.2 23.1754 18 23.1754C15.8 23.1754 13.75 23.6067 11.85 24.4692C9.95 25.3317 8.25 26.4629 6.75 27.8629V29.2504Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const SecondStepIcon = createIcon({
	displayName: "SecondStepIcon",
	viewBox: "0 0 36 36",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
			<path
				d="M13.0875 26.625H22.9125C23.2313 26.625 23.4984 26.5166 23.7141 26.2997C23.9297 26.0828 24.0375 25.814 24.0375 25.4934C24.0375 25.1728 23.9297 24.9062 23.7141 24.6938C23.4984 24.4813 23.2313 24.375 22.9125 24.375H13.0875C12.7688 24.375 12.5016 24.4834 12.2859 24.7003C12.0703 24.9172 11.9625 25.186 11.9625 25.5066C11.9625 25.8272 12.0703 26.0938 12.2859 26.3063C12.5016 26.5188 12.7688 26.625 13.0875 26.625ZM13.0875 20.25H22.9125C23.2313 20.25 23.4984 20.1416 23.7141 19.9247C23.9297 19.7078 24.0375 19.439 24.0375 19.1184C24.0375 18.7978 23.9297 18.5313 23.7141 18.3188C23.4984 18.1063 23.2313 18 22.9125 18H13.0875C12.7688 18 12.5016 18.1084 12.2859 18.3253C12.0703 18.5422 11.9625 18.811 11.9625 19.1316C11.9625 19.4522 12.0703 19.7188 12.2859 19.9313C12.5016 20.1438 12.7688 20.25 13.0875 20.25ZM8.25 33C7.65 33 7.125 32.775 6.675 32.325C6.225 31.875 6 31.35 6 30.75V5.25C6 4.65 6.225 4.125 6.675 3.675C7.125 3.225 7.65 3 8.25 3H20.85C21.1611 3 21.4576 3.0625 21.7396 3.1875C22.0215 3.3125 22.2625 3.475 22.4625 3.675L29.325 10.5375C29.525 10.7375 29.6875 10.9785 29.8125 11.2604C29.9375 11.5424 30 11.8389 30 12.15V30.75C30 31.35 29.775 31.875 29.325 32.325C28.875 32.775 28.35 33 27.75 33H8.25ZM20.6625 11.1C20.6625 11.4188 20.7703 11.6859 20.9859 11.9016C21.2016 12.1172 21.4688 12.225 21.7875 12.225H27.75L20.6625 5.25V11.1Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const ThirdStepIcon = createIcon({
	displayName: "ThirdStepIcon",
	viewBox: "0 0 36 36",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
			<path
				d="M8.25 33C7.65 33 7.125 32.775 6.675 32.325C6.225 31.875 6 31.35 6 30.75V5.25C6 4.65 6.225 4.125 6.675 3.675C7.125 3.225 7.65 3 8.25 3H20.85C21.15 3 21.4438 3.0625 21.7313 3.1875C22.0188 3.3125 22.2625 3.475 22.4625 3.675L29.325 10.5375C29.525 10.7375 29.6875 10.9813 29.8125 11.2688C29.9375 11.5563 30 11.85 30 12.15V30.75C30 31.35 29.775 31.875 29.325 32.325C28.875 32.775 28.35 33 27.75 33H8.25ZM20.6625 11.1C20.6625 11.425 20.7688 11.6938 20.9813 11.9062C21.1938 12.1188 21.4625 12.225 21.7875 12.225H27.75L20.6625 5.25V11.1ZM16.8 24.975C17.3 24.975 17.8063 24.9062 18.3188 24.7688C18.8313 24.6313 19.325 24.4125 19.8 24.1125L23.175 27.4875C23.375 27.6875 23.625 27.7875 23.925 27.7875C24.225 27.7875 24.475 27.6875 24.675 27.4875C24.875 27.2875 24.975 27.0375 24.975 26.7375C24.975 26.4375 24.875 26.1875 24.675 25.9875L21.2625 22.575C21.5375 22.15 21.7313 21.7 21.8438 21.225C21.9563 20.75 22.0125 20.2625 22.0125 19.7625C22.0125 18.2875 21.5125 17.05 20.5125 16.05C19.5125 15.05 18.275 14.55 16.8 14.55C15.325 14.55 14.0875 15.05 13.0875 16.05C12.0875 17.05 11.5875 18.2875 11.5875 19.7625C11.5875 21.2375 12.0875 22.475 13.0875 23.475C14.0875 24.475 15.325 24.975 16.8 24.975ZM16.8 22.725C15.95 22.725 15.2438 22.4438 14.6813 21.8813C14.1188 21.3188 13.8375 20.6125 13.8375 19.7625C13.8375 18.9125 14.1188 18.2063 14.6813 17.6438C15.2438 17.0813 15.95 16.8 16.8 16.8C17.65 16.8 18.3563 17.0813 18.9188 17.6438C19.4813 18.2063 19.7625 18.9125 19.7625 19.7625C19.7625 20.6125 19.4813 21.3188 18.9188 21.8813C18.3563 22.4438 17.65 22.725 16.8 22.725Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const ForthStepIcon = createIcon({
	displayName: "PeopleGroupIcon",
	viewBox: "0 0 36 36",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
			<path
				d="M5.25 30C4.65 30 4.125 29.775 3.675 29.325C3.225 28.875 3 28.35 3 27.75V8.25C3 7.65 3.225 7.125 3.675 6.675C4.125 6.225 4.65 6 5.25 6H30.75C31.35 6 31.875 6.225 32.325 6.675C32.775 7.125 33 7.65 33 8.25V27.75C33 28.35 32.775 28.875 32.325 29.325C31.875 29.775 31.35 30 30.75 30H5.25ZM18 18.45C18.125 18.45 18.2313 18.4313 18.3188 18.3938C18.4063 18.3563 18.5 18.3125 18.6 18.2625L30.375 10.575C30.5 10.5 30.5938 10.4 30.6563 10.275C30.7188 10.15 30.75 10.0125 30.75 9.8625C30.75 9.5375 30.6063 9.28125 30.3188 9.09375C30.0313 8.90625 29.7375 8.9125 29.4375 9.1125L18 16.425L6.6 9.1125C6.3 8.9125 6 8.9 5.7 9.075C5.4 9.25 5.25 9.5 5.25 9.825C5.25 9.975 5.2875 10.1188 5.3625 10.2563C5.4375 10.3938 5.525 10.5 5.625 10.575L17.4 18.2625C17.5 18.3125 17.5938 18.3563 17.6813 18.3938C17.7688 18.4313 17.875 18.45 18 18.45Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const GoalOneIcon = createIcon({
	displayName: "GoalOneIcon",
	viewBox: "0 0 60 60",
	path: (
		<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="60" height="60" rx="10" fill="#B68A35" />
			<path
				d="M38.003 21.7651C37.3345 21.7651 36.7614 21.5271 36.2837 21.0511C35.8059 20.5751 35.567 20.0029 35.567 19.3344C35.567 18.666 35.805 18.0929 36.281 17.6151C36.757 17.1373 37.3292 16.8984 37.9977 16.8984C38.6661 16.8984 39.2392 17.1364 39.717 17.6124C40.1948 18.0885 40.4337 18.6607 40.4337 19.3291C40.4337 19.9975 40.1957 20.5707 39.7197 21.0484C39.2436 21.5262 38.6714 21.7651 38.003 21.7651ZM36.0003 43.3318V32.1318C36.0003 31.4651 35.8337 30.8651 35.5003 30.3318C35.167 29.7984 34.6781 29.4096 34.0337 29.1651L35.4003 25.1651C35.5781 24.6095 35.9059 24.1651 36.3837 23.8318C36.8614 23.4984 37.4003 23.3318 38.0003 23.3318C38.6003 23.3318 39.1392 23.4984 39.617 23.8318C40.0948 24.1651 40.4225 24.6095 40.6003 25.1651L43.5337 33.6651C43.6448 33.9762 43.6059 34.2762 43.417 34.5651C43.2281 34.854 42.9559 34.9984 42.6003 34.9984H40.3337V42.3318C40.3337 42.6151 40.2378 42.8526 40.0462 43.0443C39.8545 43.2359 39.617 43.3318 39.3337 43.3318H36.0003ZM30.5003 29.3318C29.9448 29.3318 29.4725 29.1373 29.0837 28.7484C28.6948 28.3596 28.5003 27.8873 28.5003 27.3318C28.5003 26.7762 28.6948 26.304 29.0837 25.9151C29.4725 25.5262 29.9448 25.3318 30.5003 25.3318C31.0559 25.3318 31.5281 25.5262 31.917 25.9151C32.3059 26.304 32.5003 26.7762 32.5003 27.3318C32.5003 27.8873 32.3059 28.3596 31.917 28.7484C31.5281 29.1373 31.0559 29.3318 30.5003 29.3318ZM21.3363 21.7651C20.6679 21.7651 20.0948 21.5271 19.617 21.0511C19.1392 20.5751 18.9003 20.0029 18.9003 19.3344C18.9003 18.666 19.1383 18.0929 19.6143 17.6151C20.0903 17.1373 20.6626 16.8984 21.331 16.8984C21.9994 16.8984 22.5725 17.1364 23.0503 17.6124C23.5281 18.0885 23.767 18.6607 23.767 19.3291C23.767 19.9975 23.529 20.5707 23.053 21.0484C22.577 21.5262 22.0047 21.7651 21.3363 21.7651ZM20.0003 43.3318C19.717 43.3318 19.4795 43.2359 19.2878 43.0443C19.0962 42.8526 19.0003 42.6151 19.0003 42.3318V33.6651H17.667C17.3837 33.6651 17.1462 33.5693 16.9545 33.3776C16.7628 33.1859 16.667 32.9484 16.667 32.6651V25.3318C16.667 24.7818 16.8628 24.3109 17.2545 23.9193C17.6462 23.5276 18.117 23.3318 18.667 23.3318H24.0003C24.5503 23.3318 25.0212 23.5276 25.4128 23.9193C25.8045 24.3109 26.0003 24.7818 26.0003 25.3318V32.6651C26.0003 32.9484 25.9045 33.1859 25.7128 33.3776C25.5212 33.5693 25.2837 33.6651 25.0003 33.6651H23.667V42.3318C23.667 42.6151 23.5712 42.8526 23.3795 43.0443C23.1878 43.2359 22.9503 43.3318 22.667 43.3318H20.0003ZM29.667 43.3318C29.3837 43.3318 29.1462 43.2359 28.9545 43.0443C28.7628 42.8526 28.667 42.6151 28.667 42.3318V37.6651H28.0003C27.717 37.6651 27.4795 37.5693 27.2878 37.3776C27.0962 37.1859 27.0003 36.9484 27.0003 36.6651V32.1318C27.0003 31.7244 27.1429 31.3781 27.4281 31.0929C27.7133 30.8077 28.0596 30.6651 28.467 30.6651H32.5337C32.9411 30.6651 33.2874 30.8077 33.5726 31.0929C33.8577 31.3781 34.0003 31.7244 34.0003 32.1318V36.6651C34.0003 36.9484 33.9045 37.1859 33.7128 37.3776C33.5212 37.5693 33.2837 37.6651 33.0003 37.6651H32.3337V42.3318C32.3337 42.6151 32.2378 42.8526 32.0462 43.0443C31.8545 43.2359 31.617 43.3318 31.3337 43.3318H29.667Z"
				fill="white"
			/>
		</svg>
	),
});

export const GoalTwoIcon = createIcon({
	displayName: "GoalTwoIcon",
	viewBox: "0 0 60 60",
	path: (
		<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="60" height="60" rx="10" fill="#B68A35" />
			<path
				d="M15.333 40.8682V31.7349C15.333 31.1849 15.5288 30.7141 15.9205 30.3224C16.3122 29.9307 16.783 29.7349 17.333 29.7349H18.4663C19.0163 29.7349 19.4872 29.9307 19.8788 30.3224C20.2705 30.7141 20.4663 31.1849 20.4663 31.7349V40.8682C20.4663 41.4182 20.2705 41.8891 19.8788 42.2807C19.4872 42.6724 19.0163 42.8682 18.4663 42.8682H17.333C16.783 42.8682 16.3122 42.6724 15.9205 42.2807C15.5288 41.8891 15.333 41.4182 15.333 40.8682ZM32.2997 43.7682L22.4997 40.9682V29.7349H25.2663C25.383 29.7349 25.4997 29.7482 25.6163 29.7749C25.733 29.8016 25.8497 29.8327 25.9663 29.8682L34.133 32.9349C34.7997 33.1793 35.3108 33.5516 35.6663 34.0516C36.0219 34.5516 36.1997 35.0571 36.1997 35.5682C36.1997 35.747 36.1441 35.8916 36.033 36.0023C35.9219 36.1129 35.7775 36.1682 35.5997 36.1682H33.1663C32.5886 36.1682 32.0775 36.1349 31.633 36.0682C31.1886 36.0016 30.6997 35.8793 30.1663 35.7016L28.1997 35.0682C28.0663 35.0238 27.9386 35.0293 27.8163 35.0849C27.6941 35.1405 27.6108 35.2299 27.5663 35.3533C27.5219 35.4943 27.5275 35.6265 27.583 35.7498C27.6386 35.8732 27.733 35.9571 27.8663 36.0016L29.8997 36.7016C30.2552 36.8127 30.7052 36.9182 31.2497 37.0182C31.7941 37.1182 32.5663 37.1682 33.5663 37.1682H40.0663C41.333 37.1682 42.1941 37.4627 42.6497 38.0516C43.1052 38.6405 43.333 39.5349 43.333 40.7349L33.433 43.7682C33.2552 43.8127 33.0663 43.8349 32.8663 43.8349C32.6663 43.8349 32.4775 43.8127 32.2997 43.7682ZM35.5293 30.3349C35.3807 30.3349 35.2373 30.3127 35.0993 30.2682C34.9613 30.2238 34.8392 30.146 34.733 30.0349C32.7997 28.3016 31.1497 26.6487 29.783 25.0763C28.4163 23.504 27.733 22.0346 27.733 20.6682C27.733 19.5181 28.1243 18.5527 28.907 17.7723C29.6897 16.9918 30.6577 16.6016 31.811 16.6016C32.4701 16.6016 33.1275 16.7849 33.783 17.1516C34.4386 17.5182 35.0219 18.0127 35.533 18.6349C36.0441 18.0127 36.6275 17.5182 37.283 17.1516C37.9386 16.7849 38.5959 16.6016 39.255 16.6016C40.4084 16.6016 41.3764 16.9918 42.159 17.7723C42.9417 18.5527 43.333 19.5181 43.333 20.6682C43.333 22.0346 42.6497 23.504 41.283 25.0763C39.9163 26.6487 38.267 28.3016 36.3348 30.0349C36.2225 30.146 36.0973 30.2238 35.9593 30.2682C35.8213 30.3127 35.678 30.3349 35.5293 30.3349Z"
				fill="white"
			/>
		</svg>
	),
});

export const GoalThreeIcon = createIcon({
	displayName: "GoalThreeIcon",
	viewBox: "0 0 60 60",
	path: (
		<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
			<rect width="60" height="60" rx="10" fill="#B68A35" />
			<path
				d="M43.6669 36.5656C43.378 36.5656 43.1392 36.4712 42.9503 36.2823C42.7614 36.0934 42.6669 35.8545 42.6669 35.5656V27.1656L30.9336 33.499C30.778 33.5878 30.6225 33.649 30.4669 33.6823C30.3114 33.7156 30.1447 33.7323 29.9669 33.7323C29.7892 33.7323 29.628 33.7156 29.4836 33.6823C29.3392 33.649 29.1891 33.5878 29.0336 33.499L16.9336 26.8656C16.7558 26.7767 16.628 26.6545 16.5503 26.499C16.4725 26.3434 16.4336 26.1767 16.4336 25.999C16.4336 25.8212 16.4725 25.6545 16.5503 25.499C16.628 25.3434 16.7558 25.2212 16.9336 25.1323L29.0003 18.5323C29.1558 18.4434 29.3114 18.3767 29.4669 18.3323C29.6225 18.2878 29.7892 18.2656 29.9669 18.2656C30.1447 18.2656 30.3114 18.2878 30.4669 18.3323C30.6225 18.3767 30.778 18.4434 30.9336 18.5323L44.1336 25.699C44.3114 25.8101 44.4447 25.9434 44.5336 26.099C44.6225 26.2545 44.6669 26.4212 44.6669 26.599V35.5656C44.6669 35.8545 44.5725 36.0934 44.3836 36.2823C44.1947 36.4712 43.9558 36.5656 43.6669 36.5656ZM29.9669 41.699C29.7892 41.699 29.6225 41.6823 29.4669 41.649C29.3114 41.6156 29.1558 41.5545 29.0003 41.4656L21.3336 37.2656C21.0225 37.0878 20.7725 36.8434 20.5836 36.5323C20.3947 36.2212 20.3003 35.8767 20.3003 35.499V29.699L28.6003 34.2323L29.2669 34.5656C29.4892 34.6767 29.7225 34.7323 29.9669 34.7323C30.2114 34.7323 30.4503 34.6767 30.6836 34.5656C30.9169 34.4545 31.1447 34.3434 31.3669 34.2323L39.6336 29.699V35.499C39.6336 35.8767 39.5392 36.2212 39.3503 36.5323C39.1614 36.8434 38.9114 37.0878 38.6003 37.2656L30.9336 41.4656C30.778 41.5545 30.6225 41.6156 30.4669 41.649C30.3114 41.6823 30.1447 41.699 29.9669 41.699Z"
				fill="white"
			/>
		</svg>
	),
});

export const RatingStarIcon = createIcon({
	displayName: "GoalThreeIcon",
	viewBox: "0 0 28 27",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="30" height="28" viewBox="0 0 30 28" fill="none">
			<path
				d="M16.0245 3.58156C16.1741 3.1209 16.8259 3.1209 16.9755 3.58156L18.8309 9.2918C19.0317 9.90983 19.6076 10.3283 20.2575 10.3283H26.2616C26.7459 10.3283 26.9473 10.9481 26.5555 11.2328L21.6981 14.7619C21.1723 15.1439 20.9523 15.8209 21.1531 16.439L23.0085 22.1492C23.1582 22.6098 22.6309 22.9929 22.2391 22.7082L17.3817 19.1791C16.8559 18.7971 16.1441 18.7971 15.6183 19.1791L10.7609 22.7082C10.369 22.9929 9.84181 22.6098 9.99148 22.1492L11.8469 16.439C12.0477 15.8209 11.8277 15.1439 11.3019 14.7619L6.44453 11.2328C6.05267 10.9481 6.25406 10.3283 6.73842 10.3283H12.7425C13.3924 10.3283 13.9683 9.90983 14.1691 9.2918L16.0245 3.58156Z"
				fill="#B68A35"
				stroke="#B68A35"
			/>
			<path d="M11 7.5L3 7.5" stroke="#B68A35" stroke-width="1.5" stroke-linecap="round" />
			<path d="M7 14.5L1 14.5" stroke="#B68A35" stroke-width="1.5" stroke-linecap="round" />
			<path d="M7 18.5H4" stroke="#B68A35" stroke-width="1.5" stroke-linecap="round" />
		</svg>
	),
});

export const FeedbackRecivedIcon = createIcon({
	displayName: "GoalThreeIcon",
	viewBox: "0 0 28 27",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
			<path
				d="M13.1562 17.8438L10.0938 14.7812C9.90625 14.5938 9.67708 14.5 9.40625 14.5C9.13542 14.5 8.89583 14.6042 8.6875 14.8125C8.5 15 8.40625 15.2292 8.40625 15.5C8.40625 15.7708 8.5 16 8.6875 16.1875L12.5 20.0312C12.6875 20.2188 12.9062 20.3125 13.1562 20.3125C13.4062 20.3125 13.625 20.2188 13.8125 20.0312L21.2812 12.5625C21.4896 12.3542 21.5938 12.1146 21.5938 11.8438C21.5938 11.5729 21.4896 11.3333 21.2812 11.125C21.0729 10.9375 20.8281 10.849 20.5469 10.8594C20.2656 10.8698 20.0312 10.9688 19.8438 11.1562L13.1562 17.8438ZM15 27.5C13.2917 27.5 11.6771 27.1719 10.1562 26.5156C8.63542 25.8594 7.30729 24.9635 6.17188 23.8281C5.03646 22.6927 4.14062 21.3646 3.48438 19.8438C2.82812 18.3229 2.5 16.7083 2.5 15C2.5 13.2708 2.82812 11.6458 3.48438 10.125C4.14062 8.60417 5.03646 7.28125 6.17188 6.15625C7.30729 5.03125 8.63542 4.14062 10.1562 3.48438C11.6771 2.82812 13.2917 2.5 15 2.5C16.7292 2.5 18.3542 2.82812 19.875 3.48438C21.3958 4.14062 22.7188 5.03125 23.8438 6.15625C24.9688 7.28125 25.8594 8.60417 26.5156 10.125C27.1719 11.6458 27.5 13.2708 27.5 15C27.5 16.7083 27.1719 18.3229 26.5156 19.8438C25.8594 21.3646 24.9688 22.6927 23.8438 23.8281C22.7188 24.9635 21.3958 25.8594 19.875 26.5156C18.3542 27.1719 16.7292 27.5 15 27.5Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const BeneficiaryIconOne = createIcon({
	displayName: "BeneficiaryIconOne",
	viewBox: "0 0 60 60",
	path: (
		<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0 10C0 4.47715 4.47715 0 10 0H60V50C60 55.5228 55.5228 60 50 60H0V10Z"
				fill="#B68A35"
			/>
			<path
				d="M15.333 40.8682V31.7349C15.333 31.1849 15.5288 30.7141 15.9205 30.3224C16.3122 29.9307 16.783 29.7349 17.333 29.7349H18.4663C19.0163 29.7349 19.4872 29.9307 19.8788 30.3224C20.2705 30.7141 20.4663 31.1849 20.4663 31.7349V40.8682C20.4663 41.4182 20.2705 41.8891 19.8788 42.2807C19.4872 42.6724 19.0163 42.8682 18.4663 42.8682H17.333C16.783 42.8682 16.3122 42.6724 15.9205 42.2807C15.5288 41.8891 15.333 41.4182 15.333 40.8682ZM32.2997 43.7682L22.4997 40.9682V29.7349H25.2663C25.383 29.7349 25.4997 29.7482 25.6163 29.7749C25.733 29.8016 25.8497 29.8327 25.9663 29.8682L34.133 32.9349C34.7997 33.1793 35.3108 33.5516 35.6663 34.0516C36.0219 34.5516 36.1997 35.0571 36.1997 35.5682C36.1997 35.747 36.1441 35.8916 36.033 36.0023C35.9219 36.1129 35.7775 36.1682 35.5997 36.1682H33.1663C32.5886 36.1682 32.0775 36.1349 31.633 36.0682C31.1886 36.0016 30.6997 35.8793 30.1663 35.7016L28.1997 35.0682C28.0663 35.0238 27.9386 35.0293 27.8163 35.0849C27.6941 35.1405 27.6108 35.2299 27.5663 35.3533C27.5219 35.4943 27.5275 35.6265 27.583 35.7498C27.6386 35.8732 27.733 35.9571 27.8663 36.0016L29.8997 36.7016C30.2552 36.8127 30.7052 36.9182 31.2497 37.0182C31.7941 37.1182 32.5663 37.1682 33.5663 37.1682H40.0663C41.333 37.1682 42.1941 37.4627 42.6497 38.0516C43.1052 38.6405 43.333 39.5349 43.333 40.7349L33.433 43.7682C33.2552 43.8127 33.0663 43.8349 32.8663 43.8349C32.6663 43.8349 32.4775 43.8127 32.2997 43.7682ZM35.5293 30.3349C35.3807 30.3349 35.2373 30.3127 35.0993 30.2682C34.9613 30.2238 34.8392 30.146 34.733 30.0349C32.7997 28.3016 31.1497 26.6487 29.783 25.0763C28.4163 23.504 27.733 22.0346 27.733 20.6682C27.733 19.5181 28.1243 18.5527 28.907 17.7723C29.6897 16.9918 30.6577 16.6016 31.811 16.6016C32.4701 16.6016 33.1275 16.7849 33.783 17.1516C34.4386 17.5182 35.0219 18.0127 35.533 18.6349C36.0441 18.0127 36.6275 17.5182 37.283 17.1516C37.9386 16.7849 38.5959 16.6016 39.255 16.6016C40.4084 16.6016 41.3764 16.9918 42.159 17.7723C42.9417 18.5527 43.333 19.5181 43.333 20.6682C43.333 22.0346 42.6497 23.504 41.283 25.0763C39.9163 26.6487 38.267 28.3016 36.3348 30.0349C36.2225 30.146 36.0973 30.2238 35.9593 30.2682C35.8213 30.3127 35.678 30.3349 35.5293 30.3349Z"
				fill="white"
			/>
			<path
				d="M15.333 40.8638V31.7305C15.333 31.1805 15.5288 30.7096 15.9205 30.318C16.3122 29.9263 16.783 29.7305 17.333 29.7305H18.4663C19.0163 29.7305 19.4872 29.9263 19.8788 30.318C20.2705 30.7096 20.4663 31.1805 20.4663 31.7305V40.8638C20.4663 41.4138 20.2705 41.8846 19.8788 42.2763C19.4872 42.668 19.0163 42.8638 18.4663 42.8638H17.333C16.783 42.8638 16.3122 42.668 15.9205 42.2763C15.5288 41.8846 15.333 41.4138 15.333 40.8638ZM32.2997 43.7638L22.4997 40.9638V29.7305H25.2663C25.383 29.7305 25.4997 29.7438 25.6163 29.7705C25.733 29.7971 25.8497 29.8282 25.9663 29.8638L34.133 32.9305C34.7997 33.1749 35.3108 33.5471 35.6663 34.0471C36.0219 34.5471 36.1997 35.0527 36.1997 35.5638C36.1997 35.7425 36.1441 35.8872 36.033 35.9978C35.9219 36.1085 35.7775 36.1638 35.5997 36.1638H33.1663C32.5886 36.1638 32.0775 36.1305 31.633 36.0638C31.1886 35.9971 30.6997 35.8749 30.1663 35.6971L28.1997 35.0638C28.0663 35.0194 27.9386 35.0249 27.8163 35.0805C27.6941 35.136 27.6108 35.2255 27.5663 35.3489C27.5219 35.4899 27.5275 35.622 27.583 35.7454C27.6386 35.8688 27.733 35.9527 27.8663 35.9971L29.8997 36.6971C30.2552 36.8082 30.7052 36.9138 31.2497 37.0138C31.7941 37.1138 32.5663 37.1638 33.5663 37.1638H40.0663C41.333 37.1638 42.1941 37.4582 42.6497 38.0471C43.1052 38.636 43.333 39.5305 43.333 40.7305L33.433 43.7638C33.2552 43.8083 33.0663 43.8305 32.8663 43.8305C32.6663 43.8305 32.4775 43.8083 32.2997 43.7638Z"
				fill="white"
			/>
		</svg>
	),
});

export const BackToTop = createIcon({
	displayName: "BackToTop",
	viewBox: "0 0 64 64",
	path: (
		<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M29.6 29.28V41.44C29.6 42.1333 29.8267 42.7067 30.28 43.16C30.7333 43.6133 31.3067 43.84 32 43.84C32.6933 43.84 33.2667 43.6133 33.72 43.16C34.1733 42.7067 34.4 42.1333 34.4 41.44V29.28L38.64 33.52C39.12 34 39.68 34.24 40.32 34.24C40.96 34.24 41.52 34 42 33.52C42.48 33.04 42.72 32.48 42.72 31.84C42.72 31.2 42.48 30.64 42 30.16L33.68 21.84C33.2 21.36 32.64 21.12 32 21.12C31.36 21.12 30.8 21.36 30.32 21.84L22 30.16C21.52 30.64 21.28 31.2 21.28 31.84C21.28 32.48 21.52 33.04 22 33.52C22.48 34 23.04 34.24 23.68 34.24C24.32 34.24 24.88 34 25.36 33.52L29.6 29.28ZM32 64C27.6267 64 23.4933 63.16 19.6 61.48C15.7067 59.8 12.3067 57.5067 9.4 54.6C6.49333 51.6933 4.2 48.2933 2.52 44.4C0.84 40.5067 0 36.3733 0 32C0 27.5733 0.84 23.4133 2.52 19.52C4.2 15.6267 6.49333 12.24 9.4 9.36C12.3067 6.48 15.7067 4.2 19.6 2.52C23.4933 0.84 27.6267 0 32 0C36.4267 0 40.5867 0.84 44.48 2.52C48.3733 4.2 51.76 6.48 54.64 9.36C57.52 12.24 59.8 15.6267 61.48 19.52C63.16 23.4133 64 27.5733 64 32C64 36.3733 63.16 40.5067 61.48 44.4C59.8 48.2933 57.52 51.6933 54.64 54.6C51.76 57.5067 48.3733 59.8 44.48 61.48C40.5867 63.16 36.4267 64 32 64Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const HeadphoneIcon = createIcon({
	displayName: "HeadphoneIcon",
	viewBox: "0 0 34 46",
	path: (
		<svg width="34" height="46" viewBox="0 0 34 46" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M17 45.3447C15.8214 45.3447 14.8051 44.9364 13.951 44.1197C13.097 43.3031 12.6325 42.3054 12.5577 41.1268H21.4423C21.3675 42.3054 20.9031 43.3031 20.049 44.1197C19.195 44.9364 18.1786 45.3447 17 45.3447ZM10 37.896C9.50345 37.896 9.08765 37.7285 8.75262 37.3934C8.41759 37.0584 8.25008 36.6426 8.25008 36.146C8.25008 35.6495 8.41759 35.2337 8.75262 34.8986C9.08765 34.5636 9.50345 34.396 10 34.396H24C24.4966 34.396 24.9124 34.5636 25.2474 34.8986C25.5824 35.2337 25.75 35.6495 25.75 36.146C25.75 36.6426 25.5824 37.0584 25.2474 37.3934C24.9124 37.7285 24.4966 37.896 24 37.896H10ZM8.60901 31.1652C6.16503 29.6456 4.23033 27.66 2.8049 25.2085C1.37946 22.7571 0.666748 20.076 0.666748 17.1653C0.666748 12.6183 2.25221 8.75938 5.42313 5.58842C8.59409 2.41749 12.4531 0.832031 17 0.832031C21.547 0.832031 25.406 2.41749 28.5769 5.58842C31.7478 8.75938 33.3333 12.6183 33.3333 17.1653C33.3333 20.076 32.6206 22.7571 31.1951 25.2085C29.7697 27.66 27.835 29.6456 25.391 31.1652H8.60901ZM9.65002 27.6653H24.35C26.1 26.4209 27.4514 24.8848 28.4042 23.057C29.357 21.2292 29.8334 19.2653 29.8334 17.1653C29.8334 13.5875 28.5889 10.5542 26.1 8.06531C23.6111 5.57642 20.5778 4.33197 17 4.33197C13.4222 4.33197 10.3889 5.57642 7.90002 8.06531C5.41113 10.5542 4.16669 13.5875 4.16669 17.1653C4.16669 19.2653 4.64308 21.2292 5.59586 23.057C6.54863 24.8848 7.90002 26.4209 9.65002 27.6653Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const PersonIcon = createIcon({
	displayName: "HeadphoneIcon",
	viewBox: "0 0 35 29",
	path: (
		<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
			<path
				d="M24.7365 9.01961C24.7365 4.03922 20.6973 0 15.7169 0C10.7365 0 6.69727 4.03922 6.69727 9.01961C6.69727 14 10.7365 18.0392 15.7169 18.0392C20.6973 18.0392 24.7365 14 24.7365 9.01961ZM9.05021 9.01961C9.05021 5.33333 12.0306 2.35294 15.7169 2.35294C19.4031 2.35294 22.3835 5.33333 22.3835 9.01961C22.3835 12.7059 19.4031 15.6863 15.7169 15.6863C12.0306 15.6863 9.05021 12.7059 9.05021 9.01961Z"
				fill="currentColor"
			/>
			<path
				d="M0.619009 31.8424C0.815087 31.9601 1.01117 31.9993 1.20724 31.9993C1.5994 31.9993 2.03077 31.8032 2.22685 31.4111C4.97195 26.5483 10.1484 23.5287 15.717 23.5287C21.2857 23.5287 26.4621 26.5483 29.2465 31.4111C29.5602 31.9601 30.3053 32.1562 30.8543 31.8424C31.4033 31.5287 31.5994 30.7836 31.2857 30.2346C28.1092 24.666 22.1484 21.1758 15.717 21.1758C9.28568 21.1758 3.32489 24.666 0.14842 30.2346C-0.165305 30.7836 0.0307733 31.5287 0.619009 31.8424Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const Polygon = createIcon({
	displayName: "Polygon",
	viewBox: "0 0 13 16",
	path: (
		<svg width="13" height="16" viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M12.5 7.13398C13.1667 7.51888 13.1667 8.48112 12.5 8.86602L2 14.9282C1.33333 15.3131 0.5 14.832 0.5 14.0622L0.5 1.93782C0.5 1.16802 1.33333 0.686897 2 1.0718L12.5 7.13398Z"
				fill="#7C5E24"
			/>
		</svg>
	),
});

export const HierarchyIcon = createIcon({
	displayName: "HierarchyIcon",
	viewBox: "0 0 56 56",
	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M7.58337 47.2051V39.1281C7.58337 38.1439 7.92812 37.3071 8.61762 36.6176C9.30712 35.9281 10.144 35.5833 11.1282 35.5833H14.5834V30.4679C14.5834 29.3042 14.9954 28.3103 15.8196 27.4862C16.6437 26.6621 17.6376 26.25 18.8013 26.25H26.25V20.4166H22.7948C21.8106 20.4166 20.9738 20.0718 20.2843 19.3823C19.5948 18.6928 19.25 17.856 19.25 16.8718V8.7948C19.25 7.8106 19.5948 6.97375 20.2843 6.28425C20.9738 5.59475 21.8106 5.25 22.7948 5.25H33.2051C34.1893 5.25 35.0262 5.59475 35.7157 6.28425C36.4052 6.97375 36.7499 7.8106 36.7499 8.7948V16.8718C36.7499 17.856 36.4052 18.6928 35.7157 19.3823C35.0262 20.0718 34.1893 20.4166 33.2051 20.4166H29.7499V26.25H37.1987C38.3624 26.25 39.3563 26.6621 40.1804 27.4862C41.0045 28.3103 41.4166 29.3042 41.4166 30.4679V35.5833H44.8718C45.856 35.5833 46.6928 35.9281 47.3823 36.6176C48.0718 37.3071 48.4166 38.1439 48.4166 39.1281V47.2051C48.4166 48.1893 48.0718 49.0261 47.3823 49.7156C46.6928 50.4051 45.856 50.7499 44.8718 50.7499H34.4615C33.4773 50.7499 32.6405 50.4051 31.951 49.7156C31.2615 49.0261 30.9167 48.1893 30.9167 47.2051V39.1281C30.9167 38.1439 31.2615 37.3071 31.951 36.6176C32.6405 35.9281 33.4773 35.5833 34.4615 35.5833H37.9167V30.4679C37.9167 30.2585 37.8494 30.0865 37.7148 29.9518C37.5801 29.8172 37.4081 29.7499 37.1987 29.7499H18.8013C18.5919 29.7499 18.4198 29.8172 18.2852 29.9518C18.1506 30.0865 18.0833 30.2585 18.0833 30.4679V35.5833H21.5385C22.5227 35.5833 23.3595 35.9281 24.049 36.6176C24.7385 37.3071 25.0833 38.1439 25.0833 39.1281V47.2051C25.0833 48.1893 24.7385 49.0261 24.049 49.7156C23.3595 50.4051 22.5227 50.7499 21.5385 50.7499H11.1282C10.144 50.7499 9.30712 50.4051 8.61762 49.7156C7.92812 49.0261 7.58337 48.1893 7.58337 47.2051ZM22.7499 16.9167H33.25V8.74989H22.7499V16.9167ZM11.0833 47.25H21.5834V39.0832H11.0833V47.25ZM34.4166 47.25H44.9167V39.0832H34.4166V47.25Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const CalenderIcon = createIcon({
	displayName: "CalenderIcon",
	viewBox: "0 0 56 56",
	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M18.6667 32.5303C18.0953 32.5303 17.6085 32.3292 17.2061 31.9268C16.8038 31.5245 16.6026 31.0376 16.6026 30.4663C16.6026 29.8949 16.8038 29.4081 17.2061 29.0057C17.6085 28.6034 18.0953 28.4022 18.6667 28.4022C19.238 28.4022 19.7249 28.6034 20.1272 29.0057C20.5296 29.4081 20.7308 29.8949 20.7308 30.4663C20.7308 31.0376 20.5296 31.5245 20.1272 31.9268C19.7249 32.3292 19.238 32.5303 18.6667 32.5303ZM28 32.5303C27.4287 32.5303 26.9418 32.3292 26.5395 31.9268C26.1371 31.5245 25.936 31.0376 25.936 30.4663C25.936 29.8949 26.1371 29.4081 26.5395 29.0057C26.9418 28.6034 27.4287 28.4022 28 28.4022C28.5714 28.4022 29.0582 28.6034 29.4606 29.0057C29.8629 29.4081 30.0641 29.8949 30.0641 30.4663C30.0641 31.0376 29.8629 31.5245 29.4606 31.9268C29.0582 32.3292 28.5714 32.5303 28 32.5303ZM37.3334 32.5303C36.762 32.5303 36.2752 32.3292 35.8728 31.9268C35.4705 31.5245 35.2693 31.0376 35.2693 30.4663C35.2693 29.8949 35.4705 29.4081 35.8728 29.0057C36.2752 28.6034 36.762 28.4022 37.3334 28.4022C37.9047 28.4022 38.3916 28.6034 38.7939 29.0057C39.1963 29.4081 39.3974 29.8949 39.3974 30.4663C39.3974 31.0376 39.1963 31.5245 38.7939 31.9268C38.3916 32.3292 37.9047 32.5303 37.3334 32.5303ZM12.3847 50.1649C11.2061 50.1649 10.2084 49.7566 9.39175 48.9399C8.57508 48.1233 8.16675 47.1256 8.16675 45.947V14.7163C8.16675 13.5377 8.57508 12.54 9.39175 11.7234C10.2084 10.9067 11.2061 10.4984 12.3847 10.4984H15.6155V7.3573C15.6155 6.84579 15.7868 6.41878 16.1293 6.07624C16.4718 5.73375 16.8988 5.5625 17.4104 5.5625C17.9219 5.5625 18.3489 5.73375 18.6914 6.07624C19.0339 6.41878 19.2052 6.84579 19.2052 7.3573V10.4984H36.8846V7.31244C36.8846 6.81587 37.0521 6.40007 37.3871 6.06504C37.7222 5.73001 38.138 5.5625 38.6345 5.5625C39.1311 5.5625 39.5469 5.73001 39.8819 6.06504C40.217 6.40007 40.3845 6.81587 40.3845 7.31244V10.4984H43.6153C44.794 10.4984 45.7916 10.9067 46.6083 11.7234C47.425 12.54 47.8333 13.5377 47.8333 14.7163V45.947C47.8333 47.1256 47.425 48.1233 46.6083 48.9399C45.7916 49.7566 44.794 50.1649 43.6153 50.1649H12.3847ZM12.3847 46.665H43.6153C43.7948 46.665 43.9594 46.5902 44.109 46.4406C44.2586 46.291 44.3334 46.1265 44.3334 45.947V24.0497H11.6667V45.947C11.6667 46.1265 11.7415 46.291 11.891 46.4406C12.0406 46.5902 12.2052 46.665 12.3847 46.665ZM11.6667 20.5497H44.3334V14.7163C44.3334 14.5368 44.2586 14.3723 44.109 14.2227C43.9594 14.0731 43.7948 13.9983 43.6153 13.9983H12.3847C12.2052 13.9983 12.0406 14.0731 11.891 14.2227C11.7415 14.3723 11.6667 14.5368 11.6667 14.7163V20.5497Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const GroupIcon = createIcon({
	displayName: "GroupIcon",
	viewBox: "0 0 56 56",
	path: (
		<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M3.18602 41.5056C2.61388 41.5056 2.13431 41.3121 1.74728 40.9251C1.3603 40.5381 1.16681 40.0585 1.16681 39.4864V38.6608C1.16681 37.0843 1.96909 35.807 3.57364 34.8288C5.17824 33.8506 7.29338 33.3615 9.91908 33.3615C10.3931 33.3615 10.8426 33.3772 11.2673 33.4086C11.6922 33.44 12.1035 33.4946 12.5013 33.5724C12.0577 34.255 11.7329 34.9888 11.527 35.7737C11.321 36.5587 11.2181 37.3898 11.2181 38.2669V41.5056H3.18602ZM17.285 41.5056C16.6776 41.5056 16.1727 41.3035 15.7703 40.8993C15.3679 40.4951 15.1668 39.9942 15.1668 39.3966V38.3557C15.1668 36.127 16.3476 34.3299 18.7094 32.9643C21.0711 31.5988 24.165 30.916 27.9912 30.916C31.853 30.916 34.9588 31.5988 37.3086 32.9643C39.6584 34.3299 40.8333 36.127 40.8333 38.3557V39.3966C40.8333 39.9942 40.6312 40.4951 40.2269 40.8993C39.8227 41.3035 39.3218 41.5056 38.7243 41.5056H17.285ZM44.782 41.5056V38.2669C44.782 37.3898 44.6711 36.5587 44.4493 35.7737C44.2275 34.9888 43.9106 34.255 43.4987 33.5724C43.8966 33.4946 44.3071 33.44 44.7305 33.4086C45.1538 33.3772 45.6048 33.3615 46.0833 33.3615C48.7083 33.3615 50.8229 33.8506 52.4271 34.8288C54.0312 35.807 54.8333 37.0843 54.8333 38.6608V39.4864C54.8333 40.0585 54.6398 40.5381 54.2528 40.9251C53.8657 41.3121 53.3862 41.5056 52.8141 41.5056H44.782ZM28 34.4159C25.559 34.4159 23.4709 34.7472 21.7359 35.4099C20.0009 36.0724 19.0376 36.8629 18.8462 37.7813V38.0057H37.1539V37.7813C36.9385 36.839 35.9693 36.0425 34.2462 35.3919C32.5231 34.7412 30.4411 34.4159 28 34.4159ZM9.91675 31.0955C8.81289 31.0955 7.87508 30.7044 7.10333 29.9224C6.33155 29.1403 5.94565 28.2001 5.94565 27.1019C5.94565 26.0037 6.33155 25.0635 7.10333 24.2815C7.87508 23.4994 8.81289 23.1083 9.91675 23.1083C11.0206 23.1083 11.9621 23.4994 12.7414 24.2815C13.5206 25.0635 13.9103 26.0037 13.9103 27.1019C13.9103 28.2001 13.5192 29.1403 12.7372 29.9224C11.9551 30.7044 11.015 31.0955 9.91675 31.0955ZM46.0833 31.0955C44.9795 31.0955 44.0379 30.7044 43.2587 29.9224C42.4794 29.1403 42.0898 28.2001 42.0898 27.1019C42.0898 26.0037 42.4808 25.0635 43.2629 24.2815C44.0449 23.4994 44.9851 23.1083 46.0833 23.1083C47.1872 23.1083 48.125 23.4994 48.8967 24.2815C49.6685 25.0635 50.0544 26.0037 50.0544 27.1019C50.0544 28.2001 49.6685 29.1403 48.8967 29.9224C48.125 30.7044 47.1872 31.0955 46.0833 31.0955ZM28 29.166C26.3173 29.166 24.8871 28.577 23.7092 27.3992C22.5313 26.2213 21.9424 24.791 21.9424 23.1083C21.9424 21.4257 22.5313 19.9954 23.7092 18.8176C24.8871 17.6397 26.3173 17.0508 28 17.0508C29.6827 17.0508 31.113 17.6397 32.2908 18.8176C33.4687 19.9954 34.0576 21.4257 34.0576 23.1083C34.0576 24.791 33.4687 26.2213 32.2908 27.3992C31.113 28.577 29.6827 29.166 28 29.166ZM28.0039 20.5507C27.2804 20.5507 26.6725 20.7954 26.1804 21.2848C25.6884 21.7743 25.4423 22.3809 25.4423 23.1044C25.4423 23.828 25.6871 24.4358 26.1765 24.9279C26.666 25.42 27.2725 25.6661 27.9961 25.6661C28.7197 25.6661 29.3275 25.4214 29.8196 24.9319C30.3117 24.4424 30.5577 23.8359 30.5577 23.1123C30.5577 22.3887 30.313 21.7809 29.8235 21.2888C29.3341 20.7967 28.7275 20.5507 28.0039 20.5507Z"
				fill="#B68A35"
			/>
		</svg>
	),
});

export const ComplaintTypeIcon = createIcon({
	displayName: "ComplaintTypeIcon",
	viewBox: "0 0 62 38",
	path: (
		<svg width="62" height="38" viewBox="0 0 62 38" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M43.6775 0.105225H41.5218C40.262 0.105225 39.2262 1.12707 39.2262 2.40088V2.9468L5.44924 11.4155H3.11159C1.54383 11.4155 0.27002 12.6893 0.27002 14.2571V22.5439C0.27002 24.1116 1.54383 25.3854 3.11159 25.3854H5.44924L9.90058 26.5053V30.4667C9.90058 32.8463 11.5803 34.918 13.904 35.4079L23.0726 37.3536C23.4225 37.4236 23.7725 37.4656 24.1364 37.4656C25.2843 37.4656 26.4041 37.0737 27.314 36.3318C28.5038 35.3659 29.1897 33.9382 29.1897 32.3984V31.3345L39.2262 33.8542V34.4421C39.2262 35.7019 40.248 36.7377 41.5218 36.7377H43.6775C45.1473 36.7377 46.3231 35.5479 46.3231 34.0921V2.76483C46.3231 1.29505 45.1333 0.105225 43.6775 0.105225ZM26.0821 32.4124C26.0821 33.0003 25.8162 33.5462 25.3683 33.9242C24.9063 34.2881 24.3184 34.4421 23.7445 34.3161L14.5619 32.3704C13.666 32.1744 13.0221 31.3765 13.0221 30.4667V27.2891L26.0821 30.5647V32.4124Z"
				fill="currentColor"
			/>
			<path
				d="M56.2328 35.4102C56.0089 35.4102 55.7709 35.3542 55.5609 35.2282C54.889 34.8503 54.6371 33.9964 55.015 33.3245C60.6282 23.218 60.6422 14.0214 55.057 5.2167C54.6371 4.5588 54.8331 3.70493 55.491 3.28499C56.1489 2.86505 57.0027 3.06102 57.4227 3.71892C63.5118 13.3355 63.5258 23.7499 57.4647 34.6823C57.2127 35.1442 56.7368 35.4102 56.2328 35.4102Z"
				fill="currentColor"
			/>
			<path
				d="M51.2509 30.7621C51.0269 30.7621 50.7889 30.7061 50.579 30.5801C49.9071 30.2021 49.6551 29.3483 50.033 28.6764C53.7985 21.9014 53.8125 15.7423 50.075 9.86318C49.6551 9.20528 49.8511 8.35141 50.509 7.93147C51.1669 7.51154 52.0207 7.70751 52.4407 8.36541C56.71 15.0984 56.724 22.3913 52.4827 30.0342C52.2167 30.4961 51.7408 30.7621 51.2509 30.7621Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const InquiryTypeIcon = createIcon({
	displayName: "InquiryTypeIcon",
	viewBox: "0 0 47 48",
	path: (
		<svg width="47" height="48" viewBox="0 0 47 48" fill="none" xmlns="http://www.w3.org/2000/svg">
			<circle
				cx="23.4167"
				cy="23.4167"
				r="23.4167"
				transform="matrix(-1 0 0 1 47 0.356689)"
				fill="currentColor"
			/>
			<path
				d="M23.5026 19.4718C22.7109 19.4718 22.069 20.1137 22.069 20.9055V34.844C22.069 35.6358 22.7109 36.2777 23.5026 36.2777C24.2944 36.2777 24.9363 35.6358 24.9363 34.844V20.9055C24.9363 20.1137 24.2944 19.4718 23.5026 19.4718ZM24.9107 12.8867C24.133 12.1091 22.8723 12.1091 22.0946 12.8867C21.317 13.6643 21.317 14.9251 22.0946 15.7027C22.8723 16.4803 24.133 16.4803 24.9107 15.7027C25.6883 14.9251 25.6883 13.6643 24.9107 12.8867Z"
				fill="white"
			/>
		</svg>
	),
});

export const SuggestionTypeIcon = createIcon({
	displayName: "SuggestionTypeIcon",
	viewBox: "0 0 41 41",
	path: (
		<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M37.1842 0.0703125H4.11409C2.01546 0.0703125 0.298309 1.78739 0.298309 3.8861V36.9562C0.298309 39.0549 2.01546 40.772 4.11409 40.772H37.1842C39.2829 40.772 41 39.0549 41 36.9562V3.8861C41 1.78739 39.2829 0.0703125 37.1842 0.0703125ZM29.1838 5.52686L30.8246 7.18035C30.8246 7.18035 32.4654 5.53959 32.4654 5.52686C33.6101 4.38217 35.429 6.16282 34.2715 7.33297L32.618 8.97381C32.618 8.97381 34.2588 10.6146 34.2715 10.6146C35.3781 11.7211 33.6356 13.5782 32.4654 12.4207L30.8246 10.7672L29.1838 12.4207C28.9294 12.6623 28.6114 12.7896 28.2807 12.7896C27.1869 12.7896 26.5891 11.4032 27.3777 10.6146L29.0312 8.97381C29.0312 8.97381 27.3904 7.33297 27.3777 7.33297C26.2202 6.17555 28.0137 4.36943 29.1838 5.52686ZM24.4649 16.6054C24.4649 16.6054 34.615 16.6054 34.6404 16.6054C36.2812 16.6054 36.2812 19.1492 34.6404 19.1492C34.6404 19.1492 24.4904 19.1492 24.4649 19.1492C22.8496 19.1492 22.8114 16.6054 24.4649 16.6054ZM24.4141 21.6931C24.4141 21.6931 34.615 21.6931 34.6404 21.6931C36.2812 21.6931 36.2812 24.2369 34.6404 24.2369C34.6404 24.2369 24.4268 24.2369 24.4141 24.2369C22.8114 24.2369 22.7606 21.6931 24.4141 21.6931ZM29.5527 29.3247C29.5527 29.3247 24.4777 29.3247 24.4649 29.3247C22.8496 29.3247 22.8242 26.7808 24.4649 26.7808C24.4649 26.7808 29.5399 26.7808 29.5527 26.7808C31.1935 26.7808 31.1935 29.3247 29.5527 29.3247ZM16.8461 19.1492C16.8461 19.1492 6.67068 19.1492 6.65795 19.1492C5.05531 19.1492 5.01719 16.6054 6.65795 16.6054C6.65795 16.6054 16.8334 16.6054 16.8461 16.6054C18.4487 16.6054 18.4869 19.1492 16.8461 19.1492ZM5.75493 8.07071C6.25092 7.57465 7.06498 7.57465 7.56104 8.07071L9.2018 9.72421C9.2018 9.72421 13.3865 5.53959 13.3865 5.52686C14.5439 4.38217 16.35 6.16282 15.1926 7.33297L10.1049 12.4207C9.62157 12.904 8.79478 12.9167 8.29879 12.4207L5.75493 9.87683C5.25885 9.38084 5.25885 8.56678 5.75493 8.07071ZM16.8334 24.2369C16.8334 24.2369 6.67068 24.2369 6.65795 24.2369C5.02992 24.2369 5.01719 21.6931 6.65795 21.6931C6.65795 21.6931 16.8206 21.6931 16.8334 21.6931C18.4742 21.6931 18.4742 24.2369 16.8334 24.2369ZM33.3684 34.4124C33.3684 34.4124 24.4396 34.4124 24.4141 34.4124C22.7987 34.4124 22.7733 31.8685 24.4141 31.8685C24.4141 31.8685 33.343 31.8685 33.3684 31.8685C35.0093 31.8685 35.0093 34.4124 33.3684 34.4124ZM34.6404 29.3247C33.9408 29.3247 33.3684 28.7523 33.3684 28.0527C33.3684 27.3532 33.9408 26.7808 34.6404 26.7808C35.3399 26.7808 35.9123 27.3532 35.9123 28.0527C35.9123 28.7523 35.3399 29.3247 34.6404 29.3247Z"
				fill="currentColor"
			/>
		</svg>
	),
});

export const ThankyouTypeIcon = createIcon({
	displayName: "ThankyouTypeIcon",
	viewBox: "0 0 55 49",
	path: (
		<svg width="55" height="49" viewBox="0 0 55 49" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M10.7108 19.8286H1.53024C0.672951 19.8286 0 20.5008 0 21.3571V45.8106C0 46.6669 0.672951 47.3391 1.53024 47.3391H10.7108C11.568 47.3391 12.2401 46.6669 12.2401 45.8106V21.3571C12.241 20.5008 11.568 19.8286 10.7108 19.8286Z"
				fill="currentColor"
			/>
			<path
				d="M54.9769 22.7258C54.9769 20.2352 53.265 17.7148 49.9955 17.7148H35.1721C37.2897 13.9309 37.9124 8.60891 36.4403 4.71496C35.359 1.85088 33.2925 0.179658 30.6184 0.00962793L30.5752 0.00610383C28.835 -0.100495 27.3286 1.2016 27.184 2.93714C26.8029 6.79937 25.1087 13.6305 22.6806 16.0559C20.6361 18.098 18.8863 18.9534 15.9855 20.37C15.5656 20.5753 15.107 20.7991 14.6219 21.0405C14.6307 21.1453 14.636 21.2501 14.636 21.3576V45.5618C14.9862 45.6816 15.3319 45.8005 15.6715 45.9168C20.4589 47.5651 24.5962 48.9879 30.92 48.9879H42.9044C46.1747 48.9879 47.8858 46.4665 47.8858 43.9769C47.8858 43.2377 47.7359 42.4968 47.4386 41.8185C48.5279 41.6229 49.4813 41.0996 50.1878 40.2988C50.9895 39.3887 51.4313 38.1791 51.4313 36.8929C51.4313 36.1564 51.2814 35.4155 50.9851 34.7389C53.6107 34.286 54.9778 32.0351 54.9778 29.8098C54.9778 28.5191 54.5174 27.2197 53.6195 26.2682C54.5156 25.3159 54.9769 24.0164 54.9769 22.7258Z"
				fill="currentColor"
			/>
		</svg>
	),
});
