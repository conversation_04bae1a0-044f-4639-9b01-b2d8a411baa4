import { Flex, Text, VStack, Icon, Box } from "@chakra-ui/react";
import { MdOutlineCancel } from "react-icons/md";
import { InfoIcon } from "components/Icons";
import React from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { INFLATION_TEMPLATE_ID, INFLATION_TEMPLATE_ID_2 } from "config";

export type HybridToastNotificationProps = {
	title: string;
	studyingItem?: string | null;
	requestNumber?: string | null;
	reason?: string | null;
	status: "info" | "error";
	caseId: string | null;
	onClose?: () => void;
	type?: string;
};

const HybridToastNotification = ({
	title,
	studyingItem,
	requestNumber,
	reason,
	caseId,
	status,
	onClose,
	type,
}: HybridToastNotificationProps) => {
	const { locale } = useRouter();
	const dir = locale !== "ar" ? "rtl" : "ltr";
	const link =
		type === INFLATION_TEMPLATE_ID || type === INFLATION_TEMPLATE_ID_2
			? `smart-services/inflation-service/apply-inflation?requestId=${caseId}&isPending=true`
			: `smart-services/how-to-apply/apply-socialaid?requestId=${caseId}&isPending=true`;

	return (
		<Flex
			display={"flex"}
			justifyContent={"flex-start"}
			alignItems={"center"}
			w={"100%"}
			position={"relative"}
			bg={`brand.notification.background.${status}`}
			px={{ base: 2, md: 6 }}
			py={{ base: 2, md: 6 }}
			gap={{ base: 4, md: 6 }}
		>
			<InfoIcon w={42} h={42} color={`brand.notification.icon.${status}`} />
			<VStack alignItems={"flex-start"} w={"full"}>
				<Text color="brand.white.50" fontWeight={700}>
					{title}
				</Text>

				{/* <HStack alignItems={"flex-start"} spacing={1}>
					<Text color="brand.white.50">{studyingItem} </Text>
					<Link
						style={{ fontWeight: "bold", textDecoration: "underline", color: "#fff" }}
						href={link}
					>
						{requestNumber}
					</Link>
					<Text color="brand.white.50">{reason}</Text>
				</HStack> */}
				<Text color="brand.white.50">
					{studyingItem}{" "}
					<Text
						as="span"
						mx={1}
						textDecoration={"underline"}
						dir={"ltr"}
						fontWeight={"bold"}
						onClick={onClose}
					>
						<Link href={link}>{requestNumber}</Link>
					</Text>
					{reason}
				</Text>
			</VStack>
			<Box position={"relative"} mb={"auto"} onClick={onClose}>
				<Icon as={MdOutlineCancel} color="brand.white.50" w={18} h={18} />
			</Box>
		</Flex>
	);
};

export default HybridToastNotification;
