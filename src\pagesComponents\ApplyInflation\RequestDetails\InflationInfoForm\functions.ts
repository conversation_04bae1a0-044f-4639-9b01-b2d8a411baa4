import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	ApplyUtilityAllowance: "",
	UtilityProvider: "",
	UtilityAccountNumber: "",
};

const getValidationSchema = (t) => {
	return Yup.object({
		ApplyUtilityAllowance: Yup.string().required().label("thisField").nullable(),
		UtilityAccountNumber: Yup.number()
			.positive("mustBePositive")
			.when(["ApplyUtilityAllowance"], {
				is: (ApplyUtilityAllowance) => {
					return ApplyUtilityAllowance === "yes";
				},
				then: Yup.number()
					.positive("mustBePositive")
					.typeError("ThisFieldShouldbeNumber")
					.required()
					.label("thisField")
					.nullable(),
				otherwise: Yup.number().notRequired().nullable(),
			}),
		UtilityProvider: Yup.object().when(["ApplyUtilityAllowance"], {
			is: (ApplyUtilityAllowance) => {
				return ApplyUtilityAllowance === "yes";
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}),
			otherwise: Yup.object().notRequired().nullable(),
		}),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// If the form values change, trigger validation to update button state
	if (event && event.target && event.target.name === "ApplyUtilityAllowance") {
		setTimeout(() => formikProps.validateForm(), 0);
	}
};
export { getInitialValues, onChange, getValidationSchema };
