import { <PERSON>, <PERSON><PERSON>, Flex, <PERSON>, Show, Text } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { createColumnHelper } from "@tanstack/react-table";
import TablePage from "pagesComponents/TablePage";
import React, { ReactElement, useRef } from "react";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { formatCurrencyAmount, getEmiratesIdFromToken, getFormattedDate } from "utils/helpers";
import { ICrmAllowance } from "interfaces/CrmAllowance.interface";
import { MyAllowanceDetailModal } from "pagesComponents/DetailModal";
import { ChevronLeftIcon } from "@chakra-ui/icons";
import NextLink from "next/link";
import { useRouter } from "next/router";
import { DownloadIcon } from "components/Icons";

function MyAllowance(props: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["common", "tables"]);
	const { locale } = useRouter();
	const tableRef: any = useRef();

	const columnHelper = createColumnHelper<ICrmAllowance>();

	const columns = [
		columnHelper.accessor("TotalAmount", {
			cell: (info) => (
				<Text color="brand.blue.300" textDecoration="underline" fontSize="sm" cursor={"pointer"}>
					{formatCurrencyAmount(info.getValue(), t, locale)}
				</Text>
			),
			header: `${t("tables:amount")}`,
		}),
		columnHelper.accessor("RequestName", {
			cell: (info) => info.getValue(),
			header: `${t("tables:requestNumber")}`,
		}),
		columnHelper.accessor("PayDate", {
			cell: (info) => getFormattedDate(info.getValue(), "dd MMMM yyyy", locale),
			header: `${t("tables:date")}`,
			meta: {},
		}),
	];
	return (
		<Box pt={{ base: 4, md: 9 }} px={8} pb={8} w="100%" minH={"50vh"}>
			<Flex
				w="100%"
				p={{ base: 0, md: 4 }}
				border={{ base: "0px", md: "1px" }}
				borderColor={{ base: "brand.gray.250", md: "brand.gray.250" }}
				bg={{ base: "unset", md: "brand.white.50" }}
			>
				<Flex w={"100%"}>
					<Show below={"md"}>
						<Link
							as={NextLink}
							href={"/"}
							display={"flex"}
							alignItems={"center"}
							me={"-24px"}
							zIndex={2}
						>
							<ChevronLeftIcon
								h={"24px"}
								w={"24px"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
						</Link>
					</Show>
					<Text
						fontSize={{ base: "lg", md: "h4" }}
						fontWeight="medium"
						w={"100%"}
						textAlign={{ base: "center", md: "initial" }}
					>
						{t("myAllowance")}
					</Text>
					{props.allowance.length > 0 && (
						<Button
							variant="secondary"
							gap={4}
							onClick={() => {
								tableRef?.current?.downloadExcel();
							}}
						>
							<DownloadIcon w="16px" h="16px" />
							<Text>{t("common:download")}</Text>
						</Button>
					)}
				</Flex>
			</Flex>
			<TablePage
				columns={columns}
				data={props.allowance}
				Modal={MyAllowanceDetailModal}
				type={"allowance"}
				ref={tableRef}
			/>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const emiratesId = await getEmiratesIdFromToken(ctx.req);

	const allowance = (await BackendServices.retrieveAllowanceTransactions(emiratesId))?.Data || [];
	allowance.sort((a, b) => new Date(b.PayDate).getTime() - new Date(a.PayDate).getTime());

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables"])),
			allowance,
		},
	};
}

MyAllowance.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default MyAllowance;
