import { HStack, Box, Text, Link, Image } from "@chakra-ui/react";
import React from "react";

const InnerBox = ({ imgSrc, headerName, title, height, width, alt, isClicable, lineHeight }) => {
	return (
		<HStack>
			<Image src={imgSrc} h={height} w={width} alt={alt} marginRight="14px" />
			<Box lineHeight={lineHeight} width="full">
				<Text fontSize="18px" fontWeight="medium" width="full">
					{headerName}
				</Text>
				{isClicable ? (
					<Link color="brand.gray.200" opacity="0.5" href={title} isExternal fontSize="12px">
						{title}
					</Link>
				) : (
					<Text color="brand.gray.200" opacity="0.5" fontSize="12px">
						{title}
					</Text>
				)}
			</Box>
		</HStack>
	);
};

export default InnerBox;
