import React, { useRef } from "react";
import DatePicker from "react-datepicker";
import { Input, InputGroup, InputRightElement, Button } from "@chakra-ui/react";
import "react-datepicker/dist/react-datepicker.css";
import { CalendarIcon } from "@chakra-ui/icons";

const DateTimeInputField = (field: any, meta: any, props: any) => {
	const calendarRef = useRef(null);

	const icon = (
		<Button
			onClick={() => {
				if (calendarRef.current) {
					(calendarRef.current as any).setOpen(true);
				}
			}}
			mt={4}
		>
			<CalendarIcon fontSize="xl" />
		</Button>
	);

	// Handle the onChange event from react-datepicker
	const handleDateChange = (date: Date | null) => {
		// Call the parent's onChange handler with the date object
		if (props.onChange) {
			props.onChange(date);
		}
		// Also update the field value for formik
		if (field.onChange) {
			field.onChange({
				target: {
					name: field.name,
					value: date ? date.toISOString().split('T')[0] : '', // Format as YYYY-MM-DD
				},
			});
		}
	};

	return (
		<>
			<InputGroup>
				<Input
					{...field}
					{...props}
					as={DatePicker}
					dateFormat="yyyy-MM-dd"
					onChange={handleDateChange}
					onKeyDown={(e) => {
						e.preventDefault();
					}}
					value={props.value ? new Date(props.value) : ""}
					focusBorderColor={"brand.mainGold"}
					selected={props.value ? new Date(props.value) : ""}
					placeholderText={props.placeholder}
					popperPlacement={"top"}
					h="3.5rem"
					_disabled={{
						bg: "brand.gray.300",
						color: "brand.gray.400",
					}}
					ref={calendarRef}
				/>
				<InputRightElement color="brand.mainGold">{icon}</InputRightElement>
			</InputGroup>
		</>
	);
};

export default DateTimeInputField;
