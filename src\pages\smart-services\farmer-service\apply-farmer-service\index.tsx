import { Box, Button, Flex, Text } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import ProgressTracker from "components/ProgressTracker";
import { ReactElement, useEffect, useState } from "react";
import { useRouter } from "next/router";
import SocialAidInformationForm from "pagesComponents/ApplyFarmerAid/RequestDetails/SocialAidInformationForm";
import IncomeInformationForm from "pagesComponents/ApplyFarmerAid/RequestDetails/IncomeInformationForm";
import FamilyMembersInfoForm from "pagesComponents/ApplyFarmerAid/RequestDetails/FamilyMembersInfoForm";
import ReviewDocument from "pagesComponents/ApplyFarmerAid/RequestDetails/ReviewDocument";
import { useTranslation } from "next-i18next";
import AttachedDocuments from "pagesComponents/ApplyFarmerAid/RequestDetails/AttachedDocuments";
import useAppToast from "hooks/useAppToast";
import useRouterReady from "hooks/useRouterReady";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { FormContext } from "context/FormContext";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import {
	addLocalLookups,
	getContactIdFromToken,
	getLocalizedLookups,
	//handleApiErrorMessage,
	getEmiratesIdFromToken,
	mapFarmerAidFromCaseForm,
	handleApiErrorMessage,
} from "utils/helpers";
import useFamilyMembers from "pagesComponents/ApplyFarmerAid/RequestDetails/FamilyMembersInfoForm/useFamilyMembers";
import useAttachDocuments from "pagesComponents/ApplyFarmerAid/RequestDetails/AttachedDocuments/useAttachDocuments";
import useCustomerPulse from "hooks/useCustomerPulse";
import PersonalInformation from "pagesComponents/ApplyFarmerAid/RequestDetails/PersonalInformationForm";
import useFarmerRequest from "pagesComponents/ApplyFarmerAid/useFarmerRequest";
import { IFarmerForm } from "interfaces/FarmerAidForm.interface";
import _ from "lodash";
import { CUSTOMER_PULSE_AID_LINKING_ID, CUSTOMER_PULSE_SCRIPT_LINK } from "config";
import Script from "next/script";
import { modifyRequestFromPending } from "services/frontend";
import AccordionFarmerAid from "pagesComponents/ApplyFarmerAid/AccordianFarmerAid";

function ApplyFarmerService({
	masterData,
	userDetails,
	formData,
	templateId,
	customerPulseScriptLink,
	customerPulseLinkingId,
	isRequestPending,
	requestId,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	let tempActiveStep = 0;
	let tempActiveSubStep = 0;
	let tempCurrentStep = 0;

	if (isRequestPending) {
		tempCurrentStep = 4;
		tempActiveSubStep = 0;
		tempActiveStep = 1;
	}

	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const toast = useAppToast();
	const routerReady = useRouterReady();
	const { locale, query } = router;
	const [currentStep, setCurrentStep] = useState(tempCurrentStep);
	const [activeStep, setActiveStep] = useState(tempActiveStep);
	const [activeSubIndex, setActiveSubIndex] = useState(tempActiveSubStep);
	const [saveDraftLoading, setSaveDraftLoading] = useState(false);
	const [customerPulseLoading, customerPulseSubmitted, openCustomerPulse] = useCustomerPulse(
		userDetails?.EmiratesID!,
		customerPulseLinkingId!
	);

	const {
		callGetDocumentList,
		documentList,
		setDocumentStatus,
		getDocumentListLoading,
		attachDocumentsStepDisabled,
		isDocumentUploading,
	} = useAttachDocuments(query.requestId?.toString() || "", activeStep === 1);

	const { familyMembers, setFamilyMembers, familyMembersStepDisabled } = useFamilyMembers(
		formData?.CaseDetails,
		activeStep === 0 && activeSubIndex === 3
	);

	const [caseForm, setCaseForm] = useState((state) =>
		mapFarmerAidFromCaseForm(state, formData?.CaseDetails, userDetails, locale)
	);

	const [stepFormKey, setStepFormKey] = useState("");
	const [isSubmitingPending, setIsSubmittingPending] = useState(false);

	useEffect(() => {
		let selectedFormKey = "";
		if (currentStep < sectionsArray.length) {
			selectedFormKey = sectionsArray?.[activeSubIndex]?.formKey || "";
		} else if (
			currentStep >= sectionsArray.length &&
			currentStep < sectionsArray.length + documentsArray.length
		) {
			selectedFormKey = documentsArray?.[0]?.formKey || "";
		} else if (currentStep === maxSteps - 1) {
			selectedFormKey = reviewArray?.[0]?.formKey || "";
		}
		if (selectedFormKey !== stepFormKey) setStepFormKey(() => selectedFormKey);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeStep, activeSubIndex]);

	const [buttonState, setFormikState] = useState({
		personalInformation: { isDisabled: true, isLoading: false },
		socialAidInformation: { isDisabled: true, isLoading: false },
		incomeInformation: { isDisabled: false, isLoading: false },
		familyMembersInformation: { isDisabled: false, isLoading: false },
		attachedDocuments: { isDisabled: false, isLoading: false },
		reviewDocuments: { isDisabled: false, isLoading: false },
	});
	const handleSetFormikState = (newValues, formKey) => {
		if (
			buttonState?.[formKey]?.isDisabled !== newValues.isDisabled ||
			buttonState?.[formKey]?.isLoading !== newValues.isLoading
		) {
			setFormikState((prev) => ({ ...prev, [formKey]: newValues }));
		}
	};
	const handleAddDeleteFieldArray = (action, name, formKey, newObject, id = null) => {
		if (action === "delete") {
			setCaseForm((prev) => {
				let newState = { ...prev };
				newState[formKey][name].splice(id, 1);
				return newState;
			});
		}
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik, formKey, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, formKey, isFieldArray, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, formKey, isFieldArray);
		}
	};
	const handleTextChange = (event, fieldName, formik, formKey, isFieldArray, type) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (newState[formKey][parentFieldName]?.length !== formik.values[parentFieldName])
					newState[formKey][parentFieldName] = formik.values[parentFieldName].map((val) => {
						let newVal = {};
						Object.keys(val).forEach((key) => {
							if (typeof val[key] === "object") {
								newVal[key] = val[key].value || "";
							} else {
								newVal[key] = val[key];
							}
						});
						return newVal;
					});
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				) {
					if (type === "datetime") {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] = event || "";
					} else {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
							event?.target?.value || "";
					}
				}
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = event?.target?.value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, formKey, isFieldArray) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				)
					newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
						value?.value || value || "";
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = value?.value || value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, value);
	};

	useEffect(() => {
		if (isRequestPending) {
			callGetDocumentList();
		}
	}, [isRequestPending]);

	// this value is used to hide some steps in the form if the user doesn't need to enter  income info
	const doesntNeedIncomeInfo =
		caseForm.socialAidInformation.ReceiveSocialAid === "yes" ||
		caseForm.socialAidInformation.ReceiveInflationAllowance === "yes" ||
		caseForm.socialAidInformation.ReceiveSocialAid === true ||
		caseForm.socialAidInformation.ReceiveInflationAllowance === true ||
		caseForm.socialAidInformation.ReceiveSocialAid === undefined ||
		caseForm.socialAidInformation.ReceiveInflationAllowance === undefined ||
		caseForm.socialAidInformation.ReceiveInflationAllowance === "" ||
		caseForm.socialAidInformation.ReceiveSocialAid === "";

	let sectionsArray = [
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="personalInformation"
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					readOnly={isRequestPending}
				/>
			),
		},
		{
			title: t("farmerAidInformation"),
			formKey: "socialAidInformation",
			element: (
				<SocialAidInformationForm
					innerText={t("socialAidInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey="socialAidInformation"
					initialData={caseForm.socialAidInformation}
					handleSetFormikState={handleSetFormikState}
					EmiratesID={userDetails?.EmiratesID}
					readOnly={isRequestPending}
				/>
			),
		},
		{
			title: t("incomeInformation"),
			formKey: "incomeInformation",
			element: (
				<IncomeInformationForm
					innerText={t("incomeInformationSubtext")}
					key="2"
					handleChangeEvent={handleChangeEvent}
					formKey="incomeInformation"
					handleSetFormikState={handleSetFormikState}
					handleAddDeleteFieldArray={handleAddDeleteFieldArray}
					initialData={caseForm.incomeInformation}
					readOnly={isRequestPending}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="3"
					familyMembers={familyMembers}
					setFamilyMembers={setFamilyMembers}
					readOnly={isRequestPending}
				/>
			),
		},
	];

	let documentsArray = [
		{
			title: "",
			formKey: "attachedDocuments",
			element: (
				<AttachedDocuments
					innerText=""
					key="0"
					documentList={documentList}
					setDocumentStatus={setDocumentStatus}
				/>
			),
		},
	];

	let reviewArray = [
		{
			title: t("reviewDetails"),
			formKey: "reviewDetails",
			element: (
				<ReviewDocument
					innerText={t("reviewDetailsSubtext")}
					formKey="reviewDetails"
					setCurrentStep={setCurrentStep}
					handleStepsIndexes={_handleStepsIndexes}
					documentList={documentList}
					familyMembers={familyMembers}
					caseForm={caseForm}
					key="0"
					handleSetFormikState={handleSetFormikState}
					hasSSS={false}
				/>
			),
		},
	];
	const maxSteps = sectionsArray.length + documentsArray.length + reviewArray.length;
	const { updateRequest, updateRequestLoading } = useFarmerRequest(
		caseForm,
		setCaseForm,
		familyMembers,
		setFamilyMembers,
		activeStep,
		activeSubIndex,
		userDetails,
		templateId!
	);
	const steps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("personalInformation"),
				t("farmerAidInformation"),
				t("incomeInformationFarmer"),
				t("familyMembersInformationFarmer"),
			],
		},
		{ label: t("attachedDocumentsFarmer"), subSteps: [] },
		{ label: t("reviewDetailsFarmer"), subSteps: [] },
		{ label: t("requestSubmitted"), subSteps: [] },
	];

	const handleProceed = async (type) => {
		if (!isRequestPending) {
			if (activeStep !== 1 || (activeStep === 1 && doesntNeedIncomeInfo)) {
				// open customer pulse right before submitting
				// if (type === "submit" && !customerPulseSubmitted) {
				// 	openCustomerPulse();
				// 	return;
				// }
				const resp = await updateRequest({ submit: type === "submit" });
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
			if (type === "submit") return; // redirect handled by hook

			// before going to the document step, refetch the document list
			if ((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}

			if (currentStep === 1 && doesntNeedIncomeInfo) {
				// if the user fills incomer information and goes back to choose that he is receiving social aid or inflation,
				// the income information will still exist , so this request will be to clear them
				if (incomeNotEmpty(caseForm.incomeInformation)) {
					const resp = await updateRequest({ submit: false, clearIncome: true });
					if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
						handleApiErrorMessage(resp?.Errors, toast, t, locale);
						return;
					}
				}
				setCurrentStep(5);
				setActiveStep(1);
				setActiveSubIndex(0);
				setTimeout(() => {
					window.scrollTo({
						top: 0,
						behavior: "smooth",
					});
				}, 100);
				return;
			}
		} else {
			// before going to the document step, refetch the document list
			if (currentStep === 1 || currentStep === 3) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
				}
				setCurrentStep(4);
				setActiveStep(1);
				setActiveSubIndex(3);
				setTimeout(() => {
					window.scrollTo({
						top: 0,
						behavior: "smooth",
					});
				}, 100);
				return;
			}
			// submit the pending request
			if (activeStep === 1) {
				setIsSubmittingPending(true);
				const resp = await modifyRequestFromPending(requestId!);
				if (resp.IsSuccess) {
					router.push(
						`/smart-services/farmer-service/apply-farmer-service/edited-farmer?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
				setIsSubmittingPending(false);
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
		}
		setCurrentStep((currentIndex) => {
			if (currentIndex === maxSteps) {
				_handleStepsIndexes(currentIndex);
				return currentIndex;
			}
			_handleStepsIndexes(currentIndex + 1);
			setTimeout(() => {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			}, 100);
			return currentIndex + 1;
		});
	};

	const handleBack = () => {
		setCurrentStep((currentIndex) => {
			let target = 0;
			// if the user is in review and doest need income info send him back to social aid step
			if (currentIndex === 5 && doesntNeedIncomeInfo) {
				_handleStepsIndexes(1);
				target = 1;
			} else if (currentIndex === 0) {
				_handleStepsIndexes(currentIndex);
				router.push("/smart-services/farmer-service");
				target = currentIndex;
			} else {
				if (doesntNeedIncomeInfo && currentIndex === 4) {
					target = 1;
					_handleStepsIndexes(target);
				} else {
					target = currentIndex - 1;
					_handleStepsIndexes(currentIndex - 1);
				}
			}

			setTimeout(() => {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			}, 100);
			return target;
		});
	};
	function _handleStepsIndexes(currentIndex) {
		if (currentIndex < sectionsArray.length) {
			setActiveStep(0);
			setActiveSubIndex(currentIndex);
		} else if (
			currentIndex >= sectionsArray.length &&
			currentIndex < sectionsArray.length + documentsArray.length
		) {
			setActiveStep(1);
		} else if (currentIndex === maxSteps - 1) {
			setActiveStep(2);
		}
	}
	const onSaveAsDraft = async () => {
		setSaveDraftLoading(true);
		const resp = await updateRequest({ submit: false });
		let caseNumber = resp?.Data?.CaseDetails?.CaseRef || caseForm?.personalInformation?.caseID;
		if (resp?.Data?.IdCase) {
			toast({
				title: t("common:draftUpdateSuccess"),
				description: t("common:caseNumberUpdated", { draftId: caseNumber }),
				status: "info",
			});
		} else {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
		setSaveDraftLoading(false);
	};

	const proceedButtonLoading =
		!routerReady ||
		updateRequestLoading ||
		isDocumentUploading !== 0 ||
		(((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) && getDocumentListLoading) ||
		getDocumentListLoading; // Refetch document list before documents page and review page

	const showSaveAsDraft = !!query.requestId && !isRequestPending;

	// hide or show some steps on the progress bar
	const changeVisibleSteps = () => {
		if (doesntNeedIncomeInfo) {
			if (isRequestPending) {
				const copy = _.cloneDeep(steps);
				copy[0].subSteps = copy[0].subSteps.slice(0, 2);
				//copy.splice(1, 1);
				return copy;
			} else {
				const copy = _.cloneDeep(steps);
				copy[0].subSteps = copy[0].subSteps.slice(0, 2);
				copy.splice(1, 1);
				return copy;
			}
		}
		return steps;
	};

	// make the button text is "Submit" when the user is in the documents step in case of pending request (sent back)
	const isRequestPendingSubmit = activeStep === 1 && isRequestPending;

	return (
		<Box w="100%">
			<Script src={customerPulseScriptLink} strategy="afterInteractive" />

			<Box>
				<Flex direction={{ base: "column", md: "row" }}>
					<Box w={{ md: "300px", lg: "400px" }} mr={4} display={{ base: "block", md: "block" }}>
						<ProgressTracker
							activeStep={isRequestPending ? 1 : activeStep}
							activeSubIndex={isRequestPending ? 0 : activeSubIndex}
							steps={changeVisibleSteps()}
							service={t("farmerAidBreadTitle", { ns: "common" })}
						/>
					</Box>

					<Box
						pt={{ base: 0, md: 8 }}
						px={{ base: 0, md: 8 }}
						flexGrow={1}
						bg="white"
						boxShadow="unset"
					>
						<FormContext.Provider value={{ lookups: masterData }}>
							<Box mx={0} mb={4}>
								{/* First Section */}
								{currentStep < sectionsArray.length && (
									<AccordionFarmerAid
										currentStep={currentStep}
										sectionsArray={sectionsArray}
										hideBottom={true}
									/>
								)}
								{/* Second Section */}
								{currentStep >= sectionsArray.length &&
									currentStep < sectionsArray.length + documentsArray.length && (
										<AccordionFarmerAid
											currentStep={0}
											sectionsArray={documentsArray}
											hideBottom={true}
										/>
									)}
								{/* Third Section */}
								{currentStep === maxSteps - 1 && (
									<AccordionFarmerAid currentStep={0} sectionsArray={reviewArray} />
								)}
							</Box>
						</FormContext.Provider>

						<Flex mt={{ md: "2rem" }} mb="6.5rem" px={6} justifyContent="end">
							{/* <Box display={{ base: "none", md: "block" }}>
								{showSaveAsDraft && (
									mr={"24px"} pl={10}
									<Box mt={2}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && <Spinner color="brand.mainGold" />}
									</Box>
								)}
							</Box> */}
							<Flex
								w={{ base: "100%", md: "auto" }}
								px={{ base: 0, md: "unset" }}
								pb={{ base: 5, md: "unset" }}
								gap={4}
							>
								{/* {showSaveAsDraft && (
									<Box mt={2} w="100%" display={{ base: "block", md: "none" }}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												justifyContent="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && (
											<Flex>
												<Spinner mx={{ base: "auto", md: "unset" }} color="brand.mainGold" />
											</Flex>
										)}
									</Box>
								)} */}
								<Button
									w={{ base: "50%", md: "13.25rem" }}
									variant="secondary"
									isDisabled={currentStep === 0}
									onClick={handleBack}
								>
									<Text as="span">{t("back", { ns: "common" })}</Text>
								</Button>
								<Button
									w={{ base: "50%", md: "13.25rem" }}
									variant="primary"
									onClick={() => {
										handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
									}}
									isLoading={
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										customerPulseLoading ||
										isSubmitingPending
									}
									disabled={
										buttonState?.[stepFormKey]?.isDisabled ||
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										familyMembersStepDisabled ||
										attachDocumentsStepDisabled ||
										customerPulseLoading ||
										isSubmitingPending
									}
								>
									<Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1 || !customerPulseSubmitted
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text>
								</Button>
							</Flex>
						</Flex>
					</Box>
				</Flex>
			</Box>
		</Box>
	);
}
export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	return {
		redirect: {
			destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}smart-services/farmer-service`,
			permanent: false,
		},
	};
	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const profile = await BackendServices.retrieveContact(emiratesId);
	const userDetails = profile.Data;
	const requestId: string = ctx.query.requestId?.toString() ?? "";
	let isRequestPending = !!ctx.query.isPending?.toString();

	let formData: IFarmerForm | null = null;

	if (!!requestId) {
		const contactId = await getContactIdFromToken(ctx.req);
		formData = (await BackendServices.getRequest(requestId, contactId))?.Data || null;

		// if the request is not in draft or is not pending then redirect
		const allowedStatusesIds = masterData.CaseStatus.filter(
			(s) => s.Name === "Additional Information Required" || s.Name === "Drafted"
		).map((s) => s.Id);

		if (!formData || !allowedStatusesIds.includes(formData?.IdStatus!)) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/smart-services`,
					permanent: false,
				},
			};
		}
	}

	isRequestPending =
		formData?.IdStatus ===
		masterData.CaseStatus.find((s) => s.Name === "Additional Information Required")?.Id;

	//TODO to be implemented from backend
	const isUAECitizen = true;
	if (!isUAECitizen) {
		return {
			redirect: {
				destination: `/smart-services?notUaeCitizen=yes`,
				permanent: false,
			},
		};
	}

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "forms"])),
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			userDetails,
			formData,
			templateId: masterData.ProcessTemplates.find((te) => te.Name.includes("Farmer"))?.Id,
			customerPulseScriptLink: CUSTOMER_PULSE_SCRIPT_LINK,
			customerPulseLinkingId: CUSTOMER_PULSE_AID_LINKING_ID,
			isRequestPending,
			requestId: requestId || null,
		},
	};
}

function incomeNotEmpty(obj: any) {
	return !(
		_.isEmpty(obj) ||
		_.every(obj, (e) => e === undefined || e === null) ||
		(obj.householdHeadContributes === "no" &&
			obj.householdHeadPension === "no" &&
			obj.householdHeadTradeLicense === "no" &&
			obj.householdRentalIncomes === "no")
	);
}
ApplyFarmerService.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplyFarmerService;
