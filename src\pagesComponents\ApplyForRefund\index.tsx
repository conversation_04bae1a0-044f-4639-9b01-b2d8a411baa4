import {
	Button,
	Grid,
	<PERSON>ridI<PERSON>,
	Box,
	Text,
	Flex,
	useDisclosure,
	AlertDialog,
	AlertDialogContent,
	AlertDialog<PERSON>oot<PERSON>,
	AlertDialogHeader,
	AlertDialogO<PERSON>lay,
	Spinner,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Form, Formik } from "formik";
import { useRouter } from "next/router";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { SITE_URL } from "config";
import * as functions from "./functions";
import useAppToast from "hooks/useAppToast";
import { CrmRefund, CrmRefundData } from "interfaces/CrmRefund.interface";
import { useMutation } from "react-query";
import { RefundBreakdown, PurchaseRequest, RefundBreakdownSimulator } from "services/frontend";
import { ButtonArrowIcon } from "components/Icons";
import TablePage from "./TablePage";
import { AmountsTable } from "./AmountsTable";
import { createColumnHelper } from "@tanstack/react-table";
import StatusPill from "components/StatusPill";
import { getFormattedDate } from "utils/helpers";
import { useFormContext } from "context/FormContext";
import { PAYMENT_OPTIONS_MAPPING } from "config";

interface ApplyForRefundInterface {
	props?: any;
	caseId?: string;
	CaseRef?: string;
	CaseDetails?: any;
	refundInstallments?: CrmRefund[];
	contactId?: string;
	casePendingAmount?: number;
	caseReClaimAmount?: number;
	caseTotalRefundAmount?: number;
	caseAccumilatedAmount?: number;
	InstallmentsRates?: any;
	currentInstallment?: string;
}
interface Transaction {
	id: string;
	amount: number;
}
var formObject: CrmRefundData = {
	PaymentRate: "",
	CaseId: "",
	BeneficiaryId: "",
};
export default function ApplyForRefund({
	caseId,
	CaseRef,
	contactId,
	CaseDetails,
	refundInstallments,
	casePendingAmount,
	caseReClaimAmount,
	InstallmentsRates,
	currentInstallment,
	caseTotalRefundAmount,
	caseAccumilatedAmount,
}: ApplyForRefundInterface) {
	const CurrentInstallment = PAYMENT_OPTIONS_MAPPING[currentInstallment || ""];
	const { lookups } = useFormContext();
	const { t } = useTranslation(["forms", "common", "tables"]);
	const pendingPaymentStatus = "662410000";
	const paymentCompletedStatus = "2";
	const router = useRouter();
	const { locale } = router;
	const { isOpen, onOpen, onClose } = useDisclosure();
	const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
	const [selectedOption, setSelectedOption] = useState<string | null>(null);
	const cancelRef = React.useRef<any>();

	const toast = useAppToast();
	const getInitialData = () => {
		let initValues = { ...functions.getInitialValues };
		initValues.caseNumber = caseId || "";
		return initValues;
	};

	const [initialValues, setInitialValues] = useState(() => getInitialData());
	const [loadingApi, setLoadingApi] = useState(false);
	const [allPaymentsReceived, setAllPaymentsReceived] = useState(false);

	let transactionArray: Transaction[] = [];
	const ResponseUrl = `${SITE_URL}/${locale}/my-cases/apply-to-refund?caseId=${caseId}&paymentSuccess=true`;
	const ErrorUrl = `${SITE_URL}/${locale}/payment-error`;
	const [IsPendingRefund, setIsPendingRefund] = useState(CaseDetails?.IsPendingRefund || null);
	const [RefundFinancialStatus, setRefundFinancialStatus] = useState(
		CaseDetails?.RefundFinancialStatus || null
	);

	const [isLoading, setIsLoading] = useState(false);
	const [refundInstallmentsTable, setRefundInstallmentsTable] = useState<any>([]);
	const [isDisabled, setIsDisabled] = useState(false);
	const [previousSelectedOption, setPreviousSelectedOption] = useState(null);

	useEffect(() => {
		setIsPendingRefund(CaseDetails?.IsPendingRefund || null);
	}, [CaseDetails?.IsPendingRefund]);
	useEffect(() => {
		setRefundFinancialStatus(CaseDetails?.RefundFinancialStatus || null);
	}, [CaseDetails?.RefundFinancialStatus]);

	useEffect(() => {
		if (refundInstallments && refundInstallments.length > 0) {
			let allpaid = refundInstallments?.every((item) => {
				if (item.Status.toString() === paymentCompletedStatus) return true;
			});
			setAllPaymentsReceived(allpaid);
		}
	}, [refundInstallments]);

	const handleChangeEvent = (type, firstArg, secondArg, formik, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, isFieldArray);
		}
	};
	const callRefundBreakdownSimulator = (refundObject: CrmRefundData) => {
		return RefundBreakdownSimulator(
			refundObject.CaseId,
			refundObject.BeneficiaryId,
			refundObject.PaymentRate
		);
	};

	const { mutateAsync: submitrefundForm, isLoading: submittingFromLoading } = useMutation({
		mutationFn: (refundObject: CrmRefundData) =>
			RefundBreakdown(refundObject.CaseId, refundObject.BeneficiaryId, refundObject.PaymentRate),

		onSuccess: (data: any) => {
			// Handle success
		},
	});

	const handleTextChange = (event, fieldName, formik, type) => {
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, isFieldArray) => {
		formik.setFieldValue(fieldName, value);
	};

	const handlePurchaseRequest = async (responseUrl, errorUrl, transactions) => {
		try {
			setLoadingApi(true);
			let LangId = locale === "ar" ? "AR" : "EN";
			let resp = await PurchaseRequest(ResponseUrl, ErrorUrl, transactions, LangId);

			if (resp.IsSuccess) {
				toast({
					status: "info",
					title: t("tables:refundDone"),
				});

				if (resp?.Data?.status === "1") {
					window.location.href = resp?.Data?.URL;
				}
			} else {
				window.location.href = ErrorUrl;
			}
		} catch (error) {
			console.log("[+]error", error);
			// window.location.href = ErrorUrl;
			setLoadingApi(false);
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
	};

	const handleButtonClick = (id, amount) => {
		const transaction: Transaction = { id, amount };
		transactionArray = [transaction];
		handlePurchaseRequest(ResponseUrl, ErrorUrl, transactionArray);
	};

	const handlePayAllAmounts = () => {
		if (refundInstallments && refundInstallments.length > 0) {
			const transactions = refundInstallments.map((record) => ({
				id: record.Id,
				amount: record.Amount,
			}));
			handlePurchaseRequest(ResponseUrl, ErrorUrl, transactions);
		} else {
			// Handle the case where there are no transactions to pay
			toast({
				title: t("common:paymentErrorTitle"),
				description: t("common:paymentErrorDescription"),
				status: "error",
			});
		}
	};
	const sortedRefundInstallments = refundInstallments?.sort(
		(a, b) => new Date(a.DueDate)?.getTime() - new Date(b.DueDate)?.getTime()
	);
	const firstUnpaidRecord = sortedRefundInstallments?.find(
		(record) => record?.Status === 662410000
	);

	const columnHelper = createColumnHelper<CrmRefund>();
	const columns = [
		columnHelper.accessor("DueDate", {
			cell: (info) => getFormattedDate(info.getValue(), "dd MMMM yyyy", router?.locale),
			header: `${t("tables:DueDate")}`,
			meta: {},
		}),

		columnHelper.accessor("Amount", {
			cell: (info) => {
				return <Text fontSize="sm">{info.row?.original?.Amount}</Text>;
			},
			header: `${t("tables:Amount")}`,
		}),

		columnHelper.accessor("reClaimAmount", {
			cell: (info) => {
				return <Text fontSize="sm">{info.row?.original?.reClaimAmount}</Text>;
			},
			header: `${t("tables:reClaimAmount")}`,
		}),

		columnHelper.accessor("Status", {
			cell: (info) => {
				return <StatusPill status={info.getValue()} caseId={info.row?.original?.Id} />;
			},
			header: `${t("tables:status")}`,
		}),
		columnHelper.accessor("Action", {
			cell: (info) => {
				return (
					info.row?.original?.Status.toString() === pendingPaymentStatus && (
						<Button
							rightIcon={
								<ButtonArrowIcon
									w={"24px"}
									h={"24px"}
									transform={router?.locale === "ar" ? "scale(-1, 1)" : ""}
									marginInlineStart={1}
								/>
							}
							variant={"outline"}
							isLoading={loadingApi}
							isDisabled={loadingApi}
							minHeight={"0.2rem"}
							h={"2rem"}
							px={"1px !important"}
							onClick={() => handleButtonClick(firstUnpaidRecord?.Id, firstUnpaidRecord?.Amount)}
						>
							{t("common:ProceedToPayment")}
						</Button>
					)
				);
			},
			header: `${t("tables:Action")}`,
		}),
	];

	const columnsForRefundSimulat = [
		columnHelper.accessor("DueDate", {
			cell: (info) => getFormattedDate(info.getValue(), "dd MMMM yyyy", router?.locale),
			header: `${t("tables:DueDate")}`,
			meta: {},
		}),

		columnHelper.accessor("Amount", {
			cell: (info) => {
				return <Text fontSize="sm">{info.row?.original?.Amount}</Text>;
			},
			header: `${t("tables:Amount")}`,
		}),

		columnHelper.accessor("reClaimAmount", {
			cell: (info) => {
				return <Text fontSize="sm">{info.row?.original?.reClaimAmount}</Text>;
			},
			header: `${t("tables:reClaimAmount")}`,
		}),

		columnHelper.accessor("Status", {
			cell: (info) => {
				return <StatusPill status={info.getValue()} caseId={info.row?.original?.Id} />;
			},
			header: `${t("tables:status")}`,
		}),
	];

	const cancel = () => {
		router.push("/my-cases");
	};
	const handleSubmit = async (values) => {
		const selectedPaymentRateValue = values?.paymentRates?.value;
		formObject = {
			PaymentRate: selectedPaymentRateValue,
			CaseId: caseId || "",
			BeneficiaryId: contactId || "",
		};
		try {
			let resp = await submitrefundForm(formObject);

			if (resp.IsSuccess) {
				toast({
					status: "info",
					title: t("tables:refundInstallment"),
				});
				router.reload();
			} else {
				setLoadingApi(false);
				toast({
					title: t("common:genericErrorTitle"),
					description: t("common:genericErrorDescription"),
					status: "error",
				});
			}
		} catch (error) {
			setLoadingApi(false);
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
	};

	const rateOptions = InstallmentsRates?.map((rate) => ({
		label: rate?.Name,
		value: rate?.Id,
	}));
	const rateOptionsForDecreaseAllowance = rateOptions.filter((option) => option.label !== "100%");

	const columnsData = [];
	const data = [
		{ label: t("caseNumber", { ns: "common" }), value: CaseRef },
		{ label: t("TotalRefundAmount", { ns: "common" }), value: caseTotalRefundAmount },
		{ label: t("AccumulatedAmount", { ns: "common" }), value: caseAccumilatedAmount },
		{ label: t("PaidRefundAmount", { ns: "common" }), value: caseReClaimAmount },
		{ label: t("RemainingRefundAmount", { ns: "common" }), value: casePendingAmount },
	];

	const defaultValue =
		RefundFinancialStatus !== 3
			? rateOptionsForDecreaseAllowance.find((option) => option.label === CurrentInstallment)
			: rateOptions.find((option) => option.label === CurrentInstallment);

	useEffect(() => {
		const handleDefaultValue = () => {
			if (defaultValue) {
				// If defaultValue is set, call the API with it
				const selectedOptionId = defaultValue?.value;
				const refundObject = {
					CaseId: caseId || "",
					BeneficiaryId: contactId || "",
					PaymentRate: selectedOptionId,
				};
				callRefundBreakdownSimulator(refundObject)
					.then((response) => {
						const refundInstallmentsData = response?.Data;
						setRefundInstallmentsTable(refundInstallmentsData);
					})
					.catch((error) => {
						console.error("Error calling RefundBreakdownSimulator:", error);
					});
			}
		};

		handleDefaultValue();
	}, []);
	return (
		<Box p={{ base: 0, md: 8 }}>
			<AmountsTable columns={columnsData} data={data} />
			<Formik
				enableReinitialize
				initialValues={initialValues}
				validationSchema={functions.getValidationSchema}
				onSubmit={handleSubmit}
			>
				{(formik) => {
					return (
						<Form>
							<>
								<Box bg={"brand.white.50"} px={{ base: 4, md: 0 }} pb={8}>
									<Grid
										rowGap={{ base: 2, md: 2 }}
										columnGap={16}
										templateColumns="repeat(2, 1fr)"
										templateRows="auto"
									>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<Text fontWeight="bold" fontSize={{ base: "sm", md: "md" }} my="3">
												{t("PaymentRatesLabelHeader", { ns: "common" })}
											</Text>
											<FormField
												type="selectableTags"
												value={formik.values["paymentRates"] || defaultValue}
												placeholder={t("placeholder", { ns: "common" })}
												options={
													RefundFinancialStatus !== 3
														? rateOptionsForDecreaseAllowance
														: rateOptions
												}
												name={"paymentRates"}
												label={t("PaymentRatesLabel", { ns: "common" })}
												onChange={(selectedOption) => {
													const newSelectedOption = selectedOption.label;

													const handleRefund = () => {
														if (isLoading) {
															return;
														}
														setIsPendingRefund(true);
														sessionStorage.setItem(
															`selectedOption_${caseId}`,
															JSON.stringify(selectedOption)
														);
														setIsLoading(true);
														setIsDisabled(true);

														const selectedOptionId = selectedOption?.value;
														const refundObject = {
															CaseId: caseId || "",
															BeneficiaryId: contactId || "",
															PaymentRate: selectedOptionId,
														};

														callRefundBreakdownSimulator(refundObject)
															.then((response) => {
																setIsLoading(false);
																setIsDisabled(false);

																const refundInstallmentsData = response?.Data;
																setRefundInstallmentsTable(refundInstallmentsData);
															})
															.catch((error) => {
																setIsLoading(false);
																setIsDisabled(false);
																console.error("Error calling RefundBreakdownSimulator:", error);
															});
													};

													if (RefundFinancialStatus === 3) {
														if (newSelectedOption === CurrentInstallment) {
															setIsPendingRefund(false);
														} else if (newSelectedOption !== previousSelectedOption) {
															handleRefund();
														}
													} else {
														handleRefund();
													}

													setPreviousSelectedOption(newSelectedOption);

													handleChangeEvent(
														"selectableTags",
														selectedOption,
														"paymentRates",
														formik
													);
												}}
												disabled={isDisabled}
											/>

											<Box mt="1">
												{isLoading && (
													<Spinner size="sm" color="brand.mainGold" alignSelf="center" />
												)}
											</Box>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 2 }}>
											{/* // show defualt value and new selected value for decrease case  */}
											{RefundFinancialStatus !== 3 ? (
												<TablePage
													columns={columnsForRefundSimulat}
													data={refundInstallmentsTable}
												/>
											) : // show the refund table from BE after procced with an option in stop case
											!IsPendingRefund && RefundFinancialStatus === 3 ? (
												<TablePage columns={columns} data={refundInstallments} />
											) : // show defualt value for stop case
											IsPendingRefund && RefundFinancialStatus === 3 ? (
												<TablePage
													columns={columnsForRefundSimulat}
													data={refundInstallmentsTable}
												/>
											) : null}
										</GridItem>

										<GridItem colSpan={{ base: 2, md: 2 }}>
											<Flex
												justifyContent={"start"}
												alignItems={{ base: "flex-start", md: "center" }}
												mt={2}
												w="100%"
												flexDirection={{ base: "column", md: "row" }}
											>
												<Flex
													w="100%"
													direction={{ base: "column", md: "row" }}
													justifyContent="end"
												>
													{(!allPaymentsReceived || IsPendingRefund) && (
														<Button
															mx={{ base: 0, md: 4 }}
															my={{ base: 3, md: 0 }}
															variant="primary"
															type="submit"
															isDisabled={
																loadingApi ||
																(IsPendingRefund &&
																	formik.values["paymentRates"] === undefined &&
																	RefundFinancialStatus === 3) ||
																(formik.values["paymentRates"] === undefined &&
																	RefundFinancialStatus !== 3)
															}
															onClick={() => {
																setIsConfirmationModalOpen(true);
															}}
															isLoading={loadingApi}
														>
															<Text as="span">
																{(IsPendingRefund && RefundFinancialStatus === 3) ||
																(!IsPendingRefund && RefundFinancialStatus !== 3)
																	? t("proceed", { ns: "common" })
																	: RefundFinancialStatus === 3 && !IsPendingRefund
																	? t("PayFullAmount", { ns: "common" })
																	: t("proceed", { ns: "common" })}
															</Text>
														</Button>
													)}

													<Button variant={"secondary"} onClick={cancel}>
														{t("cancel", { ns: "common" })}
													</Button>
												</Flex>
											</Flex>
										</GridItem>
									</Grid>
								</Box>
							</>

							<AlertDialog
								isOpen={isConfirmationModalOpen}
								leastDestructiveRef={cancelRef}
								onClose={() => setIsConfirmationModalOpen(false)}
								isCentered
							>
								<AlertDialogOverlay>
									<AlertDialogContent>
										<AlertDialogHeader fontSize="lg" fontWeight="bold">
											<AlertDialogHeader fontSize="lg" fontWeight="bold">
												{`${t("ConfirmSelectRefundOption", { ns: "common" })} ?`}
											</AlertDialogHeader>
										</AlertDialogHeader>

										<AlertDialogFooter>
											<Button
												disabled={loadingApi}
												variant={"secondary"}
												onClick={() => setIsConfirmationModalOpen(false)}
											>
												{t("cancel", { ns: "common" })}
											</Button>
											<Button
												mx="3"
												variant="primary"
												isLoading={loadingApi}
												onClick={() => {
													setIsConfirmationModalOpen(false);
													setLoadingApi(true);
													if (IsPendingRefund) {
														handleSubmit(formik.values);
													} else if (!IsPendingRefund && RefundFinancialStatus === 3) {
														handlePayAllAmounts();
													}
												}}
											>
												{t("proceed", { ns: "common" })}
											</Button>
										</AlertDialogFooter>
									</AlertDialogContent>
								</AlertDialogOverlay>
							</AlertDialog>
						</Form>
					);
				}}
			</Formik>
		</Box>
	);
}
