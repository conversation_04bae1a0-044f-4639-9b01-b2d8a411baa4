import { Flex, Text, VStack, Icon, Box } from "@chakra-ui/react";
import { MdOutlineCancel } from "react-icons/md";
import { InfoIcon } from "components/Icons";
import React from "react";
import { useRouter } from "next/router";

export type ToastNotificationProps = {
	title: string;
	description?: string | null;
	status: "info" | "error";
	onClose?: () => void;
};

const ToastNotification = ({ title, description, status, onClose }: ToastNotificationProps) => {
	const { locale } = useRouter();
	const dir = locale !== "ar" ? "rtl" : "ltr";

	return (
		<Flex
			display={"flex"}
			justifyContent={"flex-start"}
			alignItems={"center"}
			w={"100%"}
			position={"relative"}
			bg={`brand.notification.background.${status}`}
			px={{ base: 2, md: 6 }}
			py={{ base: 2, md: 6 }}
			gap={{ base: 4, md: 6 }}
		>
			<InfoIcon w={42} h={42} color={`brand.notification.icon.${status}`} />
			<VStack alignItems={"flex-start"} w={"full"}>
				<Text color="brand.white.50" fontWeight={700}>
					{title}
				</Text>
				{description && (
					<VStack alignItems={"flex-start"} spacing={1}>
						{description.split("\n").map((item, idx) => (
							<Text color="brand.white.50" key={idx}>
								{item}
							</Text>
						))}
					</VStack>
				)}
			</VStack>
			<Box position={"relative"} mb={"auto"} onClick={onClose}>
				<Icon as={MdOutlineCancel} color="brand.white.50" w={18} h={18} />
			</Box>
		</Flex>
	);
};

export default ToastNotification;
