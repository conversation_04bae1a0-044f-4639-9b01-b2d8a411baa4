import { Button, Flex, Text } from "@chakra-ui/react";
import { ClockIcon } from "components/Icons";
import SectionTitle from "components/SectionTitle";
import TableCustom from "components/TableCustom";
// import { TableHeadersObject } from "components/TableCustom/TableCustomProps.ds";
import { useTranslation } from "react-i18next";
import NextLink from "next/link";

function PendingItems({
	tableHeaders,
	tableBody,
	caption,
	hasFooter,
	footerValues,
	showViewAll,
	statusLookup,
}) {
	const { t } = useTranslation();
	return (
		<>
			<SectionTitle>
				<Flex alignItems={"center"}>
					<ClockIcon w={7} h={7} mr={6} /> <Text>{t("myCases")}</Text>
				</Flex>
			</SectionTitle>
			<TableCustom
				tableHeaders={tableHeaders}
				tableBody={tableBody}
				caption={caption}
				hasFooter={hasFooter}
				footerValues={footerValues}
				tableName="mycases"
				statusLookup={statusLookup}
			></TableCustom>
			{showViewAll && tableBody?.length > 0 && (
				<Button as={NextLink} variant="secondary" w={{ base: "100%", md: "auto" }} href="/my-cases">
					{t("viewAll")}
				</Button>
			)}
		</>
	);
}

export default PendingItems;
