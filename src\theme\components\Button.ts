import type { ComponentStyleConfig } from "@chakra-ui/theme";

const Button: ComponentStyleConfig = {
	// The styles all button have in common
	baseStyle: {
		border: "none",
		fontWeight: "medium",
		borderRadius: "0.42rem",
		fontSize: "md",
		padding: "3 5",
		fontStyle: "normal",
	},
	variants: {
		primary: {
			bg: "brand.buttonColors.primary.bgColor",
			color: "white",
			borderRadius: "0.42rem",
			paddingLeft: "5",
			paddingTop: "3",
			paddingRight: "5",
			paddingBottom: "3",
			lineHeight: "6",
			Rad: "0.42rem",
			minH: "12",
			fontSize: "md",
			":not([disabled])": {
				_hover: {
					bg: "brand.buttonColors.primary.bgHoverColor",
					border: "1px solid",
					borderRadius: "0.42rem",
					borderColor: "brand.buttonColors.primary.borderHoverColor",
					transitionDelay: "0s, 0s",
					transitionDuration: " 0.125s, 0s",
					transitionProperty: "background-color, border",
					transitionTimingFunction: "linear, linear",
					cursor: "pointer",
					textDecoration: "none",
				},
				_active: {
					opacity: "0.8",
				},
			},
			":disabled": {
				color: "white",
				fontWeight: "medium",
				opacity: "1",
				bg: "brand.buttonColors.primary.disabled",
				_hover: {
					bg: "brand.buttonColors.primary.disabled",
				},
			},
		},
		secondary: {
			bg: "brand.buttonColors.secondary.bgColor",
			color: "brand.buttonColors.secondary.textColor",
			border: "1px solid black",
			borderColor: "brand.buttonColors.secondary.borderColor",
			paddingLeft: "5",
			paddingTop: "3",
			paddingRight: "5",
			paddingBottom: "3",
			lineHeight: "6",
			minH: "12",
			fontSize: "md",
			":not([disabled])": {
				_hover: {
					bg: "brand.buttonColors.secondary.bgHoverColor",
					transitionDelay: "0s, 0s",
					transitionDuration: " 0.125s, 0s",
					transitionProperty: "background-color, border",
					transitionTimingFunction: "linear, linear",
					cursor: "pointer",
					textDecoration: "none",
				},
				_focus: {
					borderColor: "brand.buttonColors.secondary.borderColor",
				},
				_active: {
					opacity: "0.8",
				},
			},
			":disabled": {
				_hover: {
					bg: "brand.buttonColors.secondary.bgHoverColor",
				},
			},
		},
		outline: {
			color: "brand.buttonColors.outline.textColor",
			border: "none",
			paddingLeft: "5",
			paddingTop: "3",
			paddingRight: "5",
			paddingBottom: "3",
			lineHeight: "6",
			minH: "12",
			fontSize: "md",
			":not([disabled])": {
				_hover: {
					bg: "brand.buttonColors.outline.bgHoverColor",
					transitionDelay: "0s, 0s",
					transitionDuration: " 0.125s, 0s",
					transitionProperty: "background-color, border",
					transitionTimingFunction: "linear, linear",
					cursor: "pointer",
					textDecoration: "none",
				},
				_active: {
					opacity: "0.8",
				},
			},
		},
		iconsBtn: {
			bg: "none",
		},
		link: {
			color: "#f3f3f3",
			border: "1px solid black",
			borderRadius: "0.42rem",
		},
	},
	defaultProps: {
		variant: "#DB4E18",
	},
};

export default Button;
