import { Box, <PERSON>, But<PERSON>, Flex } from "@chakra-ui/react";
import React from "react";
import NextLink from "next/link";
import { ButtonArrowIcon } from "components/Icons";
import { useRouter } from "next/router";

export default function MocdBoxComp({
	title,
	description,
	buttonText,
	bulink,
	icon,
	bg,
	isButton = false,
	disable = false,
}) {
	const { locale } = useRouter();
	return (
		<Box
			w={"100%"}
			bgImage={{
				base: bg,
				md: bg,
			}}
			backgroundSize={"cover"}
			backgroundPosition={"center"}
			position={"relative"}
			py={{ base: 8, md: 8 }}
			height={"100%"}
			px={{ base: 6, md: 9 }}
			borderRadius={"10px"}
		>
			<Flex
				zIndex={100}
				w={{ base: "100%" }}
				pr={{ base: 4, sm: 4, md: 4, lg: "unset" }}
				pl={{ base: 4, sm: 4, md: 4, lg: "unset" }}
				pt={{ base: 5, sm: 5, md: 5, lg: 6 }}
				pb={4}
				bg={"brand.secondary.bgColor"}
				direction={"column"}
				justifyContent={"space-between"}
				height={"full"}
			>
				<Box>
					{false && icon}
					<Box my={10}>
						<Text
							zIndex={100}
							color="brand.white.50"
							fontWeight={"700"}
							fontSize={{ base: "2xl", lg: "3xl" }}
						>
							{title}
						</Text>
						{false && (
							<Text
								textAlign={{ base: "justify", md: "justify" }}
								marginTop="5"
								fontWeight={400}
								color="brand.textColor"
								// @ts-ignore
								title={description}
								fontSize={"lg"} //it was sm changed by Amer Riyal based on last content changes
							>
								{description}
							</Text>
						)}
					</Box>
				</Box>
				{isButton ? (
					<Button
						variant="outline"
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
							/>
						}
						isLoading={disable}
						disabled={disable}
						fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
						color="brand.white.50"
						width={"fit-content"}
					>
						{buttonText}
					</Button>
				) : (
					<Button
						variant="outline"
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
							/>
						}
						as={NextLink}
						href={bulink}
						fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
						color="brand.white.50"
						width={"fit-content"}
					>
						{buttonText}
					</Button>
				)}
			</Flex>
		</Box>
	);
}
