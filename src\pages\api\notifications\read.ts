import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<boolean>>
) {
	const { ids } = req.body;
	if (!ids) return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const data = await BackendServices.updateNotificationsRead(ids as string[]);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
