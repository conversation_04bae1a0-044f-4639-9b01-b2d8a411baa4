import React, { useId } from "react";
import Select from "react-select";

export const customStyles = {
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
	}),
	control: (base, state) => ({
		...base,
		borderRadius: "0.42rem",
		// color: "white",
		display: "flex",
		borderColor: "#B08D44",
		"&:hover": {
			boxShadow: "0 0 0 1px #B08D44",
		},
		color: "red",
		border: state.isFocused ? 0 : "1px solid #DDE1E6",
		// This line disable the blue border
		boxShadow: state.isFocused ? "0 0 0 1px #B08D44" : 0,
		height: state.isMulti ? "auto !important" : "3.5rem",

		// border: isDisabled ? "2px solid #C1C7CD" : "1px solid #DDE1E6",
		// color: state.isDisabled ? "red" : "white",
	}),
	option: (provided, state) => ({
		...provided,
		display: "flex",
		color: "#001841",
		zIndex: 999,
		"&:hover": {
			background: "#DDE1E6",
		},
		backgroundColor: state.isSelected ? "#DDE1E6" : "white",
	}),
	menu: (styles, state) => ({
		...styles,
		marginBottom: "1px",
		// zIndex: 9999,
	}),

	multiValue: (styles) => ({
		...styles,
		background: "#B08D44",
		height: "auto !important",
		borderRadius: "16px",
		color: "white",
		"&:hover": {
			background: "#B08D44",
		},
	}),
	menuList: (provided, state) => ({
		...provided,
		paddingTop: "-1px",
		paddingBottom: "-4px",
		marginTop: "-4px",
		boxShadow: "2px 4px 10px 2px #DDE1E6",
	}),
	menuPortal: (provided) => ({
		...provided,
		left: "unset",
		right: "unset",
	}),
};

const disableStyle = {
	control: (base, state) => ({
		...base,
		// color: "white",
		display: "flex",
		background: "#F2F4F8",
		border: "0px",
		height: "3.5rem",
	}),
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
		display: "none",
	}),
};
const SelectableTagsField = (field: any, meta: any, props: any) => {
	return (
		<Select
			instanceId={useId()}
			menuPosition={"fixed"}
			isDisabled={props.isDisabled}
			options={props.options}
			isMulti={props.isMulti}
			isClearable={props.isClearable || false}
			styles={props.isDisabled ? disableStyle : customStyles}
			value={props.value}
			onChange={props.onChange}
			placeholder={props.placeholder}
			isSearchable={props.isSearchable || false}
			components={{
				IndicatorSeparator: () => null,
			}}
		/>
	);
};
export default SelectableTagsField;
