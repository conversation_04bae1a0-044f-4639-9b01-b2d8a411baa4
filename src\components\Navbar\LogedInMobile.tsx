import {
	useDisc<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON>eader,
	Flex,
	DrawerBody,
	HStack,
	Link,
	Box,
	Text,
	Divider,
	DrawerFooter,
	Accordion,
	AccordionItem,
	AccordionButton,
	AccordionPanel,
	AccordionIcon,
	VStack,
} from "@chakra-ui/react";
import { LogoutIcon } from "components/Icons";
import ProfileImage from "components/Header/ProfileImage";
import LangSwitcher from "components/LangSwitcher";
import { useLogout } from "hooks/useLogout";
import { useUserDetails } from "hooks/useUserDetails";
import NextLink from "next/link";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { formatEmiratesId } from "utils/helpers";
import { ArrowForwardIcon, CloseIcon } from "@chakra-ui/icons";
import { LANG_SWITCHER_ENABLED } from "config";
import { useSession } from "next-auth/react";
import <PERSON>ontResize<PERSON><PERSON>er from "./FontResizeDrawer";
import { Menu } from "utils/strapi/navbar";

const NavbarLogedInMobile = ({ headerMenu }: { headerMenu: Menu }) => {
	const { t } = useTranslation();
	const { push, locale, pathname } = useRouter();
	const subpath = pathname.split("/")[1];
	const { isOpen, onOpen, onClose } = useDisclosure();

	const logoutHandler = useLogout();
	const { status } = useSession();
	const userDetails = useUserDetails();
	const finalItems =
		status === "authenticated"
			? headerMenu.items
			: headerMenu.items.filter((item) => !item.required_auth);
	return (
		<>
			<Button variant={"primary"} onClick={onOpen} pe={0}>
				<Text>{t("menu")}</Text>
			</Button>
			<Drawer onClose={onClose} isOpen={isOpen} size="full" placement={"right"}>
				<DrawerOverlay />
				<DrawerContent>
					<DrawerHeader>
						<Flex w={"100%"}>
							<CloseIcon
								_hover={{ cursor: "pointer" }}
								color={"#606164"}
								onClick={onClose}
								ms={"auto"}
							/>
						</Flex>
					</DrawerHeader>
					<DrawerBody>
						<Flex flexDirection={"column"} alignItems="flex-start" gap="6" h={"100%"}>
							<Box bg="brand.gray.300" w="full" p="4" pos="relative" rounded="lg" onClick={onClose}>
								<HStack justifyContent={"space-between"}>
									<HStack
										gap={3}
										onClick={() => {
											push("/profile");
										}}
									>
										<ProfileImage />
										<Box>
											<Text fontWeight="700" fontSize="md2" color="brand.textColor" noOfLines={1}>
												{locale === "en"
													? userDetails?.fullNameEn
													: userDetails?.fullNameAr || userDetails?.fullNameEn}
											</Text>
											<Text fontWeight="400" fontSize="sm" color="brand.textSecondaryColor">
												{t("EmiratesID")}:{" "}
												<span dir={"auto"}>{formatEmiratesId(userDetails?.EmiratesID)}</span>
											</Text>
										</Box>
									</HStack>

									<Link as={NextLink} href="/profile">
										<ArrowForwardIcon transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"} />
									</Link>
								</HStack>
							</Box>
							{finalItems.map((route, idx) => {
								const isCurrentPath = `/${subpath}` === route.url ? true : false;
								var fW = isCurrentPath ? 600 : 400;
								const isDropdown = route?.children?.length > 0 || false;
								const localizedTitle = locale === "ar" ? route.title : route.english_title;
								if (isDropdown)
									return (
										<Accordion allowToggle p={0} key={idx}>
											<AccordionItem border="0" p={0}>
												<AccordionButton border="0" p={0} m={0}>
													{localizedTitle}
													<AccordionIcon mx={2} />
												</AccordionButton>

												<AccordionPanel p={0} mt={4}>
													<VStack align={"start"}>
														{route.children.map((child, idx) => {
															const isCurrentPath = `/${subpath}` === child.url ? true : false;
															var fW = isCurrentPath ? 600 : 400;
															const localizedTitle =
																locale === "ar" ? child.title : child.english_title;
															return (
																<Button
																	key={idx}
																	fontWeight={fW}
																	as={NextLink}
																	href={child.url!}
																	p={0}
																	onClick={onClose}
																>
																	{localizedTitle}
																</Button>
															);
														})}
													</VStack>
												</AccordionPanel>
											</AccordionItem>
										</Accordion>
									);
								return (
									<Button
										fontWeight={fW}
										as={NextLink}
										href={route.url!}
										ps={0}
										key={idx}
										onClick={onClose}
									>
										{localizedTitle}
									</Button>
								);
							})}

							<Divider borderColor={"#BBBCBD"} />

							<FontResizeDrawer />

							{LANG_SWITCHER_ENABLED && (
								<>
									<Divider borderColor={"#BBBCBD"} />
									<LangSwitcher onClose={onClose} />
								</>
							)}
						</Flex>
					</DrawerBody>
					<DrawerFooter justifyContent={"start"}>
						<Button
							onClick={() => {
								logoutHandler();
							}}
							mt={"auto"}
							mb={24}
							px={0}
						>
							<LogoutIcon me="2" transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"} />
							<Text fontSize={"sm"} fontWeight={"medium"}>
								{t("logOut")}
							</Text>
						</Button>
					</DrawerFooter>
				</DrawerContent>
			</Drawer>
		</>
	);
};

export default NavbarLogedInMobile;
