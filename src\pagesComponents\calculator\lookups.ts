const reason = [
	{
		label: "Low-income employed/retired household",
		labelAr: "أسرة عاملة/متقاعدة ذات الدخل المنخفض",
		value: "1",
	},
	{
		label: "Unemployed household",
		labelAr: " أسرة عاطلة عن العمل",
		value: "2",
	},
	{
		label: "PoD or health disability",
		labelAr: "أصحاب الهمم أو ذوي العجز الصحي",
		value: "3",
	},
	{
		label: "Woman in a difficult situation",
		labelAr: "امرأة تعاني من أوضاع معيشية صعبة",
		value: "4",
	},
	{
		label: "Child in a difficult situation",
		labelAr: "طفل يعاني من أوضاع معيشية صعبة",
		value: "5",
	},
];

const gender = [
	{
		label: "Male",
		labelAr: "ذكر",
		value: "1",
	},
	{
		label: "Female",
		labelAr: "أنثى",
		value: "2",
	},
];

const ageGroup = [
	{
		label: "21-29",
		labelAr: "21-29",
		value: "1",
	},
	{
		label: "30-39",
		labelAr: "30-39",
		value: "2",
	},
	{
		label: "40-49",
		labelAr: "40-49",
		value: "3",
	},
	{
		label: "50-59",
		labelAr: "50-59",
		value: "4",
	},
	{
		label: "60 years or older",
		labelAr: "٦٠ عام أو أكثر",
		value: "5",
	},
];

const ageGroupPOD = [
	{
		label: "0-17",
		labelAr: "0-17",
		value: "1",
	},
	{
		label: "18-20",
		labelAr: "18-20",
		value: "2",
	},
	{
		label: "21 years or above",
		labelAr: "21 عام أو أكثر",
		value: "3",
	},
];
const maritalStatus = [
	{
		label: "Single",
		labelAr: " أعزب / عزباء",
		value: "1",
	},
	{
		label: "Married",
		labelAr: "متزوج / متزوجة",
		value: "2",
	},
	{
		label: "Divorced",
		labelAr: " مُطلق / مُطلقة",
		value: "3",
	},
	{
		label: "Widowed",
		labelAr: " أرمل/ أرملة",
		value: "4",
	},
];

const boolean = [
	{
		label: "Yes",
		labelAr: "نعم",
		value: "1",
	},
	{
		label: "No",
		labelAr: "لا",
		value: "0",
	},
];
const ageOfTheOldestEmployedFamilyMember = [
	{
		label: "21-29",
		labelAr: "21-29",
		value: "1",
	},
	{
		label: "30-39",
		labelAr: "30-39",
		value: "2",
	},
	{
		label: "40-49",
		labelAr: "40-49",
		value: "3",
	},
	{
		label: "50-59",
		labelAr: "50-59",
		value: "4",
	},
	{
		label: "60 years or older",
		labelAr: "٦٠ عام أو أكثر",
		value: "5",
	},
];
const ageGroupShort = [
	{
		label: "25-44",
		labelAr: "25-44",
		value: "1",
	},
	{
		label: "45-49",
		labelAr: "45-49",
		value: "2",
	},
	{
		label: "50-54",
		labelAr: "50-54",
		value: "3",
	},
	{
		label: "55-59",
		labelAr: "55-59",
		value: "4",
	},
	{
		label: "60 years or older",
		labelAr: "٦٠ عام أو أكثر",
		value: "5",
	},
];
const healthDisablity = [
	{
		label: "HIV/AIDs",
		labelAr: "مرض نقص فيروس المناعة المكتسبة/ الإيدز",
		value: "1",
	},
	{
		label: "Tuberculosis",
		labelAr: "مرض السل الرئوي",
		value: "2",
	},
	{
		label: "Liver Disease",
		labelAr: "أمراض الكبد الفيروسية",
		value: "3",
	},
	{
		label: "Other",
		labelAr: "أُخرى",
		value: "4",
	},
];

const currentSituation = [
	{
		label: "Divorced",
		labelAr: "مُطلقة",
		value: "1",
	},
	{
		label: "Widowed",
		labelAr: "أرملة ",
		value: "2",
	},
	{
		label: "Spouse of a prisoner",
		labelAr: "زوجة سجين",
		value: "3",
	},
	{
		label: "Spouse of an incapacitated foreigner (has medical disability / imprisoned / deported)",
		labelAr: "زوجة أجنبي عاجز صحياً / سجين / مبعد ",
		value: "4",
	},
];
const ageGroupWomen = [
	{
		label: "21-29",
		labelAr: "21-29",
		value: "1",
	},
	{
		label: "30-39",
		labelAr: "30-39",
		value: "2",
	},
	{
		label: "40-49",
		labelAr: "40-49",
		value: "3",
	},
	{
		label: "50-59",
		labelAr: "50-59",
		value: "4",
	},
	{
		label: "60 years or older",
		labelAr: "٦٠ عام أو أكثر",
		value: "5",
	},
];
const childAttributes = [
	{
		label: "Less than 4 years old",
		labelAr: "يقل عمره/ عمرها عن 4 سنوات ",
		value: "1",
	},
	{
		label: "PoD and less than 21 years old",
		labelAr: "من أصحاب الهمم ويقل عمره/ عمرها عن 21 سنة ",
		value: "2",
	},
	{
		label: "PoD who is less than 25 years old and a qualified student",
		labelAr: "من أصحاب الهمم وهو طالب مؤهل/ طالبة مؤهلة ويقل عمره/ عمرها عن 25 عاماً",
		value: "3",
	},
	{
		label: "None of the above",
		labelAr: "لا شيء مما ذكر",
		value: "4",
	},
];
const ageGroupChild = [
	{
		label: "0-20",
		labelAr: "0-20",
		value: "1",
	},
	{
		label: "21-24",
		labelAr: "21-24",
		value: "2",
	},
	{
		label: "25 years or above",
		labelAr: "25 عام أو أكثر",
		value: "3",
	},
];
const currentChildSituation = [
	{
		label: "Orphan",
		labelAr: "يتيم",
		value: "1",
	},
	{
		label: "Child of a prisoner",
		labelAr: "ابن/ ابنة سجين",
		value: "2",
	},
	{
		label: "Child of unknown parentage",
		labelAr: "مجهول/ مجهولة النسب",
		value: "3",
	},
];
export const lookups = {
	ageGroup,
	boolean,
	gender,
	maritalStatus,
	reason,
	ageOfTheOldestEmployedFamilyMember,
	ageGroupShort,
	ageGroupPOD,
	healthDisablity,
	currentSituation,
	ageGroupWomen,
	childAttributes,
	ageGroupChild,
	currentChildSituation,
};

export const getLookUp = (name: keyof typeof lookups, locale = "ar") => {
	return lookups[name].map((lookup) => ({
		label: locale === "ar" ? lookup.labelAr : lookup.label,
		value: lookup.value,
	}));
};
export const localizedLookups = (locale = "ar") => {
	const loc = {} as any;
	for (const key in lookups) {
		loc[key] = lookups[key].map((lookup) => ({
			label: locale === "ar" ? lookup.labelAr : lookup.label,
			value: lookup.value,
		}));
	}
	return loc;
};
