import {
	<PERSON>,
	<PERSON><PERSON>,
	Text,
	Modal,
	<PERSON>dal<PERSON>ody,
	Modal<PERSON>lose<PERSON>utton,
	<PERSON>dal<PERSON>ontent,
	ModalHeader,
	ModalOverlay,
	useDisclosure,
	Flex,
} from "@chakra-ui/react";
import { ButtonArrowIcon } from "components/Icons";
import { STATUS_SLUG_MAPPING } from "config";
import { ICrmMasterData, ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import ApplyForAppeal from "pagesComponents/ApplyForAppeal";
import { useRef, useState } from "react";
import { modifyRequest } from "services/frontend";
import useAppToast from "hooks/useAppToast";

interface StatusPillProps {
	status?: any;
	customText?: string;
	customStatus?: "pending" | "submitted" | "requestApproved";
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
	onClick?: any;
	caseId?: string;
	requestName?: string;
	eligibleForAppeal?: boolean;
	eligibleForRefund?: boolean;
	eligibleForEdit?: boolean;
}

const StatusPill = ({
	status,
	customText,
	customStatus,
	statusLookup,
	eligibleForAppeal,
	eligibleForEdit,
	caseId,
	requestName,
	onClick = () => {},
}: StatusPillProps) => {
	const { t } = useTranslation();
	let bgColor;
	let textColor;
	const { isOpen, onOpen, onClose } = useDisclosure();
	const cancelRef = useRef<any>();
	const [callingApi, setCallingApi] = useState(false);
	const toast = useAppToast();
	const { query, isReady, replace, push } = useRouter();

	const router = useRouter();
	const locale = router.locale;
	const handleRefund = (caseId: string) => {
		router.push(`/my-cases/apply-to-refund?caseId=${caseId}`);
	};
	const lkpStatusObject = statusLookup?.find((i) => i.value === (status?.Key || ""));

	const lkpStatus = STATUS_SLUG_MAPPING[lkpStatusObject?.value || ""];
	const refundStatus = STATUS_SLUG_MAPPING[status || ""];

	const statusSlug = refundStatus || customStatus || lkpStatus || status?.Value || "draft";

	if (statusSlug.startsWith("pending")) {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "submitted") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "inProgress") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "requestApproved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "resolved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "requestRejected") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "draft" || statusSlug === "temporaryQueue") {
		bgColor = "#DDE1E6";
		textColor = "#697077";
	} else if (statusSlug === "annualReviewUpdate") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "PendingRefund") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else {
		bgColor = "#f5f5f5";
		textColor = "#697077";
	}

	if (refundStatus == "PendingPayment") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (refundStatus == "OverduePayment") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (refundStatus == "PendingConfirmation") {
	} else if (refundStatus == "PaymentCompleted") {
	}

	if (statusSlug === "draft") {
		return (
			<Button
				rightIcon={
					<ButtonArrowIcon
						w={"24px"}
						h={"24px"}
						transform={locale === "ar" ? "scale(-1, 1)" : ""}
						marginInlineStart={1}
					/>
				}
				variant={"outline"}
				minHeight={"0.2rem"}
				h={"2rem"}
				px={"1px !important"}
				onClick={onClick}
			>
				{t("proceed")}
			</Button>
		);
	}

	if (statusSlug === "PendingRefund") {
		return (
			<Flex
				alignItems={"center"}
				flexDir={{
					base: "column",
					md: "row",
				}}
				gap={4}
			>
				<Box
					bgColor="#FFF0F0"
					textColor="#C42828"
					px={2}
					borderRadius={"5px"}
					py="1"
					w={"fit-content"}
					borderWidth={"1px"}
					borderColor="#C42828"
					h={"fit-content"}
					onClick={onClick}
				>
					<Text
						fontSize={["sm", "sm", "sm"]}
						fontWeight={500}
						color="#C42828"
						p={-2}
						w={"100%"}
						textAlign={"center"}
					>
						{t(`PendingRefund`)}
					</Text>
				</Box>
			</Flex>
		);
	}
	const handleEditClick = async (e) => {
		e.stopPropagation();

		setCallingApi(true);
		try {
			const data = await modifyRequest({
				UpdateType: "CREATE",
				ParentCaseId: caseId || undefined,
				CaseDetails: {},
				CaseType: 2,
			});

			if (data.Data && data?.IsSuccess) {
				toast({
					title: t("pleaseWaitLoadDraft", { ns: "common" }),
					status: "info",
				});

				if (data?.Data.IdCase)
					push(
						`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&editCase=true`
					);
			} else {
				toast({
					title: t("common:genericErrorTitle"),
					description: t("common:genericErrorDescription"),
					status: "error",
				});
			}
		} catch (error) {
			console.error("API error:", error);
			setCallingApi(false);
			// Handle the error as needed
		}
	};

	const handleAppealClick = (e) => {
		e.stopPropagation();
		onOpen();
	};

	return (
		<Flex
			alignItems={"center"}
			flexDir={{
				base: "column",
				md: "row",
			}}
			gap={4}
			w="100%"
		>
			<Box w={"100%"}>
				<Box
					bg={bgColor}
					px={2}
					borderRadius={"5px"}
					py="1"
					w={"fit-content"}
					borderWidth={"1px"}
					borderColor={textColor}
					h={"fit-content"}
					onClick={onClick}
					_hover={{ cursor: "pointer" }}
				>
					<Text
						fontSize={["sm", "sm", "sm"]}
						fontWeight={500}
						color={textColor}
						p={-2}
						w={"100%"}
						textAlign={"center"}
					>
						{customText ||
							lkpStatusObject?.label ||
							status?.Value ||
							t(`caseStatus.${statusSlug}`) ||
							refundStatus}
					</Text>
				</Box>
			</Box>

			<Modal isOpen={isOpen} isCentered onClose={onClose} size={"xl"}>
				<ModalOverlay />
				<ModalContent w="fit" maxW="95%">
					<ModalHeader>{t("appeal")}</ModalHeader>
					<ModalCloseButton />
					<ModalBody p={0} maxHeight="80vh" overflowY="auto">
						<ApplyForAppeal
							caseId={caseId || ""}
							requestName={requestName || ""}
							onClose={onClose}
						></ApplyForAppeal>
					</ModalBody>
				</ModalContent>
			</Modal>
		</Flex>
	);
};

export default StatusPill;
