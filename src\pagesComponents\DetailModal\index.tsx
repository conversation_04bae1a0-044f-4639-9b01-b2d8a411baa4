import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Text,
	Table,
	TableContainer,
	Tbody,
	Td,
	Tr,
	Flex,
	ModalCloseButton,
	Box,
} from "@chakra-ui/react";
import { DownloadIcon } from "components/Icons";
import StatusPillInModal from "components/StatusPillInModal";
import { ICrmAllowance } from "interfaces/CrmAllowance.interface";
import { ICrmRequest } from "interfaces/CrmRequest.interface";
import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
import { useRouter } from "next/router";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import {
	formatEmiratesId,
	formatUaePhoneNumber,
	getFormattedDate,
	getNewLocalizedFullName,
	getSpaceSeparatedString,
} from "utils/helpers";
import usePdfDownloader from "./usePdfDownloader";

import { ICrmMasterData, ICrm<PERSON>ookupLocalized } from "interfaces/CrmMasterData.interface";
import { ICrmContact } from "interfaces/CrmContact.interface";
import CustomedStatusPill from "components/CustomedStatusPill";

interface _DetailModalProps {
	isModalShow: boolean;
	handleOnClose: () => void;
	requestDate: Date;
	requestNumber: string;
	requestStatus?: ICrmRequest["Status"];
	complaintStatus?: string;
	contactNameEn: string;
	contactNameAr: string;
	contactEmail: string;
	contactPhone: string;
	contactEmiratesId?: string;
	type: "my-cases" | "my-allowance" | "my-complaints";
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
	template?: ICrmRequest["Template"];
	data?: ComplaintForm;
}

const DetailModal = ({
	isModalShow,
	handleOnClose,
	requestDate,
	requestNumber,
	requestStatus,
	complaintStatus,
	contactNameEn,
	contactNameAr,
	contactEmail,
	contactPhone,
	contactEmiratesId,
	type,
	statusLookup,
	template,
	data,
}: _DetailModalProps) => {
	const downloadPdf = usePdfDownloader();
	const caseType = data?.CaseType;
	const caseTypeTranslationKey = (() => {
		switch (caseType) {
			case "Thank You":
				return "thankYou";
			case "Complaint":
				return "complaint";
			case "Suggestion":
				return "suggestion";
			case "Inquiry":
				return "inquiry";
			default:
				return "defaultKey";
		}
	})();

	const { t } = useTranslation(["common", "tables"]);
	const { locale } = useRouter();
	const ref = useRef(null);
	return (
		<>
			<Modal
				size={{ base: "sm", md: "xl" }}
				isCentered
				onClose={handleOnClose}
				isOpen={isModalShow}
			>
				<ModalOverlay />
				<ModalContent borderRadius="12px" ref={ref} id={"myModal2"} pt={4}>
					<ModalHeader>
						{type === "my-complaints"
							? t("common:complaintSummaryTitle", {
									inquiryType: t(`common:${caseTypeTranslationKey}`),
							  })
							: t("common:applicationSummary")}
					</ModalHeader>

					<ModalCloseButton className="myCase-close" />
					<ModalBody>
						{/* {requestNumber && (
							<Flex
								justifyContent="center"
								alignItems={"center"}
								gap={2}
								flexDirection={{ base: "column", md: "row" }}
							>
								<Text bg="brand.gray.300" px="4px" py="2px" h="24px" fontSize="sm">
									{t("tables:submittedOn")}:{" "}
									<Text as="span" color="brand.textColor" fontWeight="bold">
										{getFormattedDate(requestDate, "dd, MMMM yyyy", locale)}
									</Text>
								</Text>
								
							</Flex>
						)} */}
						{requestNumber && (
							<Flex justifyContent={"space-between"} w="100%">
								<Box>
									<Text fontWeight={"600"} fontSize={"1.25rem"} mb="0.75rem">
										{locale === "ar" ? template?.TemplateNameAr : template?.TemplateName}
									</Text>
									<Text mb="3px">
										{t("tables:submittedOn")} :{" "}
										<Text as="span">{getFormattedDate(requestDate, "dd, MMMM yyyy", locale)}</Text>
									</Text>
									<Flex alignItems="baseline">
										<Text>
											{type === "my-complaints"
												? t("tables:complaintNumber")
												: t("tables:requestNumber")}{" "}
											:
										</Text>
										<Text
											color="brand.textColor"
											marginInlineStart={"1"}
											style={{ direction: "rtl" }}
										>
											{requestNumber}
										</Text>
									</Flex>

									{type === "my-complaints" && data?.CaseResolution && (
										<Text>
											{t("tables:comment")}
											{" : "}
											<Text as="span" color="brand.textColor" marginInlineStart={"1"}>
												{data?.CaseResolution}
											</Text>
										</Text>
									)}
								</Box>
								{type === "my-cases" && requestStatus && (
									<Box>
										<StatusPillInModal status={requestStatus} statusLookup={statusLookup} />
									</Box>
								)}
								{type === "my-complaints" && (
									<CustomedStatusPill
										complaintId={data?.Id}
										AppealStatus={data?.IsReopen}
										status={data?.Status}
										ModifiedOn={data?.ModifiedOn}
										inModal={true}
									/>
								)}
							</Flex>
						)}
						<TableContainer
							borderWidth="1px"
							rounded={"lg"}
							borderColor={"brand.tableBorderColor"}
							mt="1.5rem"
						>
							<Table variant="unstyled">
								<Tbody
									borderBottomWidth="1px"
									borderColor={"brand.tableBorderColor"}
									fontSize={{ base: "sm", md: "md" }}
								>
									<Tr borderBottom={"1px solid "} borderColor={"brand.tableBorderColor"}>
										<Td px={{ base: 2 }} w={"50%"} fontWeight={"bold"}>
											{t("tables:name")}
										</Td>
										<Td px={{ base: 2 }} color="brand.familyTableRowColor" whiteSpace={"pre-line"}>
											{locale === "ar" ? contactNameAr || "-" : contactNameEn || "-"}
										</Td>
									</Tr>
									<Tr borderBottom={"1px solid "} borderColor={"brand.tableBorderColor"}>
										<Td fontWeight={"bold"} px={{ base: 2 }}>
											{t("tables:emiratesID")}
										</Td>
										<Td px={{ base: 2 }} color="brand.familyTableRowColor">
											{formatEmiratesId(contactEmiratesId) || "-"}
										</Td>
									</Tr>
									<Tr borderBottom={"1px solid "} borderColor={"brand.tableBorderColor"}>
										<Td px={{ base: 2 }} fontWeight={"bold"}>
											{t("tables:email")}
										</Td>
										<Td px={{ base: 2 }} color="brand.familyTableRowColor">
											{contactEmail || "-"}
										</Td>
									</Tr>
									<Tr>
										<Td fontWeight={"bold"} px={{ base: 2 }}>
											{t("tables:mobileNumber")}
										</Td>
										<Td
											px={{ base: 2 }}
											color="brand.familyTableRowColor"
											dir={"ltr"}
											textAlign={locale === "ar" ? "end" : "start"}
										>
											{formatUaePhoneNumber(contactPhone)}
										</Td>
									</Tr>
								</Tbody>
							</Table>
						</TableContainer>
					</ModalBody>
					<ModalFooter borderTop={"1px solid "} borderColor={"brand.tableBorderColor"} mt="4">
						<Button
							variant="secondary"
							w={"100%"}
							gap={4}
							mt={2}
							className={"download-butn"}
							onClick={() => {
								downloadPdf(ref, `Case - ${requestNumber}.pdf`);
							}}
						>
							<DownloadIcon w="16px" h="16px" />
							<Text>{t("common:download")}</Text>
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</>
	);
};

interface ModalProps<T> {
	isModalShow: boolean;
	handleOnClose: () => void;
	data: T;
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
}

export const MyCasesDetailModal = ({ statusLookup }) => {
	const MyCasesDetailModalComponent = ({
		isModalShow,
		handleOnClose,
		data,
	}: ModalProps<ICrmRequest>) => {
		return (
			<DetailModal
				isModalShow={isModalShow}
				handleOnClose={handleOnClose}
				requestNumber={data?.RequestName}
				requestDate={data?.CreatedDate}
				requestStatus={data?.Status}
				template={data?.Template}
				contactNameEn={data?.Contact?.FullNameEn || ""}
				contactNameAr={data?.Contact?.FullNameAr || ""}
				contactEmiratesId={data?.Contact.EmirateID}
				contactEmail={data?.Contact?.Email}
				contactPhone={data?.Contact?.MobileNumber}
				type="my-cases"
				statusLookup={statusLookup}
			/>
		);
	};
	return MyCasesDetailModalComponent;
};

export const MyComplaintsDetailModal = ({
	statusLookup,
	contactEmiratesId,
	contact,
}: {
	statusLookup: any;
	contactEmiratesId: any;
	contact: ICrmContact;
}) => {
	const MyComplaintsDetailModalComponent = ({
		isModalShow,
		handleOnClose,
		data,
	}: ModalProps<ComplaintForm>) => {
		return (
			<DetailModal
				isModalShow={isModalShow}
				handleOnClose={handleOnClose}
				requestNumber={data?.TicketNumber}
				requestDate={data?.CreatedOn}
				complaintStatus={String(data?.Status)}
				contactEmail={data?.EmailAddress}
				contactEmiratesId={contactEmiratesId}
				contactPhone={data?.PhoneNumber}
				data={data}
				type="my-complaints"
				statusLookup={statusLookup}
				contactNameEn={getNewLocalizedFullName(contact, "en")}
				contactNameAr={getNewLocalizedFullName(contact, "ar")}
			/>
		);
	};
	return MyComplaintsDetailModalComponent;
};

export const MyAllowanceDetailModal = ({
	isModalShow,
	handleOnClose,
	data,
}: ModalProps<ICrmAllowance>) => {
	return (
		<DetailModal
			isModalShow={isModalShow}
			handleOnClose={handleOnClose}
			requestNumber={data?.RequestName}
			requestDate={data?.PayDate}
			contactNameEn={getSpaceSeparatedString([data?.Contact?.FirstName, data?.Contact?.LastName])}
			contactNameAr={getSpaceSeparatedString([
				data?.Contact?.FirstNameAr,
				data?.Contact?.LastNameAr,
			])}
			contactEmail={data?.Contact?.Email}
			contactEmiratesId={data?.Contact?.EmirateID}
			contactPhone={data?.Contact?.MobileNumber}
			type="my-allowance"
		/>
	);
};
