import { <PERSON>, But<PERSON>, Flex } from "@chakra-ui/react";
import { SaveIcon } from "components/Icons";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import NextLink from "next/link";
import { getFormattedDate } from "utils/helpers";
import { useRouter } from "next/router";
function RequestEdited({ caseRequest, caseRef }) {
	const { t } = useTranslation(["forms", "common"]);
	const childRef = useRef<any>(null);
	const { locale } = useRouter();
	const userTableData = {
		nameAr: "",
		nameEn: "",
		emiratesId: "",
		phoneNumber: "",
		email: "",
	};
	const titles = {
		title: t("requestEditedTitle"),
		caseNumberTitle: t("tables:requestNumber"),
		caseDateTitle: t("submittedOn"),
		body: t("requestEditedBody1") + t("requestEditedBody2"),
	};
	const caseNumber = caseRequest.CaseDetails?.CaseRef;
	const date =
		caseRequest?.SubmissionTime != "" && caseRequest?.SubmissionTime != null
			? getFormattedDate(new Date(caseRequest?.SubmissionTime), "dd MMMM yyyy", locale)
			: "";
	return (
		<Box>
			<Flex direction={{ base: "column", md: "row" }}>
				<Box flexGrow={1} bg="unset" boxShadow="unset">
					<Box
						mx={0}
						mb={4}
						boxShadow="0px 1px 1px rgba(100, 116, 139, 0.06), 0px 1px 2px rgba(100, 116, 139, 0.1);"
					>
						{/* <RequestStatus
							titles={titles}
							userTableData={userTableData}
							ref={childRef}
							caseNumber={caseNumber}
							date={date}
						/> */}
					</Box>
					<Flex mt={4} justifyContent="space-between">
						<Box display={{ base: "none", md: "block" }}></Box>
						<Flex
							flexWrap="wrap"
							flexDir={{ base: "column", md: "unset" }}
							w={{ base: "100%", md: "auto" }}
							rowGap={{ base: 5, md: "unset" }}
							px={{ base: 5, md: "unset" }}
							pb={{ base: 5, md: "unset" }}
						>
							<Button
								w={{ base: "100%", md: "auto" }}
								variant="secondary"
								mr={5}
								onClick={() => {
									if (childRef?.current) {
										childRef.current.downloadPdf();
									}
								}}
								textDecor="underline"
							>
								<SaveIcon mr={2} />
								{t("download", { ns: "common" })}
							</Button>
							<Button w={{ base: "100%", md: "auto" }} variant="primary" as={NextLink} href="/">
								{t("goHome", { ns: "common" })}
							</Button>
						</Flex>
					</Flex>
				</Box>
			</Flex>
		</Box>
	);
}

export default RequestEdited;
