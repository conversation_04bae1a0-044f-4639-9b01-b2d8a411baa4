import { NoDataIcon } from "components/Icons";
import React from "react";
import { Box, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

interface Props {
	type: "allowance" | "cases" | "complaints";
}

const NoDataFound = ({ type }: Props) => {
	const { t } = useTranslation("tables");
	return (
		<Box w="full" bg="brand.white.50" textAlign="center" align-items="center">
			<NoDataIcon w="48px" h="48px" />
			<Text fontWeight="700" color="notification.background.info" mt="4" mb="0.5" fontSize="md2">
				{t("noComplaints")}
			</Text>
		</Box>
	);
};

export default NoDataFound;
