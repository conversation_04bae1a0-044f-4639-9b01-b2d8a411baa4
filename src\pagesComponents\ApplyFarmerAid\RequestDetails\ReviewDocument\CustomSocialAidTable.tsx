import { AttachmentIcon } from "@chakra-ui/icons";
import {
	Link,
	Table,
	TableCaption,
	TableContainer,
	Tbody,
	Td,
	Th,
	Thead,
	Tr,
	Text,
	Show,
	VStack,
	Flex,
} from "@chakra-ui/react";
import { farmerServiceSocialEntites } from "config";
import useDownloadAttachment from "hooks/useDownloadAttachment";
import { useTranslation } from "react-i18next";
import { formatEmiratesID } from "utils/formatters";
function CustomSocialAidTable({
	caption,
	tableHeader = "",
	tableBody,
	mb = 10,
	sourceOfTranslation = "tables",
}) {
	const { t } = useTranslation(["tables", "forms"]);

	const downloadAttachment = useDownloadAttachment();
	const handleTableData = (row, rowIdx) => {
		if (row.value === true || row.value === "yes") {
			return t("common:yes");
		} else if (row.value === false || row.value === "no") {
			return t("common:no");
		} else if (rowIdx === 4) {
			if (row.value === "-") return "-";
			return t(
				"forms:" + farmerServiceSocialEntites.find((ent) => ent.value === row.value)?.label!
			);
		} else if (rowIdx === 1) {
			return row.value;
		} else if (rowIdx === 2) {
			return formatEmiratesID(row.value);
		}
		return "-";
	};
	return (
		<>
			<Show above="md">
				<TableContainer
					mb={mb}
					border="1px"
					borderBottom="0px"
					borderColor="brand.tableBorderColor"
					rounded="lg"
				>
					<Table variant="simple">
						{caption && <TableCaption>{caption}123</TableCaption>}
						<Thead>
							<Tr fontWeight="medium" mb={4}>
								{tableHeader && (
									<Th
										color="brand.tableTextColor"
										textTransform="none"
										lineHeight="150%"
										fontSize="xl"
										fontWeight="bold"
										letterSpacing="unset"
										pt={3}
										pb={3.5}
										px={4}
										colSpan={2}
										borderColor="brand.tableBorderColor"
									>
										{tableHeader}
									</Th>
								)}
							</Tr>
						</Thead>
						<Tbody>
							{tableBody &&
								tableBody.map((row, rowIdx) => {
									return (
										<Tr key={rowIdx} display={row?.hide === true ? "none" : "table-rows"}>
											<Td
												borderColor="brand.tableBorderColor"
												fontSize="1rem"
												fontWeight="bold"
												lineHeight="150%"
												letterSpacing="unset"
												w="50%"
											>
												{`${t(row.label, { ns: sourceOfTranslation })}${
													row.index ? ` (${row.index})` : ""
												}`}
											</Td>
											<Td
												borderColor="brand.tableBorderColor"
												fontSize="0.875rem"
												fontWeight="normal"
												letterSpacing="unset"
												lineHeight="150%"
											>
												<Text dir={"auto"} w={"min-content"}>
													{!row.document && handleTableData(row, rowIdx)}
													{row.document && (
														<Link
															color={"brand.blue.300"}
															onClick={() => downloadAttachment(row.document?.Id)}
														>
															{row.value}
															<AttachmentIcon mx={2} transform={"rotate(135deg)"} />
														</Link>
													)}
												</Text>
											</Td>
										</Tr>
									);
								})}
						</Tbody>
					</Table>
				</TableContainer>
			</Show>
			<Show below="md">
				<VStack align={"start"}>
					{tableBody.map((row, index) => {
						return (
							<Flex
								key={index}
								w="full"
								p={4}
								justifyContent={"space-between"}
								borderBottom="1px solid #BBBCBD"
							>
								<VStack flex={2} align={"start"}>
									<Text>
										{" "}
										{`${t(row.label, { ns: sourceOfTranslation })}${
											row.index ? ` (${row.index})` : ""
										}`}
									</Text>
									<Text dir={"auto"}>
										{!row.document && handleTableData(row, index)}
										{row.document && (
											<Link
												color={"brand.blue.300"}
												onClick={() => downloadAttachment(row.document?.Id)}
											>
												{row.value}
												<AttachmentIcon mx={2} transform={"rotate(135deg)"} />
											</Link>
										)}
									</Text>
								</VStack>
							</Flex>
						);
					})}
				</VStack>
			</Show>
		</>
	);
}

export default CustomSocialAidTable;
