import { Box, OrderedList, ListItem, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

function OrderedListGoldenNumber({
	translationFile = "common",
	arr,
	styles = {
		listYMargin: "4",
		textFontWeight: "normal",
		textLineHeight: "unset",
		listLineHeight: "unset",
		listFontWeight: "bold",
		listItemFontSize: "lg",
	},
}) {
	const { t } = useTranslation(translationFile);

	return (
		<Box>
			<OrderedList>
				{arr?.map((val, idx) => (
					<ListItem
						key={idx}
						my={idx === 0 || idx === arr.length - 1 ? "0" : styles.listYMargin}
						color="brand.mainHeaderColor"
						fontWeight={styles.listFontWeight}
						fontSize={styles.listItemFontSize}
						lineHeight={styles.listLineHeight}
					>
						<Text
							ml="4px"
							color="brand.altTextColor"
							fontWeight={"400"}
							lineHeight={styles.textLineHeight}
						>
							{t(val)}
						</Text>
					</ListItem>
					// <ListItem
					// 	key={idx}
					// 	my={idx === 0 || idx === arr.length - 1 ? "0" : "4"}
					// 	color="brand.mainGold"
					// 	fontWeight="bold"
					// 	fontSize="lg"
					// >
					// 	<Text ml="4px" color="brand.altTextColor" fontWeight="400">
					// 		{t(val)}
					// 	</Text>
					// </ListItem>
				))}
			</OrderedList>
		</Box>
	);
}

export default OrderedListGoldenNumber;
