import { Box, Text } from "@chakra-ui/react";
import React from "react";
import { useTranslation } from "react-i18next";

const ServiceInfoTab = () => {
	const { t } = useTranslation("common");

	return (
		<Box>
			<Box mt={3}>
				<Text fontWeight="700" fontSize={"md"} color={"brand.mainGold"}>
					{t("ServiceDescription")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("ServiceDescriptionBody")}
				</Text>
				<Box mt={3}>
					<Text fontSize={"md"} color={"brand.textColor"}>
						{t("individual")}
					</Text>
					<Text fontSize={"md"} color={"brand.textColor"}>
						{t("spouse")}
					</Text>
					<Text fontSize={"md"} color={"brand.textColor"}>
						{t("children")}
					</Text>
					<Text fontSize={"md"} color={"brand.textColor"}>
						{t("unemployedJob")}
					</Text>
					<Text fontSize={"md"} color={"brand.textColor"}>
						{t("unemployedOver")}
					</Text>
				</Box>
			</Box>
			<Box mt={3}>
				<Text fontWeight="700" fontSize={"md"} color={"brand.mainGold"}>
					{t("PointofServiceDelivery")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("PointofServiceDeliveryBody")}
				</Text>
			</Box>
			<Box my={3}>
				<Text fontWeight="700" fontSize={"md"} color={"brand.mainGold"}>
					{t("program-details")}
				</Text>
				<Text fontSize={"md"} color={"brand.textColor"} py={2}>
					{t("serviceBeneficiaries")}
				</Text>
			</Box>
		</Box>
	);
};

export default ServiceInfoTab;
