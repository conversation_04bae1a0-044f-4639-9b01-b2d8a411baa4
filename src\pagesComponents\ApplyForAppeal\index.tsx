import { Button, Grid, GridI<PERSON>, Box, Text, Flex } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Form, Formik } from "formik";
import { useRouter } from "next/router";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import * as functions from "./functions";
import useAppToast from "hooks/useAppToast";
import { ICrmAppeal } from "interfaces/CrmAppeal.interface";
import { useMutation } from "react-query";
import { createAppeal } from "services/frontend";
import { ICrmAttachment } from "interfaces/CrmDocument.interface";
import AppealFileUpload from "./AppealFileUpload";

interface ApplyForAppealInterface {
	caseId: string;
	onClose: any;
	requestName: string;
}
var formObject: ICrmAppeal = {
	Case: "",
	listAttachments: [],
	Description: "",
	Beneficiary: "",
};
export default function ApplyForAppeal({ caseId, requestName, onClose }: ApplyForAppealInterface) {
	const [isInitial, setisInitial] = useState(true);
	const [isSubmitted, setisSubmitted] = useState(false);
	const [requestNumber, setrequestNumber] = useState("");
	const [ticketNumber, setTicketNumber] = useState("");
	const { replace, asPath } = useRouter();
	const ref = useRef(null);
	const { t } = useTranslation(["personalInfo", "forms", "common", "tables"]);
	const { locale } = useRouter();
	const toast = useAppToast();
	const [attachedDocument, setDocument] = useState<ICrmAttachment>();
	const getInitialData = () => {
		let initValues = { ...functions.getInitialValues };
		initValues.caseNumber = requestName || "";
		return initValues;
	};

	const [initialValues, setInitialValues] = useState(() => getInitialData());

	const handleChangeEvent = (type, firstArg, secondArg, formik, isFieldArray = false) => {
		setisInitial(false);
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, isFieldArray);
		}
	};

	const { mutateAsync: submitAppealForm, isLoading: submittingFromLoading } = useMutation({
		mutationFn: (appealObject: ICrmAppeal) => createAppeal(appealObject),
		onSuccess: (data: any) => {
			//console.log(data);
		},
	});

	const handleTextChange = (event, fieldName, formik, type) => {
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, isFieldArray) => {
		formik.setFieldValue(fieldName, value);
	};

	return (
		<Box p={{ base: 0, md: 8 }}>
			<Formik
				enableReinitialize
				initialValues={initialValues}
				validationSchema={functions.getValidationSchema}
				onSubmit={async (e: any) => {
					formObject = {
						Description: e.description,
						listAttachments: [],
						Case: caseId,
						Beneficiary: "",
					};
					if (attachedDocument) {
						formObject.listAttachments?.push(attachedDocument);
					}
					let resp = await submitAppealForm(formObject);
					if (resp.IsSuccess) {
						toast({
							status: "info",
							title: t("tables:appealDone"),
						});
						// this is to refetch the complaint list ( to show the new state )
						replace(asPath, undefined, { shallow: false, scroll: false });
					} else {
						toast({
							title: t("common:genericErrorTitle"),
							description: t("common:genericErrorDescription"),
							status: "error",
						});
					}
				}}
			>
				{(formik) => {
					return (
						<Form
							onSubmit={(e) => {
								e.preventDefault();
								formik.handleSubmit(e);
								formik.setSubmitting(false);
							}}
							onChange={(e) => {
								e.preventDefault();
								functions.onChange(e, formik);
							}}
						>
							<>
								<Box bg={"brand.white.50"} px={{ base: 4, md: 0 }} pb={8}>
									<Grid
										rowGap={{ base: 2, md: 2 }}
										columnGap={16}
										templateColumns="repeat(2, 1fr)"
										templateRows="auto"
									>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["caseNumber"]}
												isRequired={true}
												isDisabled={true}
												name={"caseNumber"}
												label={t("requestNumber", { ns: "tables" })}
												placeholder={""}
												error={formik.errors[`serviceType`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "caseNumber", formik);
												}}
											/>
										</GridItem>

										<GridItem bg="white" colSpan={{ base: 2, md: 2 }}>
											<FormField
												type="Textarea"
												value={formik.values["description"]}
												isRequired={true}
												name={"description"}
												label={t("descriptionHeader", { ns: "common" })}
												placeholder={""}
												error={formik.errors[`description`]}
												//touched={formik.touched[`email`]}
												maxLength={800}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "description", formik);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<AppealFileUpload
												isRequired={false}
												label={locale === "en" ? "Attachment" : "مستندات اضافية"}
												name={"complaintDocument"}
												allowPdf={true}
												allowImage={true}
												setDocument={setDocument}
											/>
										</GridItem>
										<GridItem></GridItem>
										<GridItem colSpan={{ base: 2, md: 2 }}>
											<Flex justifyContent={"end"} alignItems={"center"} mt={5}>
												<Button
													mr={5}
													variant="primary"
													type="submit"
													isLoading={submittingFromLoading}
													disabled={
														!formik.isValid ||
														formik.isSubmitting ||
														isInitial ||
														submittingFromLoading
													}
												>
													<Text as="span">{t("submit", { ns: "common" })}</Text>
												</Button>
												<Button variant={"secondary"} onClick={onClose}>
													{t("cancel", { ns: "common" })}
												</Button>
											</Flex>
										</GridItem>
									</Grid>
								</Box>
							</>
						</Form>
					);
				}}
			</Formik>
		</Box>
	);
}
