import { <PERSON>, Button, Flex } from "@chakra-ui/react";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useRef } from "react";
import NextLink from "next/link";
import RequestStatus from "components/RequestStatus";

interface Props {
	userTableData: {
		nameAr: string;
		nameEn: string;
		emiratesId: string;
		phoneNumber: string;
		email: string;
	};
	titles: {
		title: string;
		caseNumberTitle: string;
		caseDateTitle: string;
		body: string;
		tableTitle: string;
	};
	caseNo: string;
	pageName?: string;
	submitDate: string;
}
function RequestSuccessful({ userTableData, titles, caseNo, submitDate, pageName = "" }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const childRef = useRef<any>(null);
	const { locale } = useRouter();
	const download = () => {
		if (childRef?.current) {
			childRef.current.downloadPdf();
		}
	};
	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 8 }} w="100%">
			<Box>
				<Flex direction={{ base: "column", md: "row" }}>
					<Box flexGrow={1} bg="unset" boxShadow="unset">
						<Box mx={0} mb={4}>
							<RequestStatus
								ref={childRef}
								userTableData={userTableData}
								data={null}
								titles={titles}
								caseNumber={caseNo}
								date={submitDate}
								pageName={pageName}
								onDownload={download}
							/>
						</Box>
						<Flex mt={10} px={5} justifyContent="end" pb={20}>
							<Button w={{ base: "100%", md: "auto" }} variant="primary" as={NextLink} href="/">
								{t("goHome", { ns: "common" })}
							</Button>
						</Flex>
					</Box>
				</Flex>
			</Box>
		</Box>
	);
}

export default RequestSuccessful;
