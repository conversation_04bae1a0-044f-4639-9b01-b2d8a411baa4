import { Grid, GridItem, Text } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import useRequiredFields from "hooks/useFormRequiredFields";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import * as functions from "./functions";
import { useRouter } from "next/router";
import { DIVORCED_WOMAN_ABOVE_45 } from "config";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
	userAge = 0,
	IsInflationNominatedCaseEdit = false,
}) {
	const formikRef = useRef<any>(null);
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { lookups } = useFormContext();
	let [validationSchema] = useState(functions.getValidationSchema(t));
	let requiredList = useRequiredFields(validationSchema);
	const [dropDownError, setDropDownError] = useState(false);
	const updateDropdownValues = () => {
		let originalInitialValues: any = { ...functions.getInitialValues };

		Object.keys(initialData).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = initialData[key]
					? JSON.parse(JSON.stringify(initialData[key]))
					: initialData[key];
			}
		});
		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	const beneficiaryAge = userAge;

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
		//console.log(lookups.InflationCategory);
	}, [lookups]);
	const idsToRemove = [
		"a5c77dbc-f173-ef11-a670-6045bd146374",
		"16f133ff-f173-ef11-a670-6045bd146374",
		"dc096fd9-f273-ef11-a670-6045bd146374",
	];

	if (!IsInflationNominatedCaseEdit) {
		lookups.InflationCategory = lookups.InflationCategory.filter(
			(item) => !idsToRemove.includes(item.value.toLowerCase())
		);
	}
	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			innerRef={formikRef}
			validateOnMount
		>
			{(formik: any) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							{/* Main reason for social aid */}
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["InflationCategory"]}
									isRequired={requiredList["InflationCategory"] || false}
									name="InflationCategory"
									options={lookups.InflationCategory}
									label={t("InflationCategory")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["InflationCategory"]}
									touched={formik.touched["InflationCategory"]}
									isDisabled={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent(
											"selectableTags",
											firstArg,
											"InflationCategory",
											formik,
											formKey
										);
									}}
								/>
								{formik.values["InflationCategory"]?.value === DIVORCED_WOMAN_ABOVE_45 && (
									<Text color={"brand.gray.50"}>{t("womanOver45")}</Text>
								)}
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
