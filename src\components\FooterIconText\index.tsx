import React from "react";
import { Flex, VStack, Text } from "@chakra-ui/react";
import Link from "next/link";

const FooterIconText = ({ headerText, content, icon, href = "#" }) => {
	return (
		<Flex
			flex={1}
			textAlign={"center"}
			flexDirection={{ base: "column", md: "row" }}
			alignItems={"center"}
			gap={{ base: 2, md: "0px" }}
		>
			{icon}
			<VStack>
				<Link href={href} target={"_blank"}>
					<Text fontSize={{ base: "10px", md: "12px" }}>{headerText}</Text>
					<Text fontSize={{ base: "12px", md: "15px" }} fontWeight={"bold"}>
						{content}
					</Text>
				</Link>
			</VStack>
		</Flex>
	);
};

export default FooterIconText;
