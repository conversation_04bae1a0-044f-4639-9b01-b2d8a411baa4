import { Box } from "@chakra-ui/react";
import BoxHeader from "components/BoxHeader";
import InnerBox from "components/InnerBox";
import React from "react";

const Publication = () => {
	const data = [
		{
			title: "National family policy",
			lang: "in Arabic (6.2 mb)",
		},
		{
			title: "National family policy",
			lang: "in Arabic (6.2 mb)",
		},
	];
	return (
		<Box bg="brand.white.50" py="3" px="4" ml="9" w="full">
			<BoxHeader
				title="Publication"
				icon="../assets/images/publicationIcon.png"
				alt="image not found"
				width="33px"
				height="33px"
			/>
			{!!data.length &&
				data.map((item, index) => (
					<Box my="1" bg="brand.gray.100" p="3" w="max-content" key={index}>
						<InnerBox
							imgSrc="../assets/images/downloadIcon.png"
							headerName={item.title}
							title={item.lang}
							width="31px"
							height="31px"
							alt="Image not found"
							isClicable={false}
							lineHeight="1.5"
						/>
					</Box>
				))}
		</Box>
	);
};

export default Publication;
