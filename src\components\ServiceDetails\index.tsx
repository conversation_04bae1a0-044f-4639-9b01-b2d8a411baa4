import { AtSignIcon } from "@chakra-ui/icons";
import { VStack, Text, Link } from "@chakra-ui/react";
import React from "react";
import NextLink from "next/link";

export default function ServiceDetails({ Icon = AtSignIcon, title, body, link = "" }) {
	// a flag was inserted at Strapi CMS to render each bulletpoint on a new line
	const paragraphs: string[] = body.split("*insertNewLine");

	return (
		<VStack align={"start"} bg="brand.white.100" borderRadius={"10px"} p={"2rem"} width={"100%"}>
			<Icon w="2rem" h="2rem" color="brand.mainGold" border="px solid black" />
			<Text as="h2" fontWeight={700} fontSize={"1.5rem"}>
				{title}
			</Text>
			{paragraphs.length > 0 &&
				paragraphs.map((text, idx) => (
					<Text key={`${text}-${idx}`} as="p" fontSize={"1.125rem"}>
						{text}
						{link !== "" && (
							<Link as={NextLink} href={"https://etihadwe.ae"}>
								<Text as="span" textDecoration={"underline"} color={"blue.500"} p={1}>
									{link}
								</Text>
							</Link>
						)}
					</Text>
				))}
		</VStack>
	);
}
