import { Modal, ModalOverlay, ModalContent, ModalBody, Text } from "@chakra-ui/react";
import React from "react";

export default function RatingModal({ isModalShow }) {
	return (
		<>
			<Modal
				isCentered // onClose={handleOnClose || onClose} // isOpen={onOpen} // motionPreset="slideInBottom"
				closeOnOverlayClick={false}
				onClose={() => {}}
				isOpen={isModalShow}
			>
				<ModalOverlay />
				<ModalContent borderRadius="12px">
					<ModalBody>
						<Text textAlign="center">{"hello my name is amer"}</Text>
					</ModalBody>
				</ModalContent>
			</Modal>
		</>
	);
}
