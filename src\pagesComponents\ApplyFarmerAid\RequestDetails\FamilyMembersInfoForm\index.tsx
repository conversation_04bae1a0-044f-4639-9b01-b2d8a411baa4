import { Box } from "@chakra-ui/react";
import FamilyMembersInfoForm from "./FamilyMembersInfoForm";

function RequestDetailsForm({
	innerText,
	formKey,
	familyMembers,
	setFamilyMembers,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions, formKey });
	};
	return (
		<Box>
			<FamilyMembersInfoForm
				onSubmit={onSubmit}
				members={familyMembers}
				setMembers={setFamilyMembers}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
