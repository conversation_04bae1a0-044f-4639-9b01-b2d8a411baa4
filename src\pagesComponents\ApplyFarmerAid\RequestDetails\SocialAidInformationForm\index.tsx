import { Box } from "@chakra-ui/react";
import SocialAidInformationForm from "./SocialAidInformationForm";

function RequestDetailsForm({
	innerText,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	EmiratesID,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<SocialAidInformationForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
				EmiratesID={EmiratesID}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
