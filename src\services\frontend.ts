import {
	I<PERSON>rm<PERSON>ttachment,
	ICrmDocument<PERSON>ist,
	IUploadDocumentRequest,
} from "interfaces/CrmDocument.interface";
import { ICrmNotification } from "interfaces/CrmNotification.interface";
import { ICrmToWhomForm } from "interfaces/CrmToWhom.interface";
import { IGenerateOtpResponse } from "interfaces/GenerateOtp.interface";
import { ISocialAidForm, ISocialAidFormFERequest } from "interfaces/SocialAidForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import { frontendApi } from "./api";
import { errorResponse } from "./backend";
import { ICrmComplaint } from "interfaces/CrmComplaint.interface";
import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
import { PROCESS_TEMPLATE_ID, TESTING_PROCESS_TEMPLATE_ID } from "config";
import { <PERSON>armer<PERSON>orm, IFarmerFormFERequest } from "interfaces/FarmerAidForm.interface";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eta<PERSON>, Menu } from "utils/strapi/navbar";
import { ICrmGuardianContact } from "interfaces/CrmGuardianContact.interface";
import { ICrmAppeal } from "interfaces/CrmAppeal.interface";
import { CrmRefundData } from "interfaces/CrmRefund.interface";
import { ICrmAccountData } from "interfaces/CrmAccountData.interface";
import { ICrmLookup } from "interfaces/CrmMasterData.interface";

export const generateOtp = async (
	emiratesId: string
): Promise<IWrapperApiResponse<IGenerateOtpResponse>> => {
	try {
		const query = {
			emiratesId,
		};
		const { data: resp } = await frontendApi.get("generateOtp", { params: query });
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const updateProfile = async (
	PreferedEmail: string,
	PreferedPhoneNumber: string,
	ProfileImage?: string | null
): Promise<IWrapperApiResponse<string>> => {
	try {
		const body = {
			PreferedEmail,
			PreferedPhoneNumber,
			ProfileImage,
		};
		const { data: resp } = await frontendApi.post("updateProfile", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const validateGuardianOtp = async (
	emiratesId: string,
	code: string,
	phoneNumber: string
): Promise<IWrapperApiResponse<ICrmGuardianContact | null>> => {
	try {
		const body = {
			EmiratesId: emiratesId,
			Code: code,
			PhoneNumber: phoneNumber,
		};
		const { data: resp } = await frontendApi.post("validateGuardianOtp", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const validateAccount = async (
	accountNumber: string,
	provider: string,
	caseId: string,
	parentCaseId: string,
	inflationType: string
): Promise<IWrapperApiResponse<ICrmAccountData | null>> => {
	try {
		let ProviderKey = "";
		if (provider.toString() === "*********") ProviderKey = "DEWA";
		if (provider.toString() === "*********") ProviderKey = "EWE";
		if (provider.toString() === "*********") ProviderKey = "SEWA";
		if (provider.toString() === "*********") ProviderKey = "TAQA";
		if (provider.toString() === "*********") ProviderKey = "AADC";
		if (provider.toString() === "*********") ProviderKey = "ADDC";
		const body = {
			AccountNumber: accountNumber,
			Provider: ProviderKey,
			CaseId: caseId,
			ParentCaseId: parentCaseId,
			InflationType: inflationType,
		};
		const { data: resp } = await frontendApi.post("validateAccount", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const profileImage = async (): Promise<IWrapperApiResponse<string>> => {
	try {
		const { data: resp } = await frontendApi.get("profileImage");
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getDocumentList = async (
	idRequest: string
): Promise<IWrapperApiResponse<ICrmDocumentList>> => {
	try {
		const query = {
			idRequest,
		};
		//console.log("documentList22", idRequest);
		const { data: resp } = await frontendApi.get("document/getList", {
			params: query,
		});
		//console.log("documentList", resp);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getAttachmentById = async (
	idAttachment: string
): Promise<IWrapperApiResponse<ICrmAttachment>> => {
	try {
		const query = {
			idAttachment,
		};
		const { data: resp } = await frontendApi.get("document/getAttachment", {
			params: query,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getCaseUpdateReasonsByCaseId = async (
	caseId: string
): Promise<IWrapperApiResponse<ICrmLookup>> => {
	try {
		const query = {
			caseId,
		};
		const { data: resp } = await frontendApi.get("document/getCaseUpdateReasonByCaseId", {
			params: query,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const uploadDocument = async ({
	idDocument,
	listAttachments,
}: IUploadDocumentRequest): Promise<IWrapperApiResponse<boolean>> => {
	try {
		const body = {
			idDocument,
			listAttachments,
		};
		const { data: resp } = await frontendApi.post("document/uploadDocument", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const modifyRequest = async (
	body: ISocialAidFormFERequest,
	processTemplateId: string = PROCESS_TEMPLATE_ID
): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const { data: resp } = await frontendApi.post("request/modify", {
			...body,
			//IdProcessTempalte: body.Testing ? TESTING_PROCESS_TEMPLATE_ID : PROCESS_TEMPLATE_ID,
			IdProcessTempalte: TESTING_PROCESS_TEMPLATE_ID,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const modifyFarmerRequest = async (
	body: IFarmerFormFERequest
): Promise<IWrapperApiResponse<IFarmerForm>> => {
	try {
		const { data: resp } = await frontendApi.post("request/modifyFarmerRequest", {
			...body,
			CaseDetails: {
				...body.CaseDetails,
				Emirate: "7d899699-6a75-ed11-81ad-002248cbd873",
				Occupation: "2e98e345-ca76-ed11-81ac-0022480da237",
			},
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const modifyRequestInflation = async (
	body: ISocialAidFormFERequest,
	processTemplateId: string = PROCESS_TEMPLATE_ID
): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const { data: resp } = await frontendApi.post("request/modify", {
			...body,
			//IdProcessTempalte: body.Testing ? TESTING_PROCESS_TEMPLATE_ID : PROCESS_TEMPLATE_ID,
			IdProcessTempalte: TESTING_PROCESS_TEMPLATE_ID,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};
export const modifyRequestFromPending = async (
	requestId: string,
	CaseDetails?: any
): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const { data: resp } = await frontendApi.post("request/modifyFromPending", {
			IdCase: requestId,
			CaseDetails: CaseDetails,
			UpdateType: "DOCUMENTS",
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getFormData = async (
	body: any
): Promise<IWrapperApiResponse<ISocialAidForm | IFarmerForm>> => {
	try {
		// const { data: resp } = await frontendApi.post("request/modify", body);
		const { data: resp } = await frontendApi.get("request/get", {
			params: body,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getRequest = async (caseId: string): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const query = { caseId };
		const { data: resp } = await frontendApi.get("request/get", {
			params: query,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const RefundBreakdown = async (
	CaseId: string,
	BeneficiaryId: string,
	PaymentOption: string
): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const body = {
			CaseId,
			BeneficiaryId,
			PaymentOption,
		};
		const { data: resp } = await frontendApi.post("refund/refundBreakdown", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const RefundBreakdownSimulator = async (
	CaseId: string,
	BeneficiaryId: string,
	PaymentOption: string
): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const body = {
			CaseId,
			BeneficiaryId,
			PaymentOption,
		};
		const { data: resp } = await frontendApi.post("refund/RefundBreakdownSimulator", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const PurchaseRequest = async (
	responseUrl: string,
	errorUrl: string,
	transactions: Array<{ Id: string; Amount: number }>,
	LangId: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = {
			ResponseUrl: responseUrl,
			ErrorUrl: errorUrl,
			TransactionsList: transactions,
			LangId: LangId,
		};

		const { data: resp } = await frontendApi.post("refund/purchaseRequest", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const getNotifications = async (): Promise<IWrapperApiResponse<ICrmNotification[]>> => {
	try {
		const { data: resp } = await frontendApi.get("notifications/get");
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const updateNotificationsRead = async (
	ids: string[]
): Promise<IWrapperApiResponse<boolean>> => {
	try {
		const body = { ids };
		const { data: resp } = await frontendApi.post("notifications/read", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const createFeedback = async (
	Name: string,
	Email: string,
	Description: string
): Promise<IWrapperApiResponse<string>> => {
	try {
		const body = { Name, Email, Description };
		const { data: resp } = await frontendApi.post("feedback/create", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const ToWhomItMayConcernsRequest = async (
	body: ICrmToWhomForm
): Promise<IWrapperApiResponse<ICrmToWhomForm>> => {
	try {
		const { data: resp } = await frontendApi.post("request/toWhomItMayConcern", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const GenerateCustomerPulseToken = async (
	userId: string,
	linkingId: string
): Promise<IWrapperApiResponse<string>> => {
	try {
		const { data: resp } = await frontendApi.post("generateCustomerPulseToken", {
			userId,
			linkingId,
		});
		return resp;
	} catch (e) {
		console.error(e);
		throw e;
		return errorResponse;
	}
};

export const createAppeal = async (body: ICrmAppeal): Promise<IWrapperApiResponse<ICrmAppeal>> => {
	try {
		const { data: resp } = await frontendApi.post("appeal/createAppeal", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const createRefund = async (
	body: CrmRefundData
): Promise<IWrapperApiResponse<CrmRefundData>> => {
	try {
		const { data: resp } = await frontendApi.post("refund/createAppeal", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const complaintRequest = async (
	body: ICrmComplaint
): Promise<IWrapperApiResponse<ICrmComplaint>> => {
	try {
		const { data: resp } = await frontendApi.post("complaint/complaint", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};
export const getComplaint = async (Id: string): Promise<IWrapperApiResponse<ComplaintForm>> => {
	try {
		const query = { Id };
		const { data: resp } = await frontendApi.get("complaints/get", {
			params: query,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const createAdditionalDoc = async (
	processDocumentTemplateGUID: string,
	requestGUID: string
): Promise<IWrapperApiResponse<void>> => {
	try {
		const body = { processDocumentTemplateGUID, requestGUID };
		const { data: resp } = await frontendApi.post("document/createAdditionalDoc", {
			processDocumentTemplateGUID,
			requestGUID,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const createFamilyMemberDoc = async (
	RequestId: string,
	EmiratesId: string,
	DOB: string,
	RelationshipType: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = { RequestId, EmiratesId, DOB, RelationshipType };
		const { data: resp } = await frontendApi.post("document/createFamilyBookMember", body);
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const createPersonalDocumentDoc = async (
	processDocumentTemplateGUID: string,
	requestGUID: string
): Promise<IWrapperApiResponse<void>> => {
	try {
		const body = { processDocumentTemplateGUID, requestGUID };
		const { data: resp } = await frontendApi.post("document/createPersonalDocumentDoc", {
			processDocumentTemplateGUID,
			requestGUID,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const deleteAdditionalDoc = async (
	documentId: string
): Promise<IWrapperApiResponse<void>> => {
	try {
		const { data: resp } = await frontendApi.post("document/deleteAdditionalDoc", {
			documentId,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};

export const getStrapiGlobal = async (locale: string): Promise<HeaderAndFooterDetails | null> => {
	try {
		const { data } = await frontendApi.get(`/strapi/getGlobalData?locale=${locale}`);
		return data.data;
	} catch (e) {
		console.error(e);
		return null;
	}
};

export const getStrapiMenus = async (): Promise<Menu[] | null> => {
	try {
		const { data } = await frontendApi.get(`/strapi/getMenu`);
		return data.data;
	} catch (e) {
		console.error(e);
		return null;
	}
};

export const appealComplaint = async (
	ComplaintId: string,
	reOpenReason: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const { data: resp } = await frontendApi.post("/complaint/appeal", {
			ComplaintId,
			reOpenReason,
		});
		return resp;
	} catch (e) {
		console.error(e);
		return errorResponse;
	}
};
