import { ArrowBackIcon } from "@chakra-ui/icons";
import {
	Drawer,
	Drawer<PERSON><PERSON>,
	DrawerHeader,
	DrawerOverlay,
	DrawerContent,
	useDisclosure,
	Flex,
	Text,
} from "@chakra-ui/react";
import FontResizeComp from "pagesComponents/FontResize/FontResizeComp";
import React from "react";
import { useTranslation } from "react-i18next";
function FontResizeDrawer() {
	const { isOpen, onOpen, onClose } = useDisclosure();
	const btnRef = React.useRef<any>();
	const { t } = useTranslation();
	return (
		<>
			<Flex
				onClick={onOpen}
				ref={btnRef}
				w="full"
				justifyContent={"space-between"}
				alignItems={"center"}
			>
				<Text>{t("textResizer")}</Text>
				<ArrowBackIcon w="1.5rem" h="1.5rem" />
			</Flex>
			<Drawer
				isOpen={isOpen}
				placement="right"
				onClose={onClose}
				finalFocusRef={btnRef}
				size={"full"}
			>
				<DrawerOverlay />
				<DrawerContent>
					<DrawerHeader>
						<Flex w={"100%"} justifyContent={"end"}>
							<ArrowBackIcon color={"#606164"} onClick={onClose} w="1.5rem" h="1.5rem" />
						</Flex>
					</DrawerHeader>

					<DrawerBody>
						<FontResizeComp />
					</DrawerBody>
				</DrawerContent>
			</Drawer>
		</>
	);
}

export default FontResizeDrawer;
