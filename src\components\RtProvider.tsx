import { CacheProvider } from "@emotion/react";
import createCache from "@emotion/cache";
import rtl from "stylis-plugin-rtl";
import { useRouter } from "next/router";
import React from "react";

// A unique `key` is important for it to work!
const options = {
	rtl: { key: "css-ar", stylisPlugins: [rtl] },
	ltr: { key: "css-en" },
};
const RtlProvider: React.FC<any> = ({ children }) => {
	const { locale } = useRouter();
	const dir = locale === "ar" ? "rtl" : "ltr";
	const cache = createCache(options[dir]);
	return <CacheProvider value={cache}>{children}</CacheProvider>;
};

export default RtlProvider;
