import { Box, Heading, Text } from "@chakra-ui/react";

function SectionHeader({ innerText, mt = 0, mb = 0, textMt = 4, textMb = 4, title = "" }: any) {
	return (
		<Box w="100%" mt={mt} mb={mb}>
			{title && (
				<Heading size="md" fontSize="lg" fontWeight="bold" flexGrow={1}>
					{title}
				</Heading>
			)}
			<Text mt={textMt} mb={textMb} fontSize="sm" fontWeight="normal" color="brand.gray.400">
				{innerText}
			</Text>
		</Box>
	);
}

export default SectionHeader;
