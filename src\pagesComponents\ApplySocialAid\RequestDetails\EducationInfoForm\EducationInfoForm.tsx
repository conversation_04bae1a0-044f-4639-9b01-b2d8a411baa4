import { Grid, GridItem, Text } from "@chakra-ui/react";
import { Form, Formik } from "formik";
import { IEducationCase } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import EducationMembersTable from "./EducationMembersTable";
import EditEducationFormModal from "./EditEducationFormModal";
import * as functions from "./functions";
import { useRouter } from "next/router";
import { DIVORCED } from "config";

interface Props {
	onSubmit: any;
	members: any;
	setMembers: any;
	readOnly: boolean;
	IsEdit?: boolean;
	isEligibleForTopup?: boolean;
	caseData;
}

function EducationInfoForm({
	onSubmit,
	members,
	setMembers,
	readOnly = false,
	IsEdit,
	isEligibleForTopup = true,
	caseData,
}: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const [editMember, setEditMember] = useState<IEducationCase | null>(null);

	const DIVORCED_SUBCATEGORY_ID = "0b3eb27d-9257-ee11-be6f-6045bd14ccdc";

	const onEditMember = (edittedEducationMember: IEducationCase) => {
		setMembers((state) => {
			return state.map((member) => {
				if (member.IdChild === edittedEducationMember.IdChild) {
					return edittedEducationMember;
				}
				return member;
			});
		});
		setEditMember(null);
	};

	const showEditMemberSubForm = (member: IEducationCase) => {
		setEditMember(member);
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			handleTextChange(firstArg, secondArg, formik);
		}
	};
	const handleTextChange = (event, fieldName, formik) => {
		formik.setFieldValue(fieldName, event?.target?.value || "");
	};
	const { locale } = useRouter();
	return (
		<>
			{!isEligibleForTopup ? (
				// Only show the alert message when not eligible
				<GridItem colSpan={{ base: 2, md: 2 }}>
					<Text fontSize="md" color={"red"} borderRadius="md" p={2} textAlign="center">
						{caseData?.personalInformation?.MaritalStatus == DIVORCED ||
						caseData?.socialAidInformation?.SubCategory == DIVORCED_SUBCATEGORY_ID
							? t("educationNotEligibleMessageMaritalStatus")
							: t("educationNotEligibleMessage")}
					</Text>
				</GridItem>
			) : (
				<Formik
					enableReinitialize
					initialValues={functions.getInitialValues}
					validationSchema={functions.getValidationSchema}
					onSubmit={onSubmit}
				>
					{(formik) => (
						<Form
							onSubmit={(e) => {
								e.preventDefault();
								formik.handleSubmit(e);
							}}
							onChange={(e) => {
								e.preventDefault();
								functions.onChange(e, formik);
							}}
						>
							<Grid
								rowGap={{ base: 6, md: 6 }}
								columnGap={6}
								templateColumns="repeat(2, 1fr)"
								templateRows="auto"
							>
								<GridItem colSpan={{ base: 2, md: 2 }}>
									{members.length > 0 && (
										<>
											<EducationMembersTable
												readOnly={readOnly}
												members={members}
												setEditMember={showEditMemberSubForm}
												IsEdit={IsEdit}
											/>
										</>
									)}
									{members.length === 0 && <Text>{t("noEducationMembersData")}</Text>}
								</GridItem>
							</Grid>
						</Form>
					)}
				</Formik>
			)}
			<EditEducationFormModal
				member={editMember}
				onEditMember={onEditMember}
				onClose={() => setEditMember(null)}
				readOnly={readOnly}
			/>
		</>
	);
}

export default EducationInfoForm;
