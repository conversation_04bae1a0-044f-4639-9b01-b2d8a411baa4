import { Box, Button, Text, useDisclosure, Flex } from "@chakra-ui/react";
import { ButtonArrowIcon } from "components/Icons";
import { STATUS_SLUG_MAPPING } from "config";
import { ICrmMasterData, ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";
import { ICrmRequest } from "interfaces/CrmRequest.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useRef } from "react";

interface StatusPillProps {
	status?: ICrmRequest["Status"];
	customText?: string;
	customStatus?: "pending" | "submitted" | "requestApproved";
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
	onClick?: any;
	caseId?: string;
}

const StatusPill = ({
	status,
	customText,
	customStatus,
	statusLookup,

	caseId,

	onClick = () => {},
}: StatusPillProps) => {
	const { t } = useTranslation();
	let bgColor;
	let textColor;
	const { isOpen, onOpen, onClose } = useDisclosure();
	const cancelRef = useRef<any>();

	const router = useRouter();
	const locale = router.locale;
	const handleRefund = (caseId: string) => {
		router.push(`/my-cases/apply-to-refund?caseId=${caseId}`);
	};
	const lkpStatusObject = statusLookup?.find((i) => i.value === (status?.Key || ""));

	const lkpStatus = STATUS_SLUG_MAPPING[lkpStatusObject?.value || ""];

	const statusSlug = customStatus || lkpStatus || status?.Value || "draft";

	if (statusSlug.startsWith("pending")) {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "submitted") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "inProgress") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "requestApproved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "resolved") {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else if (statusSlug === "requestRejected") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "draft" || statusSlug === "temporaryQueue") {
		bgColor = "#DDE1E6";
		textColor = "#697077";
	} else if (statusSlug === "annualReviewUpdate") {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	} else if (statusSlug === "PaymentCompleted") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else if (statusSlug === "PendingPayment") {
		bgColor = "#FFF0F0";
		textColor = "#C42828";
	} else {
		bgColor = "#f5f5f5";
		textColor = "#697077";
	}
	if (statusSlug === "draft") {
		return (
			<Button
				rightIcon={
					<ButtonArrowIcon
						w={"24px"}
						h={"24px"}
						transform={locale === "ar" ? "scale(-1, 1)" : ""}
						marginInlineStart={1}
					/>
				}
				variant={"outline"}
				minHeight={"0.2rem"}
				h={"2rem"}
				px={"1px !important"}
				onClick={onClick}
			>
				{t("proceed")}
			</Button>
		);
	}
	return (
		<Flex
			alignItems={"center"}
			flexDir={{
				base: "column",
				md: "row",
			}}
			gap={4}
		>
			<Box
				bg={bgColor}
				px={2}
				borderRadius={"5px"}
				py="1"
				w={"fit-content"}
				borderWidth={"1px"}
				borderColor={textColor}
				h={"fit-content"}
				onClick={onClick}
				_hover={{ cursor: "pointer" }}
			>
				<Text
					fontSize={["sm", "sm", "sm"]}
					fontWeight={500}
					color={textColor}
					p={-2}
					w={"100%"}
					textAlign={"center"}
				>
					{customText || lkpStatusObject?.label || status?.Value || t(`caseStatus.${statusSlug}`)}
				</Text>
			</Box>
			{/* if the complaint status is resolved, there should be a button to allow the users to appeal the complaint */}
			{/* {eligibleForAppeal && ( //
				<Button
					w={"full"}
					rightIcon={
						<ButtonArrowIcon
							w={"24px"}
							h={"24px"}
							transform={locale === "ar" ? "scale(-1, 1)" : ""}
							marginInlineStart={1}
						/>
					}
					p={"0 !important"}
					onClick={(e) => {
						e.stopPropagation();
						onOpen();
					}}
					variant={"outline"}
				>
					{t("appeal")}
				</Button>
			)} */}
			<Box
				bgColor="#FFF0F0"
				textColor="#C42828"
				px={2}
				borderRadius={"5px"}
				py="1"
				w={"fit-content"}
				borderWidth={"1px"}
				borderColor="#C42828"
				h={"fit-content"}
				onClick={onClick}
			>
				<Text
					fontSize={["sm", "sm", "sm"]}
					fontWeight={500}
					color="#C42828"
					p={-2}
					w={"100%"}
					textAlign={"center"}
				>
					{t(`PendingRefund`)}
				</Text>
			</Box>

			<Button
				w={"full"}
				rightIcon={
					<ButtonArrowIcon
						w={"24px"}
						h={"24px"}
						transform={locale === "ar" ? "scale(-1, 1)" : ""}
						marginInlineStart={1}
					/>
				}
				p={"0 !important"}
				onClick={(e) => {
					e.stopPropagation();
					handleRefund(caseId || "");
				}}
				variant={"outline"}
			>
				{t("refund")}
			</Button>
		</Flex>
	);
};

export default StatusPill;
