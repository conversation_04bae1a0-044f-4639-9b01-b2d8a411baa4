import React, { useId } from "react";
import Select from "react-select";
// add commit
const customStyles = {
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
		fontSize: "20px",
		padding: "-50%",
	}),
	control: (base, state) => ({
		...base,
		borderRadius: "2px",
		// color: "white",
		display: "flex",
		borderColor: "#B08D44",
		"&:hover": {
			boxShadow: "0 0 0 1px #B08D44",
		},
		color: "red",
		border: state.isFocused ? 0 : "1px solid #DDE1E6",
		// This line disable the blue border
		boxShadow: state.isFocused ? "0 0 0 1px #B08D44" : 0,
		// border: isDisabled ? "2px solid #C1C7CD" : "1px solid #DDE1E6",
		// color: state.isDisabled ? "red" : "white",
		height: "90px",
		fontSize: "20px",
		paddingRight: "2%",
	}),
	option: (provided, state) => ({
		...provided,
		display: "flex",
		color: "#001841",
		zIndex: 999,
		"&:hover": {
			background: "#DDE1E6",
		},
		fontSize: "20px",
		paddingTop: "50px",
		backgroundColor: state.isSelected ? "#DDE1E6" : "white",
		height: "90px",
	}),
	menu: (styles, state) => ({
		...styles,
		marginBottom: "1px",
		fontSize: "20px",
		// zIndex: 9999,
	}),

	multiValue: (styles) => ({
		...styles,
		background: "#B08D44",
		borderRadius: "16px",
		color: "white",
		"&:hover": {
			background: "#B08D44",
		},
	}),
	menuList: (provided, state) => ({
		...provided,
		paddingTop: "-1px",
		paddingBottom: "-4px",
		marginTop: "-4px",
		boxShadow: "2px 4px 10px 2px #DDE1E6",
	}),
	menuPortal: (provided) => ({
		...provided,
		left: "unset",
		right: "unset",
	}),
};

const disableStyle = {
	control: (base, state) => ({
		...base,
		borderRadius: "2px",
		// color: "white",
		display: "flex",
		background: "#F2F4F8",
		border: " 2px solid #C1C7CD",
	}),
	dropdownIndicator: (styles) => ({
		...styles,
		color: "#343A3F",
	}),
};
const NewSelectDropDown = (props: any) => {
	return (
		<Select
			instanceId={useId()}
			menuPosition={"fixed"}
			isDisabled={props.isDisabled}
			options={props.options}
			isMulti={props.isMulti}
			styles={props.isDisabled ? disableStyle : customStyles}
			value={props.value}
			onChange={props.onChange}
			isSearchable={false}
			placeholder={props.placeholder}
			components={{
				IndicatorSeparator: () => null,
			}}
		/>
	);
};
export default NewSelectDropDown;
