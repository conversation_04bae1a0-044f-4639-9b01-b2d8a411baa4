import { Box } from "@chakra-ui/react";
import PersonalInfoForm from "./PersonalInfoForm";
function PersonalInformation({
	innerText,
	handleChangeEvent,
	formKey,
	initialData,
	handleSetFormikState,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<PersonalInfoForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				initialData={initialData}
				handleSetFormikState={handleSetFormikState}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default PersonalInformation;
