import { <PERSON>ton, Grid, GridItem } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik, Form } from "formik";
import useAppToast from "hooks/useAppToast";
import { useRouter } from "next/router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
//import { useMutation } from "react-query";
import { createFeedback } from "services/frontend";
//import { getLocalizedFullName } from "utils/helpers";
import * as functions from "./functions";

const RatingForm = ({ setHelpful }) => {
	const { t } = useTranslation(["tables", "forms", "common"]);
	const { locale } = useRouter();

	let [validationSchema] = useState(functions.getValidationSchema());
	let [initialValues] = useState(functions.getInitialValues);

	const toast = useAppToast();

	// const { mutateAsync } = useMutation({
	// 	mutationFn: (description: string) => createFeedback(description),
	// });

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={async (values) => {
				const resp = await createFeedback(values.name, values.email, values.comment);
				if (resp.IsSuccess) setHelpful("submitted");
				else {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
				}
			}}
			validateOnMount
		>
			{(formik) => (
				<Form>
					<Grid
						rowGap={{ base: 6, md: 6 }}
						columnGap={6}
						templateColumns="repeat(1, 0.5fr)"
						templateRows="auto"
					>
						<GridItem colSpan={{ base: 2, md: 1 }}>
							<FormField
								type="text"
								value={formik.values["name"]}
								borderColor="brand.gray.250"
								isRequired={true}
								disabled={false}
								name="name"
								label={t("name")}
								placeholder={t("placeholder", { ns: "common" })}
								error={formik.errors["name"]}
							/>
						</GridItem>
						<GridItem colSpan={{ base: 2, md: 1 }}>
							<FormField
								type="email"
								value={formik.values["email"]}
								borderColor="brand.gray.250"
								isRequired={true}
								disabled={false}
								name="email"
								label={t("email")}
								placeholder={t("placeholder", { ns: "common" })}
								error={formik.errors["email"]}
							/>
						</GridItem>
						<GridItem colSpan={{ base: 2, md: 1 }}>
							<FormField
								type="Textarea"
								value={formik.values["comment"]}
								borderColor="brand.gray.250"
								isRequired={true}
								name="comment"
								label={t("forms:comment")}
								placeholder={t("placeholder", { ns: "common" })}
								error={formik.errors["comment"]}
							/>
						</GridItem>
						<GridItem colSpan={{ base: 2, md: 1 }}>
							<Button
								variant={"primary"}
								type={"submit"}
								isDisabled={!formik.isValid}
								isLoading={formik.isSubmitting}
								w={{ base: "full", md: "40%" }}
							>
								{t("common:submit")}
							</Button>
						</GridItem>
					</Grid>
				</Form>
			)}
		</Formik>
	);
};

export default RatingForm;
