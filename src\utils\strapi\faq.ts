export interface FaqItem {
	id: number;
	question_title: string;
	question_body: string;
}

export interface FaqTab {
	id: number;
	title: string;
	icon: string;
	faq_items: FaqItem[];
}

export interface ServiceFaq {
	id: number;
	title: string;
	tabs: FaqTab[];
}

export interface FAQPage {
	id: number;
	page_header: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	services_faq: ServiceFaq[];
}
