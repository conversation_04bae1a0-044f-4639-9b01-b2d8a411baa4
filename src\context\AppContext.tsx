import { createContext, useContext, ReactNode, useState } from "react";
import theme from "theme";

type appContextType = {
	userName: string;
	usedTheme: any;
	showBeneficiaryAlert: boolean;
	setUsedTheme: any;
	setShowBeneficiaryAlert: any;
};

const appContextDefaultValues: appContextType = {
	userName: "Test",
	usedTheme: theme,
	setUsedTheme: null,
	showBeneficiaryAlert: true,
	setShowBeneficiaryAlert: () => {},
};

const AppContextMOCD = createContext<appContextType>(appContextDefaultValues);

export function useAppContext() {
	return useContext(AppContextMOCD);
}

type Props = {
	children: ReactNode;
};

export function AppContext({ children }: Props) {
	const [userName, setUserName]: any = useState<String>(appContextDefaultValues.userName);
	const [usedTheme, setUsedTheme]: any = useState<any>(theme);
	const [showBeneficiaryAlert, setShowBeneficiaryAlert]: any = useState<Boolean>(
		appContextDefaultValues.showBeneficiaryAlert
	);
	const value = {
		userName,
		usedTheme,
		showBeneficiaryAlert,
		setUserName,
		setUsedTheme,
		setShowBeneficiaryAlert,
	};
	return <AppContextMOCD.Provider value={value}>{children}</AppContextMOCD.Provider>;
}
