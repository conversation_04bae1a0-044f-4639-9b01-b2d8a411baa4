import {
	Grid,
	Grid<PERSON>tem,
	Modal,
	ModalBody,
	ModalContent,
	Modal<PERSON>eader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	Button,
	ModalFooter,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { CloseIcon } from "components/Icons";
import { useFormContext } from "context/FormContext";
import { Form, Formik, FormikProps } from "formik";
import { IChildFamilyMember, IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useMemo, useRef } from "react";
import { getLookupItem, getLookupLabel } from "utils/helpers";
import * as functions from "./functions";

interface Props {
	onClose: any;
	memberInfo?: {
		memberChildInfo?: IChildFamilyMember;
		member?: IFamilyMember;
	} | null;
	onEditMember: (member: IChildFamilyMember) => void;
	readOnly?: boolean;
}

function EditChildMemberForm({ onClose, memberInfo, onEditMember, readOnly }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();

	const formikRef = useRef<FormikProps<any>>(null);
	const initialValues = useMemo(
		() => ({
			// IsPursuingHigherEducation:
			// 	memberInfo?.memberChildInfo?.IsPursuingHigherEducation === true ? "yes" : "no",
			// IsDraftedinMilitaryService:
			// 	memberInfo?.memberChildInfo?.IsDraftedinMilitaryService === true ? "yes" : "no",
			Occupations: getLookupItem(lookups, "Occupations", memberInfo?.memberChildInfo?.Occupations),
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[memberInfo?.member?.Id, locale]
	);
	const onSubmit = (it) => {
		const childMember: IChildFamilyMember = {
			Id: memberInfo?.memberChildInfo?.Id!,
			IdDependentBeneficary: memberInfo?.memberChildInfo?.IdDependentBeneficary!,
			// IsDraftedinMilitaryService: it.IsDraftedinMilitaryService === "yes",
			// IsPursuingHigherEducation: it.IsPursuingHigherEducation === "yes",
			Age: memberInfo?.memberChildInfo?.Age,
			Occupations: it.Occupations.value,
		};
		onEditMember(childMember);
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};

	return (
		<>
			<Modal
				isOpen={memberInfo !== null}
				onClose={onClose}
				size={{ base: "full", md: "6xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{locale === "ar"
										? memberInfo?.member?.FullnameAR
										: memberInfo?.member?.FullnameEN}{" "}
									-{" "}
									{getLookupLabel(lookups, "FamilyRelationship", memberInfo?.member?.Relationship)}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
						validateOnMount
					>
						{(formik) => {
							return (
								<>
									<ModalBody>
										<Form
											onSubmit={(e) => {
												e.preventDefault();
												formik.handleSubmit(e);
											}}
											onChange={(e) => {
												e.preventDefault();
												functions.onChange(e, formik);
											}}
										>
											<Grid
												rowGap={{ base: 6, md: 6 }}
												columnGap={6}
												templateColumns="repeat(2, 1fr)"
												templateRows="auto"
											>
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<FormField
														type="selectableTags"
														value={formik.values["Occupations"]}
														isRequired={true}
														name="Occupations"
														label={t("Occupations")}
														isDisabled={readOnly}
														placeholder={t("placeholder", { ns: "common" })}
														options={lookups.Occupations}
														error={formik.errors["Occupations"]}
														onChange={(firstArg) => {
															handleChangeEvent("selectableTags", firstArg, "Occupations", formik);
														}}
													/>
												</GridItem>
												{/* {memberInfo?.memberChildInfo?.Age &&
													memberInfo?.memberChildInfo?.Age >= 21 &&
													memberInfo?.memberChildInfo?.Age <= 24 && (
														<>
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<FormField
																	isReadOnly={readOnly}
																	type="radio"
																	label={t("IsPursuingHigherEducation")}
																	name="IsPursuingHigherEducation"
																	value={formik.values["IsPursuingHigherEducation"]}
																	options={lookups.Boolean}
																/>
															</GridItem>
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<FormField
																	isReadOnly={readOnly}
																	type="radio"
																	label={t("IsDraftedinMilitaryService")}
																	name="IsDraftedinMilitaryService"
																	value={formik.values["IsDraftedinMilitaryService"]}
																	options={lookups.Boolean}
																/>
															</GridItem>
														</>
													)} */}
											</Grid>
										</Form>
									</ModalBody>
									<ModalFooter borderTop="1px solid #BBBCBD">
										<HStack w={"100%"} gap={2} my={4}>
											<Button variant="secondary" w={"100%"} onClick={onClose}>
												{t("common:cancel")}
											</Button>
											<Button
												variant="primary"
												w={"100%"}
												isDisabled={!formik.isValid}
												onClick={formik.submitForm}
											>
												{t("common:save")}
											</Button>
										</HStack>
									</ModalFooter>
								</>
							);
						}}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditChildMemberForm;
