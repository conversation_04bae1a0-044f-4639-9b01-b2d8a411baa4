import {
	Grid,
	Grid<PERSON>tem,
	Modal,
	Modal<PERSON>ody,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	Button,
	ModalFooter,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { CloseIcon } from "components/Icons";
import { useFormContext } from "context/FormContext";
import { Form, Formik, FormikProps } from "formik";
import { IEducationCase } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useRef, useMemo } from "react";
import * as functions from "./functions";

import { getStaticLookupItem } from "utils/helpers";

interface Props {
	onClose: any;
	member: IEducationCase | null;
	onEditMember: (member: IEducationCase) => void;
	readOnly?: boolean;
}

function EditEducationFormModal({ onClose, member, onEditMember, readOnly }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();

	const getSortedUniversities = useMemo(() => {
		if (!lookups.universities || lookups.universities.length === 0) return [];

		return [...lookups.universities].sort((a, b) => {
			if (a.value === "cc0bc8df-083d-f011-b112-005056010908") return -1;
			if (b.value === "cc0bc8df-083d-f011-b112-005056010908") return 1;

			return a.label.localeCompare(b.label);
		});
	}, [lookups.universities]);

	const formikRef = useRef<FormikProps<any>>(null);

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};
	const initialValues = useMemo(
		() => ({
			ApplyEducationAllowance: member?.ApplyEducationAllowance === true ? "yes" : "no",
			IsEnrolledInNationalService: member?.IsEnrolledInNationalService === true ? "yes" : "no",
			childCompletedSemesterInUniversity:
				member?.childCompletedSemesterInUniversity === true ? "yes" : "no",
			// highSchoolCurriculuim: getStaticLookupItem(
			// 	locale === "en" ? EducationCategoryCurriculumEn : EducationCategoryCurriculumAr,
			// 	member?.highSchoolCurriculuim
			// ),
			// enrolledEducationStream: getStaticLookupItem(
			// 	locale === "en" ? PublicEducationStreamEn : PublicEducationStreamAr,
			// 	member?.enrolledEducationStream
			// ),
			// EmSATorAdvancedPlacementScores: getStaticLookupItem(
			// 	locale === "en" ? ScoresTypeDocumentEn : ScoresTypeDocumentAr,
			// 	member?.EmSATorAdvancedPlacementScores
			// ),
			universityName: getStaticLookupItem(lookups.universities || [], member?.universityName),
			unaccreditedUniversityName: member?.unaccreditedUniversityName || "",
			cgpa: member?.cgpa || "",
			creditHours: member?.creditHours || "",
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[member, locale, lookups]
	);

	const onSubmit = (it) => {
		member &&
			onEditMember({
				...member,
				ApplyEducationAllowance: it?.ApplyEducationAllowance === "yes",
				childCompletedSemesterInUniversity: it?.childCompletedSemesterInUniversity === "yes",
				IsEnrolledInNationalService: it?.IsEnrolledInNationalService === "yes",
				/* Original code before changes:
				highSchoolCurriculuim: it?.highSchoolCurriculuim?.value,
				enrolledEducationStream: it?.enrolledEducationStream?.value,
				EmSATorAdvancedPlacementScores: it?.EmSATorAdvancedPlacementScores?.value,
				*/
				// Add university fields
				universityName: it?.universityName?.value,
				unaccreditedUniversityName:
					it?.universityName?.value === "cc0bc8df-083d-f011-b112-005056010908"
						? it?.unaccreditedUniversityName
						: "",
				cgpa: it?.cgpa,
				creditHours: it?.creditHours,
				IsCompletedFromPortal: true,
			});
	};

	return (
		<>
			<Modal
				isOpen={!!member}
				onClose={onClose}
				size={{ base: "full", md: "4xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{locale === "ar" ? member?.FullNameAr : member?.FullNameEn}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
					>
						{(formik) => (
							<>
								<ModalBody>
									<Form
										onSubmit={(e) => {
											e.preventDefault();
											formik.handleSubmit(e);
										}}
										onChange={(e) => {
											e.preventDefault();
											functions.onChange(e, formik);
										}}
									>
										<Grid
											rowGap={{ base: 6, md: 6 }}
											columnGap={6}
											templateColumns="repeat(2, 1fr)"
											templateRows="auto"
										>
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("ApplyEducationAllowance")}
													name="ApplyEducationAllowance"
													value={formik.values["ApplyEducationAllowance"]}
													touched={formik.touched["ApplyEducationAllowance"]}
													error={formik.errors["ApplyEducationAllowance"]}
													options={lookups.Boolean}
													isReadOnly={readOnly}
													onChange={(firstArg) => {
														handleChangeEvent("radio", firstArg, "ApplyEducationAllowance", formik);
														handleChangeEvent("radio", "", "IsEnrolledInNationalService", formik);
														handleChangeEvent(
															"radio",
															"",
															"childCompletedSemesterInUniversity",
															formik
														);
														handleChangeEvent("radio", "", "highSchoolCurriculuim", formik);
														handleChangeEvent("radio", "", "enrolledEducationStream", formik);
														handleChangeEvent(
															"radio",
															"",
															"EmSATorAdvancedPlacementScores",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["ApplyEducationAllowance"] === "yes" && (
												<>
													<GridItem colSpan={{ base: 2, md: 2 }}>
														<FormField
															type="radio"
															label={t("IsEnrolledInNationalService")}
															name="IsEnrolledInNationalService"
															value={formik.values["IsEnrolledInNationalService"]}
															touched={formik.touched["IsEnrolledInNationalService"]}
															error={formik.errors["IsEnrolledInNationalService"]}
															options={lookups.Boolean}
															isReadOnly={readOnly}
															onChange={(firstArg) => {
																handleChangeEvent(
																	"radio",
																	firstArg,
																	"IsEnrolledInNationalService",
																	formik
																);
															}}
														/>
														{formik.values["IsEnrolledInNationalService"] === "yes" && (
															<Text fontSize="md" color={"red"} p={4} borderRadius="md">
																{t("discliamer")}
															</Text>
														)}
													</GridItem>
													{formik.values["IsEnrolledInNationalService"] === "no" && (
														<GridItem colSpan={{ base: 2, md: 2 }}>
															<Flex>
																<FormField
																	type="radio"
																	label={t("childCompletedSemesterInUniversity")}
																	name="childCompletedSemesterInUniversity"
																	value={formik.values["childCompletedSemesterInUniversity"]}
																	touched={formik.touched["childCompletedSemesterInUniversity"]}
																	error={formik.errors["childCompletedSemesterInUniversity"]}
																	options={lookups.Boolean}
																	isReadOnly={readOnly}
																	marginTop="2rem"
																	onChange={(firstArg) => {
																		handleChangeEvent(
																			"radio",
																			firstArg,
																			"childCompletedSemesterInUniversity",
																			formik
																		);
																	}}
																/>
																<Text
																	fontSize={"sm"}
																	w="90%"
																	color="#1B1D21B8"
																	dangerouslySetInnerHTML={{
																		__html: t("common:accreditedUniversities"),
																	}}
																></Text>
															</Flex>
														</GridItem>
													)}

													{/* {formik.values["childCompletedSemesterInUniversity"] === "no" &&
														formik.values["IsEnrolledInNationalService"] === "no" && (
															<>
																<GridItem colSpan={{ base: 2, md: 1 }}>
																	<FormField
																		type="selectableTags"
																		value={formik.values["highSchoolCurriculuim"]}
																		isRequired={true}
																		name="highSchoolCurriculuim"
																		options={
																			locale === "en"
																				? EducationCategoryCurriculumEn
																				: EducationCategoryCurriculumAr
																		}
																		label={t("highSchoolCurriculuim")}
																		placeholder={t("placeholder", { ns: "common" })}
																		error={formik.errors["highSchoolCurriculuim"]}
																		touched={formik.touched["highSchoolCurriculuim"]}
																		isDisabled={readOnly}
																		onChange={(firstArg) => {
																			handleChangeEvent(
																				"selectableTags",
																				firstArg,
																				"highSchoolCurriculuim",
																				formik
																			);
																		}}
																	/>
																</GridItem>
																{formik.values["highSchoolCurriculuim"] &&
																	formik.values["highSchoolCurriculuim"]?.value &&
																	formik.values["highSchoolCurriculuim"]?.value.toString() ===
																		"662410000" && (
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={formik.values["enrolledEducationStream"]}
																				isRequired={true}
																				name="enrolledEducationStream"
																				options={
																					locale === "en"
																						? PublicEducationStreamEn
																						: PublicEducationStreamAr
																				}
																				label={t("enrolledEducationStream")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors["enrolledEducationStream"]}
																				touched={formik.touched["enrolledEducationStream"]}
																				isDisabled={readOnly}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						"enrolledEducationStream",
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	)}
																{formik.values["highSchoolCurriculuim"] &&
																	formik.values["highSchoolCurriculuim"]?.value &&
																	formik.values["highSchoolCurriculuim"]?.value.toString() ===
																		"662410002" && (
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={formik.values["EmSATorAdvancedPlacementScores"]}
																				isRequired={true}
																				name="EmSATorAdvancedPlacementScores"
																				options={
																					locale === "en"
																						? ScoresTypeDocumentEn
																						: ScoresTypeDocumentAr
																				}
																				label={t("EmSATorAdvancedPlacementScores")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors["EmSATorAdvancedPlacementScores"]}
																				touched={formik.touched["EmSATorAdvancedPlacementScores"]}
																				isDisabled={readOnly}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						"EmSATorAdvancedPlacementScores",
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	)}
															</>
														)} */}

													{formik.values.childCompletedSemesterInUniversity === "yes" && (
														<>
															<GridItem colSpan={{ base: 2, md: 1 }}>
																<FormField
																	type="selectableTags"
																	isSearchable={true}
																	value={formik.values.universityName}
																	isRequired={true}
																	name="universityName"
																	options={getSortedUniversities}
																	label={t("universityName")}
																	placeholder={t("placeholder")}
																	error={t("formik.errors.universityName")}
																	touched={formik.touched.universityName}
																	isDisabled={readOnly}
																	onChange={(value) => {
																		handleChangeEvent(
																			"selectableTags",
																			value,
																			"universityName",
																			formik
																		);
																		// Reset unaccredited university name when changing university selection
																		if (value?.value !== "cc0bc8df-083d-f011-b112-005056010908") {
																			handleChangeEvent(
																				"text",
																				{ target: { value: "" } },
																				"unaccreditedUniversityName",
																				formik
																			);
																		}
																	}}
																/>
															</GridItem>
															{formik.values.universityName?.value ===
																"cc0bc8df-083d-f011-b112-005056010908" && (
																<GridItem colSpan={{ base: 2, md: 1 }}>
																	<FormField
																		type="text"
																		value={formik.values.unaccreditedUniversityName}
																		isRequired={true}
																		name="unaccreditedUniversityName"
																		label={t("unaccreditedUniversityName")}
																		placeholder={t("enterUnaccreditedUniversityName")}
																		error={formik.errors.unaccreditedUniversityName}
																		touched={formik.touched.unaccreditedUniversityName}
																		isDisabled={readOnly}
																		onChange={(e) => {
																			let value = e.target.value;
																			// value = value.replace(/[^A-Za-z\s]/g, "");
																			value = value;
																			e.target.value = value;
																			handleChangeEvent(
																				"text",
																				e,
																				"unaccreditedUniversityName",
																				formik
																			);
																		}}
																	/>
																</GridItem>
															)}
															<GridItem colSpan={{ base: 2, md: 1 }}>
																<FormField
																	type="text"
																	value={formik.values.cgpa}
																	isRequired={true}
																	name="cgpa"
																	label={t("cgpa")}
																	placeholder={t("enterCGPA")}
																	error={t("formik.errors.cgpa")}
																	touched={formik.touched.cgpa}
																	isDisabled={readOnly}
																	onChange={(e) => {
																		let value = e.target.value;

																		value = value.replace(/[^0-9.]/g, "");
																		const parts = value.split(".");
																		if (parts.length > 2) value = parts[0] + "." + parts[1];
																		if (parts[1]) value = parts[0] + "." + parts[1].slice(0, 2);

																		e.target.value = value;
																		handleChangeEvent("text", e, "cgpa", formik);
																	}}
																	helperText={t("cgpaHelperText")}
																/>
															</GridItem>
															<GridItem colSpan={{ base: 2, md: 1 }}>
																<FormField
																	type="text"
																	value={formik.values.creditHours}
																	isRequired={true}
																	name="creditHours"
																	label={t("creditHours")}
																	placeholder={t("enterCreditHours")}
																	error={t("formik.errors.creditHours")}
																	touched={formik.touched.creditHours}
																	isDisabled={readOnly}
																	onChange={(e) => {
																		let value = e.target.value;

																		value = value.replace(/[^0-9.]/g, "");
																		const parts = value.split(".");
																		if (parts.length > 2) {
																			value = parts[0] + "." + parts[1];
																		}
																		if (parts[0].length > 2) {
																			parts[0] = parts[0].slice(0, 2);
																		}
																		if (parts[1]) {
																			parts[1] = parts[1].slice(0, 1);
																		}
																		value = parts.join(".");

																		e.target.value = value;
																		handleChangeEvent("text", e, "creditHours", formik);
																	}}
																	helperText={t("creditHoursHelperText")}
																/>
															</GridItem>
														</>
													)}

													{formik.values["childCompletedSemesterInUniversity"] === "no" &&
														formik.values["IsEnrolledInNationalService"] === "no" && (
															<GridItem colSpan={{ base: 2, md: 2 }}>
																<Text fontSize="md" color={"red"} borderRadius="md">
																	{t("discliamer2")}
																</Text>
															</GridItem>
														)}
												</>
											)}
										</Grid>
									</Form>
								</ModalBody>
								<ModalFooter borderTop="1px solid #BBBCBD">
									<HStack w={"100%"} gap={2} my={4}>
										<Button variant="secondary" w={"100%"} onClick={onClose}>
											{t("common:cancel")}
										</Button>
										<Button
											variant="primary"
											w={"100%"}
											isDisabled={!formik.isValid}
											onClick={formik.submitForm}
										>
											{t("common:save")}
										</Button>
									</HStack>
								</ModalFooter>
							</>
						)}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditEducationFormModal;
