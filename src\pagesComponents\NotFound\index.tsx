import React from "react";
import { Image, Box, Text, Button, Flex } from "@chakra-ui/react";
import { useTranslation } from "next-i18next";
import NextLink from "next/link";
const NotFound = () => {
	const { t } = useTranslation("common");

	return (
		<>
			<Image
				src="/assets/images/notFoundImg.jpg"
				width={"100%"}
				maxHeight={"40vh"}
				objectFit={"cover"}
				alt="not found"
			/>
			<Box w={"100%"} p={8}>
				<Box bg="brand.white.50" py={28} px={4}>
					<Text
						textAlign="center"
						color="brand.altTextColor"
						fontSize={{ base: "6xl", md: "9xl" }}
						fontWeight={"bold"}
					>
						404
					</Text>
					<Text
						textAlign="center"
						color="brand.altTextColor"
						fontSize={{ base: "2xl", md: "5xl" }}
						fontWeight={"bold"}
					>
						{t("ThisPageCouldNotBeFound")}
					</Text>
					<Text textAlign="center" fontWeight={"medium"}>
						{t("ThisPageCouldNotBeFoundDescription")}
					</Text>

					<Flex justifyContent="center" align-items="center">
						<Button variant="primary" textAlign="center" mt="20px" as={NextLink} href={"/"}>
							{t("BacktoHome")}
						</Button>
					</Flex>
				</Box>
			</Box>
		</>
	);
};

export default NotFound;
