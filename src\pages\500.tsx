import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import NotFound from "pagesComponents/NotFound";
import { ReactElement } from "react";

const Custom404 = () => {
	return <NotFound />;
};

Custom404.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export async function getStaticProps(ctx) {
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "buttons"])),
			// Will be passed to the page component as props
		},
	};
}
export default Custom404;
