import { extendTheme } from "@chakra-ui/react";
import breakpoints from "theme/overrides/breakpoints";
import borderRadius from "theme/overrides/borderRadius";
import sizes from "theme/overrides/sizes";
import spacing from "theme/overrides/spacing";
import typography from "theme/overrides/typography";
import colors from "theme/overrides/colors";
import globalStyles from "theme/overrides/global";
import Button from "./components/Button";
import { StepsStyleConfig as StepsConfg } from "chakra-ui-steps";
import Input from "./components/Input";
import Checkbox from "./components/Checkbox";
import Progress from "./components/Progress";

// init config
export const config = {
	initialColorMode: "light",
	useSystemColorMode: false,
	cssVarPrefix: "mocd",
};

let themeObject = {
	config,
	...globalStyles,
	...typography,
	...colors,
	...breakpoints,
	...spacing,
	...sizes,
	...borderRadius,
	components: { But<PERSON>, Steps: StepsConfg, Input, Checkbox, Progress },
};

const theme = extendTheme(themeObject);
export { themeObject };
export default theme;
