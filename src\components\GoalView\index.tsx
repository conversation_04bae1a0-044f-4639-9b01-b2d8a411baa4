import { Box, Text } from "@chakra-ui/react";
import React, { ReactElement } from "react";

interface GoalViewProps {
	Title: string;
	Desc: string;
	Icon: ReactElement;
}

export default function GoalView(props: GoalViewProps) {
	return (
		<Box my={12} textAlign={"left"}>
			{props.Icon}
			<Text my={4} color={"brand.black.50"} fontSize={"2xl"} fontWeight={500}>
				{props.Title}
			</Text>
			<Text color={"brand.textColor"} fontSize={"md"}>
				{props.Desc}
			</Text>
		</Box>
	);
}
