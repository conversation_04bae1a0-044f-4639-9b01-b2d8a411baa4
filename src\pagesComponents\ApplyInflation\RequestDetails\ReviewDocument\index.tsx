import { Box } from "@chakra-ui/react";
import ReviewDocumentComponent from "./ReviewDocumentComponent";

function ReviewDocument({
	setCurrentStep,
	innerText,
	handleStepsIndexes,
	documentList,
	familyMembers,
	caseForm,
	formKey,
	handleSetFormikState,
	hasSSS,
	childMembers,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<ReviewDocumentComponent
				onSubmit={onSubmit}
				setCurrentStep={setCurrentStep}
				handleStepsIndexes={handleStepsIndexes}
				documentList={documentList}
				familyMembers={familyMembers}
				caseForm={caseForm}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				hasSSS={hasSSS}
				childMembers={childMembers}
			/>
		</Box>
	);
}

export default ReviewDocument;
