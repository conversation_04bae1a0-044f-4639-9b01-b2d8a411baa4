import { UnemployedCase } from "../calculator";

export class UnemployedCalculator {
	public static calculateChild(data: UnemployedCase): number {
		const { personalInformation } = data;
		if (personalInformation.haveChildren === "1") {
			const numberOfPodChildren = Number(personalInformation.numberOfPODChildren);

			let podChildsAllowance = 0;
			let normalChildsAllowance = 0;

			if (personalInformation.isChildrenPOD === "1") {
				podChildsAllowance = 5000 * numberOfPodChildren;
			}
			const numberOfChildren =
				Number(personalInformation.numberOfChildren) - (numberOfPodChildren || 0);
			if (numberOfChildren === 1) {
				normalChildsAllowance = 2400;
			} else if (numberOfChildren === 2) {
				normalChildsAllowance = 4000;
			} else if (numberOfChildren === 3) {
				normalChildsAllowance = 5600;
			} else if (numberOfChildren > 3) {
				normalChildsAllowance = 5600 + 800 * (numberOfChildren - 3);
			}
			return normalChildsAllowance + podChildsAllowance;
		}
		return 0;
	}
	public static calculateSelfAllowance(data: UnemployedCase): number {
		const { personalInformation } = data;

		switch (personalInformation.ageGroupShort) {
			case "1":
				return 5000;
			case "2":
				return 2000;
			case "3":
				return 3000;
			case "4":
				return 4000;
			default:
				return 5000;
		}
	}
	public static calculateSpousesAllowance(data: UnemployedCase): number {
		const { personalInformation } = data;
		let NON_POD_SPOUSE_ALLOW: number;
		switch (String(personalInformation.numberOfSpouses)) {
			case "1":
				NON_POD_SPOUSE_ALLOW = 3500;
				break;
			case "2":
				NON_POD_SPOUSE_ALLOW = 1000;
				break;
			case "3":
				NON_POD_SPOUSE_ALLOW = 2000;
				break;
			case "4":
				NON_POD_SPOUSE_ALLOW = 3000;
				break;
			default:
				NON_POD_SPOUSE_ALLOW = 35000;
				break;
		}
		if (personalInformation.maritalStatus === "2" && personalInformation.gender === "1") {
			const numberOfSpouses = Number(personalInformation.numberOfSpouses);
			const numberOfPODSpouses = Number(personalInformation.numberOfPODSpouses);
			if (personalInformation.isSpousesPOD === "1") {
				let podSpouseAll = 0;
				let nonPodSpouseAll = 0;
				podSpouseAll = numberOfPODSpouses * 5000;
				nonPodSpouseAll = (numberOfSpouses - numberOfPODSpouses) * NON_POD_SPOUSE_ALLOW;
				return podSpouseAll + nonPodSpouseAll;
			} else {
				return NON_POD_SPOUSE_ALLOW * numberOfSpouses;
			}
		}
		return 0;
	}
	public static calculate(data: UnemployedCase) {
		const self = this.calculateSelfAllowance(data);
		const child = this.calculateChild(data);
		const spouse = this.calculateSpousesAllowance(data);
		const final = self + child + spouse - Number(data.employment.totalIncome);

		return {
			self,
			child,
			spouse,
			final,
			eightyPercant: final * 0.8,
		};
	}
}
