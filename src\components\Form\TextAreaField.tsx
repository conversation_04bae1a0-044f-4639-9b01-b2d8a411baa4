import { Textarea } from "@chakra-ui/react";
import { Field } from "formik";
const TextField = (field: any, meta: any, props: any) => {
	return (
		<Field
			maxLength={10000}
			as={Textarea}
			type={props.type}
			focusBorderColor="brand.mainGold"
			fontSize={"1.063rem"}
			borderRadius="0.3125rem"
			boxShadow={"unset"}
			h="3.5rem"
			errorBorderColor="brand.errorFieldBorder"
			_placeholder={{ color: "brand.fieldPlaceholder" }}
			_disabled={{ bg: "brand.gray.300", color: "brand.gray.400", border: "0px" }}
			{...field}
			{...props}
		/>
	);
};

export default TextField;
