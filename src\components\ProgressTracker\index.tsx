import { Box, CircularProgress, CircularProgress<PERSON><PERSON>l, Flex, Text } from "@chakra-ui/react";
import React from "react";
import Stepper from "components/Stepper";
import { useTranslation } from "next-i18next";

interface Props {
	activeStep: number;
	activeSubIndex: number;
	steps: Array<{ label: string; subSteps: string[] }>;
	service: string;
}
function ProgressTracker({ activeStep = 0, activeSubIndex = 0, steps, service }: Props) {
	const { t } = useTranslation("forms");

	const isOverLimit = activeStep + 1 > steps.length ? true : false;
	return (
		<>
			<Flex
				display={{ base: "none", md: "flex" }}
				bg="brand.white.100"
				flexDir="column"
				w="100%"
				h="full"
				p={8}
				mr={4}
				pt={{ base: 0, md: 8 }}
			>
				<Stepper
					activeStep={activeStep}
					activeSubIndex={activeSubIndex}
					steps={steps}
					stepOnClick={(activeIndex) => {}}
					subStepOnClick={(activeSubIndex) => {}}
					service={service}
				/>
			</Flex>
			<Box
				mb="2.125rem"
				w="100%"
				bg="brand.white.100"
				px={4}
				py={4}
				borderY="1px solid "
				borderColor="brand.mainGold"
				display={{ md: "none" }}
			>
				<Text my={2}>{service}</Text>
				<Flex>
					<Box>
						<CircularProgress
							value={activeStep + 1}
							max={steps.length}
							size="5.9rem"
							color="brand.mainGold"
							thickness="6px"
							trackColor="white"
							transform="scale(-1,1)"
						>
							<CircularProgressLabel fontSize="2xl" fontWeight="bold">
								<Text transform="scale(-1,1)" color={"black"}>
									{isOverLimit ? steps.length : activeStep + 1}/{steps.length}
								</Text>
							</CircularProgressLabel>
						</CircularProgress>
					</Box>
					<Flex direction="column" justifyContent="center" ml={3.5}>
						<Box mb={1}>
							<Text fontSize="xl" fontWeight="medium" lineHeight="1.5rem">
								{isOverLimit ? steps[steps.length - 1]?.label : steps[activeStep]?.label}
							</Text>
						</Box>
						{steps[activeStep + 1] && (
							<Box>
								<Text fontSize="xs" lineHeight="1.5rem" fontWeight="normal" opacity="0.5">
									{t("next")}: {steps[activeStep + 1].label}
								</Text>
							</Box>
						)}
					</Flex>
				</Flex>
			</Box>
		</>
	);
}

export default ProgressTracker;
