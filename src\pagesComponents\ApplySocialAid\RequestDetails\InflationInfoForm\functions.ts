import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	ApplyInflationAllowance: "",
	ApplyUtilityAllowance: "",
	UtilityProvider: "",
	UtilityAccountNumber: "",
};

const getValidationSchema = (t) => {
	return Yup.object({
		ApplyInflationAllowance: Yup.string().required().label("thisField").nullable(),
		ApplyUtilityAllowance: Yup.string().when(["ApplyInflationAllowance"], {
			is: (ApplyInflationAllowance) => {
				return ApplyInflationAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		UtilityAccountNumber: Yup.number()
			.positive("mustBePositive")
			.when(["ApplyInflationAllowance", "ApplyUtilityAllowance"], {
				is: (ApplyInflationAllowance, ApplyUtilityAllowance) => {
					return ApplyInflationAllowance === "yes" && ApplyUtilityAllowance === "yes";
				},
				then: Yup.number()
					.positive("mustBePositive")
					.typeError("ThisFieldShouldbeNumber")
					.required()
					.label("thisField")
					.nullable(),
				otherwise: Yup.number().notRequired().nullable(),
			}),
		UtilityProvider: Yup.object().when(["ApplyHousingAllowance", "ApplyUtilityAllowance"], {
			is: (ApplyInflationAllowance, ApplyUtilityAllowance) => {
				return ApplyInflationAllowance === "yes" && ApplyUtilityAllowance === "yes";
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}),
			otherwise: Yup.object().notRequired().nullable(),
		}),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// If the form values change, trigger validation to update button state
	if (
		event &&
		event.target &&
		(event.target.name === "ApplyUtilityAllowance" ||
			event.target.name === "ApplyInflationAllowance")
	) {
		setTimeout(() => formikProps.validateForm(), 0);
	}
};
export { getInitialValues, onChange, getValidationSchema };
