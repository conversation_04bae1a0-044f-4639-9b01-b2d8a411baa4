import React, { ReactElement, useEffect, useState } from "react";
import MainLayout from "layouts/MainLayout";
import { Box, Button } from "@chakra-ui/react";
import { Login, Validate } from "pagesComponents/Login";
import { signIn } from "next-auth/react";
import { useRouter } from "next/router";
import { EMIRATES_ID_REGEX, SHOW_MOCK_LOGIN } from "config";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { generateOtp } from "services/frontend";
import { useMutation } from "react-query";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import { handleApiErrorMessage } from "utils/helpers";
import { getEmiratesIdFromToken } from "utils/helpers";

function LoginPage(props) {
	const router = useRouter();
	const { t } = useTranslation(["common", "login"]);
	const callbackUrl = router.query.callbackUrl;
	const errorMessage = router.query.error;
	const locale = router.locale;

	const toast = useAppToast();

	const [username, setUsername] = useState("");
	const [encryptedMobileNumber, setEncryptedMobileNumber] = useState("");
	const [usernameError, setUsernameError] = useState(false);
	const [showValidate, setShowValidate] = useState(false);
	const [loginSuccess, setLoginSuccess] = useState(false);

	const [otp, setOtp] = useState("");
	const [otpNumber, setOtpNumber] = useState("");
	const [otpError, setOtpError] = useState(false);

	const { mutateAsync: callGenerateOtp, isLoading: generateOtpLoading } = useMutation({
		mutationFn: () => generateOtp(username),
		mutationKey: "generateOtp",
	});

	const { mutateAsync: callSignIn, isLoading: signInLoading } = useMutation({
		mutationFn: () =>
			signIn("credentials", {
				redirect: false,
				username,
				otp,
				phone: encryptedMobileNumber,
			}),
		mutationKey: ["signInOtp", username, otp],
	});

	useEffect(() => {
		if (errorMessage) {
			if (errorMessage === "SessionRequired") {
				toast({
					title: t("login:loginToAccess"),
					status: "error",
				});
			} else {
				toast({
					title: t("genericErrorTitle"),
					description: t("genericErrorDescription"),
					status: "error",
				});
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [!!errorMessage]);

	useEffect(() => {
		if (props.isAuth) {
			router.push("/");
		}
	}, []);
	const onRequestOtp = async () => {
		setOtpError(false);
		if (!EMIRATES_ID_REGEX.test(username)) {
			setUsernameError(true);
			return;
		} else {
			setUsernameError(false);
		}
		const data = await callGenerateOtp();

		if (data?.IsSuccess) {
			if (showValidate) {
				toast({
					title: t("login:otpResentSuccessTitle"),
					description: t("login:otpResentSuccessDescription"),
					status: "info",
				});
			}
			setOtpNumber(data?.Data?.MobileNumber || "XXXXXXX");
			setEncryptedMobileNumber(data?.Data?.EncryptedMobileNumber || "");
			setShowValidate(true);
		} else {
			handleApiErrorMessage(data?.Errors, toast, t, locale);
		}
	};

	const onEnterUsername = (username) => {
		setUsernameError(false);
		const re = /^[0-9\b]+$/;
		if (username === "" || re.test(username)) {
			setUsername(username);
		}
	};

	const onEnterOtp = (otp) => {
		setOtpError(false);
		setOtp(otp);
	};

	const onValidateOtp = async () => {
		if (!otp || otp.length !== 6) {
			setOtpError(true);
			return;
		}

		const res = await callSignIn();

		if (res?.error) {
			setOtpError(true);
		}

		if (res?.ok) {
			setLoginSuccess(true);
			if (callbackUrl?.toString()) {
				const url = new URL(callbackUrl.toString(), window.location.origin);
				url.pathname = `/${locale}${url.pathname}`;
				window.location.href = url.toString();
				return;
			}
			window.location.href = `${window.location.origin}${locale === "ar" ? "" : "/" + locale}/`;
		}
	};

	const onCancelOtp = () => {
		setUsername("");
		setShowValidate(false);
	};

	return (
		<Box width={"100%"} position={"relative"}>
			<Box
				width={{ base: "100%", sm: "100%", md: "65%" }}
				position={"relative"}
				bg={"url('/assets/images/loginImg.jpg')"}
				height={{ base: "91vh", md: "calc(100vh - 98px)" }}
				backgroundSize={"cover"}
				backgroundPosition={{ base: "center", md: "unset" }}
				bgRepeat={"no-repeat"}
			>
				{!showValidate && (
					<Login
						onRequestOtp={onRequestOtp}
						onEnterUsername={onEnterUsername}
						username={username}
						usernameError={usernameError}
						generateOtpLoading={generateOtpLoading}
					/>
				)}
				{showValidate && (
					<Validate
						otpNumber={otpNumber}
						onEnterOtp={onEnterOtp}
						otpError={otpError}
						onValidateOtp={onValidateOtp}
						onRequestOtp={onRequestOtp}
						onCancel={onCancelOtp}
						signInLoading={signInLoading || loginSuccess}
					/>
				)}
				{SHOW_MOCK_LOGIN && (
					<Button
						onClick={async () => {
							setUsername("MOCK");
							setOtp("MOCK");
							setEncryptedMobileNumber("MOCK");
							setTimeout(async () => {
								const res = await callSignIn();
								if (res?.ok)
									window.location.href =
										callbackUrl?.toString() ||
										`${window.location.origin}${locale === "ar" ? "" : "/" + locale}/about`;
							}, 1000);
						}}
					>
						Dev Login
					</Button>
				)}
			</Box>
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	let emiratesId = await getEmiratesIdFromToken(ctx.req);
	let isAuth = false;
	if (emiratesId) {
		isAuth = true;
	} else {
		emiratesId = "";
	}
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "login"])),
			isAuth,
		},
	};
}

LoginPage.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout minimalLayout>{page}</MainLayout>;
};

export default LoginPage;
