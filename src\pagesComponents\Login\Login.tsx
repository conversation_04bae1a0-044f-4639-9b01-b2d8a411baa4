import { <PERSON><PERSON>, Card, Center, HStack, Input, VStack, Text, Flex, Box } from "@chakra-ui/react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { signIn } from "next-auth/react";
import { useRouter } from "next/router";
import { onEnterKey } from "utils/helpers";
import { formatEmiratesID } from "utils/formatters";
import { useEffect, useState } from "react";
import { UaePass } from "components/Icons";
import { useCallback } from "react";

function Login({ onRequestOtp, onEnterUsername, username, usernameError, generateOtpLoading }) {
	const { t } = useTranslation("login");
	const router = useRouter();
	const callbackUrl = router.query.callbackUrl;
	const locale = router.locale;
	const [focused, setFocused] = useState(false);
	const [formattedValue, setFormattedValue] = useState("");
	useEffect(() => {
		setFormattedValue(formatEmiratesID(username));
	}, [username]);

	const emIdInput = useCallback((inputElement) => {
		if (inputElement) {
			inputElement.focus();
		}
	}, []);

	return (
		<Card
			bg={"brand.white.50"}
			p={{ base: "32px 1rem", md: "42px 58px" }}
			position={{ base: "unset", md: "absolute" }}
			top={"10%"}
			m={{ base: "0px 20px;", md: "0px" }}
			right={{ base: "0", md: "-35%" }}
			borderRadius={"unset"}
			boxShadow={{ base: "none", md: "base" }}
			marginX={"auto"}
			h={{ base: "100vh", md: "unset" }}
		>
			<VStack spacing={2.5} mb={8.5}>
				<Text fontSize={"4xl"} color={"brand.mainHeaderColor"} fontWeight={"bold"} w={"100%"}>
					{t("title")}
				</Text>
			</VStack>
			<Text fontSize={"md"} color={"brand.fieldLabelColor"} pb={2}>
				{t("emiratesID", { ns: "common" })}
			</Text>
			<Input
				ref={emIdInput}
				width={{ base: "100%", md: "100%" }}
				height={"56px"}
				color={"brand.inputColors.fontColor"}
				type="text"
				borderWidth={"px"}
				onFocus={() => {
					setFocused(true);
				}}
				onBlurCapture={() => {
					setFocused(false);
				}}
				_placeholder={{
					color: "brand.inputColors.placeHolderColor",
				}}
				borderColor={"brand.mainGold"}
				focusBorderColor={"brand.mainGold"}
				placeholder={t("eidPlaceholder") || ""}
				onChange={focused ? (e) => onEnterUsername(e.target.value) : undefined}
				value={focused ? username : formattedValue}
				isInvalid={usernameError}
				maxLength={15}
				onKeyPress={(e) => onEnterKey(e, onRequestOtp)}
			/>
			{usernameError && (
				<Text fontSize={"sm"} color={"brand.errorColor"} mt={2}>
					{t("pleaseEnterValidEid")}
				</Text>
			)}
			<Text fontSize={"md"} color={"brand.fieldLabelColor"} mt={2}>
				* {t("otpNotification")}
			</Text>
			<VStack spacing={5} mt={10}>
				<Button
					height={"56px"}
					variant="primary"
					w={"100%"}
					onClick={onRequestOtp}
					isLoading={generateOtpLoading}
				>
					<Center w="100%">
						<Text fontSize={{ base: "sm", md: "lg" }} color={"brand.white.50"}>
							{t("requestOtp")}
						</Text>
					</Center>
				</Button>
				<Flex flexDirection={"row"} w={"100%"} alignItems={"center"} justifyContent={"center"}>
					{/* <Box bg={"brand.mainGold"} w={"100%"} h={"1px"} /> */}
					<Text fontSize={{ base: "sm", md: "md" }} fontWeight={"bold"} px={3} textAlign={"center"}>
						{t("loginOr")}
					</Text>
					{/* <Box bg={"brand.mainGold"} w={"100%"} h={"1px"} /> */}
				</Flex>
				{/* <Button
					variant="link"
					w={"100%"}
					onClick={() => {
						signIn(
							"uaepass",
							{
								callbackUrl: callbackUrl?.toString() || `/${locale}`,
							},
							{ ui_locales: locale || "ar" }
						);
					}}
				>
					<Image src={`/assets/images/uaepass_${locale}.png`} alt="uaePassLogo" />
				</Button> */}
				<Button
					height={"56px"}
					leftIcon={<UaePass h={"24px"} w={"24px"} />}
					borderWidth={"px"}
					borderColor={"brand.textColor"}
					borderRadius={"5px"}
					variant="link"
					w={"100%"}
					onClick={() => {
						signIn(
							"uaepass",
							{
								callbackUrl: callbackUrl?.toString() || `/${locale}`,
							},
							{ ui_locales: locale || "ar" }
						);
					}}
				>
					<Text color={"#1B1D21"} fontSize={"18px"}>
						{t("Sign_In_UAE_PASS")}
					</Text>
				</Button>
				{/* <Text fontSize={"md"} color={"brand.textColor"}>
					{t("description")}
				</Text> */}
			</VStack>
			{/* <Divider bg={"brand.mainGold"} height={"px"} m={"2.5rem 0 1.25rem"} borderBottomWidth={0} /> */}
			<Box bg={"#BBBCBD"} w={"100%"} h={"1px"} my={"28px"} />
			<HStack>
				<Text fontSize={"sm"} color={"brand.textColor"}>
					{t("dontHaveUaePass")}
				</Text>
				<Text fontSize={"sm"} color={"brand.mainGold"} fontWeight={"bold"}>
					<Link href={"https://selfcare.uaepass.ae/signup"} target={"_blank"}>
						{t("createNewUaePass")}
					</Link>
				</Text>
			</HStack>
		</Card>
	);
}

export default Login;
