import { Box, Button, Grid, GridItem } from "@chakra-ui/react";
import BoxHeader from "components/BoxHeader";
import Card from "components/Card";
import { useTranslation } from "next-i18next";
import React from "react";

const NewsAndUpdates = () => {
	const { t } = useTranslation();

	const data = [
		{
			imgSrc: "../assets/images/testImg1.png",
			title: "Lorem Ipsum",
			date: "01/03/2021",
			description:
				"Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis, voluptatum delectus culpa neque maiores voluptates ad consequuntur architecto, aperiam doloribus officiis, natus quam excepturi omnis temporibus atque molestiae recusandae nemo!",
		},
		{
			imgSrc: "../assets/images/testImg1.png",
			title: "Lorem Ipsum",
			date: "01/03/2021",
			description:
				"Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis, voluptatum delectus culpa neque maiores voluptates ad consequuntur architecto, aperiam doloribus officiis, natus quam excepturi omnis temporibus atque molestiae recusandae nemo!",
		},
		{
			imgSrc: "../assets/images/testImg1.png",
			title: "Lorem Ipsum",
			date: "01/03/2021",
			description:
				"Lorem ipsum dolor sit amet consectetur adipisicing elit. Veritatis, voluptatum delectus culpa neque maiores voluptates ad consequuntur architecto, aperiam doloribus officiis, natus quam excepturi omnis temporibus atque molestiae recusandae nemo!",
		},
	];
	return (
		<Box bg="brand.white.50">
			<BoxHeader
				title={t("newsAndUpdatesTitle")}
				icon="../assets/images/newsIcon.png"
				width="2.1875rem"
				height="2rem"
				alt="image not found"
			/>
			<Grid templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(3, 1fr)" }} gap="4">
				{!!data.length &&
					data.map((news, index) => (
						<GridItem
							colSpan={1}
							key={index}
							display={{ base: `${index > 0 ? "none" : "block"}`, md: "block" }}
						>
							<Card
								imgSrc={news.imgSrc}
								description={news.description}
								date={news.date}
								title={news.title}
							/>
						</GridItem>
					))}
			</Grid>
			<Button variant="secondary" mt="1.875rem" w={{ base: "100%", md: "auto" }}>
				{t("viewAll")}
			</Button>
		</Box>
	);
};

export default NewsAndUpdates;
