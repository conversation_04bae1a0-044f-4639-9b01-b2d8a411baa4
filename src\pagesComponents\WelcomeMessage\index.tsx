import { Box, Flex, Text } from "@chakra-ui/react";
import Breadcrumbs from "components/Breadcrumbs";
import { useTranslation } from "react-i18next";

function WelcomeMessage({
	image,
	mobileImg,
	title,
	description,
	breadcrumbsData = [],
	butTitle = "",
}) {
	const { t } = useTranslation("home");

	return (
		<Flex w="100%">
			<Box
				w={{ base: "100%", md: "100%" }}
				minH={{ base: "500px", md: "640px" }}
				bgImage={{
					base: mobileImg,
					md: image,
				}}
				bgPosition={{ base: "100%", md: "100%" }}
				bgSize="cover"
				bgRepeat={"no-repeat"}
				bgPos={{ base: "top", md: "center" }}
				className="overlay"
				pos={"relative"}
			>
				<Flex h="full" alignItems={"end"} pos={"relative"} zIndex={1}>
					<Box
						width={{ base: "100%", sm: "90%", md: "40%" }}
						mb="20"
						marginInlineStart={{ base: "5", lg: "10" }}
					>
						{breadcrumbsData.length > 0 && (
							<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
								<Breadcrumbs data={breadcrumbsData} isLight />
							</Box>
						)}
						<Text color={"brand.white.50"} fontSize={{ base: "2rem", lg: "6xl" }}>
							{t(title)}
						</Text>
						<Text color={"brand.white.50"} fontSize={{ base: "1.25rem", lg: "2xl" }}>
							{t(description)}
						</Text>
					</Box>
				</Flex>
			</Box>
		</Flex>
	);
}

export default WelcomeMessage;
