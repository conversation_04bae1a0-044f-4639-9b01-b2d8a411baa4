import React, { forwardRef, useEffect, useRef } from "react";
import {
	Table,
	Thead,
	Tbody,
	Tr,
	Th,
	Td,
	Flex,
	useBreakpoint,
	Box,
	Text,
	Show,
	VStack,
} from "@chakra-ui/react";
import {
	useReactTable,
	flexRender,
	getCoreRowModel,
	ColumnDef,
	SortingState,
	getSortedRowModel,
	getPaginationRowModel,
} from "@tanstack/react-table";
import { TableSortIcon } from "components/Icons";

import ReactPaginate from "react-paginate";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";
import { DRAFT_STATUS_ID, PENDING_STATUS_ID, SOCIALAID_TEMPLATE_ID } from "config";

type DataTableProps = {
	data: any[];
	columns: ColumnDef<any, any>[];
};

export const DataTable = forwardRef(({ columns, data }: DataTableProps, ref) => {
	const { push: routerPush, locale } = useRouter();
	const [sorting, setSorting] = React.useState<SortingState>([]);

	const tableDataRef = useRef(null);
	const toast = useAppToast();
	const { t } = useTranslation(["forms", "common"]);

	const table = useReactTable({
		columns,
		data,
		getCoreRowModel: getCoreRowModel(),
		onSortingChange: setSorting,
		getSortedRowModel: getSortedRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		state: {
			sorting,
		},
		initialState: {
			pagination: {
				pageSize: 10,
				pageIndex: 0,
			},
		},
	});

	const handleClick = (rowData: any) => {
		if (rowData?.Status?.Key === DRAFT_STATUS_ID) {
			toast({
				title: t("pleaseWaitLoadDraft", { ns: "common" }),
				status: "info",
			});
			if (rowData?.Template?.TemplateId === SOCIALAID_TEMPLATE_ID)
				routerPush(`/smart-services/how-to-apply/apply-socialaid?requestId=${rowData.CaseId}`);
			else if (rowData?.Template?.TemplateName.includes("Farmer")) {
				routerPush(
					`/smart-services/farmer-service/apply-farmer-service?requestId=${rowData.CaseId}`
				);
			}
		} else if (rowData?.Status.Key === PENDING_STATUS_ID) {
			// this when the request is pending state (it was sent back by agent)
			const targetUrl = rowData?.Template?.TemplateName.includes("Farmer")
				? "farmer-service/apply-farmer-service"
				: "how-to-apply/apply-socialaid";
			toast({
				title: t("pleaseWaitLoadDraft", { ns: "common" }),
				status: "info",
			});
			routerPush(`/smart-services/${targetUrl}?requestId=${rowData.CaseId}&isPending=true`);
		} else {
		}
	};

	const breakpoint = useBreakpoint();
	useEffect(() => {
		const allColumns = table.getAllColumns();
		const lastColumn = allColumns[allColumns.length - 1];
		//lastColumn.toggleVisibility(breakpoint !== "base");
	}, [breakpoint]);

	return (
		<>
			<Show above="md">
				<Box
					border="1px solid var(--black-30, #BBBCBD)"
					rounded={"lg"}
					roundedBottom={"none"}
					borderBottom={"none"}
					overflowX={"auto"}
				>
					<Table
						variant="unstyled"
						w="100%"
						bg="white"
						mt={{ base: 4, md: 0 }}
						ref={tableDataRef}
						size={["sm", "md"]}
						sx={{ md: { tableLayout: "fixed" }, base: { tableLayout: "unset" } }}
						rounded={"204"}
					>
						<Thead>
							{table.getHeaderGroups().map((headerGroup) => (
								<Tr key={headerGroup.id}>
									{headerGroup.headers.map((header, idx) => {
										return (
											<Th
												borderBottom={"1px solid #BBBCBD"}
												key={idx}
												onClick={header.column.getToggleSortingHandler()}
											>
												<Flex alignItems={"center"} my="2">
													<Text fontSize={{ md: "1rem", base: "0.875rem" }}>
														{flexRender(header.column.columnDef.header, header.getContext())}
													</Text>
													<Show above="md">
														<TableSortIcon ms="4" />
													</Show>
												</Flex>
											</Th>
										);
									})}
								</Tr>
							))}
						</Thead>

						<Tbody textAlign="start" marginLeft="20px">
							{table.getRowModel().rows.map((row, index) => (
								<React.Fragment key={index}>
									<Tr
										key={row.id}
										pos="relative"
										textAlign="start"
										onClick={() => handleClick(row.original)}
										borderBottom={"1px solid #BBBCBD"}
									>
										{row.getVisibleCells().map((cell) => (
											<Td key={cell.id} color={"black"} padding={0} fontWeight={"normal"}>
												<Text fontSize={{ base: "0.7rem", md: "small" }} as="span">
													{flexRender(cell.column.columnDef.cell, cell.getContext())}
												</Text>
											</Td>
										))}
									</Tr>
								</React.Fragment>
							))}
						</Tbody>
					</Table>
				</Box>
			</Show>
			<Show below="md">
				<VStack align={"start"}>
					{table.getRowModel().rows.map((row, index) => {
						const [reqNum, reqName, date, tag] = row.getVisibleCells();
						return (
							<Flex
								key={index}
								w="full"
								p={4}
								justifyContent={"space-between"}
								borderBottom="1px solid #BBBCBD"
								_hover={{ cursor: "pointer" }}
							>
								<VStack flex={2} align={"start"} w="50%">
									<Text fontSize={"1rem"} fontWeight={"bold"}>
										{flexRender(reqName.column.columnDef.cell, reqName.getContext())}
									</Text>
									<Text fontSize={"0.875rem"}>
										{t("requestNumber")} :{" "}
										<Text
											as="span"
											textColor={"#005DBA"}
											textDecoration={"underline"}
											onClick={() => handleClick(row.original)}
										>
											{String(reqNum.renderValue())}
										</Text>
									</Text>
									<Text fontSize={"0.875rem"}>
										{flexRender(date.column.columnDef.cell, date.getContext())}
									</Text>
								</VStack>
								{/* <Box w="40%" fontSize={"sm"}>
										{" "}
										{flexRender(tag.column.columnDef.cell, tag.getContext())}
									</Box> */}
							</Flex>
						);
					})}
				</VStack>
			</Show>
			{table.getPageCount() > 1 && (
				<ReactPaginate
					breakLabel="..."
					nextLabel={
						<>
							<Flex marginInlineStart={"1rem"}>
								<Text>{t("next", { ns: "common" })}</Text>
								<ChevronRightIcon
									h={"24px"}
									w={"24px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
							</Flex>
						</>
					}
					onPageChange={({ selected }) => {
						table.setPageIndex(selected);
					}}
					pageRangeDisplayed={0}
					pageCount={table.getPageCount()}
					initialPage={0}
					previousLabel={
						<Flex marginInlineEnd={"1rem"}>
							<ChevronLeftIcon
								h={"24px"}
								w={"24px"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
							<Text>{t("previous", { ns: "common" })}</Text>
						</Flex>
					}
					className={`pagination pagination-${breakpoint}`}
					// previousLinkClassName="links"
					// nextLinkClassName="links"
					pageClassName="page-num"
					// breakClassName="break-label"
					activeClassName="active-page"
					forcePage={table.getState().pagination.pageIndex}
					// disabledLinkClassName="disable-link"
				/>
			)}
		</>
	);
});

DataTable.displayName = "DataTable";
