import { IGenerateOtpResponse } from "interfaces/GenerateOtp.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<IGenerateOtpResponse>>
) {
	const { emiratesId } = req.query;
	if (!emiratesId) return res.status(400).json({ ...errorResponse, Errors: "emiratesId missing" });

	const data = await BackendServices.generateOtp(emiratesId.toString());
	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
