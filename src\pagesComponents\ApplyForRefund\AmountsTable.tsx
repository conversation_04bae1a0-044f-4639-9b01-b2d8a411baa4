import React, { forwardRef, useEffect, useRef } from "react";
import { Table, Tbody, Tr, Td, Flex, useBreakpoint, Box, Text, Show } from "@chakra-ui/react";
import {
	useReactTable,
	getCoreRowModel,
	ColumnDef,
	SortingState,
	getSortedRowModel,
	getPaginationRowModel,
} from "@tanstack/react-table";

import ReactPaginate from "react-paginate";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";

type DataTableProps = {
	data: any[];
	columns: ColumnDef<any, any>[];
};

export const AmountsTable = forwardRef(({ columns, data }: DataTableProps, ref) => {
	const { push: routerPush, locale } = useRouter();
	const [sorting, setSorting] = React.useState<SortingState>([]);

	const tableDataRef = useRef(null);
	const toast = useAppToast();
	const { t } = useTranslation(["forms", "common"]);

	const table = useReactTable({
		columns,
		data,
		getCoreRowModel: getCoreRowModel(),
		onSortingChange: setSorting,
		getSortedRowModel: getSortedRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		state: {
			sorting,
		},
		initialState: {
			pagination: {
				pageSize: 10,
				pageIndex: 0,
			},
		},
	});

	const breakpoint = useBreakpoint();
	useEffect(() => {
		const allColumns = table.getAllColumns();
		const lastColumn = allColumns[allColumns.length - 1];
		//lastColumn.toggleVisibility(breakpoint !== "base");
	}, [breakpoint]);

	return (
		<>
			<Show above="md">
				<Box
					border="1px solid var(--black-30, #BBBCBD)"
					rounded={"lg"}
					my="4"
					overflowX={"auto"}
					w="48%"
				>
					<Table
						variant="unstyled"
						w="100%"
						bg="white"
						mt={{ base: 4, md: 2 }}
						ref={tableDataRef}
						size={["sm", "md"]}
						sx={{ md: { tableLayout: "fixed" }, base: { tableLayout: "unset" } }}
						rounded={"204"}
					>
						<Tbody textAlign="start">
							{data.map((row, rowIndex) =>
								row.label === t("AccumulatedAmount", { ns: "common" }) && row.value === 0 ? null : (
									<Tr key={rowIndex}>
										<Td fontWeight="bold">{row.label}</Td>
										{/* {row.value !== 0 ? ( */}
										<Td fontWeight="bold">
											{row.label === t("caseNumber", { ns: "common" })
												? row.value
												: `${row.value} ${t("AED", { ns: "common" })}`}
										</Td>
										{/* ) : (
            <Td></Td>
          )} */}
									</Tr>
								)
							)}
						</Tbody>
					</Table>
				</Box>
			</Show>
			<Show below="md">
				<Box
					border="1px solid var(--black-30, #BBBCBD)"
					rounded={"lg"}
					my="4"
					overflowX={"auto"}
					w="100%"
				>
					<Table
						variant="unstyled"
						w="100%"
						bg="white"
						mt={{ base: 4, md: 2 }}
						ref={tableDataRef}
						size={["sm", "md"]}
						sx={{ md: { tableLayout: "fixed" }, base: { tableLayout: "unset" } }}
						rounded={"204"}
					>
						<Tbody textAlign="start">
							{data.map((row, rowIndex) => (
								<Tr key={rowIndex}>
									<Td fontWeight="bold">{row.label}</Td>
									{row.value !== 0 ? (
										<Td fontWeight="bold">
											{row.label === t("caseNumber", { ns: "common" })
												? row.value
												: `${row.value} ${t("AED", { ns: "common" })}`}
										</Td>
									) : (
										<Td></Td>
									)}
								</Tr>
							))}
						</Tbody>
					</Table>
				</Box>
			</Show>

			{table.getPageCount() > 1 && (
				<ReactPaginate
					breakLabel="..."
					nextLabel={
						<>
							<Flex marginInlineStart={"1rem"}>
								<Text>{t("next", { ns: "common" })}</Text>
								<ChevronRightIcon
									h={"24px"}
									w={"24px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
							</Flex>
						</>
					}
					onPageChange={({ selected }) => {
						table.setPageIndex(selected);
					}}
					pageRangeDisplayed={0}
					pageCount={table.getPageCount()}
					initialPage={0}
					previousLabel={
						<Flex marginInlineEnd={"1rem"}>
							<ChevronLeftIcon
								h={"24px"}
								w={"24px"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
							<Text>{t("previous", { ns: "common" })}</Text>
						</Flex>
					}
					className={`pagination pagination-${breakpoint}`}
					// previousLinkClassName="links"
					// nextLinkClassName="links"
					pageClassName="page-num"
					// breakClassName="break-label"
					activeClassName="active-page"
					forcePage={table.getState().pagination.pageIndex}
					// disabledLinkClassName="disable-link"
				/>
			)}
		</>
	);
});

AmountsTable.displayName = "AmountsTable";
