import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	Occupations: "",
};

const getValidationSchema = () => {
	return Yup.object({
		Occupations: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("amer", event, formikProps);
};

const getListDefault = (arr) => (!arr || arr?.length === 0 ? [{}] : arr);

export { getInitialValues, onChange, getValidationSchema, getListDefault };
