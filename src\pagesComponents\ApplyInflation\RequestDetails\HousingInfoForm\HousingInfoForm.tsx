import { Grid, GridItem, Text } from "@chakra-ui/react";
import <PERSON><PERSON>ield from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import * as functions from "./functions";
import { useRouter } from "next/router";
import { DIVORCED, EMPLOYEDLOWINCOME, EMPLOYED_ID } from "config";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	caseData,
	handleSetFormikState,
	initialData,
	readOnly = false,
}) {
	const formikRef = useRef<any>(null);
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { lookups } = useFormContext();
	let [validationSchema] = useState(functions.getValidationSchema(t, caseData));

	const updateDropdownValues = () => {
		let originalInitialValues: any = { ...functions.getInitialValues };
		if (initialData) {
			Object.keys(initialData).forEach((key) => {
				if (key in lookups) {
					let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				} else {
					originalInitialValues[key] = initialData[key]
						? JSON.parse(JSON.stringify(initialData[key]))
						: initialData[key];
				}
				if (key === "LivingSituation") {
					let indexOfItem = lookups["accomadations"].findIndex(
						(val) => val.value === initialData["LivingSituation"]
					);
					if (indexOfItem >= 0)
						originalInitialValues["LivingSituation"] = lookups["accomadations"][indexOfItem];
				}
			});
		}

		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			innerRef={formikRef}
			validateOnMount
		>
			{(formik: any) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							{/* Main reason for social aid */}
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="radio"
									label={t("ApplyHousingAllowance")}
									name="ApplyHousingAllowance"
									value={formik.values["ApplyHousingAllowance"]}
									touched={formik.touched["ApplyHousingAllowance"]}
									error={formik.errors["ApplyHousingAllowance"]}
									options={lookups.Boolean}
									isReadOnly={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent("radio", firstArg, "ApplyHousingAllowance", formik, formKey);
									}}
								/>
							</GridItem>
							{formik.values["ApplyHousingAllowance"] &&
								formik.values["ApplyHousingAllowance"] === "yes" && (
									<>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("ReceivingFederalLocalhousingsupport")}
												name="ReceivingFederalLocalhousingsupport"
												value={formik.values["ReceivingFederalLocalhousingsupport"]}
												touched={formik.touched["ReceivingFederalLocalhousingsupport"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"radio",
														firstArg,
														"ReceivingFederalLocalhousingsupport",
														formik,
														formKey
													);
												}}
											/>
											<Text color={"#1b1d21b8"} mt={1.5}>
												{t("localSupText2")}
											</Text>
										</GridItem>
										{(caseData.socialAidInformation.Category === EMPLOYEDLOWINCOME ||
											caseData.personalInformation.Occupations === EMPLOYED_ID) && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="radio"
													label={t("ReceivingHousingAllowanceFromEmployer")}
													name="ReceivingHousingAllowanceFromEmployer"
													value={formik.values["ReceivingHousingAllowanceFromEmployer"]}
													touched={formik.touched["ReceivingHousingAllowanceFromEmployer"]}
													options={lookups.Boolean}
													isReadOnly={readOnly}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"ReceivingHousingAllowanceFromEmployer",
															formik,
															formKey
														);
													}}
												/>
											</GridItem>
										)}
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="radio"
												label={t("FullOwnershipResidentialProperty")}
												name="FullOwnershipResidentialProperty"
												value={formik.values["FullOwnershipResidentialProperty"]}
												touched={formik.touched["FullOwnershipResidentialProperty"]}
												options={lookups.Boolean}
												isReadOnly={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"radio",
														firstArg,
														"FullOwnershipResidentialProperty",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
										{formik.values["FullOwnershipResidentialProperty"] &&
											formik.values["FullOwnershipResidentialProperty"] === "yes" && (
												<GridItem colSpan={{ base: 2, md: 1 }}>
													<FormField
														type="radio"
														label={t("IsUtilityBillIssuedForFullyOwnedProperty")}
														name="IsUtilityBillIssuedForFullyOwnedProperty"
														value={formik.values["IsUtilityBillIssuedForFullyOwnedProperty"]}
														touched={formik.touched["IsUtilityBillIssuedForFullyOwnedProperty"]}
														options={lookups.Boolean}
														isReadOnly={readOnly}
														onChange={(firstArg) => {
															handleChangeEvent(
																"radio",
																firstArg,
																"IsUtilityBillIssuedForFullyOwnedProperty",
																formik,
																formKey
															);
														}}
													/>
												</GridItem>
											)}
										{(formik.values["IsUtilityBillIssuedForFullyOwnedProperty"] === "no" ||
											formik.values["FullOwnershipResidentialProperty"] === "no") && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="selectableTags"
													value={formik.values["LivingSituation"]}
													options={lookups.accomadations}
													isRequired={true}
													name="LivingSituation"
													label={t("LivingSituation")}
													isDisabled={readOnly}
													placeholder={t("placeholder", { ns: "common" })}
													error={formik.errors["LivingSituation"]}
													touched={formik.touched["LivingSituation"]}
													onChange={(firstArg) => {
														handleChangeEvent(
															"selectableTags",
															firstArg,
															"LivingSituation",
															formik,
															formKey
														);
													}}
												/>
											</GridItem>
										)}
										{caseData.socialAidInformation.SubCategory === DIVORCED && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="radio"
													label={t("ReceivesHousingSupportFromHusband")}
													name="ReceivesHousingSupportFromHusband"
													value={formik.values["ReceivesHousingSupportFromHusband"]}
													touched={formik.touched["ReceivesHousingSupportFromHusband"]}
													options={lookups.Boolean}
													isReadOnly={readOnly}
													onChange={(firstArg) => {
														handleChangeEvent(
															"radio",
															firstArg,
															"ReceivesHousingSupportFromHusband",
															formik,
															formKey
														);
													}}
												/>
											</GridItem>
										)}
									</>
								)}
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
