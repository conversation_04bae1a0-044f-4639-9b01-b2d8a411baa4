import { PODCase } from "../calculator";

export class PoDCaseCalculator {
	public static calculateChild(data: PODCase): number {
		const { personalInformation } = data;
		if (
			personalInformation.haveChildren === "1" &&
			(personalInformation.ageGroupPOD === "3" || personalInformation.ageGroupPOD === "2")
		) {
			const numberOfPodChildren = Number(personalInformation.numberOfPODChildren);

			let podChildsAllowance = 0;
			let normalChildsAllowance = 0;

			if (personalInformation.isChildrenPOD === "1") {
				podChildsAllowance = 5000 * numberOfPodChildren;
			}
			const numberOfChildren =
				Number(personalInformation.numberOfChildren) - (numberOfPodChildren || 0);
			if (numberOfChildren === 1) {
				normalChildsAllowance = 2400;
			} else if (numberOfChildren === 2) {
				normalChildsAllowance = 4000;
			} else if (numberOfChildren === 3) {
				normalChildsAllowance = 5600;
			} else if (numberOfChildren > 3) {
				normalChildsAllowance = 5600 + 800 * (numberOfChildren - 3);
			}
			return normalChildsAllowance + podChildsAllowance;
		}
		return 0;
	}
	public static calculateSelfAllowance(data: PODCase): number {
		return 5000;
	}
	public static calculateSpousesAllowance(data: PODCase): number {
		const { personalInformation } = data;
		if (
			personalInformation.maritalStatus === "2" &&
			personalInformation.gender === "1" &&
			(personalInformation.ageGroupPOD === "3" || personalInformation.ageGroupPOD === "2")
		) {
			const numberOfSpouses = Number(personalInformation.numberOfSpouses);
			const numberOfPODSpouses = Number(personalInformation.numberOfPODSpouses);
			if (personalInformation.isSpousesPOD === "1") {
				let podSpouseAll = 0;
				let nonPodSpouseAll = 0;
				podSpouseAll = numberOfPODSpouses * 5000;
				nonPodSpouseAll = (numberOfSpouses - numberOfPODSpouses) * 3500;
				return podSpouseAll + nonPodSpouseAll;
			} else {
				return 3500 * numberOfSpouses;
			}
		}
		return 0;
	}
	public static calculate(data: PODCase) {
		const self = 5000; // this.calculateSelfAllowance(data);
		const child = this.calculateChild(data);
		const spouse = this.calculateSpousesAllowance(data);
		const final = self + child + spouse - Number(data.personalInformation.totalIncome);
		const isEligble = final > 0;
		return {
			self,
			child,
			spouse,
			final,
			eightyPercant: final * 0.8,
			isEligble,
		};
	}
}
