import { Grid, GridItem, HStack, Text } from "@chakra-ui/react";
import ModalDialog from "components/ModalDialog";
import { Form, Formik } from "formik";
import { IChildFamilyMember, IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import FamilyMembersTable from "../FamilyMembersTable";
import EditFamilyMemberFormModal from "./EditFamilyMemberFormModal";
import * as functions from "./functions";
import EditChildMemberForm from "./EditChildMemberFormModal";
import { CHILD_LIST, WIFE_LOOKUP_ID } from "config";
import { useRouter } from "next/router";

interface Props {
	onSubmit: any;
	members: any;
	setMembers: any;
	childMembers: IChildFamilyMember[];
	setChildMembers: any;
	khulasitQaidNumber: string;
	readOnly: boolean;
	IsInflationBaseEdit: boolean;
	IsChildCase: boolean;
	IsInflationNominatedCaseEdit: boolean;
}

function FamilyMembersInfoForm({
	onSubmit,
	members,
	setMembers,
	childMembers,
	setChildMembers,
	khulasitQaidNumber,
	readOnly = false,
	IsInflationBaseEdit = false,
	IsChildCase = false,
	IsInflationNominatedCaseEdit = false,
}: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const [deleteMemberId, setDeleteMemberId] = useState("");
	const [editMember, setEditMember] = useState<IFamilyMember | null>(null);
	const [editChildMember, setEditChildMember] = useState<{
		memberChildInfo?: IChildFamilyMember;
		member?: IFamilyMember;
	} | null>(null);

	const handleDeleteMember = () => {
		setMembers((state) => {
			return state.filter((member) => member.Id !== deleteMemberId);
		});
		setDeleteMemberId("");
	};

	const onEditMember = (edittedFamilyMember: IFamilyMember) => {
		setMembers((state) => {
			return state.map((member) => {
				if (member.Id === edittedFamilyMember.Id) {
					return edittedFamilyMember;
				}
				return member;
			});
		});
		setEditMember(null);
	};

	const onEditChildMember = (edittedChildMember: IChildFamilyMember) => {
		setChildMembers((state) => {
			return state.map((member) => {
				if (member.Id === edittedChildMember.Id) {
					return edittedChildMember;
				}
				return member;
			});
		});
		setMembers((state) => {
			return state.map((member) => {
				if (member.Id === edittedChildMember.Id) {
					return { ...member, IsInformationUpdated: true };
				}
				return member;
			});
		});
		setEditChildMember(null);
	};

	const showEditMemberSubForm = (member: IFamilyMember) => {
		if (member.Relationship === WIFE_LOOKUP_ID) return setEditMember(member);
		else if (CHILD_LIST.includes(member.Relationship))
			setEditChildMember({ member, memberChildInfo: childMembers.find((m) => m.Id === member.Id) });
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			handleTextChange(firstArg, secondArg, formik);
		}
	};
	const handleTextChange = (event, fieldName, formik) => {
		formik.setFieldValue(fieldName, event?.target?.value || "");
	};
	const { locale } = useRouter();
	return (
		<>
			<Formik
				enableReinitialize
				initialValues={functions.getInitialValues}
				validationSchema={functions.getValidationSchema}
				onSubmit={onSubmit}
			>
				{(formik) => (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								{members.length > 0 && (
									<>
										<HStack mb={6}>
											<Text>{t("FamilyHousholdName")}</Text>
											<Text>
												{locale === "en" ? members[0]?.FamilyHeadEN : members[0]?.FamilyHeadAR}
											</Text>
										</HStack>
										<HStack mb={6}>
											<Text>{t("khulasitQaidNumber")}</Text>
											<Text>{khulasitQaidNumber}</Text>
										</HStack>
										<FamilyMembersTable
											readOnly={readOnly}
											members={members}
											setDeleteMemberId={setDeleteMemberId}
											setEditMember={showEditMemberSubForm}
											IsInflationBaseEdit={IsInflationBaseEdit}
											IsChildCase={IsChildCase}
											IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
										/>
									</>
								)}
								{members.length === 0 && <Text>{t("noFamilyMembersData")}</Text>}
							</GridItem>
						</Grid>
					</Form>
				)}
			</Formik>
			<ModalDialog
				isModalShow={!!deleteMemberId}
				handleOnClick={handleDeleteMember}
				handleOnClose={() => {
					setDeleteMemberId("");
				}}
				confirmText={t("confirm", { ns: "common" })}
				cancelText={t("cancel", { ns: "common" })}
				imgSrc="/assets/images/deleteIcon.png"
				headerTitle={t("deleteFamilyMember")}
				confirmationMessage={t("deleteFamilyMemberText")}
				undoneAction={t("deleteCantBeUndone")}
			/>
			<EditFamilyMemberFormModal
				member={editMember}
				onEditMember={onEditMember}
				onClose={() => setEditMember(null)}
				readOnly={readOnly}
			/>
			<EditChildMemberForm
				memberInfo={editChildMember}
				onEditMember={onEditChildMember}
				onClose={() => setEditChildMember(null)}
				readOnly={readOnly}
			/>
		</>
	);
}

export default FamilyMembersInfoForm;
