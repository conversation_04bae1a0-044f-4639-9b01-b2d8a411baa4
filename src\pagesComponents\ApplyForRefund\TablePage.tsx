import { DataTable } from "./DataTable";
import { Box } from "@chakra-ui/react";
import { forwardRef } from "react";

const TablePage = forwardRef((props: any, ref) => {
	return (
		<Box width="100%">
			{props?.data?.length > 0 && (
				<DataTable columns={props.columns} data={props?.data} ref={ref} />
			)}
			{/* {(props?.data?.length || 0) === 0 && (
				<Box bg="brand.white.50" py={32} px={6} mt={{ base: 2, md: 0 }}>
					<NoDataFound type={props.type} />
				</Box>
			)} */}
		</Box>
	);
});

TablePage.displayName = "TablePage";

export default TablePage;
