import * as Yup from "yup";

export const validationSchema = Yup.object({
	currentChildSituation: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	gender: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	ageGroupChild: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),

	qualifiedStudent: Yup.string().when("ageGroupChild", {
		is: (ageGroupChild) => {
			return ageGroupChild.value === "2";
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),

	haveSiblings: Yup.string().label("thisField").required(),

	numberOfSiblings: Yup.number().when("haveSiblings", {
		is: "1",
		then: Yup.number()
			.typeError("ThisFieldShouldbeNumber")
			.test({
				name: "is-1-or-2-digit",
				message: "PleaseEntera1or2-digit",
				test: (value: any) => /^[0-9]{1,2}$/.test(value),
			})
			.test({
				name: "no-signs-or-dots",
				message: "PleaseEnteranIntegerNumber",
				test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
			})
			.positive("PleaseEnteraPositivenumber")
			.min(1, "PleaseEnterNumberBiggerThanZero")
			.required(),
		otherwise: Yup.number().notRequired().nullable(),
	}),

	isSiblingsPOD: Yup.string().when("haveSiblings", {
		is: (haveSiblings) => {
			return haveSiblings === "1";
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),
	numberOfPODSiblings: Yup.number().when(["haveSiblings", "isSiblingsPOD"], {
		is: (haveSiblings, isSiblingsPOD) => {
			return haveSiblings === "1" && isSiblingsPOD === "1";
		},
		then: Yup.number()
			.label("thisField")
			.typeError("ThisFieldShouldbeNumber")
			.test("is-2-digit", "PleaseEntera1or2-digit", (value: any) => {
				return /^[0-9]{1,2}$/.test(value);
			})
			.test({
				name: "no-signs-or-dots",
				message: "PleaseEnteranIntegerNumber",
				test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
			})
			.positive("PleaseEnteraPositivenumber")
			.min(1, "PleaseEnterNumberBiggerThanZero")
			.max(Yup.ref("numberOfSiblings"), "NumberofPoDSiblings")
			.required(),
		otherwise: Yup.number().notRequired().nullable(),
	}),

	totalIncome: Yup.number()
		.label("thisField")
		.typeError("ThisFieldShouldbeNumber")
		.test({
			name: "no-signs-or-dots",
			message: "PleaseEnteranIntegerNumber",
			test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
		})
		.required(),
});
