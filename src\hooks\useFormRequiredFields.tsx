import { useState } from "react";

function useRequiredFields(validationSchema) {
	let [requiredList] = useState(() => {
		let requiredListObject = {};
		Object.keys(validationSchema.fields).forEach((field) => {
			requiredListObject[field] =
				validationSchema?.fields[field]?.exclusiveTests?.required || false;
		});
		return requiredListObject;
	});
	return requiredList;
}
export default useRequiredFields;
