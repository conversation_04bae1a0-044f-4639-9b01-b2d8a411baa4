import { Box, Flex } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import { ReactElement, useRef } from "react";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import {
	getContactIdFromToken,
	getEmiratesIdFromToken,
	getFormattedDate,
	getLocalizedFullName,
} from "utils/helpers";
import RequestSuccessful from "components/RequestSuccefull";
import ProgressTracker from "components/ProgressTracker";

function EditedSocialAid({
	caseRequest,
	profile,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["forms", "common"]);
	const childRef = useRef<any>(null);
	const router = useRouter();
	const { locale } = router;
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "navbar-howToApply",
			link: "/smart-services/how-to-apply",
			isCurrentPage: false,
		},
		{
			label: t("common:applayForSocial"),
			id: "howToApplyForSocialAid",
			link: "#",
			isCurrentPage: true,
		},
	];
	const titles = {
		title: t("requestEditedTitle"),
		caseNumberTitle: t("requestNumber"),
		caseDateTitle: t("forms:complaintSuccessDateTitle"),
		body: t("requestEditedBody1") + t("requestEditedBody2"),
		tableTitle: t("common:applicationSummary"),
	};
	const caseNumber = caseRequest.CaseDetails?.CaseRef;
	const date =
		caseRequest?.SubmissionTime != "" && caseRequest?.SubmissionTime != null
			? getFormattedDate(new Date(caseRequest?.SubmissionTime), "dd MMMM yyyy", locale)
			: "";
	const userTableData = {
		nameAr: getLocalizedFullName(profile!, "ar"),
		nameEn: getLocalizedFullName(profile!, "en"),
		emiratesId: profile?.EmiratesID || "",
		phoneNumber: `${caseRequest.Mobile} `,
		email: `${caseRequest.Email}`,
	};
	const steps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("personalInformation"),
				t("socialAidInformation"),
				t("incomeInformation"),
				t("familyMembersInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	return (
		<Box w="100%">
			<Flex>
				<Box
					minW={{ md: "300px", lg: "400px" }}
					w={{ base: "100%", md: "auto" }}
					display={{ base: "none", md: "block" }}
				>
					<ProgressTracker
						service={t("swfProgram")}
						activeStep={4}
						activeSubIndex={4}
						steps={steps}
					/>
				</Box>
				<RequestSuccessful
					userTableData={userTableData}
					titles={titles}
					caseNo={`${caseRequest?.CaseRef}`}
					submitDate={getFormattedDate(new Date(), "dd MMMM yyyy", locale)}
				/>
			</Flex>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const { requestId } = ctx.query;

	if (!requestId) {
		return {
			redirect: {
				destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}my-cases`,
				permanent: false,
			},
		};
	}

	const contactId = await getContactIdFromToken(ctx.req);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const profile = (await BackendServices.retrieveContact(emiratesId)).Data;
	let endpoint = "Request/GetCaseSummaryDetails";
	const caseRequest =
		(await BackendServices.getRequest(requestId.toString(), contactId, endpoint))?.Data || null;

	if (!caseRequest) {
		return {
			redirect: {
				destination: `/${ctx.locale || "ar"}/my-cases`,
				permanent: false,
			},
		};
	}

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "forms"])),
			caseRequest,
			profile,
		},
	};
}

EditedSocialAid.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};

export default EditedSocialAid;
