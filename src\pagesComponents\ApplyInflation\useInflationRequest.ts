import { INFLATION_TYPE } from "config";
import {
	ICaseDetails,
	IChildFamilyMember,
	IFamilyMember,
} from "interfaces/InflationForm.interface";
import { useRouter } from "next/router";
import { Dispatch, SetStateAction, useState } from "react";
import { useMutation } from "react-query";
import { getFormData, modifyRequest } from "services/frontend";
import { mapInflationFormToCaseForm, mapInflationFormToFamilyMembers } from "utils/helpers";

const initialCaseDetails: ICaseDetails = {
	Occupation: "",
	JobTitle: "",
	Alternativenumber: "",
	AlternativeEmail: "",
	Region: "",
	CaseRef: "",
	Education: "",
	MaritalStatus: "",
	AccomodationType: "",
	IsHouseholdHeadContributeToIncome: false,
	ListIncomeSourceDetails: [],
	IsHouseholdHeadReceivePensionIncome: false,
	ListPensionDetails: [],
	IshouseholdHeadTradeLicense: false,
	ListTradeLicenseDetails: [],
	IshouseholdHeadReceiveRentalIncome: false,
	ListRentalDetails: [],
	ListFamilyMember: [],
	//PortalPersona: "",
	//SubPersona: "",
	ChildEligibilityforWomeninDifficulty: "",
	Category: "",
	SubCategory: "",
	Area: "",
	Emirate: "",
	IsActiveStudent: false,
	PursuingHigherEducation: false,
	DraftedinMilitaryService: false,
	ReceivedLocalSupport: 662410001,
	NumberOfChildren: 0,
	NumberOfChildrenLessThan25: 0,
	//HaveChildrenCustody: 662410001,
	GuardianEmiratesID: "",
};

const modifyDataForRequest = (objectArray: any) => {
	let values = "";
	if (Array.isArray(objectArray)) {
		if (objectArray && objectArray.length > 0) {
			values = objectArray.map((item) => item.value).join(",");
		}
	} else {
		values = objectArray;
	}
	return values;
};

const useInflationRequest = (
	caseForm: any,
	setCaseForm: any,
	familyMembers: IFamilyMember[],
	setFamilyMembers: Dispatch<SetStateAction<IFamilyMember[]>>,
	activeIndex: any,
	subIndex: any,
	userDetails: any,
	childMembers: IChildFamilyMember[],
	setChildMembers: any,
	setKhulasitQaidNumber: any,
	documentList?: any,
	testing = false,
	isCategoryChange = false
) => {
	const { push: routerPush, query, locale } = useRouter();

	let docIdList: string[] = [];
	let arr1 = [];
	let arr2 = [];
	if (documentList && documentList?.ListAdditionalDoc.length > 0)
		arr1 = documentList?.ListAdditionalDoc.map((item) => item?.IdDocuments);

	if (documentList && documentList?.ListPersonalDocs.length > 0)
		arr2 = documentList?.ListPersonalDocs.map((item) => item?.IdDocuments);

	docIdList = [...arr1, ...arr2];

	const [submitLoading, setSubmitLoading] = useState(false);

	const requestId = query.requestId?.toString() || "";

	const { personalInformation, informationForm, inflationInformation } = caseForm;

	const { mutateAsync, isLoading } = useMutation({
		mutationFn: async (submit: boolean = false) => {
			let getDataRequest: any = {
				endpoint: null,
				caseId: requestId,
				beneficiaryId: userDetails?.ContactId || "",
			};

			if (activeIndex === 0) {
				if (subIndex === 0) getDataRequest.endpoint = "Request/GetCasePersonalDetails";
				if (subIndex === 1) getDataRequest.endpoint = "Request/GetCaseFamilyMembersListDetails";
				if (subIndex === 2) getDataRequest.endpoint = "Request/GetCaseFamilyMembersListDetails";
			}
			if (activeIndex === 1) getDataRequest.endpoint = "Request/GetCaseSummaryDetails";

			const data = await modifyRequest({
				UpdateType: !requestId ? "CREATE" : submit ? "SUBMIT" : "DRAFT",
				IdCase: requestId || undefined,
				CaseDetails: {
					...initialCaseDetails,
					Occupation: personalInformation.Occupations || "",
					Emirate: personalInformation.Emirates || personalInformation.Emirate || "",
					//JobTitle: personalInformation.jobTitle || "",
					Area: personalInformation.Area || "",
					Center: personalInformation.Center || "",
					Alternativenumber: personalInformation.alternativeNumber || "",
					AlternativeEmail: personalInformation.AlternativeEmail || "",
					Region: personalInformation.Region || "",
					CaseRef: personalInformation.caseID || "",
					MaritalStatus: personalInformation.MaritalStatus || "",
					Category: informationForm.InflationCategory || "",
					ApplyInflationAllowance: true,
					ApplyUtilityAllowance: inflationInformation.ApplyUtilityAllowance === "yes",
					UtilityProvider: inflationInformation.UtilityProvider || "",
					UtilityAccountNumber: inflationInformation.UtilityAccountNumber || "",
					ListofChildren: activeIndex === 0 && subIndex === 2 ? childMembers : [],
					ListUploadedDocuments: docIdList,

					ListFamilyMember:
						activeIndex === 0 && subIndex === 2
							? [
									...(familyMembers || []).map((i) => ({
										...i,
										ListIncomeSourceDetails: i.IsFamilyMemberContributeToIncome
											? i.ListIncomeSourceDetails
											: [],
										ListPensionDetails: i.IsFamilyMemberReceivePensionIncome
											? i.ListPensionDetails
											: [],
										ListTradeLicenseDetails: i.IsFamilyMemberReceiveTradeLicense
											? i.ListTradeLicenseDetails
											: [],
										ListRentalDetails: i.IsFamilyMemberReceiveRentalIncome
											? i.ListRentalDetails
											: [],
									})),
							  ]
							: [],
				},
				CaseType: INFLATION_TYPE,
				Index: activeIndex,
				SubIndex: subIndex === 3 ? 5 : subIndex,
			});

			const resp = data.Data;
			if (!resp?.IdCase) return data;
			const query =
				getDataRequest.endpoint === "Request/GetCaseFamilyMembersListDetails"
					? {
							caseId: getDataRequest.caseId || resp?.IdCase || null,
							endpoint: getDataRequest.endpoint,
							isCategoryChange: isCategoryChange,
					  }
					: {
							caseId: getDataRequest.caseId || resp?.IdCase || null,
							endpoint: getDataRequest.endpoint,
					  };
			const caseDataRequest = await getFormData(query);
			const caseData = caseDataRequest.Data;
			//console.log("data returned caseData", caseData);
			//i did this as per to drop 1 changes to prevent spouses from edit anything / don't create a new api in the backend
			if (caseData?.ListFamilyMember && caseData?.ListFamilyMember?.length > 0) {
				caseData.ListFamilyMember = caseData.ListFamilyMember.map((item) => {
					// if (item?.Relationship === WIFE_LOOKUP_ID) {
					// 	item.IsInformationUpdated = true;
					// }
					item.Fullname = locale === "en" ? item.FullnameEN : item.FullnameAR;
					return item;
				});
			}
			if (!caseData) return caseData;
			// @ts-ignore
			if (data.Data) data.Data.CaseDetails = caseData;
			if (submit) {
				setSubmitLoading(true);
				return data;
			}

			setCaseForm((state) => mapInflationFormToCaseForm(state, data?.Data?.CaseDetails));
			if (data?.Data?.CaseDetails?.ListFamilyMember)
				setFamilyMembers(mapInflationFormToFamilyMembers(data?.Data?.CaseDetails));

			if (data?.Data?.CaseDetails?.ListofChildren)
				setChildMembers(data?.Data?.CaseDetails?.ListofChildren);
			setKhulasitQaidNumber(data?.Data?.CaseDetails?.KhulasitQaidNumber);
			return data;
		},
		mutationKey: "modifyRequest",
		onSuccess: (resp, submit) => {
			if (resp?.IsSuccess && resp?.Data) {
				if (!requestId)
					routerPush(
						`/smart-services/inflation-service/apply-inflation?requestId=${resp?.Data?.IdCase}`,
						// `/smart-services/how-to-apply/apply-inflation?requestId=${resp?.Data?.IdCase}`,

						undefined,
						{
							shallow: true,
						}
					);
				if (submit) {
					routerPush(
						`/smart-services/inflation-service/apply-inflation/review-inflation?requestId=${resp?.Data?.IdCase}`
						// `/smart-services/how-to-apply/apply-inflation/review-inflation?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
			}
		},
	});

	return {
		updateRequest: mutateAsync,
		updateRequestLoading: isLoading || submitLoading,
	};
};

export default useInflationRequest;
