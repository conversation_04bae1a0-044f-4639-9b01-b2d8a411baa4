import { COMPLAINT_TYPE, INQUIRY_TYPE } from "config";
import * as Yup from "yup";

const getInitialValues = {
	email: "",
	mobileNo: "",
	icpMobileNo: "",
	title: "",
	complaintServices: null,
	Topic: null,
	complaintType: "",
	document: "",
	complaintSubServices: null,
	complaintDetails: "",
	caseNumber: undefined,
	isAuth: false,
};

const getValidationSchema = () => {
	return Yup.object({
		Topic: Yup.object().required().label("thisField"),
		mobileNo: Yup.string()
			.required()
			.label("thisField")
			.matches(/^05(\d){8}$/, "uaeMobileNumberError"),
		icpMobileNo: Yup.string().notRequired().nullable(),
		// email: Yup.string().when("isAuth", {
		// 	is: (isAuth) => {
		// 		return isAuth;
		// 	},
		// 	then: Yup.string().email("wrongEmailAddress").required().label("thisField"),
		// 	otherwise: Yup.string().notRequired(),
		// }),
		email: Yup.string().email("wrongEmailAddress").notRequired(),
		complaintServices: Yup.object().required().label("thisField"),
		document: Yup.string().notRequired(),
		complaintSubServices: Yup.object().when("complaintType", {
			is: (complaintType) => {
				return complaintType === COMPLAINT_TYPE || complaintType === INQUIRY_TYPE;
			},
			then: Yup.object().required().label("thisField"),
			otherwise: Yup.object().notRequired(),
		}),
		complaintDetails: Yup.string()
			.required()
			.matches(/^\S.*$/, "forms:spacesnotallowed")
			.label("thisField"),
		title: Yup.string()
			.required()
			.matches(/^\S.*$/, "forms:spacesnotallowed")
			.label("thisField"),
		caseNumber: Yup.object().notRequired().nullable().label("thisField"),
	});
};

const onChange = (event: any, formikProps: any) => {
	//console.log("formikProps amer", formikProps);
};

export { getInitialValues, onChange, getValidationSchema };
