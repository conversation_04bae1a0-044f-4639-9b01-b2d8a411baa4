import { Box, Flex, HStack, Circle, Text } from "@chakra-ui/react";
import { DocumentIcon } from "components/Icons";
import SectionTitle from "components/SectionTitle";
import { useTranslation } from "react-i18next";

const RequiredDocuments = ({ documentslist }) => {
	const { t } = useTranslation();
	return (
		<Flex ml={"auto"}>
			<Box
				px={5}
				py={5}
				bg={"brand.mainGoldLight"}
				height={"fit-content"}
				width={{ base: "100%", md: "326px" }}
				borderColor={"brand.mainGold"}
				borderWidth={1}
			>
				<SectionTitle fontColor={"brand.mainGold"}>
					<DocumentIcon w={7} h={7} mr={6} />
					{t("required-documents")}
				</SectionTitle>
				<Text fontSize={"xl"} color={"brand.mainGold"} mb={3} fontWeight={700}>
					{t("required-documents-des")}
				</Text>
				{documentslist.map((item, index) => {
					return (
						<HStack key={index} mb={5} alignItems="flex-start">
							<Circle size="24px" bg="brand.mainGold" color="brand.textColor">
								<Text fontWeight={"700"} fontSize={"md"} color={"brand.white.50"}>
									{index + 1}
								</Text>
							</Circle>
							<Text fontSize={"md"} color={"brand.mainGold"}>
								{item}
							</Text>
						</HStack>
					);
				})}
			</Box>
		</Flex>
	);
};

export default RequiredDocuments;
