import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Text,
	useMediaQuery,
} from "@chakra-ui/react";

function FooterAccordion({ children, accordionTitle }) {
	const [isDesktop] = useMediaQuery("(min-width: 48em)", {
		ssr: true,
		fallback: false, // return false on the server, and re-evaluate on the client side
	});
	return (
		<Accordion index={isDesktop ? 0 : undefined} allowToggle={isDesktop ? false : true}>
			<AccordionItem border="0px" isFocusable={false}>
				<h2>
					<AccordionButton px={{ base: 0, md: 4 }} _hover={{ bg: "unset", cursor: "unset" }}>
						<Box flex="1" textAlign="left">
							<Text lineHeight="base" fontWeight="bold" fontSize="lg" color={"brand.mainGold"}>
								{accordionTitle}
							</Text>
						</Box>
						{!isDesktop && <AccordionIcon />}
					</AccordionButton>
				</h2>
				<AccordionPanel px={{ base: 0, md: 4 }} pb={4}>
					{children}
				</AccordionPanel>
			</AccordionItem>
		</Accordion>
	);
}

export default FooterAccordion;
