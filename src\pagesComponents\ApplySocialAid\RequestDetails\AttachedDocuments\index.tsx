import { Box } from "@chakra-ui/react";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import AttachedDocumentsForm from "./AttachedDocumentsForm";

interface Props {
	innerText: string;
	documentList?: ICrmDocumentList;
	setDocumentStatus: any;
}

function RequestDetailsForm({ innerText, documentList, setDocumentStatus }: Props) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			{/* <Divider orientation="horizontal" borderColor="brand.dividerColor" mb={6} /> */}
			<AttachedDocumentsForm
				onSubmit={onSubmit}
				documentList={documentList}
				setDocumentStatus={setDocumentStatus}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
