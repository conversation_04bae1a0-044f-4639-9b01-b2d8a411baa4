import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	// validWorkContractFamily: "",
};

const getValidationSchema = () => {
	return Yup.object({
		// validWorkContractFamily: Yup.string().notRequired(),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};

export { getInitialValues, onChange, getValidationSchema };
