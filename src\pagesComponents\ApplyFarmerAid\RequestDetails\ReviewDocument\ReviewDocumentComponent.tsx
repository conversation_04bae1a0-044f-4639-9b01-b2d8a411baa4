import { Button, Checkbox, Flex, Grid, GridItem, Link, Text, Tooltip } from "@chakra-ui/react";
import { Form, Formik } from "formik";
import SocialAidStripedTable from "pagesComponents/ApplySocialAid/SocialAidStripedTable";
import * as functions from "./functions";
import { useTranslation } from "react-i18next";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useRouter } from "next/router";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useFormContext } from "context/FormContext";
import { useEffect, useState } from "react";
import { getLookupLabel } from "utils/helpers";
import { formatAmount, formatEmiratesID, formatLocalNumber } from "utils/formatters";
import { TCS_PDF_FILE_LOCATION_AR_FARMER } from "config";
import CustomSocialAidTable from "./CustomSocialAidTable";

interface Props {
	onSubmit: any;
	setCurrentStep: any;
	handleStepsIndexes: any;
	documentList: ICrmDocumentList;
	familyMembers: IFamilyMember[];
	caseForm: any;
	handleSetFormikState: any;
	formKey: string;
	hasSSS: boolean;
}

const tableRows = {
	personalInformation: [
		"EmiratesID",
		"IDNBackNumber",
		"FirstName",
		"LastName",
		"caseID",
		"PreferredEmail",
		"AlternativeEmail",
		"PreferredPhoneNumber",
		"alternativeNumber",
	],
	socialAidInformation: [
		"RegisteredWithEWE",
		"EWEBill",
		"RelatedEmiratesID",
		"ReceiveSocialAid",
		"EntityReceivedFrom",
		"ReceiveInflationAllowance",
	],
};

const formatIDs = {
	EmiratesID: formatEmiratesID,
	PreferredPhoneNumber: formatLocalNumber,
	alternativeNumber: formatLocalNumber,
	incomeAmount: formatAmount,
	pensionAmount: formatAmount,
	tradeLicenseAmount: formatAmount,
	RentAmount: formatAmount,
};

function ReviewDocumentComponent({
	onSubmit,
	setCurrentStep,
	handleStepsIndexes,
	documentList,
	familyMembers,
	caseForm,
	handleSetFormikState,
	formKey,
	hasSSS = false,
}: Props) {
	const { t } = useTranslation(["forms", "tables", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();
	const handleEdit = (stepNumber: number) => {
		setCurrentStep(stepNumber);
		handleStepsIndexes(stepNumber);
	};
	const [termsRead, setTermsRead] = useState(false);
	const updateUserData = () => {
		let finalizedIncomeInfoArr: any = {};
		let hideKeysArr: any = []; // This array is used to add keys to hide specific rows
		for (let formKey in tableRows) {
			finalizedIncomeInfoArr[formKey] = [];
			for (let key of tableRows[formKey]) {
				if (!(key in caseForm[formKey])) continue;
				if (
					key === "ReceiveInflationAllowance" ||
					key === "RegisteredWithEWE" ||
					key === "ReceiveSocialAid"
				) {
					finalizedIncomeInfoArr[formKey].push({ label: key, value: caseForm[formKey]?.[key]! });
					continue;
				}
				let newItem: any = {
					label: key,
					value:
						getLookupLabel(lookups, key, caseForm?.[formKey]?.[key]) ||
						formatIDs?.[key]?.(caseForm?.[formKey]?.[key] || "-") ||
						caseForm?.[formKey]?.[key] ||
						"-",
				};

				if (hideKeysArr.length > 0 && hideKeysArr.includes(key)) {
					newItem.hide = true;
				}
				finalizedIncomeInfoArr[formKey].push(newItem);
			}
		}
		return finalizedIncomeInfoArr;
	};

	const [userData, setUserData] = useState(() => updateUserData());

	useEffect(() => {
		setUserData(() => updateUserData());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [locale]);

	const documentsTableData = functions.createDocumentsTableData(documentList, locale);
	const familyMembersTablesData = functions.createFamilyMembersTablesData(
		familyMembers,
		lookups,
		locale,
		formatIDs
	);
	const incomeTablesData = functions.createIncomeTablesData(
		caseForm["incomeInformation"],
		lookups,
		locale,
		formatIDs
	);

	return (
		<Formik
			enableReinitialize
			initialValues={functions.getInitialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || !formik.dirty,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 10, md: 10 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("personalInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(0)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<SocialAidStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.personalInformation}
									caption={null}
								/>
							</GridItem>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("socialAidInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(1)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<CustomSocialAidTable
									sourceOfTranslation="forms"
									tableBody={userData.socialAidInformation}
									caption={null}
								/>
							</GridItem>
							{(incomeTablesData.income.length > 0 ||
								incomeTablesData.pension.length > 0 ||
								incomeTablesData.tradeLicense.length > 0 ||
								incomeTablesData.RentalIncomes.length > 0) && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<>
										{incomeTablesData.income.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("income")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.income}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
										{incomeTablesData.pension.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("pension")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.pension}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
										{incomeTablesData.tradeLicense.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("tradeLicense")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.tradeLicense}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
										{incomeTablesData.RentalIncomes.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("RentalIncomes")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.RentalIncomes}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
									</>
								</GridItem>
							)}
							{familyMembersTablesData.length > 0 &&
								familyMembersTablesData.map((tableData, idx) => (
									<GridItem colSpan={{ base: 2, md: 2 }} key={idx}>
										<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
											<Text fontSize={"1.25rem"} fontWeight={"bold"}>
												{t("familyMemberInformation")}
											</Text>
											{tableData.isWife && (
												<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(3)}>
													{t("edit", { ns: "common" })}
												</Button>
											)}
										</Flex>
										<SocialAidStripedTable
											tableBody={tableData.data}
											caption={null}
											sourceOfTranslation="forms"
										/>
									</GridItem>
								))}
							{documentsTableData.length > 0 && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("documents")}
										</Text>
										<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(4)}>
											{t("edit", { ns: "common" })}
										</Button>
									</Flex>
									<SocialAidStripedTable tableBody={documentsTableData} caption={null} />
								</GridItem>
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex>
									<Tooltip label={t("youHaveToReadTerms")} isDisabled={termsRead}>
										<Checkbox
											onChange={() => {
												formik.setFieldValue(
													"termsAndConditions",
													!formik.values.termsAndConditions
												);
											}}
											name="termsAndConditions"
											disabled={!termsRead}
											id="termsAndcond"
										></Checkbox>
									</Tooltip>
									<Text ms={2.5} as="label" htmlFor="termsAndcond">
										{t("readTermsAndConditions", { ns: "tables" })}{" "}
										<Text as="span" color="brand.mainGold" fontSize={"larger"}>
											<Link
												onClick={() => setTermsRead(true)}
												href={TCS_PDF_FILE_LOCATION_AR_FARMER}
												target={"_blank"}
												fontSize={"xl"}
											>
												{t("acknowledgementAndUndertaking", { ns: "tables" })}
											</Link>
											.
										</Text>
									</Text>
								</Flex>
								<Flex mt={3}>
									<Checkbox
										onChange={() => {
											formik.setFieldValue(
												"confirmAccurateInformation",
												!formik.values.confirmAccurateInformation
											);
										}}
										name="confirmAccurateInformation"
										id="confirmac"
									></Checkbox>
									<Text as="label" htmlFor="confirmac" ms={2.5}>
										{t("confirmAccurateInformation", { ns: "tables" })}
									</Text>
								</Flex>
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default ReviewDocumentComponent;
