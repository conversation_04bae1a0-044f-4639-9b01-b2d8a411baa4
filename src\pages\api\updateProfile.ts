import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<string>>
) {
	const { PreferedEmail, PreferedPhoneNumber, ProfileImage } = req.body;
	// if (!PreferedEmail || !PreferedPhoneNumber)
	// 	return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.updateProfile(
		contactId,
		PreferedEmail,
		PreferedPhoneNumber,
		ProfileImage
	);
	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
