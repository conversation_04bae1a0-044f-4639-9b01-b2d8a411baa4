import { Text, Grid, GridItem } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Formik, Form } from "formik";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import * as functions from "./functions";
import { useRouter } from "next/router";
import { DIVORCED, EMPLOYEDLOWINCOME, EMPLOYED_ID } from "config";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	caseData,
	handleSetFormikState,
	initialData,
	readOnly = false,
	IsEdit,
	isEligibleForTopup = true,
}) {
	const DIVORCED_SUBCATEGORY_ID = "0b3eb27d-9257-ee11-be6f-6045bd14ccdc";
	const formikRef = useRef<any>(null);
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { lookups } = useFormContext();
	const customBooleanLookup = [
		{
			Code: "yes",
			RelatedId: null,
			label: t("YesCustomText", { ns: "common" }),
			value: "yes",
		},
		{
			Code: "no",
			RelatedId: null,
			label: t("NoCustomText", { ns: "common" }),
			value: "no",
		},
	];

	let [validationSchema] = useState(functions.getValidationSchema(t, caseData));

	const getQuestionNumber = (questionId, formik) => {
		const isEmployed =
			caseData.socialAidInformation.Category === EMPLOYEDLOWINCOME ||
			caseData.personalInformation.Occupations === EMPLOYED_ID;
		const isDivorced = caseData.socialAidInformation.SubCategory === DIVORCED;

		let questionNumber = 1;

		if (questionId === "ApplyHousingAllowance") {
			return questionNumber;
		}
		questionNumber++;

		if (questionId === "ReceivingFederalLocalhousingsupport") {
			return questionNumber;
		}
		questionNumber++;

		if (isEmployed) {
			if (questionId === "ReceivingHousingAllowanceFromEmployer") {
				return questionNumber;
			}
			questionNumber++;
		}

		if (questionId === "FullOwnershipResidentialProperty") {
			return questionNumber;
		}
		questionNumber++;

		if (formik.values["FullOwnershipResidentialProperty"] === "yes") {
			if (questionId === "IsUtilityBillIssuedForFullyOwnedProperty") {
				return questionNumber;
			}
			questionNumber++;
		}

		if (questionId === "LivingSituation") {
			return questionNumber;
		}
		questionNumber++;

		if (questionId === "ReceivesHousingSupportFromHusband") {
			return questionNumber;
		}

		return questionNumber;
	};

	const updateDropdownValues = () => {
		let originalInitialValues: any = { ...functions.getInitialValues };
		if (initialData) {
			Object.keys(initialData).forEach((key) => {
				if (key in lookups) {
					let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				} else {
					originalInitialValues[key] = initialData[key]
						? JSON.parse(JSON.stringify(initialData[key]))
						: initialData[key];
				}
				if (key === "LivingSituation") {
					let indexOfItem = lookups["accomadations"].findIndex(
						(val) => val.value === initialData["LivingSituation"]
					);
					if (indexOfItem >= 0)
						originalInitialValues["LivingSituation"] = lookups["accomadations"][indexOfItem];
				}
			});
		}

		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	useEffect(() => {
		if (!isEligibleForTopup) {
			handleSetFormikState(
				{
					isLoading: false,
					isDisabled: false, // Enable the proceed button
				},
				formKey
			);
		}
	}, [isEligibleForTopup, formKey, handleSetFormikState]);

	return (
		<>
			{!isEligibleForTopup ? (
				// Only show the alert message when not eligible
				<GridItem colSpan={{ base: 2, md: 2 }}>
					<Text fontSize="md" color={"red"} borderRadius="md" p={2} textAlign="center">
						{caseData.personalInformation?.MaritalStatus == DIVORCED ||
						caseData?.socialAidInformation?.SubCategory == DIVORCED_SUBCATEGORY_ID
							? t("housingNotEligibleMessageMaritalStatus")
							: t("housingNotEligibleMessage")}
					</Text>
				</GridItem>
			) : (
				// Show the form when eligible
				<Formik
					enableReinitialize
					initialValues={initialValues}
					validationSchema={validationSchema}
					onSubmit={onSubmit}
					innerRef={formikRef}
					validateOnMount
				>
					{(formik: any) => {
						formikRef.current = formik;
						handleSetFormikState(
							{
								isLoading: formik.isSubmitting,
								isDisabled: !formik.isValid || formik.isSubmitting,
							},
							formKey
						);

						return (
							<Form
								onSubmit={(e) => {
									e.preventDefault();
									formik.handleSubmit(e);
								}}
								onChange={(e) => {
									e.preventDefault();
									functions.onChange(e, formik);
								}}
							>
								<Grid
									rowGap={{ base: 6, md: 6 }}
									columnGap={6}
									templateColumns="repeat(2, 1fr)"
									templateRows="auto"
								>
									{/* Question 1: Main reason for social aid */}
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											number={getQuestionNumber("ApplyHousingAllowance", formik)}
											type="radio"
											label={t("ApplyHousingAllowance")}
											name="ApplyHousingAllowance"
											value={formik.values["ApplyHousingAllowance"]}
											touched={formik.touched["ApplyHousingAllowance"]}
											error={formik.errors["ApplyHousingAllowance"]}
											options={lookups.Boolean}
											isReadOnly={readOnly}
											isRequired={true}
											onChange={(firstArg) => {
												handleChangeEvent(
													"radio",
													firstArg,
													"ApplyHousingAllowance",
													formik,
													formKey
												);

												// Reset other fields
												handleChangeEvent(
													"radio",
													"",
													"IsUtilityBillIssuedForFullyOwnedProperty",
													formik,
													formKey
												);
												handleChangeEvent("radio", "", "LivingSituation", formik, formKey);
												handleChangeEvent(
													"radio",
													"",
													"ReceivingFederalLocalhousingsupport",
													formik,
													formKey
												);
												handleChangeEvent(
													"radio",
													"",
													"ReceivingHousingAllowanceFromEmployer",
													formik,
													formKey
												);
												handleChangeEvent(
													"radio",
													"",
													"FullOwnershipResidentialProperty",
													formik,
													formKey
												);
												handleChangeEvent(
													"radio",
													"",
													"ReceivesHousingSupportFromHusband",
													formik,
													formKey
												);
											}}
										/>
									</GridItem>
									{formik.values["ApplyHousingAllowance"] &&
										formik.values["ApplyHousingAllowance"] === "yes" && (
											<>
												{/* Question 2: Receiving Federal/Local housing support */}
												<GridItem colSpan={{ base: 2, md: 1 }}>
													<FormField
														number={getQuestionNumber(
															"ReceivingFederalLocalhousingsupport",
															formik
														)}
														type="radio"
														label={t("ReceivingFederalLocalhousingsupport")}
														name="ReceivingFederalLocalhousingsupport"
														value={formik.values["ReceivingFederalLocalhousingsupport"]}
														touched={formik.touched["ReceivingFederalLocalhousingsupport"]}
														options={lookups.Boolean}
														isRequired={true}
														isReadOnly={readOnly}
														customText={true}
														marginTop="1.5rem"
														onChange={(firstArg) => {
															handleChangeEvent(
																"radio",
																firstArg,
																"ReceivingFederalLocalhousingsupport",
																formik,
																formKey
															);
														}}
													/>
												</GridItem>
												{/* Question 3: Receiving Housing Allowance From Employer (conditional) */}
												{(caseData.socialAidInformation.Category === EMPLOYEDLOWINCOME ||
													caseData.personalInformation.Occupations === EMPLOYED_ID) && (
													<GridItem colSpan={{ base: 2, md: 1 }}>
														<FormField
															number={getQuestionNumber(
																"ReceivingHousingAllowanceFromEmployer",
																formik
															)}
															type="radio"
															label={t("ReceivingHousingAllowanceFromEmployer")}
															name="ReceivingHousingAllowanceFromEmployer"
															value={formik.values["ReceivingHousingAllowanceFromEmployer"]}
															touched={formik.touched["ReceivingHousingAllowanceFromEmployer"]}
															options={lookups.Boolean}
															isRequired={true}
															isReadOnly={readOnly}
															marginTop="2rem"
															onChange={(firstArg) => {
																handleChangeEvent(
																	"radio",
																	firstArg,
																	"ReceivingHousingAllowanceFromEmployer",
																	formik,
																	formKey
																);
															}}
														/>
													</GridItem>
												)}
												{/* Full Ownership Residential Property */}
												<GridItem colSpan={{ base: 2, md: 1 }}>
													<FormField
														number={getQuestionNumber("FullOwnershipResidentialProperty", formik)}
														type="radio"
														label={t("FullOwnershipResidentialProperty")}
														name="FullOwnershipResidentialProperty"
														value={formik.values["FullOwnershipResidentialProperty"]}
														touched={formik.touched["FullOwnershipResidentialProperty"]}
														options={customBooleanLookup}
														isRequired={true}
														isReadOnly={readOnly}
														onChange={(firstArg) => {
															handleChangeEvent(
																"radio",
																firstArg,
																"FullOwnershipResidentialProperty",
																formik,
																formKey
															);
															handleChangeEvent("radio", "", "LivingSituation", formik, formKey);
															handleChangeEvent(
																"radio",
																"",
																"IsUtilityBillIssuedForFullyOwnedProperty",
																formik,
																formKey
															);
														}}
													/>
												</GridItem>
												{/* Utility Bill for Fully Owned Property */}
												{formik.values["FullOwnershipResidentialProperty"] &&
													formik.values["FullOwnershipResidentialProperty"] === "yes" && (
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																number={getQuestionNumber(
																	"IsUtilityBillIssuedForFullyOwnedProperty",
																	formik
																)}
																type="radio"
																label={t("IsUtilityBillIssuedForFullyOwnedProperty")}
																name="IsUtilityBillIssuedForFullyOwnedProperty"
																value={formik.values["IsUtilityBillIssuedForFullyOwnedProperty"]}
																touched={formik.touched["IsUtilityBillIssuedForFullyOwnedProperty"]}
																options={lookups.Boolean}
																isRequired={true}
																isReadOnly={readOnly}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"radio",
																		firstArg,
																		"IsUtilityBillIssuedForFullyOwnedProperty",
																		formik,
																		formKey
																	);
																}}
															/>
														</GridItem>
													)}
												{/* Living Situation */}
												{(formik.values["IsUtilityBillIssuedForFullyOwnedProperty"] === "no" ||
													formik.values["FullOwnershipResidentialProperty"] === "no") && (
													<GridItem colSpan={{ base: 2, md: 1 }}>
														<FormField
															number={getQuestionNumber("LivingSituation", formik)}
															type="selectableTags"
															label={t("LivingSituation")}
															value={formik.values["LivingSituation"]}
															options={lookups.accomadations}
															isRequired={true}
															name="LivingSituation"
															isDisabled={readOnly}
															placeholder={t("placeholder", { ns: "common" })}
															error={formik.errors["LivingSituation"]}
															touched={formik.touched["LivingSituation"]}
															onChange={(firstArg) => {
																handleChangeEvent(
																	"selectableTags",
																	firstArg,
																	"LivingSituation",
																	formik,
																	formKey
																);
															}}
														/>
													</GridItem>
												)}
												{/* Housing Support From Husband (for divorced cases) */}
												{caseData.socialAidInformation.SubCategory === DIVORCED && (
													<GridItem colSpan={{ base: 2, md: 1 }}>
														<FormField
															number={getQuestionNumber(
																"ReceivesHousingSupportFromHusband",
																formik
															)}
															type="radio"
															label={t("ReceivesHousingSupportFromHusband")}
															name="ReceivesHousingSupportFromHusband"
															value={formik.values["ReceivesHousingSupportFromHusband"]}
															touched={formik.touched["ReceivesHousingSupportFromHusband"]}
															options={lookups.Boolean}
															isReadOnly={readOnly}
															isRequired={true}
															onChange={(firstArg) => {
																handleChangeEvent(
																	"radio",
																	firstArg,
																	"ReceivesHousingSupportFromHusband",
																	formik,
																	formKey
																);
															}}
														/>
													</GridItem>
												)}
											</>
										)}
								</Grid>
							</Form>
						);
					}}
				</Formik>
			)}
		</>
	);
}

export default RequestDetailsForm;
