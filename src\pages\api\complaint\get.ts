import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ComplaintForm[]>>
) {
	const { Id, endpoint } = req.query;
	if (!Id) return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.getComplaint(Id.toString(), endpoint);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
