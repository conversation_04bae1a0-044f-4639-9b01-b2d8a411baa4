import { Grid, GridItem, Text } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import useRequiredFields from "hooks/useFormRequiredFields";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import { formatEmiratesID, formatLocalNumber } from "utils/formatters";
import * as functions from "./functions";
import { EMPLOYED_ID } from "config";

interface formikObj {
	value: string;
	label: string;
}

function PersonalInfoForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	initialData,
	handleSetFormikState,
	isMartialEmpty,
	isMaritalInit,
	readOnly = false,
	reInitialize = "",
	isEdit,
}) {
	const { t } = useTranslation(["forms", "common"]);
	const { lookups } = useFormContext();

	const hideEIDCardNum = false;

	let [validationSchema] = useState(functions.getValidationSchema());

	let requiredList = useRequiredFields(validationSchema);

	const checkMartial = initialData.hasMaritalStatus;
	const updateDropdownValues = () => {
		let originalInitialValues = { ...functions.getInitialValues };
		Object.keys(initialData).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = initialData[key]
					? JSON.parse(JSON.stringify(initialData[key]))
					: initialData[key];
			}
		});

		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());
	const formikRef: any = useRef();

	useEffect(() => {
		if (reInitialize !== "") {
			setInitialValues(updateDropdownValues());
		}
	}, [reInitialize]);

	useEffect(() => {
		formikRef.current.resetForm(initialValues);
	}, [JSON.stringify(initialValues)]);

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	const getAreasFromEmirate = (emirate) => {
		return lookups.Area.filter((area) => area.RelatedId! === emirate.value);
	};
	let isEditChildCase = false;
	if (initialData?.caseID?.includes("-")) {
		isEditChildCase = true;
		if (!initialData.alternativeNumber) {
			initialData.alternativeNumber = initialData.PreferredPhoneNumber;
		}
		if (!initialData.AlternativeEmail) {
			initialData.AlternativeEmail = initialData.PreferredEmail;
		}
	}
	return (
		<Formik
			innerRef={formikRef}
			enableReinitialize={true}
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			validateOnMount
		>
			{(formik) => {
				let occupationField: formikObj = JSON.parse(JSON.stringify(formik.values.Occupations));
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["EmiratesID"]}
									borderColor="brand.gray.250"
									isRequired={true}
									isDisabled={true}
									customFormat={formatEmiratesID}
									name="EmiratesID"
									label={t("EmiratesID")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["EmiratesID"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "EmiratesID", formik, formKey);
									}}
								/>
							</GridItem>
							{/* <GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["PassportNumber"]}
									borderColor="brand.gray.250"
									isRequired={true}
									isDisabled={true}
									name="PassportNumber"
									label={t("PassportNumber")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PassportNumber"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PassportNumber", formik, formKey);
									}}
								/>
							</GridItem> */}

							{hideEIDCardNum && (
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="text"
										value={formik.values["IDNBackNumber"]}
										borderColor="brand.gray.250"
										isRequired={true}
										isDisabled={true}
										name="IDNBackNumber"
										label={t("IDNBackNumber")}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["IDNBackNumber"]}
										onChange={(firstArg) => {
											handleChangeEvent("text", firstArg, "IDNBackNumber", formik, formKey);
										}}
									/>
								</GridItem>
							)}
							{initialData.caseID && (
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="text"
										value={initialData.caseID}
										borderColor="brand.gray.250"
										isRequired={true}
										isDisabled={true}
										name="caseID"
										label={t("caseID")}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["caseID"]}
										// onChange={(firstArg) => {
										// 	handleChangeEvent("text", firstArg, "caseID", formik, formKey);
										// }}
									/>
								</GridItem>
							)}
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["MaritalStatus"]}
									isRequired={requiredList["MaritalStatus"] || false}
									name="MaritalStatus"
									options={lookups.MaritalStatus}
									label={t("MaritalStatus")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["MaritalStatus"]}
									touched={formik.touched["MaritalStatus"]}
									isDisabled={!isMartialEmpty || checkMartial}
									onChange={(firstArg) => {
										handleChangeEvent("selectableTags", firstArg, "MaritalStatus", formik, formKey);
									}}
								/>
							</GridItem>
						</Grid>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
							mt={6}
						>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values["PreferredPhoneNumber"]}
									isRequired={true}
									name="PreferredPhoneNumber"
									customFormat={formatLocalNumber}
									isDisabled={true}
									label={t("PreferredPhoneNumber")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PreferredPhoneNumber"]}
									dir={"auto"}
									textAlign={"left"}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PreferredPhoneNumber", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["alternativeNumber"]}
									isRequired={requiredList["alternativeNumber"] || false}
									customFormat={formatLocalNumber}
									subtext={t("localNumberFormatSubtext")}
									name="alternativeNumber"
									label={t("alternativeNumber")}
									placeholder={"05XXXXXXXX"}
									isDisabled={isEdit ? (isEditChildCase ? false : true) : readOnly}
									error={formik.errors["alternativeNumber"]}
									maxLength={10}
									dir={"auto"}
									textAlign={"left"}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "alternativeNumber", formik, formKey);
									}}
								/>
								{isEdit && (
									<Text mt={1} color={"brand.gray.400"}>
										{t("disabledFieldMessage")}
									</Text>
								)}
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values["PreferredEmail"]}
									isRequired={isEditChildCase ? false : true}
									name="PreferredEmail"
									isDisabled={true}
									label={t("PreferredEmail")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PreferredEmail"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PreferredEmail", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["AlternativeEmail"]}
									isRequired={true}
									name="AlternativeEmail"
									label={t("AlternativeEmail")}
									isDisabled={isEdit ? (isEditChildCase ? false : true) : readOnly}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["AlternativeEmail"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "AlternativeEmail", formik, formKey);
									}}
								/>
								{isEdit && (
									<Text mt={2} color={"brand.gray.400"}>
										{t("disabledFieldMessage")}
									</Text>
								)}
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["Occupations"]}
									isRequired={requiredList["Occupations"] || false}
									name="Occupations"
									label={t("Occupations")}
									isDisabled={readOnly}
									placeholder={t("placeholder", { ns: "common" })}
									options={lookups.Occupations}
									error={formik.errors["Occupations"]}
									onChange={(firstArg) => {
										handleChangeEvent("selectableTags", firstArg, "Occupations", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem colSpan={{ base: 2, md: 1 }}>
								{occupationField.value === EMPLOYED_ID && (
									<FormField
										type="text"
										value={formik.values["jobTitle"]}
										isRequired={requiredList["jobTitle"] || false}
										name="jobTitle"
										label={t("jobTitle")}
										isDisabled={readOnly}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["jobTitle"]}
										onChange={(firstArg) => {
											handleChangeEvent("text", firstArg, "jobTitle", formik, formKey);
										}}
									/>
								)}
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									value={formik.values["Emirates"]}
									isRequired={requiredList["Emirates"] || false}
									name="Emirates"
									isDisabled={readOnly}
									label={t("EmiratesResd")}
									placeholder={t("placeholder", { ns: "common" })}
									options={lookups.Emirates}
									error={formik.errors["Emirates"]}
									onChange={(firstArg) => {
										handleChangeEvent("selectableTags", firstArg, "Emirates", formik, formKey);
										handleChangeEvent("selectableTags", "", "Area", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								{formik.values["Emirates"] && (
									<FormField
										type="selectableTags"
										value={formik.values["Area"]}
										isRequired={requiredList["Area"] || false}
										name="Area"
										label={t("Area")}
										isDisabled={isEditChildCase ? false : readOnly}
										options={getAreasFromEmirate(formik.values["Emirates"])}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["Area"]}
										onChange={(firstArg) => {
											handleChangeEvent("selectableTags", firstArg, "Area", formik, formKey);
										}}
									/>
								)}
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								{formik.values["Area"] && (
									<FormField
										type="selectableTags"
										value={formik.values["Center"]}
										isRequired={requiredList["Center"] || false}
										name="Center"
										label={t("Center")}
										isDisabled={readOnly}
										options={lookups.Center}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["Center"]}
										onChange={(firstArg) => {
											handleChangeEvent("selectableTags", firstArg, "Center", formik, formKey);
										}}
									/>
								)}
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default PersonalInfoForm;
