import { Box, Input } from "@chakra-ui/react";
import { ErrorInput } from "components/Icons";
import { Field } from "formik";
import { useEffect, useState } from "react";
const CustomFormatField = (field: any, meta: any, props: any) => {
	const [focused, setFocused] = useState(false);
	const [formattedValue, setFormattedValue] = useState("");
	useEffect(() => {
		setFormattedValue(props.customFormat(props.value));
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [props.value]);

	const newProps = {
		...props,
		value: focused ? props.value : formattedValue,
		onChange: focused ? props.onChange : undefined,
	};

	return (
		<Box position={"relative"}>
			<Field
				as={Input}
				onFocus={() => {
					setFocused(true);
				}}
				onBlurCapture={() => {
					setFocused(false);
				}}
				type={props.type}
				focusBorderColor="brand.mainGold"
				h="3.5rem"
				borderRadius="0.3125rem"
				errorBorderColor="brand.errorFieldBorder"
				{...field}
				{...newProps}
				_placeholder={{ color: "brand.fieldPlaceholder" }}
				_disabled={{ bg: "brand.gray.300", color: "brand.gray.400", border: "0px" }}
				autoComplete="off"
			/>
			{meta.error && (
				<ErrorInput position={"absolute"} right={"3%"} top="23%" w={"22px"} h={"22px"} />
			)}
		</Box>
	);
};

export default CustomFormatField;
