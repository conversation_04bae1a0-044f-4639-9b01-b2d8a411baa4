import { Box, Button, ListItem, Text, UnorderedList } from "@chakra-ui/react";
import { ButtonArrowIcon } from "components/Icons";
import React from "react";
import NextLink from "next/link";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
import { SocialAidContent } from "utils/strapi/socialAid";

function ServiceRequirements({
	required_documents,
	isEidExp,
	isEmirates,
	userEligibleAge = true,
	eligibleInflation = true,
}: {
	required_documents: SocialAidContent["required_documents"];
	isEidExp?: boolean;
	isEmirates?: boolean;
	userEligibleAge?: boolean;
	eligibleInflation?: boolean;
}) {
	const { t } = useTranslation();
	const { locale } = useRouter();

	const isEnabled = !isEidExp && isEmirates && userEligibleAge && eligibleInflation;

	const renderError = () => {
		if (isEidExp) {
			return (
				<Text textAlign={"center"} color={"red"}>
					{t("expEidDate")}
				</Text>
			);
		} else if (!isEmirates) {
			return (
				<Text textAlign={"center"} color={"red"}>
					{t("notEmarati")}
				</Text>
			);
		} else if (!userEligibleAge || !eligibleInflation) {
			return (
				<Text textAlign={"center"} color={"red"}>
					{t("notEligibleForInflation")}
				</Text>
			);
		}
	};

	return (
		<Box
			w="full"
			p={"2rem"}
			bg="brand.white.100"
			borderRadius={"10px"}
			mb="1rem"
			position={{ lg: "sticky" }}
			top={5}
		>
			<Text mb="4" fontSize={"1.5rem"} fontWeight={"bold"}>
				{required_documents.header}
			</Text>
			<UnorderedList fontSize={"lg"} spacing={"1.25rem"}>
				{required_documents.documents_list.map((doc) => {
					const isSubTerm = !!doc?.is_sub_term;
					const paragraph = doc.text.split("*insertNewLine");
					return (
						paragraph.length > 0 &&
						paragraph.map((text, idx) => {
							return (
								<ListItem fontSize={"1.125rem"} key={idx} as={isSubTerm ? "p" : "li"}>
									{text}
								</ListItem>
							);
						})
					);
				})}
			</UnorderedList>
			{isEnabled ? (
				<NextLink href={required_documents.apply_button_link} passHref>
					<Button
						w={{ lg: "full", base: "70%" }}
						mt="2rem"
						variant={"primary"}
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								color="white"
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
							/>
						}
						fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
					>
						{required_documents.apply_button_text}
					</Button>
				</NextLink>
			) : (
				<Button
					w={{ lg: "full", base: "70%" }}
					mt="2rem"
					variant={"primary"}
					isDisabled={true}
					rightIcon={
						<ButtonArrowIcon
							w={"24px"}
							h={"24px"}
							color="white"
							transform={locale === "ar" ? "scale(-1, 1)" : ""}
						/>
					}
					fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
				>
					{required_documents.apply_button_text}
				</Button>
			)}
			{renderError()}
		</Box>
	);
}

export default ServiceRequirements;
