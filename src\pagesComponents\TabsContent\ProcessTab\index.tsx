import { Box, ComponentWithAs, Flex, IconProps, Text } from "@chakra-ui/react";

import React from "react";
import { useTranslation } from "react-i18next";

interface Props {
	topMargin?: number;
	steps: {
		title: string;
		body: string;
		Icon: ComponentWithAs<"svg", IconProps>;
	}[];
}
const ProcessTab = ({ steps, topMargin = 8 }: Props) => {
	const { t } = useTranslation("common");

	return (
		<Box mt="4">
			<Box>
				<Text fontSize="2xl" fontWeight="bold">
					{t("processSteps")}
				</Text>
				<Flex mt="1.5rem" gap="0.8rem" wrap={"wrap"} w="full">
					{steps.map((step, idx) => (
						<Box
							key={idx}
							w="12rem"
							p="1.25rem"
							pr={"0.5rem"}
							border="1px solid #BBBCBD"
							borderRadius={"10px"}
						>
							<step.Icon mb="0.5rem" color="brand.mainGold" h="2.25rem" w="2.25rem" />
							<Text fontSize={"1rem"} fontWeight={"bold"} as="h2">
								{step.title}
							</Text>
							<Text as="h3" fontSize={"0.875rem"}>
								{step.body}
							</Text>
						</Box>
					))}
				</Flex>
			</Box>
		</Box>
	);
};

export default ProcessTab;
