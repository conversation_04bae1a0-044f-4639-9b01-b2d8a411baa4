import { Flex, Text } from "@chakra-ui/react";
import { ApplyingTimeIcon, HandShakeIcon, RequestTimeIcon } from "components/Icons";
import React from "react";

function ServiceInformation({ applyingTime, serviceTime, serviceFees }) {
	return (
		<Flex wrap={"wrap"} mt="2rem" mb="2rem" maxW={"500px"} gap="1rem" color="#606164">
			<Flex gap="2">
				<ApplyingTimeIcon h="1.5rem" w="1.5rem" />
				<Text>{applyingTime}</Text>
			</Flex>
			|
			<Flex gap="2">
				<RequestTimeIcon h="1.5rem" w="1.5rem" />
				<Text>{serviceFees}</Text>
			</Flex>
			<Flex gap="2">
				<HandShakeIcon h="1.5rem" w="1.5rem" />
				<Text>{serviceTime}</Text>
			</Flex>
		</Flex>
	);
}

export default ServiceInformation;
