import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON>ist, Text } from "@chakra-ui/react";
import NextLink from "next/link";
import React, { useState } from "react";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { ChevronDownIcon } from "@chakra-ui/icons";
import { useSession } from "next-auth/react";
import { Menu as Menut } from "utils/strapi/navbar";

const NavbarDesktop = ({ headerMenu }: { headerMenu: Menut }) => {
	const { t } = useTranslation();

	const router = useRouter();
	const { pathname, locale } = router;
	let isSmartServicesHighlighted = false;
	const subpath = pathname.split("/")[1];
	const [isOpenObject, setIsOpenObject] = useState({});

	const openMenu = (id) => {
		setIsOpenObject((prev) => {
			return {
				...prev,
				[id]: true,
			};
		});
	};
	const closeMenu = (id) => {
		setIsOpenObject((prev) => {
			prev[id] = false;
			return {
				[id]: false,
			};
		});
	};
	const { push } = useRouter();
	const { status } = useSession();
	const finalItems =
		status === "authenticated"
			? headerMenu.items
			: headerMenu.items.filter((item) => !item.required_auth);

	const highlightSmartServicesPaths = [
		"/smart-services/how-to-apply",
		"/smart-services/farmer-service",
		"/smart-services/to-whom-apply",
		"/smart-services/inflation-service",
		// "/smart-services/topups-service",
	];
	return (
		<Flex
			borderTop={"1px"}
			borderBottom={"1px"}
			borderColor={"brand.mainGold"}
			bg="rgba(255, 255, 255, 0.93)"
			w="100%"
			py={0}
			flexDirection={"row"}
			color="brand.textColor"
		>
			{finalItems.map((route, idx) => {
				const isCurrentPath = `/${subpath}` === route.url;
				const isDropdown = route?.children?.length > 0;
				const isSmartServices = route.english_title === "Smart Services";
				const localizedTitle = locale === "ar" ? route.title : route.english_title;
				const isHome = route.english_title === "Home";

				const shouldHighlightSmartServices = highlightSmartServicesPaths.includes(pathname);
				if (shouldHighlightSmartServices) {
					isSmartServicesHighlighted = true;
				}

				return (
					<Box
						key={idx}
						_hover={{
							bg: isCurrentPath ? "none" : "brand.selectedNavbar",
							cursor: "pointer",
						}}
						bg={
							(isCurrentPath && !isSmartServicesHighlighted) ||
							(isSmartServices && shouldHighlightSmartServices && !isHome)
								? "brand.selectedNavbar"
								: "none"
						}
					>
						{isDropdown ? (
							<Menu
								closeOnSelect
								matchWidth
								offset={[0, 0]}
								isOpen={isOpenObject[route.id] || false}
							>
								<MenuButton
									onMouseEnter={() => openMenu(route.id)}
									onMouseLeave={() => closeMenu(route.id)}
								>
									<Flex alignItems={"center"} px={"1.5rem"} py={4} gap={2}>
										<Text
											noOfLines={3}
											fontSize={{ base: "sm", lg: "lg" }}
											fontWeight={
												(isCurrentPath && !isSmartServicesHighlighted) ||
												(isSmartServices && shouldHighlightSmartServices && !isHome)
													? "bold"
													: "medium"
											}
										>
											{localizedTitle}
										</Text>
										<ChevronDownIcon
											transition="transform 350ms ease"
											transform={isOpenObject?.[route.id] ? "rotate(180deg)" : ""}
										/>
									</Flex>
								</MenuButton>
								<MenuList
									onMouseEnter={() => openMenu(route.id)}
									onMouseLeave={() => closeMenu(route.id)}
									mt={"1px"}
									roundedTop={0}
									py={0}
									zIndex={20}
								>
									{route.children.map((child, idx) => {
										const isCurrent = pathname.startsWith(child.url || "");
										const localizedTitle = locale === "ar" ? child.title : child.english_title;

										return (
											<MenuItem
												closeOnSelect
												_hover={{
													bg: isCurrent ? "brand.selectedNavbar" : "none",
												}}
												w="full"
												as={NextLink}
												href={child.url!}
												key={idx}
												py={2}
												bg={isCurrent ? "brand.selectedNavbar" : "none"}
											>
												{localizedTitle}
											</MenuItem>
										);
									})}
								</MenuList>
							</Menu>
						) : (
							<Text
								noOfLines={3}
								fontSize={{ base: "sm", lg: "lg" }}
								fontWeight={
									(isCurrentPath && !isSmartServicesHighlighted) ||
									(isSmartServices && shouldHighlightSmartServices && !isHome)
										? "bold"
										: "medium"
								}
								px={"1.5rem"}
								py={4}
								as={NextLink}
								href={route.url!}
								onClick={() => {
									if (route.url === "/calculator") {
										window.location.reload();
									} else if (route.url === "/allowance-calculator") {
										push(route.url!).then(() => {
											router.reload();
										});
									} else {
										push(route.url!);
									}
								}}
							>
								{localizedTitle}
							</Text>
						)}
					</Box>
				);
			})}
		</Flex>
	);
};

export default NavbarDesktop;
