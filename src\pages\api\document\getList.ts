import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmDocumentList>>
) {
	const { idRequest } = req.query;
	if (!idRequest)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const data = await BackendServices.getDocumentList(idRequest.toString());

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
