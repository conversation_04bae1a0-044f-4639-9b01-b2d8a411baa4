import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	validWorkContractFamily: "",
};

const getValidationSchema = () => {
	// const SUPPORTED_FORMATS_FILE = [
	// 	"application/vnd.ms-powerpoint",
	// 	"application/vnd.openxmlformats-officedocument.presentationml.presentation",
	// ];
	return Yup.object({
		validWorkContractFamily: Yup.string().notRequired(),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("document", event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
