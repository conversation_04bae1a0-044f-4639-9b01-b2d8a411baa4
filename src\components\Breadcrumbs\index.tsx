import { ChevronRightIcon } from "@chakra-ui/icons";
import { Box, Breadcrumb, BreadcrumbItem, Flex, HStack, Text } from "@chakra-ui/react";
import Link from "next/link";
import { useRouter } from "next/router";

const Breadcrumbs = ({ data, isLight = false }) => {
	const { locale } = useRouter();
	return (
		<Flex color="brand.orange.100" mb={2.5}>
			<HStack>
				<Breadcrumb
					separator={
						<ChevronRightIcon
							color={isLight ? "white" : "brand.mainGold"}
							transform={"scale(1.2,1.2)" + (locale === "ar" ? " scale(-1,1)" : "")}
							my="2"
						/>
					}
				>
					{data.map((item, index) => (
						<BreadcrumbItem isCurrentPage={item.isCurrentPage} key={index} fontWeight="bold">
							<Box
								color={isLight ? "white" : "brand.darkGold"}
								fontSize={"md"}
								textDecoration={"unset"}
								fontWeight="normal"
							>
								<Link href={item.link} passHref>
									<Text as="h2">{item.label}</Text>
								</Link>
							</Box>
						</BreadcrumbItem>
					))}
				</Breadcrumb>
			</HStack>
		</Flex>
	);
};

export default Breadcrumbs;
