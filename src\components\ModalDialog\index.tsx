import {
	<PERSON>dal,
	ModalOverlay,
	Modal<PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON>dal<PERSON>ooter,
	ModalBody,
	ModalCloseButton,
	Button,
	HStack,
	useDisclosure,
	Text,
	VStack,
	Image,
} from "@chakra-ui/react";
import React, { useEffect } from "react";

const ModalDialog = ({
	cancelText,
	confirmText,
	isModalShow,
	handleOnClose,
	handleOnClick,
	imgSrc,
	headerTitle,
	confirmationMessage,
	undoneAction,
}) => {
	const { isOpen, onOpen, onClose } = useDisclosure();

	useEffect(() => {
		isModalShow ? onOpen() : onClose();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isModalShow]);
	return (
		<>
			<Modal
				isCentered
				// onClose={handleOnClose || onClose}
				// isOpen={onOpen}
				// motionPreset="slideInBottom"
				closeOnOverlayClick={false}
				onClose={handleOnClose || onClose}
				isO<PERSON>={isO<PERSON>}
			>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>
						<VStack>
							<Image src={imgSrc} alt="image not found" />
							<Text>{headerTitle}</Text>
							{/* <Text bg="#F2F4F8" p="0.5rem">
                              Request Number.
                              <Text as="span" color="brand.textColor" fontWeight="bold">
                                  {requestNo}
                              </Text>
                          </Text> */}
						</VStack>
					</ModalHeader>
					<ModalCloseButton />
					<ModalBody>
						<Text textAlign="center">{confirmationMessage}</Text>
						{undoneAction && <Text textAlign="center"> {undoneAction}</Text>}
					</ModalBody>
					<ModalFooter>
						<HStack w="full">
							<Button w="50%" variant="secondary" onClick={handleOnClose || onClose}>
								<Text as="span">{cancelText}</Text>
							</Button>
							<Button
								mr={3}
								onClick={handleOnClick || onOpen}
								w="50%"
								variant="primary"
								bg="red"
								color="white"
								borderRadius="0px"
							>
								<Text as="span">{confirmText}</Text>
							</Button>
						</HStack>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</>
	);
};

export default ModalDialog;
