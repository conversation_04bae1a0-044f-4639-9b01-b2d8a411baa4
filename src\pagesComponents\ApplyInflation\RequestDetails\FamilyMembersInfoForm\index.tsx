import { Box } from "@chakra-ui/react";
import FamilyMembersInfoForm from "./FamilyMembersInfoForm";

function RequestDetailsForm({
	innerText,
	formKey,
	familyMembers,
	setFamilyMembers,
	childMembers,
	setChildMembers,
	khulasitQaidNumber,
	readOnly = false,
	IsInflationBaseEdit = false,
	IsChildCase = false,
	IsInflationNominatedCaseEdit = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions, formKey });
	};
	return (
		<Box>
			<FamilyMembersInfoForm
				onSubmit={onSubmit}
				members={familyMembers}
				setMembers={setFamilyMembers}
				readOnly={readOnly}
				childMembers={childMembers}
				khulasitQaidNumber={khulasitQaidNumber}
				setChildMembers={setChildMembers}
				IsInflationBaseEdit={IsInflationBaseEdit}
				IsChildCase={IsChildCase}
				IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
