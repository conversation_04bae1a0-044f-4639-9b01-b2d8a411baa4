import { ICaseDetails, IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useEffect, useState } from "react";
import { mapSocialAidFormToFamilyMembers } from "utils/helpers";

const useFamilyMembers = (
	initialData: ICaseDetails | undefined,
	isFamilyMembersStep = false,
	hasSubmitted = false
) => {
	const [members, setMembers] = useState<IFamilyMember[]>(
		mapSocialAidFormToFamilyMembers(initialData)
	);
	const [proceedDisabled, setProceedDisabled] = useState(true);

	if (hasSubmitted && members && members.length > 0) {
		members.map((item) => {
			item.IsInformationUpdated = true;
		});
	}

	useEffect(() => {
		setProceedDisabled(
			isFamilyMembersStep && members.some((member) => !member.IsInformationUpdated)
		);
	}, [members, isFamilyMembersStep]);
	return {
		familyMembers: members,
		setFamilyMembers: setMembers,
		familyMembersStepDisabled: proceedDisabled,
	};
};

export default useFamilyMembers;
