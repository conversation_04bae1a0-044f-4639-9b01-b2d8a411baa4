import { Card, Flex, Text } from "@chakra-ui/react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import React from "react";
import { useTranslation } from "react-i18next";

function RequestBlocked() {
	const { t } = useTranslation(["common"]);
	return (
		<Flex
			width={"100%"}
			position={"relative"}
			bg="url('/assets/images/loginImg.jpg')"
			height={{ base: "91vh", md: "calc(89vh)" }}
			backgroundSize={"cover"}
			justifyContent="center"
			alignItems={"center"}
		>
			<Card
				bg={"brand.lightGold"}
				width={{ base: "100%", sm: "80%", md: "40%" }}
				borderRadius={0}
				shadow={"unset"}
				py={10}
				px={12}
				color={"brand.textColor"}
				textAlign="center"
			>
				<Text pb={5} fontSize={"5xl"}>
					{t("request-bloced-title")}
				</Text>
				<Text color={"brand.textColor"}>{t("request-bloced-desc")}</Text>
			</Card>
		</Flex>
	);
}

export async function getServerSideProps(ctx) {
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common"])),
		},
	};
}

// RequestBlocked.getLayout = function getLayout(page: ReactElement) {
// 	return (
// 		<MainLayout minimalLayout notBlockPage={false} maintenanceMode>
// 			{page}
// 		</MainLayout>
// 	);
// };

export default RequestBlocked;
