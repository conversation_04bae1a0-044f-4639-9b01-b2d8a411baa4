import { Box } from "@chakra-ui/react";
import ReviewDocumentComponent from "./ReviewDocumentComponent";

function ReviewDocument({
	setCurrentStep,
	innerText,
	handleStepsIndexes,
	documentList,
	familyMembers,
	caseForm,
	formKey,
	handleSetFormikState,
	hasSSS,
	childMembers,
	educationMembers,
	isEligibleForTopup = true, // Add isEligibleForTopup prop with default value
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<ReviewDocumentComponent
				onSubmit={onSubmit}
				setCurrentStep={setCurrentStep}
				handleStepsIndexes={handleStepsIndexes}
				documentList={documentList}
				familyMembers={familyMembers}
				caseForm={caseForm}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				hasSSS={hasSSS}
				childMembers={childMembers}
				educationMembers={educationMembers}
				isEligibleForTopup={isEligibleForTopup} // Pass the prop
			/>
		</Box>
	);
}

export default ReviewDocument;
