import {
	V<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	PinInput,
	PinInputField,
	Button,
	Center,
	Link,
	Text,
	Flex,
} from "@chakra-ui/react";
import { OTP_RESEND_TIMEOUT } from "config";
import { useTranslation } from "next-i18next";
import { useState, useEffect } from "react";
import { onEnterKey } from "utils/helpers";
import { useCallback } from "react";

const inputFieldWidth = {
	minWidth: {
		base: "40px",
		md: "65px",
	},
	height: {
		base: "50px",
		md: "65px",
	},
};
const ValidateOtp = ({
	otpNumber,
	onEnterOtp,
	onValidateOtp,
	otpError,
	onRequestOtp,
	onCancel,
	signInLoading,
}) => {
	const { t } = useTranslation("login");
	const [showResendOtp, setShowResendOtp] = useState(false);
	const [runTimer, setRunTimer] = useState(true);
	const [counter, setCounter] = useState(() => OTP_RESEND_TIMEOUT / 60);

	const otpInput = useCallback((inputElement) => {
		if (inputElement) {
			inputElement.focus();
		}
	}, []);

	useEffect(() => {
		const timeout =
			!showResendOtp &&
			setTimeout(() => {
				setShowResendOtp(true);
			}, OTP_RESEND_TIMEOUT);

		return () => {
			timeout && clearTimeout(timeout);
		};
	}, [showResendOtp]);

	// useEffect(() => {
	// 	const interval: any =
	// 		runTimer &&
	// 		counter > 0 &&
	// 		setInterval(() => {
	// 			setCounter((prevState) => prevState - 1000 / 60);
	// 		}, 1000);
	// 	return () => clearInterval(interval);
	// }, [runTimer]);

	return (
		<>
			<VStack spacing={4}>
				{/* <LockIcon width={"40px"} height={"40px"} /> */}
				<Text fontSize={"4xl"} color={"brand.mainHeaderColor"} fontWeight={"bold"} w={"100%"}>
					{t("validateOtpTitle")}
				</Text>
				<Text mt={4} fontSize={"md"} color={"brand.textColor"} w={"100%"}>
					{t("validateOtpDescription", { otpNumber })}
				</Text>
			</VStack>
			<HStack justifyContent={"space-around"} mt={6} dir={"ltr"}>
				<PinInput
					otp
					onChange={(e) => onEnterOtp(e)}
					isInvalid={otpError}
					focusBorderColor={"brand.mainGold"}
					placeholder="0"
				>
					<PinInputField {...inputFieldWidth} />
					<PinInputField {...inputFieldWidth} />
					<PinInputField {...inputFieldWidth} />
					<PinInputField {...inputFieldWidth} />
					<PinInputField {...inputFieldWidth} />
					<PinInputField {...inputFieldWidth} onKeyPress={(e) => onEnterKey(e, onValidateOtp)} />
				</PinInput>
			</HStack>
			{otpError && (
				<Text fontSize={"sm"} color={"brand.errorColor"} mt={2}>
					{t("pleaseEnterValidOtp")}
				</Text>
			)}
			{showResendOtp && (
				<HStack mt={2}>
					<Text fontSize={"sm"} color={"brand.textColor"}>
						{t("didntReceiveOtp")}
					</Text>
					<Link
						onClick={() => {
							setShowResendOtp(false);
							onRequestOtp();
						}}
						_hover={{ textDecoration: "none" }}
					>
						<Text fontSize={"sm"} color={"brand.mainGold"} fontWeight={"bold"}>
							{t("sendOtpAgain")}
						</Text>
					</Link>
				</HStack>
			)}
			<Flex mt={14} gap={3} flexDirection={{ base: "column-reverse", md: "row" }}>
				<Button height={"48px"} variant="secondary" w={"100%"} onClick={onCancel}>
					<Center w="100%">
						<Text fontSize={"lg"} color={"brand.mainGold"}>
							{t("otpCancel")}
						</Text>
					</Center>
				</Button>
				<Button
					height={"48px"}
					variant="primary"
					w={"100%"}
					onClick={onValidateOtp}
					isLoading={signInLoading}
				>
					<Center w="100%">
						<Text fontSize={"lg"} color={"brand.white.50"}>
							{t("otpSubmit")}
						</Text>
					</Center>
				</Button>
			</Flex>
		</>
	);
};

export default ValidateOtp;
