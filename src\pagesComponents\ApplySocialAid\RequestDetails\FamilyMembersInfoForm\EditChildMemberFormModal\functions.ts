import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	Occupations: "",
	IsDraftedinMilitaryService: "",
	IsPursuingHigherEducation: "",
};

const getValidationSchema = () => {
	return Yup.object({
		Occupations: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
		IsDraftedinMilitaryService: Yup.string().required().label("thisField"),
		IsPursuingHigherEducation: Yup.string().required().label("thisField"),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};

const getListDefault = (arr) => (!arr || arr?.length === 0 ? [{}] : arr);

export { getInitialValues, onChange, getValidationSchema, getListDefault };
