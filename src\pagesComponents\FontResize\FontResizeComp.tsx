import React from "react";
import {
	extendTheme,
	Flex,
	Box,
	<PERSON>,
	Slider,
	SliderMark,
	SliderFilledTrack,
	SliderTrack,
} from "@chakra-ui/react";
import { useAppContext } from "context/AppContext";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { themeObject } from "theme";

function FontResizeComp() {
	const { setUsedTheme } = useAppContext();
	const [currentSize, setCurrentSize] = useState(16);
	const [showResizer, setshowResizer] = useState(false);
	const { t } = useTranslation();
	const maxSize = 20;
	const minSize = 12;
	const stepSize = 1;
	function decreaseSize(newValue = null) {
		let themeObjectCopy = JSON.parse(JSON.stringify(themeObject));
		let newSize = newValue ? newValue : currentSize;
		if (newSize > minSize) {
			newSize -= stepSize;
		}
		themeObjectCopy.styles.global.body.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		themeObjectCopy.styles.global.html.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		setCurrentSize(newSize);
		setUsedTheme(extendTheme(themeObjectCopy));
	}
	function increaseSize(newValue = null) {
		let themeObjectCopy = JSON.parse(JSON.stringify(themeObject));
		let newSize = newValue ? newValue : currentSize;
		if (newSize < maxSize) {
			newSize += stepSize;
		}
		themeObjectCopy.styles.global.body.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		themeObjectCopy.styles.global.html.fontSize = {
			base: `${newSize}px`,
			xl: `${newSize}px`,
		};
		setCurrentSize(newSize);
		setUsedTheme(extendTheme(themeObjectCopy));
	}
	return (
		<Box textAlign={"left"}>
			<Text fontSize={["3xl", "md"]} color={"brand.textColor"}>
				{t("textResizer")}
			</Text>
			<Text fontSize={["xl", "md"]} color={"brand.textSecondaryColor"} py={4}>
				{t("textResizerDesc")}
			</Text>
			<Flex justifyContent={"space-between"} alignItems={"center"}>
				<Text
					cursor={"pointer"}
					fontWeight="bold"
					fontSize="20px"
					color="black"
					onClick={() => decreaseSize()}
				>
					A-
				</Text>
				<Text
					cursor={"pointer"}
					fontWeight="bold"
					fontSize="20px"
					color="black"
					onClick={() => increaseSize()}
				>
					A+
				</Text>
			</Flex>
			<Slider
				width="100%"
				id="slider"
				defaultValue={16}
				value={currentSize}
				step={1}
				min={12}
				max={20}
				colorScheme="gray"
				onChange={(v: any) => (v > currentSize ? increaseSize(v) : decreaseSize(v))}
			>
				<SliderMark value={12} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 12 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={13} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 13 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={14} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 14 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={15} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 15 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={16} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 16 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={17} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 17 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={18} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 18 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={19} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 19 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderMark value={20} mt="1" ml="-2.5" fontSize="sm">
					<Box
						h={3.5}
						w={1}
						bg={currentSize >= 20 ? "gray" : "#CDD0D4"}
						position={"relative"}
						top={"-11px"}
						zIndex={100}
						borderRadius={"10px"}
					></Box>
				</SliderMark>
				<SliderTrack>
					<SliderFilledTrack />
				</SliderTrack>
			</Slider>
		</Box>
	);
}

export default FontResizeComp;
