import { Grid, GridItem } from "@chakra-ui/react";
import Form<PERSON>ield from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import useRequiredFields from "hooks/useFormRequiredFields";
import { useTranslation } from "next-i18next";
import { useEffect, useState } from "react";
import { formatEmiratesID, formatLocalNumber } from "utils/formatters";
import * as functions from "./functions";

interface formikObj {
	value: string;
	label: string;
}

function PersonalInfoForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	initialData,
	handleSetFormikState,
	readOnly = false,
}) {
	const { t } = useTranslation(["forms", "common"]);

	const { lookups } = useFormContext();
	let [validationSchema] = useState(functions.getValidationSchema());
	let requiredList = useRequiredFields(validationSchema);

	const updateDropdownValues = () => {
		let originalInitialValues = { ...functions.getInitialValues };
		Object.keys(initialData).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = initialData[key]
					? JSON.parse(JSON.stringify(initialData[key]))
					: initialData[key];
			}
		});

		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			validateOnMount
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["EmiratesID"]}
									borderColor="brand.gray.250"
									isRequired={true}
									isDisabled={true}
									customFormat={formatEmiratesID}
									name="EmiratesID"
									label={t("EmiratesID")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["EmiratesID"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "EmiratesID", formik, formKey);
									}}
								/>
							</GridItem>
							{/* <GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["PassportNumber"]}
									borderColor="brand.gray.250"
									isRequired={true}
									isDisabled={true}
									name="PassportNumber"
									label={t("PassportNumber")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PassportNumber"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PassportNumber", formik, formKey);
									}}
								/>
							</GridItem> */}

							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["IDNBackNumber"]}
									borderColor="brand.gray.250"
									isRequired={true}
									isDisabled={true}
									name="IDNBackNumber"
									label={t("IDNBackNumber")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["IDNBackNumber"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "IDNBackNumber", formik, formKey);
									}}
								/>
							</GridItem>
							{initialData.caseID && (
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="text"
										value={initialData.caseID}
										borderColor="brand.gray.250"
										isRequired={true}
										isDisabled={true}
										name="caseID"
										label={t("caseID")}
										placeholder={t("placeholder", { ns: "common" })}
										error={formik.errors["caseID"]}
										// onChange={(firstArg) => {
										// 	handleChangeEvent("text", firstArg, "caseID", formik, formKey);
										// }}
									/>
								</GridItem>
							)}
						</Grid>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
							mt={6}
						>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values.FirstName}
									isRequired={true}
									name="FirstName"
									isDisabled={true}
									label={t("firstName")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["FirstName"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "FirstName", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values.LastName}
									isRequired={true}
									name="LastName"
									isDisabled={true}
									label={t("lastName")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["LastName"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "LastName", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values["PreferredPhoneNumber"]}
									isRequired={true}
									name="PreferredPhoneNumber"
									customFormat={formatLocalNumber}
									isDisabled={true}
									label={t("PreferredPhoneNumber")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PreferredPhoneNumber"]}
									dir={"auto"}
									textAlign={"left"}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PreferredPhoneNumber", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["alternativeNumber"]}
									isRequired={true}
									customFormat={formatLocalNumber}
									subtext={t("localNumberFormatSubtext")}
									name="alternativeNumber"
									label={t("alternativeNumber")}
									placeholder={"05XXXXXXXX"}
									error={formik.errors["alternativeNumber"]}
									maxLength={10}
									dir={"auto"}
									textAlign={"left"}
									isDisabled={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "alternativeNumber", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									borderColor="brand.gray.250"
									type="text"
									value={formik.values["PreferredEmail"]}
									isRequired={true}
									name="PreferredEmail"
									isDisabled={true}
									label={t("PreferredEmail")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["PreferredEmail"]}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "PreferredEmail", formik, formKey);
									}}
								/>
							</GridItem>
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="text"
									value={formik.values["AlternativeEmail"]}
									isRequired={true}
									name="AlternativeEmail"
									label={t("AlternativeEmail")}
									placeholder={t("placeholder", { ns: "common" })}
									error={formik.errors["AlternativeEmail"]}
									isDisabled={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent("text", firstArg, "AlternativeEmail", formik, formKey);
									}}
								/>
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default PersonalInfoForm;
