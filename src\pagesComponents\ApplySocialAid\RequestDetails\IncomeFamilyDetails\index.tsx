import { Text } from "@chakra-ui/react";
//import { tempData } from "./datamock";
import { useRouter } from "next/router";
import TempTable from "./TempTable";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { useFormContext } from "context/FormContext";
import { getLookupItem } from "utils/helpers";

interface Props {
	incomeData: any;
	readOnly: boolean;
}

let salaryListHeaders = [
	{
		id: "FullName",
	},
	{
		id: "Income",
		width: "25%",
	},
	{
		id: "IncomeSourceText",
		width: "15%",
	},
	{
		id: "StatusCode",
		width: "25%",
	},
];

let pensionListHeaders = [
	{
		id: "FullName",
	},
	{
		id: "PensiontAmount",
		width: "25%",
	},
	{
		id: "PensiontAuthorityText",
		width: "15%",
	},
	{
		id: "StatusCode",
		width: "25%",
	},
];

let rentalListHeaders = [
	{
		id: "FullName",
	},
	{
		id: "RentAmount",
		width: "25%",
	},
	{
		id: "RentalSourceText",
		width: "15%",
	},
	{
		id: "StatusCode",
		width: "25%",
	},
];

let tradeListHeaders = [
	{
		id: "FullName",
	},
	{
		id: "Income",
		width: "25%",
	},
	{
		id: "TradeSourceText",
		width: "15%",
	},
	{
		id: "StatusCode",
		width: "25%",
	},
];

function IncomeFamilyDetails({ incomeData, readOnly = false }: Props) {
	const { lookups } = useFormContext();

	const { locale } = useRouter();
	const { t } = useTranslation(["forms", "common"]);

	useEffect(() => {
		incomeData.ListSalaryIncome.map((item) => {
			let obj = getLookupItem(lookups, "IncomeSources", item.IncomeSource);
			if (obj) {
				item.IncomeSourceText = obj.label;
			} else {
				item.IncomeSourceText = "-";
			}
		});
		incomeData.ListPensionIncome.map((item) => {
			let obj = getLookupItem(lookups, "PensionAuthority", item.PensiontAuthority);
			if (obj) {
				item.PensiontAuthorityText = obj.label;
			} else {
				item.PensiontAuthorityText = "-";
			}
		});
		incomeData.ListRentalIncome.map((item) => {
			let obj = getLookupItem(lookups, "rentalSource", item.RentalSource);
			if (obj) {
				item.RentalSourceText = obj.label;
			} else {
				item.RentalSourceText = "-";
			}
		});
		incomeData.ListTradeIncome.map((item) => {
			let obj = getLookupItem(lookups, "TradeSources", item.TradeSource);
			if (obj) {
				item.TradeSourceText = obj.label;
			} else {
				item.TradeSourceText = "-";
			}
		});
	}, [locale]);
	return (
		<>
			<Text fontSize={"1.25rem"} fontWeight={"bold"} my={6}>
				{t("income")}
			</Text>
			<TempTable headers={salaryListHeaders} tableBody={incomeData.ListSalaryIncome} />
			<Text fontSize={"1.25rem"} fontWeight={"bold"} mb={6}>
				{t("pension")}
			</Text>
			<TempTable headers={pensionListHeaders} tableBody={incomeData.ListPensionIncome} />
			<Text fontSize={"1.25rem"} fontWeight={"bold"} mb={6}>
				{t("tradeLicense")}
			</Text>
			<TempTable headers={tradeListHeaders} tableBody={incomeData.ListTradeIncome} />
			<Text fontSize={"1.25rem"} fontWeight={"bold"} mb={6}>
				{t("RentalIncomes")}
			</Text>
			<TempTable headers={rentalListHeaders} tableBody={incomeData.ListRentalIncome} />
		</>
	);
}

export default IncomeFamilyDetails;
