import { Box, Flex, Text, Image, VisuallyHiddenInput, VStack, Hide } from "@chakra-ui/react";
import { ProfilePictureIcon } from "components/Icons";
import useAppToast from "hooks/useAppToast";
import { useUserDetails } from "hooks/useUserDetails";
import { useRouter } from "next/router";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { MdCameraAlt } from "react-icons/md";
import { formatEmiratesId, getBase64DataUri, toBase64DataUri } from "utils/helpers";

const PROFILE_IMAGE_MIME_TYPES = ["image/jpeg", "image/jpg", "image/png"];
const PROFILE_IMAGE_MAX_SIZE = 1048576;

const Card = ({ profileImage, setProfileImage, setProfileImageChanged, profileData }) => {
	const { locale } = useRouter();
	const { t } = useTranslation("profile");
	const user = useUserDetails();
	const profilePicUploadRef = useRef<HTMLInputElement>(null);
	const toast = useAppToast();
	let firstName, lastName, middleName;
	if (locale === "ar") {
		firstName = profileData.FirstNameArabic;
		lastName = profileData.LastNameArabic;
		middleName = profileData.MiddleNameArabic;
	} else {
		firstName = profileData.FirstName;
		lastName = profileData.LastName;
		middleName = profileData.MiddleName;
	}
	return (
		<>
			<Box>
				<Flex flexDirection="column" w={"full"}>
					<Flex alignItems={"center"} p={4} gap={"1.5rem"} px={{ base: "1rem", md: "2.5rem" }}>
						<Box
							width={"6.25rem"}
							h={"6.25rem"}
							border="1px solid black"
							rounded={"full"}
							borderColor={"brand.mainGold"}
							position={"relative"}
						>
							{!profileImage && (
								<ProfilePictureIcon pt={"6"} width={"full"} height={"full"} rounded={"full"} />
							)}
							{profileImage && (
								<Image
									rounded={"full"}
									src={getBase64DataUri(profileImage)}
									alt="new profile image"
									objectFit={"cover"}
									h={"100%"}
								/>
							)}
							<Flex
								justifyContent={"center"}
								alignItems={"center"}
								position={"absolute"}
								bottom={"0"}
								right={"0"}
								rounded={"full"}
								bg="white"
								w="1.75rem"
								h="1.75rem"
								border="2px solid white"
								outline={"1px solid gray"}
								_hover={{ cursor: "pointer" }}
								onClick={() => profilePicUploadRef.current?.click()}
							>
								<MdCameraAlt color="gray" height={"55"} width={"1.25rem"} />
							</Flex>
						</Box>

						{/* <Flex flexDir={"column"} bg="red" justifyContent={"space-between"}></Flex> */}
						<VStack
							justifyContent={"space-between"}
							alignItems={"start"}
							alignContent={"space-between"}
						>
							<Text fontSize={"1.875rem"}>
								{firstName} {lastName}
							</Text>
							<Text
								color={"brand.textSecondaryColor"}
								fontSize={"0.875rem"}
								fontWeight={"normal"}
								mb={6}
							>
								{t("emiratesId")}:{" "}
								<Hide above="sm">
									<br />
								</Hide>
								<Text as={"span"} dir={"auto"}>
									{formatEmiratesId(user?.EmiratesID)}
								</Text>
							</Text>
						</VStack>
						{/* <Text
							color={"brand.textColor"}
							fontSize={"lg"}
							fontWeight={"bold"}
							mb={1}
							textAlign={"center"}
						>
							{locale === "ar" ? user?.fullNameAr : user?.fullNameEn}
						</Text>
						<Text color={"brand.textSecondaryColor"} fontSize={"sm"} fontWeight={"normal"} mb={6}>
							{t("emiratesId")}:{" "}
							<Text as={"span"} dir={"auto"}>
								{formatEmiratesId(user?.EmiratesID)}
							</Text>
						</Text> */}
						{/* <Flex gap="10">
							<Link
								color={"brand.mainGold"}
								fontSize={"md"}
								fontWeight={"medium"}
								onClick={() => profilePicUploadRef.current?.click()}
							>
								<Flex gap={1.5} alignItems={"center"}>
									<UpdateProfilePictureIcon />
									<Text>{t("updateProfilePicture")}</Text>
								</Flex>
							</Link>
							{profileImage && (
								<Link
									color={"brand.mainGold"}
									fontSize={"md"}
									fontWeight={"medium"}
									onClick={() => {
										setProfileImageChanged(true);
										setProfileImage("");
									}}
									mt={2}
								>
									<Flex gap={1.5} alignItems={"center"}>
										<Text>{t("removeProfilePicture")}</Text>
									</Flex>
								</Link>
							)}
						</Flex> */}
					</Flex>
				</Flex>
			</Box>
			<VisuallyHiddenInput
				ref={profilePicUploadRef}
				type={"file"}
				accept={".jpg, .png"}
				onChange={async (event) => {
					if (event.target.files && event.target.files.length > 0) {
						const file = event.target.files[0];

						if (
							(!file.name.toLowerCase().endsWith(".jpg") &&
								!file.name.toLowerCase().endsWith(".png")) ||
							!PROFILE_IMAGE_MIME_TYPES.includes(file.type)
						) {
							toast({
								title: t("pleaseUploadOnlyJpgOrPng"),
								status: "error",
							});
							return;
						}
						if (file.size > PROFILE_IMAGE_MAX_SIZE) {
							toast({
								title: t("pleaseUploadImageLessThan1MB"),
								status: "error",
							});
							return;
						}
						const b64Image = await toBase64DataUri(file);
						setProfileImageChanged(true);
						setProfileImage(b64Image);
					}
				}}
			/>
		</>
	);
};

export default Card;
