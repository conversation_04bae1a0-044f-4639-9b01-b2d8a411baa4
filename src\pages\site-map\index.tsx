import { <PERSON>, <PERSON>lex, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Text } from "@chakra-ui/react";
import Breadcrumbs from "components/Breadcrumbs";
import { Polygon } from "components/Icons";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import React, { ReactElement } from "react";
import { useTranslation } from "react-i18next";

function SiteMap() {
	const { t } = useTranslation(["common"]);
	const { locale } = useRouter();

	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:siteMap"),
			id: "site",
			link: "#",
			isCurrentPage: true,
		},
	];

	return (
		<Box pt={"2rem"} px={{ base: "4", lg: "6.25rem" }} w="full">
			<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>

			<Heading fontSize={"2.5rem"} mb={10}>
				{t("sitemap")}
			</Heading>
			<Flex
				w={"100%"}
				alignItems={"start"}
				direction={{ base: "column", md: "row" }}
				justifyContent="space-between"
			>
				<Box w={{ base: "100%", md: "20%" }} pb={36}>
					<Box bg={"brand.white.100"} px={6} py={2} w={"100%"} mb={8}>
						<Text fontWeight={500} color={"brand.textColor"} fontSize={"2xl"}>
							{t("pages")}
						</Text>
					</Box>
					<Flex flexDirection={"column"} flex={1} gap={2.5} py={2} px={6}>
						<Link href={`/`} target={"_blank"} _hover={{ cursor: "pointer" }}>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("navbar-home")}
								</Text>
							</HStack>
						</Link>
						<Link href={`/`} target={"_blank"} _hover={{ cursor: "pointer" }}>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("allowanceCalculator")}
								</Text>
							</HStack>
						</Link>
					</Flex>
				</Box>
				<Box w={{ base: "100%", md: "20%" }} pb={36}>
					<Box bg={"brand.white.100"} px={6} py={2} w={"100%"} mb={8}>
						<Text fontWeight={500} color={"brand.textColor"} fontSize={"2xl"}>
							{t("navbar-howToApply")}
						</Text>
					</Box>
					<Flex flexDirection={"column"} flex={1} gap={2.5} py={2} px={6}>
						<Link
							href={`/smart-services/how-to-apply`}
							target={"_blank"}
							_hover={{ cursor: "pointer" }}
						>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("landingPageTitle")}
								</Text>
							</HStack>
						</Link>
						<Link
							href={`/smart-services/farmer-service`}
							target={"_blank"}
							_hover={{ cursor: "pointer" }}
						>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("applying-for-farmer-service")}
								</Text>
							</HStack>
						</Link>
						<Link
							href={`/smart-services/to-whom-apply`}
							target={"_blank"}
							_hover={{ cursor: "pointer" }}
						>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("applying-for-to-whom-but")}
								</Text>
							</HStack>
						</Link>
					</Flex>
				</Box>
				<Box w={{ base: "100%", md: "20%" }} pb={36}>
					<Box bg={"brand.white.100"} px={6} py={2} w={"100%"} mb={8}>
						<Text fontWeight={500} color={"brand.textColor"} fontSize={"2xl"}>
							{t("aboutWebsite")}
						</Text>
					</Box>
					<Flex flexDirection={"column"} flex={1} gap={2.5} py={2} px={6}>
						<Link href={`/about`} target={"_blank"} _hover={{ cursor: "pointer" }}>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("aboutWebsite")}
								</Text>
							</HStack>
						</Link>
						<Link href={`/about`} target={"_blank"} _hover={{ cursor: "pointer" }}>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("Beneficiaries")}
								</Text>
							</HStack>
						</Link>
					</Flex>
				</Box>
				<Box w={{ base: "100%", md: "20%" }} pb={36}>
					<Box bg={"brand.white.100"} px={6} py={2} w={"100%"} mb={8}>
						<Text fontWeight={500} color={"brand.textColor"} fontSize={"2xl"}>
							{t("more")}
						</Text>
					</Box>
					<Flex flexDirection={"column"} flex={1} gap={2.5} py={2} px={6}>
						<Link href={`/faq`} target={"_blank"} _hover={{ cursor: "pointer" }}>
							<HStack>
								<Polygon
									w={"13px"}
									h={"16px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text color="brand.darkGold" fontSize="lg" _hover={{ color: "brand.mainGold" }}>
									{t("Faq")}
								</Text>
							</HStack>
						</Link>
					</Flex>
				</Box>
			</Flex>
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common"])),
		},
	};
}

SiteMap.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};

export default SiteMap;
