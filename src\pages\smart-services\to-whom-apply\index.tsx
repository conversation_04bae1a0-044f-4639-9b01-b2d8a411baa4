import { <PERSON>, Flex, Text, VStack, But<PERSON> } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ReactElement } from "react";
import { useTranslation } from "next-i18next";
import { ButtonArrowIcon, GroupIcon, HeadphoneIcon } from "components/Icons";
import { useRouter } from "next/router";
import StarRating from "components/StarRating";
import Breadcrumbs from "components/Breadcrumbs";
import ServiceDetails from "components/ServiceDetails";
import NextLink from "next/link";
import { getStrapiContent } from "services/strapi";
import { ToWhomeContent } from "utils/strapi/toWhome";
import { InferGetServerSidePropsType } from "next";
import { getImageUrls } from "utils/strapi/helpers";
import { Note } from "components/DLS/ServiceDescription";

function SocialAid({ content }: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation();
	const { locale } = useRouter();

	// const serviceDescList = [
	// 	{
	// 		title: t("service-description"),
	// 		body: t("whom-service-desc"),
	// 	},
	// 	{
	// 		title: t("the-target-audience"),
	// 		body: t("the-target-audience-desc"),
	// 	},
	// ];

	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "howToApply",
			link: "/smart-services",
			isCurrentPage: false,
		},
		{
			label: t("common:applying-for-to-whom-but"),
			id: "howToApply",
			link: "#",
			isCurrentPage: true,
		},
	];
	const icons = {
		HeadphoneIcon,
		GroupIcon,
	};
	const ServiceInfoIcon = icons[content.service_info.icon];
	const TargetAudIcon = icons[content.target_audience.icon];
	return (
		<Box w="100%">
			<Box pos={"relative"}>
				<Flex
					w={{ base: "100%", md: "100%" }}
					minH={"23rem"}
					bgImage={getImageUrls(content.landing_image.url)}
					bgPosition={{ base: "100%", md: "100%" }}
					bgSize="cover"
					bgRepeat={"no-repeat"}
					bgPos={{ base: "top", md: "left" }}
					className="overlay"
					alignItems={"center"}
				>
					<VStack
						width={{ base: "100%", sm: "90%", md: "40%" }}
						marginInlineStart={{ base: 0, md: "6.5rem" }}
						alignItems={"start"}
						zIndex={10}
						px={{ base: "1rem" }}
						w="full"
					>
						<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
							<Breadcrumbs data={breadcrumbsData} isLight />
						</Box>
						<Text color={"brand.white.50"} fontSize={{ base: "2rem", lg: "2.5rem" }}>
							{content.page_header}
						</Text>
					</VStack>
				</Flex>
			</Box>
			<Box px={{ base: "1rem", md: "3rem", lg: "6rem" }} pt={"3rem"}>
				<Flex w="full" gap="1.5rem" flexDir={{ base: "column", md: "row" }}>
					<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "70%" }}>
						<ServiceDetails
							title={content.service_info.header}
							body={content.service_info.body}
							Icon={ServiceInfoIcon}
						/>
						<ServiceDetails
							Icon={TargetAudIcon}
							title={content.target_audience.header}
							body={content.target_audience.body}
						/>
					</VStack>
					<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "30%" }}>
						<Note serviceDetails={content.service_details} />
					</VStack>
				</Flex>
			</Box>
			<Box px={{ base: "1rem", md: "3rem", lg: "6rem" }} pt={0} pb={10}>
				<Button
					w={{ lg: "25%", base: "100%" }}
					mt="2rem"
					variant={"primary"}
					href={content.apply_button_link}
					rightIcon={
						<ButtonArrowIcon
							w={"24px"}
							h={"24px"}
							color="white"
							transform={locale === "ar" ? "scale(-1, 1)" : ""}
						/>
					}
					fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
					as={NextLink}
				>
					{content.apply_button_text}
				</Button>
				<StarRating mt="10" />
			</Box>
		</Box>
	);
}

// <Box fontSize="lg">
// 	<StarRating />
// </Box>
export async function getServerSideProps(ctx) {
	const content = await getStrapiContent("/to-whome?populate=deep", ctx.locale);
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "buttons", "forms", "tables"])),
			// Will be passed to the page component as props
			content: content.data as ToWhomeContent,
		},
	};
}
SocialAid.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default SocialAid;
