//import { HomeIcon } from "components/Icons";

const userRoutes: any[] = [
	// {
	// 	path: "/",
	// 	id: "navbar-home",
	// 	icon: <HomeIcon w="23px" h="22px" fill="currentcolor" />,
	// },
	{
		id: "navbar-home",
		path: "/smart-services",
	},
	{
		id: "navbar-about",
		path: "/about",
	},
	{
		id: "navbar-howToApply",
		path: "",
		children: [
			{ id: "برنامج الدعم الاجتماعي ", path: "/smart-services/how-to-apply" },
			{ id: "دعم أصحاب المزارع", path: "/smart-services/farmer-service" },
			{ id: "شهادة لمن يهمه الأمر بشأن الدعم الاجتماعي", path: "/smart-services/to-whom-apply" },
			{ id: "التضخم", path: "/smart-services/inflation-service" },
		],
	},
	{
		id: "navbar-myCases",
		path: "/my-cases",
	},
	// {
	// 	id: "navbar-myAllowance",
	// 	path: "/my-allowance",
	// },
	{
		id: "navbar-complaints",
		path: "/complaints",
	},
	{
		id: "navbar-faq",
		path: "/faq",
	},
	{
		id: "حساب الدعم",
		path: "/",
	},

	// {
	// 	id: "test-dropdown",
	// 	path: "/about",
	// 	children: [
	// 		{ id: "navbar-myCases", path: "/my-cases" },
	// 		{ id: "child2", path: "/" },
	// 		{ id: "child3", path: "/" },
	// 	],
	// },
];

const routes: any[] = [
	// {
	// 	path: "/",
	// 	id: "navbar-home",
	// 	icon: <HomeIcon w="23px" h="22px" fill="currentcolor" />,
	{
		id: "navbar-home",
		path: "/smart-services",
	},
	{
		id: "navbar-about",
		path: "/about",
	},
	{
		id: "navbar-howToApply",
		path: "/---",
		children: [
			{ id: "برنامج الدعم الاجتماعي ", path: "/smart-services/how-to-apply" },
			{ id: "دعم أصحاب المزارع", path: "/smart-services/farmer-service" },
			{ id: "شهادة لمن يهمه الأمر بشأن الدعم الاجتماعي", path: "/smart-services/to-whom-apply" },
		],
	},
	{
		id: "navbar-faq",
		path: "/faq",
	},
	{
		id: "حساب الدعم",
		path: "/",
	},
];

export { routes, userRoutes };
