import NextDocument, { Html, <PERSON>, Main, NextScript } from "next/document";

class Document extends NextDocument {
	render() {
		const { locale } = this.props.__NEXT_DATA__;
		const dir = locale === "ar" ? "rtl" : "ltr";
		return (
			<Html dir={dir} lang={locale} translate="no">
				<Head>
					<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
					<link rel="apple-touch-icon-precomposed" href="/apple-touch-icon-precomposed.png" />
					<link rel="apple-touch-icon" sizes="57x57" href="/apple-touch-icon-57x57.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="57x57"
						href="/apple-touch-icon-57x57-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="72x72" href="/apple-touch-icon-72x72.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="72x72"
						href="/apple-touch-icon-72x72-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="76x76"
						href="/apple-touch-icon-76x76-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="114x114" href="/apple-touch-icon-114x114.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="114x114"
						href="/apple-touch-icon-114x114-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="120x120"
						href="/apple-touch-icon-120x120-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="144x144" href="/apple-touch-icon-144x144.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="144x144"
						href="/apple-touch-icon-144x144-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="152x152"
						href="/apple-touch-icon-152x152-precomposed.png"
					/>
					<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon-180x180.png" />
					<link
						rel="apple-touch-icon-precomposed"
						sizes="180x180"
						href="/apple-touch-icon-180x180-precomposed.png"
					/>
				</Head>
				<body>
					<Main />
					<NextScript />
				</body>
			</Html>
		);
	}
}

export default Document;
