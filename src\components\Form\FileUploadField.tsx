import { <PERSON><PERSON>, <PERSON>, Spinner, Text, VisuallyHiddenInput, Tooltip } from "@chakra-ui/react";
import { CloseIcon, UploadIcon } from "components/Icons";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "next-i18next";
import { useMutation } from "react-query";
import { uploadDocument } from "services/frontend";
import { toBase64DataUri } from "utils/helpers";
import useAppToast from "hooks/useAppToast";
import { ICrmAttachment } from "interfaces/CrmDocument.interface";
import useDownloadAttachment from "hooks/useDownloadAttachment";
import {
	PDF_MIME_TYPE,
	PDF_EXTENSION,
	IMAGE_MIME_TYPE,
	IMAGE_EXTENSION,
	IMAGE_VISIBLE_EXTENSION,
	MAX_FILE_SIZE_MB,
	MAX_FILE_SIZE,
} from "config";

// const PDF_MIME_TYPE = ["application/pdf"];
// const PDF_EXTENSION = [".pdf"];
// const IMAGE_MIME_TYPE = ["image/jpeg", "image/jpg", "image/png", "image/heif", "image/heic"];
// const IMAGE_EXTENSION = [".jpg", ".jpeg", ".png", ".heif", ".heic"];
// const IMAGE_VISIBLE_EXTENSION = [".jpg", ".png"];
// const MAX_FILE_SIZE_MB = 5;
// const MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024;

interface FileInputProps {
	idDocument: string;
	isRequired: boolean;
	multiple: boolean;
	allowPdf: boolean;
	allowImage: boolean;
	attachment?: ICrmAttachment;
	onUploadStatusChange: (status: boolean) => void;
	isDisabled: boolean;
}

const FileUpload = (field: any, meta: any, props: FileInputProps) => {
	const { t } = useTranslation(["common", "forms"]);
	const [fileName, setFileName] = useState<string>("");
	// const [dragActive, setDragActive] = useState(false);
	const [attachmentId, setAttachmentId] = useState<string>("");
	const [isDeleting, setIsDeleting] = useState<boolean>(false);
	const hiddenFileInput = useRef<HTMLInputElement>(null);
	const toast = useAppToast();
	useEffect(() => {
		if (props.attachment) {
			setFileName(props.attachment.FileName);
			setAttachmentId(props.attachment.Id || "");
		}
	}, [props.attachment?.Id]);

	const handleClick = () => {
		hiddenFileInput.current?.click();
	};

	const { mutateAsync, isLoading } = useMutation({
		mutationFn: uploadDocument,
		mutationKey: "uploadDocument",
	});

	const downloadAttachment = useDownloadAttachment();

	const allowedMimeTypes = [
		...(props.allowPdf ? PDF_MIME_TYPE : []),
		...(props.allowImage ? IMAGE_MIME_TYPE : []),
	];
	const allowedExtensions = [
		...(props.allowPdf ? PDF_EXTENSION : []),
		...(props.allowImage ? IMAGE_EXTENSION : []),
	];
	const allowedVisibleExtensions = [
		...(props.allowPdf ? PDF_EXTENSION : []),
		...(props.allowImage ? IMAGE_VISIBLE_EXTENSION : []),
	];

	const handleOnChange = async (event, type?) => {
		const file =
			type === "dragDrop" ? (event.dataTransfer.files[0] as File) : (event.target.files[0] as File);
		if (
			!allowedMimeTypes.includes(file.type) ||
			!allowedExtensions.includes(`.${file.name.toLowerCase().split(".").pop() || ""}`)
		) {
			toast({
				title: t("forms:fileUploadErrorFileTypes", {
					extensions: allowedVisibleExtensions.join(", "),
				}),
				status: "error",
			});
			return;
		}
		if (file.size > MAX_FILE_SIZE) {
			toast({
				title: t("forms:fileUploadErrorFileSize", { size: `${MAX_FILE_SIZE_MB}MB` }),
				status: "error",
			});
			return;
		}

		const resp = await mutateAsync({
			idDocument: props.idDocument,
			listAttachments: [
				{
					AttachmentBody: await toBase64DataUri(file),
					FileName: file.name,
					MimeType: file.type,
				},
			],
		});

		if (resp?.IsSuccess && resp?.Data?.[0]) {
			props.onUploadStatusChange(true);
			setFileName(file.name);
			setAttachmentId(resp.Data[0]);
		} else {
			toast({
				title: t("genericErrorTitle"),
				description: t("genericErrorDescription"),
				status: "error",
			});
		}
	};

	const handleOnDeleteFile = async () => {
		setIsDeleting(true);
		const resp = await mutateAsync({
			idDocument: props.idDocument,
			listAttachments: [],
		});

		if (resp?.IsSuccess) {
			props.onUploadStatusChange(false);
			setIsDeleting(false);
			setFileName("");
			setAttachmentId("");
			if (hiddenFileInput.current) hiddenFileInput.current.value = "";
		} else {
			toast({
				title: t("genericErrorTitle"),
				description: t("genericErrorDescription"),
				status: "error",
			});
		}
	};

	const handleDrag = function (e) {
		e.preventDefault();
		e.stopPropagation();
		// if (e.type === "dragenter" || e.type === "dragover") {
		// 	setDragActive(true);
		// } else if (e.type === "dragleave") {
		// 	setDragActive(false);
		// }
	};

	// triggers when file is dropped
	const handleDrop = function (e) {
		e.preventDefault();
		e.stopPropagation();
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			handleOnChange(e, "dragDrop");
		}
	};

	return (
		<>
			<VisuallyHiddenInput
				type="file"
				name={props.idDocument}
				ref={hiddenFileInput}
				onChange={handleOnChange}
				multiple={props.multiple}
				required={props.isRequired}
				accept={allowedMimeTypes.join(", ")}
			/>
			<Flex
				bg={"brand.inputColor.formInputBg"}
				w={"100%"}
				alignItems={"center"}
				p={2}
				border={"2px"}
				onDragEnter={handleDrag}
				onDragLeave={handleDrag}
				onDragOver={handleDrag}
				onDrop={handleDrop}
				borderColor={"#DDE1E6"}
			>
				<Flex
					alignItems={"center"}
					cursor={props.isDisabled ? "not-allowed" : "pointer"}
					borderRadius="0.42rem"
					bg={props.isDisabled ? "brand.buttonColors.primary.disabled" : "brand.mainGold"}
					px={5}
					py={2}
					onClick={isLoading || props.isDisabled ? undefined : handleClick}
					minW={"fit-content"}
				>
					{isLoading && (
						<Flex gap={4}>
							<Spinner color={"brand.white.50"} />
							<Text fontSize={"sm"} color={"brand.white.50"} fontWeight={700}>
								{isDeleting ? t("forms:deletingFile") : t("forms:uploadingFile")}
							</Text>
						</Flex>
					)}
					{!isLoading && (
						<>
							<UploadIcon />
							<Text ms={4} fontSize={"sm"} color={"brand.white.50"} fontWeight={700}>
								{t("forms:selectFiles")}
							</Text>
						</>
					)}
				</Flex>
				{!isLoading && fileName && (
					<>
						<Tooltip label={fileName} fontSize="sm">
							<Link
								ms={4}
								fontSize={"sm"}
								color={"brand.textColor"}
								onClick={() => downloadAttachment(attachmentId)}
							>
								<Text noOfLines={[1]}>
									{fileName.length > 20 ? `${fileName.slice(0, 40)}...` : fileName}
								</Text>
							</Link>
						</Tooltip>
						{!props.isDisabled && <CloseIcon ms={"auto"} me={2} onClick={handleOnDeleteFile} />}
					</>
				)}

				{!isLoading && !fileName && (
					<Text ms={4} fontSize={"sm"} color={"brand.textColor"}>
						{t("forms:dropFile")}
					</Text>
				)}
			</Flex>
			<Flex w={"100%"} alignItems={"center"} justifyContent={"space-between"} my={2}>
				<Text fontSize={"xs"} color={"brand.textColor"} dir="ltr">
					{t("forms:filesType", { extensions: allowedVisibleExtensions.join(", ") })}
				</Text>
				<Text fontSize={"xs"} color={"brand.textColor"}>
					{t("forms:maxSize", { size: `${MAX_FILE_SIZE_MB}MB` })}
				</Text>
			</Flex>
		</>
	);
};

export default FileUpload;
