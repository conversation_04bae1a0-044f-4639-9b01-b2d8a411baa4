export interface ComplaintForm {
	Id: string;
	Title: string;
	Description: string;
	CaseType: string;
	Status: number;
	StateCode: number;
	PhoneNumber: string;
	EmailAddress: string;
	TicketNumber: string;
	CreatedOn: Date;
	ModifiedOn?: Date;
	AppealStatus: number;
	IsReopen: boolean;
	Topic: {
		Id: string;
		name: string;
	};
	Service: {
		Id: string;
		name: string;
	};
	SubService: {
		Id: string;
		name: string;
	};
	CaseResolution?: string;
}

interface Status {
	Key: string;
	Value: string;
}
