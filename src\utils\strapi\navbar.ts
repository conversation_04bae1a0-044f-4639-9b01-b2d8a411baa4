export interface MenuItem {
	id: number;
	order: number;
	title: string;
	url?: string;
	target: string | null;
	createdAt: string;
	updatedAt: string;
	english_title: string;
	required_auth: boolean | null;
	children: MenuItem[];
}
export interface Menu {
	id: number;
	title: string;
	slug: string;
	createdAt: string;
	updatedAt: string;
	items: MenuItem[];
}

interface Image {
	id: number;
	name: string;
	alternativeText: string | null;
	caption: string | null;
	width: number;
	height: number;
	hash: string;
	ext: string;
	mime: string;
	size: number;
	url: string;
	previewUrl: string | null;
	provider: string;
	provider_metadata: any | null;
	createdAt: string;
	updatedAt: string;
}
interface HeaderLogo {
	id: number;
	logo_url: string;
	logo_img: Image;
}

interface Footer {
	id: number;
	copy_right: string;
	footer_logo: Image;
}

export interface HeaderAndFooterDetails {
	id: number;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	header_logo: HeaderLogo;
	footer: Footer;
}

export interface GlobalCmsData {
	headerAndFooterDetails: HeaderAndFooterDetails;
	footerMenu: Menu;
	headerMenu: Menu;
}
