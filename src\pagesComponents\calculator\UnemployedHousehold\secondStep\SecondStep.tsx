import { Box, Grid, GridItem, ListItem, Text, UnorderedList } from "@chakra-ui/react";
import <PERSON>Field from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState } from "react";
import { UnemployedCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import functions from "./functions";
import { differenceInMonths, format } from "date-fns";
import { formatAmount } from "utils/formatters";

export default function SecondStep({
	formData: { employment, personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: UnemployedCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();
	const toast = useAppToast();

	const lookups = localizedLookups(locale);

	const updateDropdownValues = () => {
		let originalInitialValues = { ...functions.empty };
		Object.keys(employment).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === employment[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = employment[key]
					? JSON.parse(JSON.stringify(employment[key]))
					: employment[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);
	const maleAndMarried =
		personalInformation.gender === "1" && personalInformation.maritalStatus === "2";
	const age25_44 = personalInformation.ageGroupShort === "1";
	const validationSchema = functions.getValidationsSchena(maleAndMarried, age25_44);
	const { t } = useTranslation("calculator");
	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				const { registeredWithNafis, spouseEmployedOrRetired, nafisDate } = formik.values;
				const spouseEmpOrRet = spouseEmployedOrRetired === "1";
				const nafisIsFalse = registeredWithNafis === "0";
				const nafisPeriod = isDateDifferenceLessThanThreeMonths(nafisDate);
				const enEligbleConditions = [spouseEmpOrRet, nafisIsFalse, nafisPeriod];
				const enEligble = enEligbleConditions.some((t) => t);
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || enEligble,
					},
					formKey
				);

				return (
					<Grid
						rowGap={{ base: 6, md: 4 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						{maleAndMarried && (
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("spouseEmployedOrRetired")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="spouseEmployedOrRetired"
										error={formik.errors.spouseEmployedOrRetired}
										value={formik.values.spouseEmployedOrRetired}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "spouseEmployedOrRetired", formik, formKey);
											if (firstArg === "1") {
												toast({
													status: "error",
													title: t("enEligble.wifeHasIcome"),
												});
											}
										}}
									/>
								</GridItem>
							</>
						)}
						{age25_44 && (
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("registeredWithNafis")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="registeredWithNafis"
										error={formik.errors.registeredWithNafis}
										value={formik.values.registeredWithNafis}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "registeredWithNafis", formik, formKey);
											if (firstArg === "0") {
												toast({
													status: "error",
													title: t("enEligble.nafis"),
												});
											}
										}}
									/>
								</GridItem>
								{registeredWithNafis === "1" && (
									<GridItem colSpan={2} maxW="500px">
										<FormField
											type="datetime"
											label={t("nafisDate")}
											options={getLookUp("boolean", locale)}
											placeholder={t("chooseAnOption")}
											name="nafisDate"
											error={formik.errors.nafisDate}
											value={formik.values.nafisDate}
											maxDate={new Date()}
											onChange={(firstArg) => {
												handleChange(
													"text",
													{ target: { value: format(firstArg, "yyyy-M-d") } },
													"nafisDate",
													formik,
													formKey
												);
												if (isDateDifferenceLessThanThreeMonths(firstArg)) {
													toast({
														status: "error",
														title: t("enEligble.nafis"),
													});
												}
											}}
										/>
									</GridItem>
								)}
							</>
						)}
						{!enEligble && (
							<>
								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
										{t("incomeInformation")}
									</Text>

									<GrayBox>
										<UnorderedList>
											<Text fontWeight={"bold"}>{t("incomeRules1")}</Text>

											<UnorderedList>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules2")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules3")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules4")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules5")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules6")}</Text>
												</ListItem>
											</UnorderedList>
										</UnorderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="text"
										label={t("totalIncome")}
										name="totalIncome"
										error={formik.errors.totalIncome}
										placeholder={t("writeAnswerHere")}
										customFormat={formatAmount}
										value={formik.values.totalIncome}
										onChange={(firstArg) => {
											handleChange("text", firstArg, "totalIncome", formik, formKey);
										}}
									/>
								</GridItem>
							</>
						)}
					</Grid>
				);
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};

function isDateDifferenceLessThanThreeMonths(givenDate: string | Date) {
	const today = new Date();

	// Calculate the difference in months
	const monthsDifference = differenceInMonths(
		today,
		typeof givenDate === "string" ? new Date(givenDate) : givenDate
	);

	// Check if the difference is greater than 3 months
	return monthsDifference < 3;
}
