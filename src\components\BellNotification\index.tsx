import { Show } from "@chakra-ui/react";
import { NOTIFICATION_MARK_READ_INTERVAL, NOTIFICATION_REFETCH_INTERVAL } from "config";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "react-query";
import { getNotifications, updateNotificationsRead } from "services/frontend";
import NotificationDesktop from "./NotificationDesktop";
import NotificationMobile from "./NotificationMobile";

const BellNotification = () => {
	const [notificationsOpen, setNotificationsOpen] = useState(false);

	const { data } = useQuery("notifications", getNotifications, {
		refetchInterval: NOTIFICATION_REFETCH_INTERVAL,
	});
	const notifications = data?.Data || [];

	const { mutateAsync: markNotificationsRead } = useMutation({
		mutationFn: () => updateNotificationsRead(notifications.map((i) => i.Id)),
	});

	useEffect(() => {
		const timeout =
			notificationsOpen &&
			notifications.length > 0 &&
			setTimeout(() => {
				markNotificationsRead();
			}, NOTIFICATION_MARK_READ_INTERVAL);

		return () => {
			timeout && clearTimeout(timeout);
		};
	}, [markNotificationsRead, notifications.length, notificationsOpen]);

	return (
		<>
			<Show above={"md"}>
				<NotificationDesktop
					notifications={notifications}
					notificationCount={notifications.length}
					setNotificationsOpen={setNotificationsOpen}
				/>
			</Show>
			<Show below="md">
				<NotificationMobile
					notifications={notifications}
					notificationCount={notifications.length}
					setNotificationsOpen={setNotificationsOpen}
				/>
			</Show>
		</>
	);
};

export default BellNotification;
