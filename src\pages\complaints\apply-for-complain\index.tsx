import { Box, Flex, <PERSON>ing, HStack, Show, Link, Center } from "@chakra-ui/react";
import Breadcrumbs from "components/Breadcrumbs";
import { LeftArr } from "components/Icons";
import MainLayout from "layouts/MainLayout";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import ApplyForComplainForm from "pagesComponents/ApplyForComplaint";
import { ReactElement, useRef } from "react";
import { useTranslation } from "react-i18next";
import { BackendServices } from "services/backend";
import { addLocalLookups, getEmiratesIdFromToken, getLocalizedLookups } from "utils/helpers";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";

function ApplyForComplaint({
	userDetails,
	prevRequests,
	masterData,
	prevComplaints,
	isAuth,
	complaintType,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["personalInfo", "forms", "common"]);
	const router = useRouter();
	const { locale, query } = router;
	const title = useRef("");

	if (complaintType === "1") title.current = "complaint";
	if (complaintType === "2") title.current = "inquiry";
	if (complaintType === "662410000") title.current = "suggestion";
	if (complaintType === "3") title.current = "thankYouTitle";
	const breadcrumbsData = [
		{
			label: t("common:navbar-complaints"),
			id: "navbar-complaints",
			link: "/complaints",
			isCurrentPage: false,
		},
		{
			label: t(`common:${title.current}`),
			id: "newComplaint",
			link: "#",
			isCurrentPage: true,
		},
		{
			label: t(`common:newRequest`),
			id: "newComplaint",
			link: "#",
			isCurrentPage: true,
		},
	];

	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 8 }} w="100%">
			<Show below={"md"}>
				<HStack my={5} mx={6}>
					<Link onClick={() => {}}>
						<Center>
							<LeftArr
								w={"8px"}
								h={"100%"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
						</Center>
					</Link>

					<Heading
						textAlign="center"
						flexGrow={1}
						size="md"
						fontSize="lg"
						fontWeight="medium"
						p={1}
					>
						{t(`${title.current}`, { ns: "common" })}
					</Heading>
				</HStack>
			</Show>
			<Box display={{ base: "none", md: "block" }}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Flex display={{ base: "none", md: "flex" }} mt={4}>
				<Heading size="md" fontSize="h4" fontWeight="medium" pb={12.5} flexGrow={1}>
					{t(`${title.current}`, { ns: "common" })}
				</Heading>
			</Flex>
			<Box>
				<ApplyForComplainForm
					userDetails={userDetails!}
					setDocumentStatus={null}
					prevRequests={prevRequests!}
					masterData={masterData}
					prevComplaints={prevComplaints}
					complaintType={complaintType}
					isAuth={isAuth}
					title={title.current}
				></ApplyForComplainForm>
			</Box>
			{/* <RatingModal isModalShow={true} /> */}
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	let emiratesId = await getEmiratesIdFromToken(ctx.req);
	const complaintType = ctx.query.complaintType?.toString() ?? "1";
	let userDetails: any = null;
	let prevRequests: any = null;
	let prevComplaints: any = null;
	let isAuth = false;
	if (emiratesId) {
		const profile = await BackendServices.retrieveContact(emiratesId);
		userDetails = profile.Data;
		prevRequests = (await BackendServices.retrieveAllRequests(emiratesId)).Data || [];
		prevComplaints = (await BackendServices.getComplaint(emiratesId))?.Data || [];
		isAuth = true;
	} else {
		emiratesId = "";
	}

	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "personalInfo", "forms"])),
			userDetails,
			prevRequests,
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			prevComplaints,
			isAuth,
			complaintType,
		},
	};
}

ApplyForComplaint.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplyForComplaint;
