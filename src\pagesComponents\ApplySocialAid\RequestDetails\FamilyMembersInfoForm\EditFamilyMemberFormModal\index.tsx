import {
	<PERSON><PERSON>,
	<PERSON>rid<PERSON><PERSON>,
	<PERSON>dal,
	<PERSON>dal<PERSON>ody,
	Modal<PERSON>ontent,
	Modal<PERSON>eader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	But<PERSON>,
	<PERSON>dalFooter,
} from "@chakra-ui/react";
import { formatEmiratesID } from "utils/formatters";
import FormField from "components/Form/FormField";
import { CloseIcon } from "components/Icons";
import { useFormContext } from "context/FormContext";
import { Form, Formik, FormikProps } from "formik";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import router, { useRouter } from "next/router";
import React, { useRef, useMemo, useState, useEffect } from "react";
import { getLookupLabel, getLookupItem } from "utils/helpers";
import * as functions from "./functions";
import { WIFE_LOOKUP_ID, HUSBAND_LOOKUP_ID } from "config";
import { createFamilyMemberDoc } from "services/frontend";
import useAppToast from "hooks/useAppToast";

interface Props {
	onClose: any;
	member: IFamilyMember | null;
	onEditMember: (member: IFamilyMember) => void;
	readOnly?: boolean;
}

function EditFamilyMemberForm({ onClose, member, onEditMember, readOnly }: Props) {
	const { t } = useTranslation(["forms", "common", "about", "login"]);
	const { locale } = useRouter();
	const toast = useAppToast();
	const { lookups } = useFormContext();
	const [isModalShow, setIsModalShow] = useState({
		incomes: false,
		pensions: false,
		tradeLicenses: false,
		RentalIncomes: false,
	});
	const [incomeID, setIncomeID] = useState("");
	const [pensionID, setPensionID] = useState("");
	const [tradeLicenseID, setTradeLicenseID] = useState("");
	const [rentalIncomeID, setRentalIncomeID] = useState("");
	const formikRef = useRef<FormikProps<any>>(null);
	const [updatedmember, setUpdateMember] = useState(member);
	const [addingFamilyMember, setAddingFamilyMember] = useState(false);
	let _dummyId = member?.dummyID;
	const handleDeleteIncomeModal = (id, type) => {
		if (type === "incomes") {
			setIncomeID(id);
		} else if (type === "pensions") {
			setPensionID(id);
		} else if (type === "tradeLicenses") {
			setTradeLicenseID(id);
		} else if (type === "RentalIncomes") {
			setRentalIncomeID(id);
		}
		handleShowHideModal(true, type);
	};

	useEffect(() => {
		setUpdateMember(member);
		setAddingFamilyMember(false);
	}, [member]);

	const handleDeleteIncome = (remove, type) => {
		if (type === "incomes") {
			remove(incomeID);
		} else if (type === "pensions") {
			remove(pensionID);
		} else if (type === "tradeLicenses") {
			remove(tradeLicenseID);
		} else if (type === "RentalIncomes") {
			remove(rentalIncomeID);
		}
		handleShowHideModal(false, type);
	};

	const handleShowHideModal = (hideOrShow, type) => {
		setIsModalShow((prev) => ({ ...prev, [type]: hideOrShow }));
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};

	let isEditFamilyMember = false;
	if (
		(member?.Relationship === WIFE_LOOKUP_ID || member?.Relationship === HUSBAND_LOOKUP_ID) &&
		!member?.Id
	) {
		isEditFamilyMember = true;
	}

	const initialValues = useMemo(
		() => ({
			WifeEmiratesID: "",
			WifeDOB: "",
			HusbandEmiratesID: "",
			HusbandDOB: "",
			Occupations: getLookupItem(lookups, "Occupations", member?.Occupations),
		}),
		[member?.Id, locale]
	);

	const onVerify = async (values) => {
		setAddingFamilyMember(true);
		const { query } = router;
		if (member?.Relationship === WIFE_LOOKUP_ID) {
			const WifeEmiratesID = values.WifeEmiratesID.replace(/-/g, "");
			const { WifeDOB } = values;
			if (query.requestId?.toString()) {
				const response = await createFamilyMemberDoc(
					query.requestId?.toString(),
					WifeEmiratesID,
					WifeDOB,
					"wife"
				).then((response) => {
					setAddingFamilyMember(false);
					if (response?.IsSuccess) {
						member = response?.Data; // Update the member state
						setUpdateMember({ ...member, dummyID: _dummyId } as IFamilyMember);
					} else {
						if (response?.Errors == "invalid-emirates-id" || response?.Errors == "NO DATA FOUND") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidation"),
								status: "error",
							});
						} else if (response?.Errors == "gender-error") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidationFailedGender"),
								status: "error",
							});
						} else if (response?.Errors == "dob-error") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidationFailedDob"),
								status: "error",
							});
						} else if (response?.Errors == "nationality-error") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:incorrectNationality"),
								status: "error",
							});
						}
					}
				});
			}
		} else if (member?.Relationship === HUSBAND_LOOKUP_ID) {
			const HusbandEmiratesID = values.HusbandEmiratesID.replace(/-/g, "");
			const { HusbandDOB } = values;
			if (query.requestId?.toString()) {
				const response = await createFamilyMemberDoc(
					query.requestId?.toString(),
					HusbandEmiratesID,
					HusbandDOB,
					"husband"
				).then((response) => {
					setAddingFamilyMember(false);
					if (response?.IsSuccess) {
						member = response?.Data; // Update the member state
						setUpdateMember({ ...member, dummyID: _dummyId } as IFamilyMember);
					} else {
						if (response?.Errors == "invalid-emirates-id" || response?.Errors == "NO DATA FOUND") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidation"),
								status: "error",
							});
						} else if (response?.Errors == "gender-error") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidationFailedGender"),
								status: "error",
							});
						} else if (response?.Errors == "dob-error") {
							toast({
								title: t("common:emiratesIdValidationTitle"),
								description: t("common:emiratesIdValidationFailedDob"),
								status: "error",
							});
						}
					}
				});
			}
		}
	};

	const onSubmit = (it) => {
		updatedmember &&
			onEditMember({
				...updatedmember,
				Occupations: it.Occupations.value,
				IsInformationUpdated: true,
			});
	};

	return (
		<>
			<Modal
				isOpen={!!member}
				onClose={onClose}
				size={{ base: "full", md: "4xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{locale === "ar" ? updatedmember?.FullnameAR : updatedmember?.FullnameEN} -{" "}
									{getLookupLabel(lookups, "FamilyRelationship", updatedmember?.Relationship)}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
					>
						{(formik) => (
							<>
								<ModalBody>
									<Form
										onSubmit={(e) => {
											e.preventDefault();
											formik.handleSubmit(e);
										}}
										onChange={(e) => {
											e.preventDefault();
											functions.onChange(e, formik);
										}}
									>
										<Grid
											rowGap={{ base: 6, md: 6 }}
											columnGap={6}
											templateColumns="repeat(2, 1fr)"
											templateRows="auto"
										>
											{member?.Relationship === WIFE_LOOKUP_ID && !updatedmember?.Id && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<FormField
														type="text"
														borderColor="brand.gray.250"
														isRequired={true}
														value={formik.values["WifeEmiratesID"]}
														name="WifeEmiratesID"
														customFormat={formatEmiratesID}
														label={t("EmiratesID")}
														placeholder={t("placeholder", { ns: "common" })}
														error={formik.errors["EmiratesID"]}
														onChange={(firstArg) => {
															handleChangeEvent("text", firstArg, "WifeEmiratesID", formik);
														}}
													/>
												</GridItem>
											)}
											{member?.Relationship === WIFE_LOOKUP_ID && !updatedmember?.Id && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<FormField
														type="date"
														borderColor="brand.gray.250"
														isRequired={true}
														value={formik.values["WifeDOB"]}
														name="WifeDOB"
														label={t("dateOfBirth")}
														placeholder={t("placeholder", { ns: "common" })}
														error={formik.errors["WifeDOB"]}
													/>
												</GridItem>
											)}
											{member?.Relationship === WIFE_LOOKUP_ID && !updatedmember?.Id && (
												<Button
													variant="primary"
													w={"100%"}
													isDisabled={
														!formik.values.WifeEmiratesID ||
														!formik.values.WifeDOB ||
														!!updatedmember?.Id ||
														addingFamilyMember == true ||
														formik.values.WifeEmiratesID.replace(/-/g, "").length != 15
													} // Disable button until both fields are filled
													onClick={() => onVerify(formik.values)} // Pass the current values to onVerify
												>
													{t("Verify Emirates ID")}
												</Button>
											)}
											{member?.Relationship === HUSBAND_LOOKUP_ID && !updatedmember?.Id && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<FormField
														type="text"
														borderColor="brand.gray.250"
														isRequired={true}
														value={formik.values["HusbandEmiratesID"]}
														name="HusbandEmiratesID"
														customFormat={formatEmiratesID}
														label={t("EmiratesID")}
														placeholder={t("placeholder", { ns: "common" })}
														error={formik.errors["HusbandEmiratesID"]}
														onChange={(firstArg) => {
															handleChangeEvent("text", firstArg, "HusbandEmiratesID", formik);
														}}
													/>
												</GridItem>
											)}
											{member?.Relationship === HUSBAND_LOOKUP_ID && !updatedmember?.Id && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<FormField
														type="date"
														borderColor="brand.gray.250"
														isRequired={true}
														value={formik.values["HusbandDOB"]}
														name="HusbandDOB"
														label={t("dateOfBirth")}
														placeholder={t("placeholder", { ns: "common" })}
														error={formik.errors["HusbandDOB"]}
													/>
												</GridItem>
											)}
											{member?.Relationship === HUSBAND_LOOKUP_ID && !updatedmember?.Id && (
												<Button
													variant="primary"
													w={"100%"}
													isDisabled={
														!formik.values.HusbandEmiratesID ||
														!formik.values.HusbandDOB ||
														!!updatedmember?.Id ||
														addingFamilyMember == true ||
														formik.values.HusbandEmiratesID.replace(/-/g, "").length != 15
													} // Disable button until both fields are filled
													onClick={() => onVerify(formik.values)} // Pass the current values to onVerify
												>
													{t("Verify Emirates ID")}
												</Button>
											)}
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="selectableTags"
													value={formik.values["Occupations"]}
													isRequired={true}
													name="Occupations"
													label={t("Occupations")}
													isDisabled={
														(readOnly && member?.IsInformationUpdated) || !updatedmember?.Id
													}
													placeholder={t("placeholder", { ns: "common" })}
													options={lookups.Occupations}
													touched={formik.touched["Occupations"]}
													error={formik.errors["Occupations"]}
													onChange={(firstArg) => {
														handleChangeEvent("selectableTags", firstArg, "Occupations", formik);
													}}
												/>
											</GridItem>
										</Grid>
									</Form>
								</ModalBody>
								<ModalFooter borderTop="1px solid #BBBCBD">
									<HStack w={"100%"} gap={2} my={4}>
										<Button variant="secondary" w={"100%"} onClick={onClose}>
											{t("common:cancel")}
										</Button>
										<Button
											variant="primary"
											w={"100%"}
											isDisabled={
												(isEditFamilyMember && !formik.isValid && !updatedmember?.Id) ||
												(!isEditFamilyMember && !formik.values.Occupations)
											} // Disable button until both fields are filled
											onClick={formik.submitForm}
										>
											{t("common:save")}
										</Button>
									</HStack>
								</ModalFooter>
							</>
						)}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditFamilyMemberForm;
