import { Flex, Text, Image } from "@chakra-ui/react";
import { ServiceRating } from "components/Icons";
import React from "react";

export default function TestimonialView() {
	return (
		<Flex
			flexDirection={{ base: "column", md: "row" }}
			alignItems={{ base: "flex-start", md: "center" }}
			mr={2}
		>
			<Flex
				flexDirection={"column"}
				bg={"brand.white.50"}
				py={8}
				pl={8}
				width={{ base: "100%", md: "unset" }}
				pr={{ base: 8, md: 40 }}
				alignItems="flex-start"
			>
				<Text fontSize={"4xl"} color={"#A2A9B0"} height="35px">
					&#8220;
				</Text>
				<Text fontSize={"md"} color={"#000000"} fontWeight={700}>
					Mustafa <PERSON>
				</Text>
				<Text fontSize={"md"} color={"#000000"} fontWeight={400} my={2}>
					Customer
				</Text>
				<ServiceRating w={"152px"} h={"40px"} />
			</Flex>
			<Flex
				position={"relative"}
				left={{ base: "0px", md: "-68px" }}
				top={{ base: "-17px", sm: "0px", md: "0px" }}
				mb={{ base: 6, sm: 0, lg: 0 }}
				flexDirection={{ base: "column", sm: "row", lg: "row" }}
			>
				<Image
					src={"/assets/images/profileOne.png"}
					h={{ base: "unset", sm: "136px", lg: "136px" }}
					width={{ base: "60%", sm: "unset", lg: "unset" }}
					mx={{ base: "auto", sm: "unset", lg: "unset" }}
					alt="profileOne"
				/>
				<Flex flexDirection={"column"} alignItems={"flex-start"} justifyContent={"center"} ml={5}>
					<Text fontWeight={700} color={"#4D5358"}>
						Very Satisfied!
					</Text>
					<Text color={"#4D5358"} textAlign={"start"}>
						Etiam commodo luctus rhoncus. Nullam placerat auctor nulla, sit amet volutpat mauris
						fermentum nec. Praesent molestie{" "}
					</Text>
				</Flex>
			</Flex>
		</Flex>
	);
}
