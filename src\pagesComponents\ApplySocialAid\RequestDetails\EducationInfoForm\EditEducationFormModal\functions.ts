import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	ApplyEducationAllowance: "",
	IsEnrolledInNationalService: "",
	childCompletedSemesterInUniversity: "",
	highSchoolCurriculuim: "",
	enrolledEducationStream: "",
	EmSATorAdvancedPlacementScores: "",
	universityName: "",
	unaccreditedUniversityName: "",
	cgpa: "",
	creditHours: "",
};

const getValidationSchema = () => {
	return Yup.object({
		ApplyEducationAllowance: Yup.string().required().label("thisField").nullable(),
		childCompletedSemesterInUniversity: Yup.string().when("ApplyEducationAllowance", {
			is: (ApplyEducationAllowance) => {
				return ApplyEducationAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		IsEnrolledInNationalService: Yup.string().when("ApplyEducationAllowance", {
			is: (ApplyEducationAllowance) => {
				return ApplyEducationAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		// highSchoolCurriculuim: Yup.object().when(
		// 	["ApplyEducationAllowance", "childCompletedSemesterInUniversity"],
		// 	{
		// 		is: (ApplyEducationAllowance, childCompletedSemesterInUniversity) => {
		// 			return ApplyEducationAllowance === "yes" && childCompletedSemesterInUniversity === "no";
		// 		},
		// 		then: Yup.object().shape({
		// 			label: Yup.string(),
		// 			value: Yup.string().required().label("thisField"),
		// 		}),
		// 		otherwise: Yup.object().notRequired().nullable(),
		// 	}
		// ),
		// enrolledEducationStream: Yup.object().when(
		// 	["ApplyEducationAllowance", "childCompletedSemesterInUniversity", "highSchoolCurriculuim"],
		// 	{
		// 		is: (
		// 			ApplyEducationAllowance,
		// 			childCompletedSemesterInUniversity,
		// 			highSchoolCurriculuim
		// 		) => {
		// 			return (
		// 				ApplyEducationAllowance === "yes" &&
		// 				childCompletedSemesterInUniversity === "no" &&
		// 				highSchoolCurriculuim?.value === "662410000"
		// 			);
		// 		},
		// 		then: Yup.object().shape({
		// 			label: Yup.string(),
		// 			value: Yup.string().required().label("thisField"),
		// 		}),
		// 		otherwise: Yup.object().notRequired().nullable(),
		// 	}
		// ),
		// EmSATorAdvancedPlacementScores: Yup.object().when(
		// 	["ApplyEducationAllowance", "childCompletedSemesterInUniversity", "highSchoolCurriculuim"],
		// 	{
		// 		is: (
		// 			ApplyEducationAllowance,
		// 			childCompletedSemesterInUniversity,
		// 			highSchoolCurriculuim
		// 		) => {
		// 			return (
		// 				ApplyEducationAllowance === "yes" &&
		// 				childCompletedSemesterInUniversity === "no" &&
		// 				highSchoolCurriculuim?.value === "662410002"
		// 			);
		// 		},
		// 		then: Yup.object().shape({
		// 			label: Yup.string(),
		// 			value: Yup.string().required().label("thisField"),
		// 		}),
		// 		otherwise: Yup.object().notRequired().nullable(),
		// 	}
		// ),
		// Add validation for university fields
		universityName: Yup.object().when("childCompletedSemesterInUniversity", {
			is: "yes",
			then: Yup.object()
				.shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				})
				.required()
				.label("thisField"),
			otherwise: Yup.object().notRequired().nullable(),
		}),
		cgpa: Yup.string().when("childCompletedSemesterInUniversity", {
			is: "yes",
			then: Yup.string()
				.required()
				.label("thisField")
				.matches(/^\d+(\.\d{1,2})?$/, "invalidCGPAFormat")
				.test(
					"is-valid-range",
					"cgpaRangeError",
					(value) => !value || (parseFloat(value) >= 0 && parseFloat(value) <= 4)
				),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		creditHours: Yup.string().when("childCompletedSemesterInUniversity", {
			is: "yes",
			then: Yup.string()
				.required()
				.label("thisField")
				.matches(/^\d{1,2}(\.\d)?$/, "invalidCreditHoursFormat")
				.test(
					"is-valid-range",
					"creditHoursRangeError",
					(value) => !value || (parseFloat(value) >= 0 && parseFloat(value) <= 21.0)
				),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		unaccreditedUniversityName: Yup.string().when(["universityName"], {
			is: (universityName) => {
				return universityName?.value === "cc0bc8df-083d-f011-b112-005056010908";
			},
			then: Yup.string().required().label("thisField"),
			otherwise: Yup.string().notRequired(),
		}),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("amer", event, formikProps);
};

const getListDefault = (arr) => (!arr || arr?.length === 0 ? [{}] : arr);

export { getInitialValues, onChange, getValidationSchema, getListDefault };
