import { ComponentWithAs, Flex, IconProps, Tab, Text } from "@chakra-ui/react";
import React from "react";

interface Props {
	tabItems: {
		tabTitle: string;
		Icon: ComponentWithAs<"svg", IconProps>;
	}[];
}

function VerticalTablist({ tabItems }: Props) {
	return (
		<>
			{tabItems.map((tab, idx) => (
				<Tab
					key={idx}
					_selected={{ bg: "brand.mainGold", color: "white" }}
					css={{ borderRadius: "0.625rem" }}
					color="#1B1D21"
					w={{ base: "full", md: "full" }}
				>
					<Flex gap={4} w="full" alignItems={"center"}>
						<tab.Icon w="6" h="6" />
						<Text fontWeight={"medium"} fontSize={"1.125rem"} w="full" textAlign={"left"}>
							{tab.tabTitle}
						</Text>
					</Flex>
				</Tab>
			))}
		</>
	);
}

export default VerticalTablist;
