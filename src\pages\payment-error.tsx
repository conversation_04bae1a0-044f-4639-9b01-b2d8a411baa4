import React, { ReactElement, useEffect } from "react";
import { useRouter } from "next/router";
import { Heading, Card, Flex, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { FaTimes } from "react-icons/fa";
import MainLayout from "layouts/MainLayout";

import { serverSideTranslations } from "next-i18next/serverSideTranslations";

function ErrorPage() {
	const { t } = useTranslation(["common"]);
	const router = useRouter();

	useEffect(() => {
		const timeoutId = setTimeout(() => {
			router.push("/");
		}, 10000);

		return () => clearTimeout(timeoutId);
	}, [router]);
	return (
		<Flex
			width={"100%"}
			position={"relative"}
			bg="url('/assets/images/loginImg.jpg')"
			height={{ base: "91vh", md: "calc(89vh)" }}
			backgroundSize={"cover"}
			justifyContent="center"
			alignItems="center"
		>
			<Card
				bg={"brand.lightGold"}
				width={{ base: "100%", sm: "80%", md: "40%" }}
				borderRadius={0}
				shadow={"unset"}
				py={10}
				px={12}
				color={"brand.textColor"}
				textAlign="center"
			>
				<Flex justifyContent="center" alignItems="center" mb={4}>
					<FaTimes style={{ fontSize: "4em", color: "red" }} />
				</Flex>
				<Heading as="h1" fontSize="3xl" mb={4}>
					{t("PaymentFailed")}
				</Heading>
				<Text fontSize="xl" mb={4}>
					{t("payment-error-title")}
				</Text>
				<Text fontSize="xl"> {t("payment-error-subTitle")}</Text>
			</Card>
		</Flex>
	);
}
ErrorPage.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout minimalLayout>{page}</MainLayout>;
};
export async function getServerSideProps(ctx) {
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common"])),
		},
	};
}

export default ErrorPage;
