import { WomenIDSCase } from "../calculator";

export class WomenInDFSCalculator {
	public static calculateChild(data: WomenIDSCase) {
		const { personalInformation } = data;
		const isDivorced = personalInformation.currentSituation === "1";
		const spouseOfForeigner = personalInformation.currentSituation === "4";

		// if the woman is divorced or the spouse is a foreigner, she is not eligible for the child allowance
		if (spouseOfForeigner || isDivorced) return 0;

		if (personalInformation.haveChildren === "1") {
			const numberOfPodChildren = Number(personalInformation.numberOfPODChildren);
			const numberOfChildren = Number(personalInformation.numberOfChildren);
			let podChildsAllowance = 0;
			let normalChildsAllowance = 0;

			if (personalInformation.isChildrenPOD === "1") {
				podChildsAllowance = 5000 * numberOfPodChildren;
			}
			const numberOfChildrenResult = numberOfChildren - (numberOfPodChildren || 0);
			if (numberOfChildrenResult === 1) {
				normalChildsAllowance = 2400;
			} else if (numberOfChildrenResult === 2) {
				normalChildsAllowance = 4000;
			} else if (numberOfChildrenResult === 3) {
				normalChildsAllowance = 5600;
			} else if (numberOfChildrenResult > 3) {
				normalChildsAllowance = 5600 + 800 * (numberOfChildrenResult - 3);
			}
			return normalChildsAllowance + podChildsAllowance;
		}
		return 0;
	}
	public static calculateSelfAllowance(data: WomenIDSCase) {
		return 5000;
	}

	public static calculate(data: WomenIDSCase) {
		const self = this.calculateSelfAllowance(data);

		const child = this.calculateChild(data);
		const final = self + child - Number(data.personalInformation.totalIncome);
		const isEligble = final > 0;

		return {
			self,
			child,
			final,
			eightyPercant: final * 0.8,
			isEligble,
		};
	}
}
