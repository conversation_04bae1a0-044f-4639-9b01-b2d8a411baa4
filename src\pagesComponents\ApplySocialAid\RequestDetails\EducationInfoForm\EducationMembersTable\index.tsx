import {
	Button,
	Flex,
	Show,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
} from "@chakra-ui/react";
import { ButtonArrowIcon, EditIcon, ExlamationMark, PencilIcon } from "components/Icons";
import { useTranslation } from "next-i18next";
import { IEducationCase } from "interfaces/SocialAidForm.interface";
import StatusPill from "components/StatusPill";
import { useFormContext } from "context/FormContext";
import { useRouter } from "next/router";

interface Props {
	members: IEducationCase[];
	setEditMember: Function;
	mb?: number;
	readOnly?: boolean;
	IsEdit?: boolean;
}

function EducationMembersTable({
	members,
	setEditMember,
	mb = 0,
	readOnly = false,
	IsEdit,
}: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();
	let headers = [
		{
			id: "Fullname",
		},
		{
			id: "Age",
		},
		{
			id: "ApplyEducation",
			width: "25%",
			wrap: true,
		},
		{
			id: "status",
			width: "25%",
		},
		{
			id: "Action",
		},
	];

	const sortedMembers = [...(members || [])].sort((a, b) => {
		const ageA = parseInt(a?.Age ?? "", 10);
		const ageB = parseInt(b?.Age ?? "", 10);

		if (isNaN(ageA)) return 1;
		if (isNaN(ageB)) return -1;

		return ageA - ageB;
	});

	return (
		<>
			<Show above="md">
				<TableContainer
					mb={mb}
					rounded={"lg"}
					border="1px"
					borderBottom={"0px"}
					borderColor="#BBBCBD"
				>
					<Table variant="simple">
						<Thead>
							<Tr fontWeight="medium" mb={4}>
								{headers.map((header, idx) => {
									return (
										<Th
											color="brand.tableTextColor"
											key={idx}
											textTransform="none"
											lineHeight="150%"
											fontSize="0.9375rem"
											fontWeight="bold"
											letterSpacing="unset"
											py={4}
											px={4}
											borderColor="brand.tableBorderColor"
											width={header.width || "unset"}
											whiteSpace={header.wrap ? "normal" : "nowrap"}
										>
											{t(header.id)}
										</Th>
									);
								})}
							</Tr>
						</Thead>
						<Tbody>
							{sortedMembers?.map((member, memberRow) => {
								return (
									<Tr key={member.IdChild}>
										{headers.map((header, idx) => {
											return (
												<Th
													key={idx}
													textTransform="none"
													fontFamily="Helvetica Neue"
													color="brand.familyTableRowColor"
													fontWeight="normal"
													fontSize="0.9375rem"
													letterSpacing="unset"
													lineHeight="150%"
													p={4}
													borderColor="brand.tableBorderColor"
												>
													{header.id === "numberOfHousehold" ? (
														memberRow + 1
													) : header.id === "actions" ? (
														<Flex>
															{!readOnly && (
																<PencilIcon
																	mr={8}
																	color="brand.gray.400"
																	transform="scale(1.25, 1.25)"
																	onClick={() => {
																		if (!readOnly) setEditMember(member);
																	}}
																	cursor="pointer"
																/>
															)}
														</Flex>
													) : header.id === "status" ? (
														member.IsCompletedFromPortal ? (
															<StatusPill
																customText={t("complete")!}
																customStatus={"requestApproved"}
																onClick={() => {
																	if (!readOnly && !IsEdit) {
																		setEditMember(member);
																	} else if (IsEdit && member.ApplyEducationAllowance) {
																		setEditMember(member);
																	}
																}}
															/>
														) : (
															<Button
																onClick={() => {
																	if (!IsEdit) {
																		setEditMember(member);
																	} else if (IsEdit && member.ApplyEducationAllowance) {
																		setEditMember(member);
																	}
																}}
																rightIcon={
																	<ButtonArrowIcon
																		w={"24px"}
																		h={"24px"}
																		transform={locale === "ar" ? "scale(-1, 1)" : ""}
																		marginInlineStart={1}
																	/>
																}
																variant={"outline"}
																minHeight={"0.2rem"}
																h={"2rem"}
																px={"1px !important"}
															>
																{t("completeInfo")}
															</Button>
														)
													) : header.id === "Action" ? (
														IsEdit &&
														member.ApplyEducationAllowance && (
															<EditIcon
																w={"40px"}
																height={"40px"}
																transform="scale(1.25, 1.25)"
																onClick={() => {
																	if (IsEdit && member.ApplyEducationAllowance) {
																		setEditMember(member);
																	}
																}}
																cursor="pointer"
															/>
														)
													) : (
														<Flex alignItems={"center"} gap={2}>
															{!member.IsCompletedFromPortal && (
																<ExlamationMark color="brand.mainGold" />
															)}
															{header.id === "Fullname" ? (
																<Text>
																	{locale === "en" ? member["FullNameEn"] : member["FullNameAr"]}
																</Text>
															) : (
																<Text>
																	{header.id === "ApplyEducation"
																		? t(`applyed${member.ApplyEducationAllowance}`)
																		: member[header.id]}
																</Text>
															)}
														</Flex>
													)}
												</Th>
											);
										})}
									</Tr>
								);
							})}
						</Tbody>
					</Table>
				</TableContainer>
			</Show>

			<Show below="md">
				<VStack align={"start"}>
					{sortedMembers?.map((member, index) => {
						return (
							<Flex
								key={index}
								w="full"
								p={4}
								justifyContent={"space-between"}
								borderBottom="1px solid #BBBCBD"
								_hover={{ cursor: "pointer" }}
							>
								<VStack flex={2} align={"start"}>
									<Flex alignItems={"center"} gap={2}>
										{!member.IsCompletedFromPortal && <ExlamationMark color="brand.mainGold" />}
										<Text fontSize={"1rem"} fontWeight={"bold"}>
											{locale === "en" ? member["FullNameEn"] : member["FullNameAr"]}
										</Text>
									</Flex>
									<Text fontSize={"0.875rem"}>
										{t("numberOfHousehold")} : {index + 1}
									</Text>
								</VStack>
								{member.IsCompletedFromPortal ? (
									<StatusPill
										customText={t("complete")!}
										customStatus={"requestApproved"}
										onClick={() => {
											if (!readOnly) setEditMember(member);
										}}
									/>
								) : (
									<Button
										onClick={() => {
											setEditMember(member);
										}}
										rightIcon={
											<ButtonArrowIcon
												w={"24px"}
												h={"24px"}
												transform={locale === "ar" ? "scale(-1, 1)" : ""}
												marginInlineStart={1}
											/>
										}
										variant={"outline"}
										minHeight={"0.2rem"}
										h={"2rem"}
										px={"1px !important"}
									>
										{t("completeInfo")}
									</Button>
								)}
							</Flex>
						);
					})}
				</VStack>
			</Show>
		</>
	);
}

export default EducationMembersTable;
