import { ICrmToWhomForm } from "interfaces/CrmToWhom.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getEmiratesIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmToWhomForm>>
) {
	const {
		Email,
		MobileNumber,
		EntityAddressed,
		OtherEntity,
		TypeOfCertification,
		OtherEntityAddressed,
	} = req.body;
	if (!Email || !MobileNumber || !EntityAddressed || !TypeOfCertification)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });
	const EmiratesId = await getEmiratesIdFromToken(req);
	const data = await BackendServices.toWhomItMayConcernRequest({
		EmiratesId,
		Email,
		MobileNumber,
		EntityAddressed,
		OtherEntity,
		TypeOfCertification,
		OtherEntityAddressed,
	});

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
