import {
	AlertDialog,
	AlertDialogBody,
	AlertDialogContent,
	AlertDialog<PERSON>ooter,
	AlertDialogHeader,
	AlertDialogOverlay,
	Box,
	Button,
	Flex,
	Text,
	useDisclosure,
	Input,
} from "@chakra-ui/react";
import { ButtonArrowIcon } from "components/Icons";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useMutation } from "react-query";
import { appealComplaint } from "services/frontend";

const CustomedStatusPill = ({ status, complaintId, AppealStatus, ModifiedOn, inModal = false }) => {
	const { t } = useTranslation(["common", "tables", "personalInfo"]);
	let bgColor;
	let textColor;
	let statusPillTitle = "";
	const { locale, replace, asPath } = useRouter();
	const [daysPassed, setDaysPassed] = useState(true);

	useEffect(() => {
		const lastModifiedDate: any = new Date(ModifiedOn);
		const currentDate: any = new Date();
		const timeDifference = currentDate - lastModifiedDate;

		const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
		if (daysDifference > 3) {
			setDaysPassed(true);
		} else {
			setDaysPassed(false);
		}
	}, []);
	if (status === 5) {
		bgColor = "#F7FFFB";
		textColor = "#1A804C";
	} else {
		bgColor = "#FFF9F0";
		textColor = "#996516";
	}

	if (status === 5) {
		statusPillTitle = t("problemSolved");
	} else if (status === 1) {
		statusPillTitle = t("inProgress");
	} else if (status === 2) {
		statusPillTitle = t("closed");
	} else if (status === 662410002) {
		statusPillTitle = t("Reopened");
	} else if (status === 1000) {
		statusPillTitle = t("InformationProvided");
	}
	const { isOpen, onOpen, onClose } = useDisclosure();
	const cancelRef = React.useRef<any>();
	const { isLoading, mutateAsync } = useMutation({
		mutationFn: async () => {
			return await appealComplaint(complaintId, reopenReason);
		},
	});
	const toast = useAppToast();
	const appeal = async () => {
		const res = await mutateAsync();
		if (res.IsSuccess) {
			toast({
				status: "info",
				title: t("tables:reOpenDone"),
			});
			// this is to refetch the complaint list ( to show the new state )
			replace(asPath, undefined, { shallow: false, scroll: false });
		} else {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
		onClose();
	};

	const [reopenReason, setReopenReason] = useState("");

	const handleReopen = () => {
		if (reopenReason !== "") {
			appeal();
		}
	};

	return (
		<Flex
			alignItems={"center"}
			flexDir={{
				base: "column",
				md: "row",
			}}
			gap={4}
		>
			<Box
				bg={bgColor}
				px={2}
				borderRadius={"5px"}
				py="1"
				w={"fit-content"}
				borderWidth={"1px"}
				borderColor={textColor}
				h={"fit-content"}
			>
				<Text
					fontSize={"sm"}
					fontWeight={500}
					color={textColor}
					p={-2}
					w={"100%"}
					textAlign={"center"}
				>
					{statusPillTitle}
				</Text>
			</Box>
			{/* if the complaint status is resolved, there should be a button to allow the users to appeal the complaint */}
			{status === 5 &&
				false &&
				!daysPassed &&
				!AppealStatus &&
				!inModal && ( //
					<Button
						w={"full"}
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
								marginInlineStart={1}
							/>
						}
						p={"0 !important"}
						onClick={(e) => {
							e.stopPropagation();
							onOpen();
						}}
						variant={"outline"}
					>
						{t("reOpen")}
					</Button>
				)}
			<AlertDialog isOpen={isOpen} leastDestructiveRef={cancelRef} onClose={onClose} isCentered>
				<AlertDialogOverlay>
					<AlertDialogContent>
						<AlertDialogHeader fontSize="lg" fontWeight="bold">
							{t("reOpen")}
						</AlertDialogHeader>

						<AlertDialogBody>
							{/* <Text>{t("reOpenConfirmation")}</Text> */}
							<Text mb="3">{t("reOpenReson")}</Text>
							<Input
								type="text"
								value={reopenReason}
								onChange={(e) => setReopenReason(e.target.value)}
							/>
						</AlertDialogBody>

						<AlertDialogFooter>
							<Button ref={cancelRef} disabled={isLoading} variant={"secondary"} onClick={onClose}>
								{t("cancel")}
							</Button>
							<Button
								colorScheme="red"
								variant={"primary"}
								disabled={reopenReason === ""}
								isLoading={isLoading}
								onClick={handleReopen}
								ml={3}
							>
								{t("confirm")}
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialogOverlay>
			</AlertDialog>
		</Flex>
	);
};

export default CustomedStatusPill;
