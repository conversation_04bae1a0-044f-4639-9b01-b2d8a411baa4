import { ICrmNotification } from "interfaces/CrmNotification.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmNotification[]>>
) {
	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.getNotifications(contactId);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
