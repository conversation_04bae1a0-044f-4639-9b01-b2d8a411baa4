import { ISocialAidForm } from "interfaces/SocialAidForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ISocialAidForm>>
) {
	const { caseId, endpoint, isCategoryChange } = req.query;
	if (!caseId) return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.getRequest(
		caseId.toString(),
		contactId,
		endpoint,
		isCategoryChange === "true"
	);
	//console.log("get Request", data);
	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
