import { ICrmLookupLocalized, ICrmMasterData } from "interfaces/CrmMasterData.interface";
import { createContext, useContext } from "react";

export type FormContent = {
	lookups: ICrmMasterData<ICrmLookupLocalized>;
};

export const FormContext = createContext<FormContent>({
	lookups: {
		Occupations: [],
		Emirates: [],
		MaritalStatus: [],
		Educations: [],
		accomadations: [],
		IncomeTypes: [],
		PensionType: [],
		PensionAuthority: [],
		FamilyRelationship: [],
		rentalSource: [],
		CaseStatus: [],
		Boolean: [],
		IncomeSources: [],
		OtherEntities: [],
		ProcessTemplates: [],
		Area: [],
		Category: [],
		InflationCategory: [],
		PortalPersona: [],
		SubCategory: [],
		SubPersona: [],
		Center: [],
		DocumentProcessTemplate: [],
		ComplaintTopics: [],
		ComplaintServices: [],
		ComplaintSubServices: [],
		InstallmentsRates: [],
		ReasonsToEditInflation: [],
		ReasonsToEditSwp: [],
		universities: [],
		rehabCenters: [],
	},
});

export const useFormContext = () => useContext(FormContext);
