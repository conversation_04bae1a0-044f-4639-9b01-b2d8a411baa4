import { Box, Grid, GridItem } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import MyAllowance from "pagesComponents/MyAllowance";
import PendingItems from "pagesComponents/PendingItems";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ReactElement } from "react";
import WelcomeMessage from "pagesComponents/WelcomeMessage";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import {
	addLocalLookups,
	formatCurrencyAmount,
	getEmiratesIdFromToken,
	getFormattedDate,
	getLocalizedLookups,
	getLocalizedRequestName,
} from "utils/helpers";
import { BackendServices } from "services/backend";
import React from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";

const pendingItemsData = {
	tableHeaders: [
		{
			id: "requestNumber",
			type: "string",
			isBold: true,
		},
		{
			id: "status",
			type: "string",
		},
		{
			id: "createdDate",
			type: "string",
		},
	],
	showViewAll: true,
};

const myAllowanceData = {
	tableHeaders: [
		{
			id: "amount",
			type: "string",
			isBold: true,
		},
		{
			id: "requestNumber",
			type: "string",
		},
		{
			id: "date",
			type: "string",
		},
	],
	showViewAll: true,
};

function Home(props: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { locale } = useRouter();
	const { t } = useTranslation();

	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 4 }} w="100%">
			<Grid
				rowGap={{ base: 2.5, md: 4 }}
				columnGap={4}
				templateColumns="repeat(2, 1fr)"
				templateRows="auto"
			>
				<GridItem bg="white" colSpan={2}>
					<WelcomeMessage
						image={"url('../assets/images/15-2.jpg')"}
						mobileImg={"url('../assets/images/15-2.jpg')"}
						title={"heroTitle"}
						description={"heroDescription"}
						butTitle={"heroButtonTitle"}
					/>
				</GridItem>
				<GridItem
					bg="white"
					pt={{ base: 7, md: 7 }}
					pb={{ base: 8, md: 8 }}
					px={{ base: 6, md: 10 }}
					colSpan={{ base: 2, md: 1 }}
					overflowX={{ base: "scroll", md: "unset" }}
				>
					<PendingItems
						tableHeaders={pendingItemsData.tableHeaders}
						tableBody={props.requests.map((request) => {
							return [
								getLocalizedRequestName(request.RequestName, t("request")),
								request.Status,
								getFormattedDate(request.CreatedDate, "dd MMMM yyyy", locale),
								request.CaseId,
							];
						})}
						caption={null}
						hasFooter={false}
						footerValues={null}
						showViewAll={pendingItemsData.showViewAll}
						statusLookup={props.masterData?.CaseStatus || []}
					/>
				</GridItem>
				<GridItem
					bg="white"
					py={8}
					px={{ base: 6, md: 10 }}
					colSpan={{ base: 2, md: 1 }}
					overflowX={{ base: "scroll", md: "unset" }}
				>
					<MyAllowance
						tableHeaders={myAllowanceData.tableHeaders}
						tableBody={props.allowance.map((request) => {
							return [
								formatCurrencyAmount(request.TotalAmount, t, locale),
								getLocalizedRequestName(request.RequestName, t("request")),
								getFormattedDate(request.PayDate, "dd MMMM yyyy", locale),
							];
						})}
						caption={null}
						hasFooter={false}
						footerValues={null}
						showViewAll={myAllowanceData.showViewAll}
					/>
				</GridItem>
				{/* <GridItem bg="white" py={8} px={{ base: 6, md: 10 }} colSpan={2}>
					<NewsAndUpdates />
				</GridItem> */}
			</Grid>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	return {
		redirect: {
			destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}smart-services`,
			permanent: false,
		},
	};
	if (emiratesId != null) {
		return {
			redirect: {
				destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}about`,
				permanent: false,
			},
		};
	} else {
		return {
			redirect: {
				destination: `/${ctx.locale === "ar" ? "" : ctx.locale + "/"}home`,
				permanent: false,
			},
		};
	}

	const requests = (await BackendServices.retrieveAllRequests(emiratesId))?.Data || [];
	requests.sort((a, b) => new Date(b.CreatedDate).getTime() - new Date(a.CreatedDate).getTime());

	const allowance = (await BackendServices.retrieveAllowanceTransactions(emiratesId))?.Data || [];
	allowance.sort((a, b) => new Date(b.PayDate).getTime() - new Date(a.PayDate).getTime());

	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "home"])),
			requests: requests.slice(0, 5),
			allowance: allowance.slice(0, 5),
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
		},
	};
}

Home.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};

export default Home;
