{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noImplicitAny": false, "downlevelIteration": true, "baseUrl": "src"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "next-i18next.config.js", "src/pagesComponents/calculator/ChildInDFS/ResonStep/index.js"], "exclude": ["node_modules"]}