import { AppContext } from "context/AppContext";
import NextNProgress from "nextjs-progressbar";
import theme from "theme";
import "../styles/globals.scss";
import "../styles/fonts.css";
import RtlProvider from "components/RtProvider";
import { appWithTranslation } from "next-i18next";
import { SessionProvider } from "next-auth/react";
import { QueryClient, QueryClientProvider } from "react-query";
import ChakraProviderContainer from "pagesComponents/ChakraProviderContainer";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { LANG_SWITCHER_ENABLED } from "config";
import { reactPlugin } from "../context/AppInsights";
import { AppInsightsContext } from "@microsoft/applicationinsights-react-js";

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchOnWindowFocus: false,
			refetchOnMount: false,
			refetchOnReconnect: false,
			retry: false,
		},
	},
});
declare global {
	// for customer pulse script
	interface Window {
		CustomerPulse: any;
	}
}
function MyApp({ Component, pageProps: { session, ...pageProps } }: any) {
	const getLayout = Component.getLayout ?? ((page) => page);
	const router = useRouter();
	useEffect(() => {
		if (!LANG_SWITCHER_ENABLED && router.pathname !== "/home") {
			if (router.locale !== "ar")
				router
					.push(
						{
							pathname: router.pathname,
							query: router.query,
						},
						router.asPath,
						{ locale: "ar" }
					)
					.then(() => {
						document.querySelector("html")?.setAttribute("dir", "rtl");
					});
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [router.locale]);
	return (
		<AppInsightsContext.Provider value={reactPlugin}>
			<AppContext>
				<ChakraProviderContainer>
					<RtlProvider>
						<NextNProgress
							color={theme.colors.brand.mainGold}
							startPosition={0.3}
							stopDelayMs={200}
							height={3}
							showOnShallow={false}
							options={{ showSpinner: false }}
						/>
						{/* <FontResize /> */}
						<SessionProvider session={session}>
							<QueryClientProvider client={queryClient}>
								{getLayout(<Component {...pageProps} />)}
							</QueryClientProvider>
						</SessionProvider>
					</RtlProvider>
				</ChakraProviderContainer>
			</AppContext>
		</AppInsightsContext.Provider>
	);
}

export default appWithTranslation(MyApp);
