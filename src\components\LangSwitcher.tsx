import { Link } from "@chakra-ui/react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

function LangSwitcher({ onClose = () => {} }) {
	const { locale, push, pathname, query, asPath } = useRouter();
	const { t } = useTranslation();

	const nextLocale = locale === "en" ? "ar" : "en";
	const dir = locale !== "ar" ? "rtl" : "ltr";

	const onClick = () => {
		push({ pathname, query }, asPath, { locale: nextLocale }).then(() => {
			document.querySelector("html")?.setAttribute("dir", dir);
		});
		onClose();
	};

	return (
		<Link
			fontSize={{ base: "md", md: "lg" }}
			color={"brand.mainGold"}
			onClick={onClick}
			//textDecoration={"underline"}
		>
			{t("altLanguage")}
		</Link>
	);
}

export default LangSwitcher;
