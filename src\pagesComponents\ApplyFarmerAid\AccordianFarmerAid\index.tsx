import {
	Accordion,
	AccordionItem,
	AccordionButton,
	Box,
	Heading,
	AccordionPanel,
} from "@chakra-ui/react";
import { ThreeVerticalDots } from "components/Icons";

function AccordionFarmerAid({
	currentStep = 0,
	sectionsArray,
	showIcon = false,
	hideBottom = false,
}) {
	return (
		<Accordion index={currentStep}>
			{sectionsArray?.map((child, idx) => {
				let pb = hideBottom ? "unset" : 4;
				if (!hideBottom && currentStep === idx) pb = 2;
				return (
					<AccordionItem
						borderColor="transparent"
						hidden={currentStep !== idx}
						key={idx}
						mt={idx > 0 ? 4 : 0}
						borderRadius="1px"
					>
						{child.title && (
							<AccordionButton py={4} pb={pb} px={6} _hover={{ bg: "white", cursor: "unset" }}>
								<Box flex="1" textAlign="left">
									<Heading size="md" mb="1.5rem" fontSize="lg" fontWeight="bold" flexGrow={1}>
										{child.title}
									</Heading>
								</Box>
								{showIcon && <ThreeVerticalDots transform="scale(0.9)" color="brand.gray.500" />}
							</AccordionButton>
						)}
						{!child.title && <AccordionButton p={0}></AccordionButton>}
						<AccordionPanel pb={6} pt={hideBottom ? "-0.75rem" : 0} px={6}>
							{child.element}
						</AccordionPanel>
					</AccordionItem>
				);
			})}
		</Accordion>
	);
}

export default AccordionFarmerAid;
