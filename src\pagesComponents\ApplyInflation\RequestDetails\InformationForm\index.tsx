import { Box } from "@chakra-ui/react";
import InformationForm from "./InformationForm";

function RequestDetailsForm({
	innerText,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
	userAge = 0,
	IsInflationNominatedCaseEdit = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<InformationForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
				userAge={userAge}
				IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
