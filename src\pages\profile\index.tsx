import { Box } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ReactElement, useState } from "react";
import ProfilePage from "pagesComponents/Profile";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import {
	formatUaePhoneNumberIntl,
	getEmiratesIdFromToken,
	validateEmail,
	validateUaePhoneNumber,
} from "utils/helpers";
import { updateProfile } from "services/frontend";
import { useQuery, useQueryClient } from "react-query";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";

function Profile({ profile }: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["common", "profile"]);
	const [preferredEmailAddress, setPreferredEmailAddress] = useState(
		profile.Data?.PreferredEmail || ""
	);
	const [preferredMobileNumber, setPreferredMobileNumber] = useState(
		formatUaePhoneNumberIntl(profile.Data?.PreferredPhoneNumber) || ""
	);
	const [preferredEmailAddressError, setPreferredEmailAddressError] = useState(false);
	const [preferredMobileNumberError, setPreferredMobileNumberError] = useState(false);
	const [profileImage, setProfileImage] = useState<string | null>(profile.Data?.Image || null);
	const [profileImageChanged, setProfileImageChanged] = useState(false);
	const toast = useAppToast();

	const { refetch: callUpdateProfile, isFetching: updateProfileLoading } = useQuery(
		["updateProfile"],
		() =>
			updateProfile(
				preferredEmailAddress,
				preferredMobileNumber.length > 5
					? `0${preferredMobileNumber?.split("+971")?.[1] || ""}`
					: "",
				profileImageChanged ? profileImage : undefined
			),
		{ enabled: false }
	);
	const queryClient = useQueryClient();

	const onPreferredEmailAddressChange = (e) => {
		setPreferredEmailAddressError(false);
		setPreferredEmailAddress(e.target.value);
	};

	const onChangePreferredMobileNumber = (e) => {
		setPreferredMobileNumberError(false);
		const value = e.target.value;
		if (value.startsWith("+9715")) return setPreferredMobileNumber(value);
		if (!value.startsWith("+9715") || value.length < 5) return setPreferredMobileNumber("+9715");
		setPreferredMobileNumber(`+9715${e.target.value}`);
	};

	const onUpdateProfile = async () => {
		let hasError = false;
		let errorMessage = "";
		if (preferredEmailAddress && !validateEmail(preferredEmailAddress)) {
			errorMessage += t("profile:wrongEmailAddress");
			setPreferredEmailAddressError(true);
			hasError = true;
		}
		if (preferredMobileNumber.length > 5 && !validateUaePhoneNumber(preferredMobileNumber)) {
			if (errorMessage) errorMessage += "\n";
			errorMessage += t("profile:wrongPreferredMobileNumber");
			setPreferredMobileNumberError(true);
			hasError = true;
		}

		if (hasError) {
			toast({
				title: t("profile:validate"),
				description: errorMessage,
				status: "error",
			});
			return;
		}

		const { data: resp } = await callUpdateProfile();

		if (resp?.IsSuccess) {
			toast({
				title: t("profile:profileUpdateSuccess"),
				description: t("profile:profileUpdateSuccessDescription"),
				status: "info",
			});
			queryClient.refetchQueries({ queryKey: ["profileImage"] });
		} else {
			toast({
				title: t("genericErrorTitle"),
				description: t("genericErrorDescription"),
				status: "error",
			});
		}
	};

	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 8 }} w="100%">
			<ProfilePage
				profileData={profile.Data}
				profileImage={profileImage}
				setProfileImage={setProfileImage}
				setProfileImageChanged={setProfileImageChanged}
				preferredEmailAddress={preferredEmailAddress}
				preferredMobileNumber={preferredMobileNumber}
				onPreferredEmailAddressChange={onPreferredEmailAddressChange}
				onChangePreferredMobileNumber={onChangePreferredMobileNumber}
				onUpdateProfile={onUpdateProfile}
				preferredEmailAddressError={preferredEmailAddressError}
				preferredMobileNumberError={preferredMobileNumberError}
				updateProfileLoading={updateProfileLoading}
			/>
		</Box>
	);
}
export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const profile = await BackendServices.retrieveContact(emiratesId);
	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "profile", "login"])),
			profile,
		},
	};
}

Profile.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default Profile;
