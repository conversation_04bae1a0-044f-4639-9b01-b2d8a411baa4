import { Box, Text, Grid, GridItem, Flex, Image } from "@chakra-ui/react";
import GoalView from "components/GoalView";
import { GoalOneIcon, GoalTwoIcon, GoalThreeIcon } from "components/Icons";
import OrderedListGoldenNumber from "components/Lists/OrderedListGoldenNumber";
import React from "react";
import { AboutContent } from "utils/strapi/about";
import { getImageUrls } from "utils/strapi/helpers";

const AboutProgramTab = ({ content }: { content: AboutContent }) => {
	// const { t } = useTranslation("about");
	// let goalsList = [
	// 	"Individual Allowance",
	// 	"Spouse Allowance",
	// 	"Child Allowance",
	// 	"Job Seekers (Aged 25-44)",
	// 	"Unemployed (Aged 45 or above)",
	// ];
	// let bonusCategoriesList = [
	// 	t("bonusCategoriesListItem1"),
	// 	t("bonusCategoriesListItem2"),
	// 	t("bonusCategoriesListItem3"),
	// 	t("bonusCategoriesListItem4"),
	// 	t("bonusCategoriesListItem5"),
	// ];
	const icons = {
		GoalOneIcon,
		GoalThreeIcon,
		GoalTwoIcon,
	};
	const matches = content.allowances.header.match(/"([^"]*)"/)!;
	const text1 = content.allowances.header.substring(0, matches.index) || ""; // Text before the double quotes
	const text2 = matches[1]; // Text within the double quotes
	return (
		<Box>
			<Box>
				{/* <Box fontSize={"md"} color={"brand.mainGold"} fontWeight="bold">
					{t("brief")}
					<Text fontWeight="normal" color="brand.textColor" lineHeight="1.5rem">
						{t("briefDesc")}
					</Text>
				</Box> */}
				{/* <Box fontSize={"md"} color={"brand.mainGold"} fontWeight="bold">
					{t("overview")}
					<Text fontWeight="normal" color="brand.textColor" lineHeight="1.5rem">
						{t("overviewDesc")}
					</Text>
				</Box> */}
				<Box my={20} width={"100%"}>
					<Text fontSize={"4xl"} fontWeight={600} color={"brand.textColor"} pb={12}>
						{content.goals_header}
					</Text>
					<Grid
						rowGap={{ base: 2.5, md: 8 }}
						columnGap={36}
						templateColumns="repeat(3, 1fr)"
						templateRows="auto"
						width={"100%"}
					>
						{content.goals.map((goal) => {
							const Icon = icons[goal.goal_icon];
							return (
								<GridItem key={goal.id} colSpan={{ base: 3, md: 1 }}>
									<GoalView
										Title={goal.goal_title}
										Desc={goal.goal_body}
										Icon={<Icon w={"60px"} h={"60px"} />}
									/>
								</GridItem>
							);
						})}
					</Grid>
					{/* <OrderedListGoldenNumber
						arr={goalsList}
						styles={{
							listYMargin: "0px",
							textFontWeight: "normal",
							textLineHeight: "1.5rem",
							listLineHeight: "1.5rem",
							listFontWeight: "bold",
							listItemFontSize: "md",
						}}
					/> */}

					<Flex mt={6} flexDirection={{ base: "column", md: "row" }}>
						<Image
							src={getImageUrls(content.allowances.image.url)}
							rounded={"base"}
							alt={"happy family"}
							maxW={{ base: "100%", md: "50%" }}
							objectFit="cover"
						/>
						<Box ml={{ base: 0, md: 12 }} mt={{ base: 12, md: 0 }}>
							<Box>
								<Text fontSize={"4xl"} color={"brand.mainHeaderColor"} fontWeight={600}>
									{text1}
								</Text>
								<Text fontSize={"4xl"} color={"brand.mainGold"} fontWeight={600}>
									{text2}
								</Text>
							</Box>

							<Text my={6} color={"brand.mainHeaderColor"} fontSize={"lg"}>
								{content.allowances.sub_header}
							</Text>
							<OrderedListGoldenNumber
								arr={content.allowances.allowances_list.map((s) => s.text)}
								styles={{
									listYMargin: "0px",
									textFontWeight: "normal",
									textLineHeight: "2.5rem",
									listLineHeight: "2.5rem",
									listFontWeight: "400",
									listItemFontSize: "md",
								}}
							/>
						</Box>
					</Flex>
				</Box>

				{/* <Box mt={6}>
					<Text fontSize={"md"} color={"brand.mainGold"} fontWeight="bold">
						{t("beneficators2")}
					</Text>
					<Text fontWeight="normal" color="brand.textColor" lineHeight="1.5rem">
						{t("bonusCategoriesDesc")}
					</Text>
					<OrderedListGoldenNumber
						arr={bonusCategoriesList}
						styles={{
							listYMargin: "0px",
							textFontWeight: "normal",
							textLineHeight: "1.5rem",
							listLineHeight: "1.5rem",
							listFontWeight: "bold",
							listItemFontSize: "md",
						}}
					/>
				</Box> */}
			</Box>
		</Box>
	);
};

export default AboutProgramTab;
