import { formatAmount } from "utils/formatters";
import { WomenIDSCase } from "../calculator";
import { localizedLookups } from "../lookups";

export const WomenIDSTableBuilder = (data: WomenIDSCase, locale: string = "ar", reason: string) => {
	const { personalInformation } = data;
	const personalInformationData = {
		header: "personalInformation",
		data: [
			{ label: "reasonForApplying", value: convertLookup(reason, "reason", locale) },
			{
				label: "currentSituation",
				value: convertLookup(personalInformation.currentSituation!, "currentSituation", locale),
			},
			{
				label: "ageGroup",
				value: convertLookup(personalInformation.ageGroupWomen!, "ageGroupWomen", locale),
			},
		],
	};

	const haveChildrenData = {
		label: "haveChildren",
		value: convertLookup(personalInformation.haveChildren!, "boolean", locale),
	};
	let numberOfChildren: any | null = null;
	let isChildrenPOD: any = null;
	let childrenPODNumber: any = null;

	personalInformationData.data.push(haveChildrenData);

	const childAttributesData = {
		label: "childAttributesTable",
		value: convertLookup(personalInformation.childAttributes!, "childAttributes", locale),
	};
	personalInformationData.data.push(childAttributesData);

	if (personalInformation.haveChildren === "1") {
		numberOfChildren = {
			label: "numberOfChildren",
			value: String(personalInformation.numberOfChildren),
		};
		isChildrenPOD = {
			label: "isChildrenPOD",
			value: convertLookup(personalInformation.isChildrenPOD!, "boolean", locale),
		};
		if (personalInformation.isChildrenPOD === "1")
			childrenPODNumber = {
				label: "numberOfPODChildren",
				value: String(personalInformation.numberOfPODChildren),
			};

		personalInformationData.data.push(numberOfChildren);
		personalInformationData.data.push(isChildrenPOD);

		if (childrenPODNumber) personalInformationData.data.push(childrenPODNumber);
	}

	const incomeData = {
		header: "incomeInformation",
		data: [
			{
				label: "totalIncome",
				value: formatAmount(String(personalInformation.totalIncome), 0),
			},
		],
	};
	return [personalInformationData, incomeData];
};

export const convertLookup = (
	lookupValue: string,
	lookupName: string,
	locale: string = "ar"
): string => {
	const lookups = localizedLookups(locale);
	return lookups[lookupName].find((lookup) => lookup.value === lookupValue).label;
};
