import {
	Box,
	Flex,
	Grid,
	GridItem,
	Image,
	Text,
	useBreakpoint,
	Center,
	Link,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import WelcomeMessage from "pagesComponents/WelcomeMessage";
import React, { ReactElement, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import MocdBoxComp from "pagesComponents/MocdBoxComp";
import { ApplyIcon, ApplyToWhomIcon, ApplyFormerIcon } from "components/Icons";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import Slider from "react-slick";
import { getStrapiContent } from "services/strapi";
import { SmartServicesPage } from "utils/strapi/smartServices";
import { InferGetServerSidePropsType } from "next";
import { getImageUrls } from "utils/strapi/helpers";
import { BackendServices } from "services/backend";
import { getEmiratesIdFromToken } from "utils/helpers";
function SmartServices({
	content,
	idCase,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["smartservice"]);
	const { query, isReady, replace, locale, push } = useRouter();
	const [callingApi, setCallingApi] = useState(false);
	const [allowanceCalled, setAllowanceCalled] = useState(false);
	const toast = useAppToast();
	// comment for testing
	useEffect(() => {
		if (query.notUaeCitizen === "yes") {
			toast({
				status: "error",
				title: t("common:genericErrorTitle"),
				description: t("onlyUaeApplyFarmer"),
			});
			setTimeout(() => {
				replace("/smart-services", undefined, { shallow: true });
			}, 1000);
		}
	}, [isReady, query]);

	// const sliderData = [
	// 	{
	// 		imageUrl: "/assets/images/sliderImg1.jpeg",
	// 		url: "https://sdgsuae-fcsa.opendata.arcgis.com/",
	// 	},
	// 	{ imageUrl: "/assets/images/sliderImg6.jpeg", url: `https://u.ae/${locale}/participate/` },
	// 	{ imageUrl: "/assets/images/sliderImg5.jpg", url: `https://bayanat.ae/${locale}` },
	// 	{ imageUrl: "/assets/images/sliderImg3.jpeg", url: `https://www.vision2021.ae` },
	// 	{ imageUrl: "/assets/images/sliderImg4.jpeg", url: "http://ms.1.ae/" },
	// 	{ imageUrl: "/assets/images/sliderImg2.jpeg", url: `https://u.ae/${locale}#/` },
	// 	{
	// 		imageUrl: "/assets/images/sliderImg7.jpg",
	// 		url: `https://171.ae/ncrmstorefront/ncrm/${locale}/?lang=${locale}`,
	// 	},
	// ];

	const settings = {
		autoplay: true,
		infinite: true,
		speed: 500,
		slidesToShow: 5,
		slidesToScroll: 1,
		initialSlide: 0,
		arrows: false,
		nextArrow: null,
		prevArrow: null,
		responsive: [
			{
				breakpoint: 1200, // width to change options
				settings: {
					slidesToShow: 4,
					slidesToScroll: 1,
					initialSlide: 0,
					infinite: false,
				},
			},
			{
				breakpoint: 900, // width to change options
				settings: {
					slidesToShow: 3,
					slidesToScroll: 1,
					initialSlide: 0,
					infinite: false,
				},
			},
			{
				breakpoint: 600,
				settings: {
					slidesToShow: 2,
					slidesToScroll: 1,
					initialSlide: 0,
					infinite: false,
				},
			},
		],
	};

	const breakpoint = useBreakpoint();

	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "howToApply",
			link: "#",
			isCurrentPage: true,
		},
	];
	const Icons = {
		ApplyFormerIcon,
		ApplyIcon,
		ApplyToWhomIcon,
	};
	// this to make the text in the double qoutation in gold color
	const matches = content.app_info.header.match(/"([^"]*)"/)!;
	const text1 = content.app_info.header.substring(0, matches.index) || ""; // Text before the double quotes
	const text2 = matches[1]; // Text within the double quotes
	const text3 = content.app_info.header.substring(matches.index! + matches[0].length) || "";
	return (
		<Box w="100%">
			<WelcomeMessage
				image={getImageUrls(content.landing_image.url)}
				mobileImg={getImageUrls(content.landing_image.url)}
				title={content.page_header}
				// breadcrumbsData={breadcrumbsData}
				description={content.page_description}
			/>
			<Box p={{ base: 4, lg: 24 }} mt={2} w="100%" bg="brand.white.50">
				<Text
					textAlign={"left"}
					fontSize={{ base: "1.5rem", lg: "4xl" }}
					fontWeight={600}
					color={"brand.textColor"}
					pb={{ base: 8, lg: 10 }}
				>
					{content.cards_header}
				</Text>
				<Grid
					rowGap={{ base: 2.5, md: 4 }}
					columnGap={4}
					templateColumns="repeat(3, 1fr)"
					templateRows="auto"
				>
					{content.services_cards.map((card, index) => {
						const Icon = Icons[card.icon];
						return (
							<GridItem
								key={card.id}
								cursor={"pointer"}
								bg="white"
								onClick={() => {
									push(card.service_url);
								}}
								colSpan={{ base: 3, md: 1 }}
								borderWidth={1}
								borderColor={"#BBBCBD"}
								borderRadius={"10px"}
								_hover={{ borderColor: "brand.mainGold" }}
							>
								<MocdBoxComp
									bg={getImageUrls(card.background_image.url)}
									title={card.header}
									description={card.body}
									buttonText={card.button_text}
									bulink={card.service_url}
									icon={<Icon w={"56px"} h={"56px"} mr={2} />}
								/>
							</GridItem>
						);
					})}
					{idCase && false && (
						<GridItem
							cursor={"pointer"}
							bg="white"
							onClick={async () => {
								push(`/${locale}/smart-services/topups-service`);
								// if (!allowanceCalled) {
								// 	setCallingApi(true);
								// 	setAllowanceCalled(true);
								// 	const data = await modifyRequest({
								// 		UpdateType: "CREATE",
								// 		ParentCaseId: idCase || undefined,
								// 		CaseDetails: {},
								// 		CaseType: 7,
								// 	});
								// 	setCallingApi(false);
								// 	if (data.Data && data?.IsSuccess) {
								// 		toast({
								// 			title: t("pleaseWaitLoadDraft", { ns: "common" }),
								// 			status: "info",
								// 		});
								// 		if (data?.Data.IdCase)
								// 			push(
								// 				`/${locale}/smart-services/how-to-apply/apply-socialaid?requestId=${data?.Data.IdCase}&applyforAllowance=true`
								// 			);
								// 	} else {
								// 		setAllowanceCalled(false);
								// 		toast({
								// 			title: t("common:genericErrorTitle"),
								// 			description: t("common:genericErrorDescription"),
								// 			status: "error",
								// 		});
								// 	}
								// }
							}}
							colSpan={{ base: 3, md: 1 }}
							borderWidth={1}
							borderColor={"#BBBCBD"}
							borderRadius={"10px"}
							_hover={{ borderColor: "brand.mainGold" }}
						>
							<MocdBoxComp
								isButton={true}
								disable={callingApi}
								bg={"../assets/images/serviceCard4.jpg"}
								title={t("common:applyingForHousing/EducationAllownce")}
								description={""}
								buttonText={t("smartservice:startTheService")}
								bulink={""}
								icon={<ApplyIcon w={"56px"} h={"56px"} mr={2} />}
							/>
						</GridItem>
					)}
				</Grid>
			</Box>
			{/* <Flex
				bg={"brand.white.100"}
				w={"100%"}
				minH={"477px"}
				position={"relative"}
				justifyContent={"center"}
				alignItems={"flex-end"}
				px={{ base: 4, lg: 0 }}
			>
				<Flex alignItems={"flex-end"} w={{ base: "100%", md: "70%" }}>
					<Image
						src={"../assets/images/iPhone-14.png"}
						h={"382px"}
						alt={"iphone"}
						position={"relative"}
						bottom={0}
						display={{ base: "none", lg: "block" }}
					/>
					<Box pb={28} pt={{ base: 4 }} pl={{ base: 2, lg: 16 }}>
						<Text fontSize={"4xl"} color={"brand.textColor"} fontWeight={600}>
							{text1}
							<Text as={"span"} color={"brand.mainGold"}>
								{text2}
							</Text>{" "}
							{text3}
						</Text>
						<Text fontSize={"xl"} my={2} color={"brand.textColor"}>
							{content.app_info.body}
						</Text>
						<Flex mt={5}>
							<Image
								src={getImageUrls(content.app_info.appstore_image.url)}
								w={{ base: "7.7rem", lg: "11rem" }}
								h={{ base: "2.45rem", lg: "3.5rem" }}
								alt={"iphone"}
							/>
							<Image
								src={getImageUrls(content.app_info.google_play_image.url)}
								w={{ base: "7.7rem", lg: "11rem" }}
								h={{ base: "2.45rem", lg: "3.5rem" }}
								alt={"iphone"}
								mx={2}
							/>
						</Flex>
					</Box>
				</Flex>
			</Flex> */}
			<Center w={"100%"} bg={"brand.white.50"} p={{ base: 5, lg: 28 }} display={{ md: "block" }}>
				<Flex flexDirection={"column"} w={"100%"} bg={"brand.white"}>
					<Text
						textAlign={"center"}
						fontSize={"4xl"}
						fontWeight={600}
						color={"brand.textColor"}
						pb={12}
					>
						{content.partner_header}
					</Text>
					<Slider {...settings} style={{ paddingBottom: "24px" }} className="slider-img">
						{content.partners.map((item, index) => {
							return (
								<Box key={index}>
									<Link href={item.url} target={"_blank"}>
										<Image
											w={"100%"}
											h={"100%"}
											maxH={"118px"}
											src={getImageUrls(item.partner_image.url)}
											alt=""
											objectFit={"contain"}
										/>
									</Link>
								</Box>
							);
						})}
					</Slider>
				</Flex>
			</Center>
			{/* <Flex display={{ base: "flex", lg: "none" }} justifyContent={"center"} bg="green">
				{" "}
				{sliderData.map((item, index) => {
					return (
						<Link key={index} href={item.url} target={"_blank"}>
							<Image w={"100%"} h={"100%"} src={item.imageUrl} alt="" objectFit={"contain"} />
						</Link>
					);
				})}
			</Flex> */}
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	const content = await getStrapiContent("/smart-services-page?populate=deep", ctx.locale);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const apiData = await BackendServices.checkAllowanceEligibility(emiratesId);
	const allowanceData = apiData.Data;
	let idCase =
		allowanceData && (allowanceData?.eligibleEducation || allowanceData?.eligibleHousing)
			? allowanceData.IdCase
			: null;
	let eligibleEducation = allowanceData?.eligibleEducation;
	let eligibleHousing = allowanceData?.eligibleHousing;
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "home", "smartservice"])),
			// Will be passed to the page component as props
			content: content.data as SmartServicesPage,
			idCase,
		},
	};
}

SmartServices.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default SmartServices;
