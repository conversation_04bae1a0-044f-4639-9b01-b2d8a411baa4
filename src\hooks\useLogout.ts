import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/router";

export const useLogout = () => {
	const { locale } = useRouter();
	const { data: session } = useSession();
	const onLogout = () => {
		if (session?.provider === "oauth") {
			signOut({ callbackUrl: `OauthSignOut/${locale}` });
		} else {
			signOut({ callbackUrl: `/${locale}/smart-services` });
		}
	};

	return onLogout;
};
