import { Radio, RadioGroup, Stack, Flex, Box } from "@chakra-ui/react";

const CustomRadioButtonField = (field: any, meta: any, props: any) => (
	<RadioGroup value={props.value} {...field} {...props}>
		<Stack spacing={10} direction={{ base: "column", md: "row" }} w="100%" mt="6">
			{props.options.map((value, idx) => (
				<Box
					key={idx}
					borderRadius="35px  35px"
					//   border="1px solid #1b1d2152"
					border={value.value === props.value ? " 2px solid #A0813E" : "1px solid #1b1d2152"}
					px={4}
					py="1"
				>
					<Radio
						w="100%"
						{...field}
						value={value.value}
						outline="1px solid #1b1d2152"
						borderColor="brand.white.50"
						_checked={{
							bg: "#A0813E",
							color: "white",
							border: "2px solid #B68A35",
							borderColor: "white",
							outline: "1px solid #B68A35",
						}}
						_focus={{
							boxShadow: "none",
						}}
						isReadOnly={props.isReadOnly}
						_readOnly={{
							_checked: {
								bg: "#B68A35",
								opacity: 0.6,
							},
						}}
						readOnly={props.isReadOnly}
					>
						<Flex fontSize="md" w="100%" fontWeight="bold">
							{value.label}
						</Flex>
					</Radio>
				</Box>
			))}
		</Stack>
	</RadioGroup>
);

export default CustomRadioButtonField;
