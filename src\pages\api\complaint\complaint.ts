import { ICrmComplaint } from "interfaces/CrmComplaint.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getEmiratesIdFromToken } from "utils/helpers";
export const config = {
	api: {
		bodyParser: {
			sizeLimit: "8mb",
		},
	},
	// Specifies the maximum allowed duration for this function to execute (in seconds)
};

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmComplaint>>
) {
	const {
		EmailAddress,
		PhoneNumber,
		Title,
		Description,
		CaseType,
		Case,
		listAttachments,
		topicId,
		serviceId,
		subServiceId,
		IsAuth,
	} = req.body;

	if (!PhoneNumber || !Description || !CaseType)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const EmirateId = IsAuth ? await getEmiratesIdFromToken(req) : "";
	const data = await BackendServices.ComplaintRequest({
		EmirateId,
		EmailAddress,
		PhoneNumber,
		Title,
		Description,
		CaseType,
		Case,
		listAttachments,
		topicId,
		serviceId,
		subServiceId,
		AppealStatus: -11,
	});

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
