import * as Yup from "yup";

const getInitialValues = {
	document: "",
	description: "",
	caseNumber: "",
};

const getValidationSchema = () => {
	return Yup.object({
		document: Yup.string().notRequired(),
		description: Yup.string().required().max(700, "DescriptionLimitationMsg").label("thisField"),
		caseNumber: Yup.string().notRequired(),
	});
};

const onChange = (event: any, formikProps: any) => {
	//console.log("formikProps amer", formikProps);
};

export { getInitialValues, onChange, getValidationSchema };
