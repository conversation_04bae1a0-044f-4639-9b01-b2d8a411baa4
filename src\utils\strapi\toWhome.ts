interface LandingImage {
	id: number;
	name: string;
	alternativeText: string | null;
	caption: string | null;
	width: number;
	height: number;
	hash: string;
	ext: string;
	mime: string;
	size: number;
	url: string;
	previewUrl: string | null;
	provider: string;
	provider_metadata: any | null;
	createdAt: string;
	updatedAt: string;
}

interface ServiceInfo {
	id: number;
	header: string;
	body: string;
	icon: string;
}

interface TargetAudience {
	id: number;
	header: string;
	body: string;
	icon: string;
}

interface ServiceDetails {
	id: number;
	header: string;
	application_time_label: string;
	application_time_value: string;
	process_duration_label: string;
	process_duration_value: string;
	process_fees_label: string;
	process_fees_value: string;
}

export interface ToWhomeContent {
	id: number;
	page_header: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	apply_button_text: string;
	landing_image: LandingImage;
	service_info: ServiceInfo;
	target_audience: TargetAudience;
	service_details: ServiceDetails;
	localizations: any[];
	apply_button_link: string;
}
