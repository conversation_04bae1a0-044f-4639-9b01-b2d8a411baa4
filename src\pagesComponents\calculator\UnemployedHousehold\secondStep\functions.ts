import * as Yup from "yup";
export const validationSchemaCase1 = Yup.object({
	totalIncome: Yup.number().required().label("thisField"),
});

const empty = {
	spouseEmployedOrRetired: "",
	nafisDate: "",
	registeredWithNafis: "",
	totalIncome: "",
};
const getValidationsSchena = (maleAndMarried: boolean, age25_44: boolean) => {
	return Yup.object({
		spouseEmployedOrRetired: Yup.string().when("totalIncome", {
			is: (totalIncome) => maleAndMarried,
			then: Yup.string().required().label("thisField"),
			otherwise: Yup.string().nullable().notRequired(),
		}),
		totalIncome: Yup.number()
			.required()
			.label("thisField")
			.typeError("ThisFieldShouldbeNumber")
			.test({
				name: "no-signs-or-dots",
				message: "PleaseEnteranIntegerNumber",
				test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
			}),
		registeredWithNafis: Yup.string().when("spouseEmployedOrRetired", {
			is: (d) => {
				//notSingle && !isFemaleAndMarried
				return age25_44;
			},
			then: Yup.string().label("thisField").required(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		nafisDate: Yup.string().when("registeredWithNafis", {
			is: (registeredWithNafis) => {
				//notSingle && !isFemaleAndMarried
				return age25_44 && registeredWithNafis === "1";
			},
			then: Yup.string().label("thisField").required(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
	});
};

const functions = {
	getValidationsSchena,
	empty,
};
export default functions;
