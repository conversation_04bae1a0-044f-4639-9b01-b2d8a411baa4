import { Grid, GridItem, Text } from "@chakra-ui/react";
import { Form, Formik } from "formik";
import { IEducationCase } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import EducationMembersTable from "./EducationMembersTable";
import EditEducationFormModal from "./EditEducationFormModal";
import * as functions from "./functions";
import { useRouter } from "next/router";

interface Props {
	onSubmit: any;
	members: any;
	setMembers: any;
	readOnly: boolean;
}

function EducationInfoForm({ onSubmit, members, setMembers, readOnly = false }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const [editMember, setEditMember] = useState<IEducationCase | null>(null);

	const onEditMember = (edittedEducationMember: IEducationCase) => {
		setMembers((state) => {
			return state.map((member) => {
				if (member.IdChild === edittedEducationMember.IdChild) {
					return edittedEducationMember;
				}
				return member;
			});
		});
		setEditMember(null);
	};

	const showEditMemberSubForm = (member: IEducationCase) => {
		setEditMember(member);
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			handleTextChange(firstArg, secondArg, formik);
		}
	};
	const handleTextChange = (event, fieldName, formik) => {
		formik.setFieldValue(fieldName, event?.target?.value || "");
	};
	const { locale } = useRouter();
	return (
		<>
			<Formik
				enableReinitialize
				initialValues={functions.getInitialValues}
				validationSchema={functions.getValidationSchema}
				onSubmit={onSubmit}
			>
				{(formik) => (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								{members.length > 0 && (
									<>
										<EducationMembersTable
											readOnly={readOnly}
											members={members}
											setEditMember={showEditMemberSubForm}
										/>
									</>
								)}
								{members.length === 0 && <Text>{t("noEducationMembersData")}</Text>}
							</GridItem>
						</Grid>
					</Form>
				)}
			</Formik>
			<EditEducationFormModal
				member={editMember}
				onEditMember={onEditMember}
				onClose={() => setEditMember(null)}
				readOnly={readOnly}
			/>
		</>
	);
}

export default EducationInfoForm;
