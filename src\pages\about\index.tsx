import React from "react";
import {
	<PERSON>,
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>b<PERSON>ane<PERSON>,
	<PERSON>b<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { ReactElement } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "react-i18next";
import AboutProgramTab from "pagesComponents/TabsContent/AboutProgramTab";
import BeneficiariesTab from "pagesComponents/TabsContent/BeneficiariesTab";
import { CloseIconDynamic, ExlamationMark } from "components/Icons";
import NextLink from "next/link";
import { useAppContext } from "context/AppContext";
import { useSession } from "next-auth/react";
import WelcomeMessage from "pagesComponents/WelcomeMessage";
import { getStrapiContent } from "services/strapi";
import { AboutContent } from "utils/strapi/about";
import { InferGetServerSidePropsType } from "next";
import { getImageUrls } from "utils/strapi/helpers";

const AboutPage = ({ content }: InferGetServerSidePropsType<typeof getServerSideProps>) => {
	const { t } = useTranslation(["about", "common"]);
	const { showBeneficiaryAlert, setShowBeneficiaryAlert } = useAppContext();
	const { status } = useSession();
	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-about"),
			id: "about",
			link: "#",
			isCurrentPage: true,
		},
	];

	const hideBeneficiaryAlert = () => setShowBeneficiaryAlert(false);

	return (
		<>
			{showBeneficiaryAlert && status === "authenticated" && (
				<Box marginX="auto" w={{ base: "100%", md: "calc(100% - 3rem)" }} pt="4">
					<Alert backgroundColor="brand.textColor" color="white">
						<ExlamationMark w="1.875rem" h="1.875rem" mr="5" color="brand.mainGold" />
						<Flex justifyContent="space-between" alignItems="center" w="100%">
							<AlertTitle fontSize="lg" fontWeight="normal">
								{t("previousBeneficiaryAlertContent.text1")}
								<Text as="span" fontWeight="bold" fontSize="xl">
									{t("previousBeneficiaryAlertContent.text2")}
								</Text>
								{t("previousBeneficiaryAlertContent.text5")}
								<Text
									as={NextLink}
									href="/my-cases"
									onClick={hideBeneficiaryAlert}
									textDecor="underline"
									fontWeight="bold"
								>
									{t("common:myCases")}
								</Text>
							</AlertTitle>
							<Flex>
								<CloseIconDynamic
									cursor="pointer"
									color="white"
									w="1.125rem"
									h="1.125rem"
									onClick={hideBeneficiaryAlert}
								/>
							</Flex>
						</Flex>
					</Alert>
				</Box>
			)}
			{/* <Flex
				w="100%"
				alignItems="center"
				flexWrap={{ base: "wrap", md: "nowrap" }}
				flexDir={{ base: "column-reverse", md: "row" }}
			>
				<Box zIndex="11" minW={{ base: "100%", md: "35%" }}>
					<Card
						bgColor="white"
						w={{ base: "94%", md: "637px" }}
						transform={{ base: "translateY(-50px)", md: "translateX(32px)" }}
						px={{ base: "8", md: "9" }}
						py={{ base: "8", md: "8" }}
						mx="auto"
						borderRadius="0"
					>
						<Flex flexDirection={"column"}>
							<Text fontSize={"1.875rem"} color={"brand.mainGold"} fontWeight={700} mb="1rem">
								{t("about-title")}
							</Text>
							<Text
								fontSize={"xl"}
								color={"brand.textColor"}
								fontWeight={500}
								lineHeight="2.813rem"
							>
								{t("about-desc")}
							</Text>
						</Flex>
					</Card>
				</Box>
				<Box minW={{ base: "100%", md: "65%" }}>
					<Image
						alt="aboutImage"
						src="/assets/images/aboutImage.jpg"
						objectFit={"cover"}
						w="100%"
						objectPosition={{ base: "0", md: "unset" }}
						h={{ base: "70vw", md: "auto" }}
						draggable="false"
					></Image>
				</Box>
			</Flex> */}
			<WelcomeMessage
				image={getImageUrls(content.landing_image.url)}
				mobileImg={getImageUrls(content.landing_image.url)}
				title={content.page_header}
				breadcrumbsData={breadcrumbsData}
				description={content.page_description}
			/>
			<Box w={{ base: "100%", md: "100%" }} mx="auto" bgColor="white">
				<Box w={"100%"}>
					<Tabs>
						<TabList border={0} pt="1.875rem" px={{ base: 4, md: 24 }} bg={"#F2F2F2"}>
							<Tab
								_selected={{
									color: "brand.textColor",
									fontWeight: "700",
									opacity: 1,
									borderColor: "brand.mainGold",
									borderBottomWidth: "4px",
								}}
								px={0}
								pb={2.5}
								me={{ base: 10, md: 15 }}
								opacity={0.5}
								fontSize={{ base: "md", md: "xl" }}
							>
								{content.about_tab_header}
							</Tab>
							<Tab
								_selected={{
									color: "brand.textColor",
									fontWeight: "700",
									opacity: 1,
									borderColor: "brand.mainGold",
									borderBottomWidth: "4px",
								}}
								px={0}
								pb={2.5}
								me={{ base: 10, md: 15 }}
								opacity={0.5}
								fontSize={{ base: "md", md: "xl" }}
							>
								{content.benf_tab_header}
							</Tab>
						</TabList>

						<TabPanels lineHeight="2.375rem" px={{ base: 4, md: 18, lg: 24 }}>
							<TabPanel p={0} my={8}>
								<AboutProgramTab content={content} />
							</TabPanel>
							<TabPanel p={0} my={12}>
								<BeneficiariesTab content={content} />
							</TabPanel>
						</TabPanels>
					</Tabs>
				</Box>
			</Box>
		</>
	);
};
export async function getServerSideProps(ctx) {
	const content = await getStrapiContent("/about-page?populate=deep", ctx.locale);
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "about"])),
			// Will be passed to the page component as props
			content: content.data as AboutContent,
		},
	};
}

AboutPage.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};

export default AboutPage;
