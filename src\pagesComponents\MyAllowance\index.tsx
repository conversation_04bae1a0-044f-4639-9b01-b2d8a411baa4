import { Button, Flex, Text } from "@chakra-ui/react";
import { BarChartIcon } from "components/Icons";
import SectionTitle from "components/SectionTitle";
import TableCustom from "components/TableCustom";
import { useTranslation } from "react-i18next";
import NextLink from "next/link";

function MyAllowance({ tableHeaders, tableBody, caption, hasFooter, footerValues, showViewAll }) {
	const { t } = useTranslation();

	return (
		<>
			<SectionTitle>
				<Flex alignItems={"center"}>
					<BarChartIcon w={7} h={7} mr={6} /> <Text>{t("myAllowance")}</Text>
				</Flex>
			</SectionTitle>
			<TableCustom
				tableHeaders={tableHeaders}
				tableBody={tableBody}
				caption={caption}
				hasFooter={hasFooter}
				footerValues={footerValues}
			></TableCustom>
			{showViewAll && tableBody?.length > 0 && (
				<Button
					as={NextLink}
					variant="secondary"
					w={{ base: "100%", md: "auto" }}
					href="/my-allowance"
				>
					{t("viewAll")}
				</Button>
			)}
		</>
	);
}

export default MyAllowance;
