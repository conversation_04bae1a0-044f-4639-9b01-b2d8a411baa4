import { Box, Text, OrderedList, ListItem } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

function TickList({
	maxW = "675px",
	arr,
	translationFile = "about",
	tickColor = "brand.mainGold",
}) {
	const { t } = useTranslation(translationFile);
	let counter = 0;

	return (
		<OrderedList lineHeight={"2.375rem"} maxW={maxW}>
			{/* {arr?.map((val, idx) => (
				<Flex key={idx} my="1.125rem" lineHeight="1.5rem" alignItems="center" gap={5}>
					{val.isSub ? <SubSpace /> : <CheckBox color={tickColor} />}
					<Text as={"h2"}>{t(val.val)}</Text>
				</Flex>
			))} */}
			{arr.map((val, idx) => {
				let Comp = val.isSub ? Text : ListItem;

				const paragraph: string[] = val.val.split("*insertNewLine");
				return (
					<Comp key={idx}>
						{paragraph.length > 0 &&
							paragraph?.map((text, i) => {
								return (
									<Text
										// mt={2}
										mb={i === paragraph.length - 1 ? 4 : 2}
										as={"span"}
										display={"block"}
										key={i}
										// marginInlineStart={val.isSub ? 10 : 0}
										marginInlineEnd={2}
									>
										{text}
									</Text>
								);
							})}
					</Comp>
				);
			})}
		</OrderedList>
	);
}
function SubSpace(key, val) {
	return (
		<Box
			key={key}
			width={1}
			height={1}
			rounded="full"
			display="inline-block"
			backgroundColor={"brand.altTextColor"}
			marginInlineStart={10}
			marginInlineEnd={2}
			mb={1}
		>
			{val}
		</Box>
	);
}
export default TickList;
