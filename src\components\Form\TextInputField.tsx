import { Input } from "@chakra-ui/react";
import { Box } from "@chakra-ui/react";
import { ErrorInput } from "components/Icons";
import { Field } from "formik";
const TextField = (field: any, meta: any, props: any) => {
	return (
		<Box position={"relative"}>
			<Field
				as={Input}
				type={props.type}
				focusBorderColor="brand.mainGold"
				borderRadius="0.3125rem"
				errorBorderColor="brand.errorFieldBorder"
				{...field}
				{...props}
				h="3.5rem"
				_placeholder={{ color: "brand.fieldPlaceholder" }}
				_disabled={{ bg: "brand.gray.300", color: "brand.gray.400", border: "0px" }}
			/>
			{meta.error && (
				<ErrorInput position={"absolute"} right={"2%"} top="23%" w={"22px"} h={"22px"} />
			)}
		</Box>
	);
};

export default TextField;
