import type { ComponentStyleConfig } from "@chakra-ui/theme";

const Checkbox: ComponentStyleConfig = {
	// The styles all button have in common
	baseStyle: {
		control: {
			bg: "transparent",
			borderColor: "#3D4C62",
			_active: {},
			_focus: {},
			_hover: {
				bg: "#3F8E50",
			},
			_checked: {
				bg: "#3F8E50",
				border: "none",
			},
		},
	},
	variants: {
		golden: {
			control: {
				_checked: {
					bg: "brand.mainGold",
				},
				borderColor: "#1b1d2152",
			},
		},
	},
};

export default Checkbox;
