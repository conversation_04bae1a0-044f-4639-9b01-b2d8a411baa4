import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export const config = {
	api: {
		bodyParser: {
			sizeLimit: "8mb",
		},
	},
};
export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<any>>
) {
	const { CaseId, BeneficiaryId, PaymentOption } = req.body;
	const Beneficiary = await getContactIdFromToken(req);

	const data = await BackendServices.RefundBreakdownSimulator(CaseId, BeneficiaryId, PaymentOption);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
