import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>r, Flex } from "@chakra-ui/react";
import Disclaimer from "../Disclaimer";
import { useTranslation } from "react-i18next";
import { WomenIDSCase } from "../calculator";
import SecondStep from "./SecondStep";
import FirstStep from "./FirstStep";
import { useRouter } from "next/router";

export default function WomenInDFSCase({
	stepManager,
	reason,
}: {
	stepManager: {
		nextStep: () => void;
		prevStep: () => void;
		reset: () => void;
		setStep: (step: number) => void;
		activeStep: number;
	};
	reason: string;
}) {
	const { activeStep, nextStep, prevStep, reset, setStep } = stepManager;
	const { t } = useTranslation(["calculator", "common"]);
	const [eligible, setEligible] = useState(false);

	const handleChangeEvent = (type, firstArg, secondArg, formik, formKey, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, formKey, isFieldArray, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, formKey, isFieldArray);
		}
	};
	const [formData, setFormData] = useState<WomenIDSCase>({
		personalInformation: {},
	});

	const handleTextChange = (event, fieldName, formik, formKey, isFieldArray, type) => {
		setFormData((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (newState[formKey][parentFieldName]?.length !== formik.values[parentFieldName])
					newState[formKey][parentFieldName] = formik.values[parentFieldName].map((val) => {
						let newVal = {};
						Object.keys(val).forEach((key) => {
							if (typeof val[key] === "object") {
								newVal[key] = val[key].value || "";
							} else {
								newVal[key] = val[key];
							}
						});
						return newVal;
					});
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				) {
					if (type === "datetime") {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] = event || "";
					} else {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
							event?.target?.value || "";
					}
				}
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = event?.target?.value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, formKey, isFieldArray) => {
		setFormData((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				)
					newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
						value?.value || value || "";
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = value?.value || value || "";
				return newState;
			}
		});
		formik?.setFieldValue(fieldName, value);
	};
	const [buttonState, setFormikState] = useState({
		personalInformation: { isDisabled: true, isLoading: false },
		employment: { isDisabled: true, isLoading: false },
	});
	const handleSetFormikState = (newValues, formKey) => {
		if (
			buttonState?.[formKey]?.isDisabled !== newValues.isDisabled ||
			buttonState?.[formKey]?.isLoading !== newValues.isLoading
		) {
			setFormikState((prev) => ({ ...prev, [formKey]: newValues }));
		}
	};
	const changeElegable = (childData) => {
		setEligible(childData);
	};
	const steps = [
		{
			Component: (
				<FirstStep
					formData={formData}
					handleChange={handleChangeEvent}
					handleSetFormikState={handleSetFormikState}
					formKey="personalInformation"
				/>
			),
			formKey: "personalInformation",
		},
		{
			Component: (
				<SecondStep
					reason={reason!}
					changeElegable={changeElegable}
					formData={formData}
					formKey="employment"
				/>
			),
			formKey: "result",
		},
	];
	const goBack = () => {
		if (activeStep === 0) {
			router.push("/");
		} else {
			// prevStep();
			reset();
			setTimeout(() => {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			}, 100);
		}
	};
	const goNext = () => {
		nextStep();
		setTimeout(() => {
			window.scrollTo({
				top: 0,
				behavior: "smooth",
			});
		}, 100);
	};
	const isLastStep = activeStep === steps.length - 1;
	const router = useRouter();
	return (
		<>
			{reason && <Box my={4}>{steps[activeStep].Component}</Box>}
			<Flex justifyContent={"end"} gap={4} my={8} mt={20}>
				<Button w="12rem" variant={"secondary"} onClick={goBack}>
					{isLastStep ? t("editAnswers") : t(activeStep === 0 ? "common:cancel" : "common:back")}
				</Button>
				<Button
					w="12rem"
					variant={"primary"}
					onClick={() => {
						if (isLastStep && !eligible) {
							return router.push("/");
						} else if (isLastStep) {
							return router.push("/smart-services/how-to-apply");
						}
						goNext();
					}}
					disabled={
						buttonState?.[steps[activeStep].formKey]?.isDisabled ||
						buttonState?.[steps[activeStep].formKey]?.isLoading
					}
				>
					{isLastStep
						? !eligible
							? t("common:BacktoHome")
							: t("startTheService")
						: t("common:next")}
				</Button>
			</Flex>
			<Divider borderColor="#D0D0D0" />
			<Disclaimer />
		</>
	);
}
