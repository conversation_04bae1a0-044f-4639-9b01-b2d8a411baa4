import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	Occupations: "",
	Emirates: "",
	//jobTitle: "",
	PreferredPhoneNumber: "",
	EmiratesID: "",
	caseID: "",
	IDNBackNumber: "",
	alternativeNumber: "",
	Area: "",
	AlternativeEmail: "",
	Center: "",
	MaritalStatus: "",
};
const englishLettersOnly = /^[^\u0600-\u06FF]+$/;

const getValidationSchema = () => {
	return Yup.object({
		Occupations: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
		Emirates: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
		//jobTitle: Yup.string().notRequired().nullable().label("thisField"),
		PreferredPhoneNumber: Yup.string().notRequired().nullable(),
		caseID: Yup.string().notRequired().nullable(),
		IDNBackNumber: Yup.string().notRequired().nullable(),
		EmiratesID: Yup.string().notRequired().nullable(),
		alternativeNumber: Yup.string()
			.required()
			.label("thisField")
			.matches(/^05(\d){8}$/, "uaeMobileNumberError"),
		AlternativeEmail: Yup.string()
			.email("wrongEmailAddress")
			.matches(englishLettersOnly, "wrongEmailAddress")
			.required()
			.label("thisField"),
		Area: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
		Center: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required()
			.typeError("thisField"),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("formik", event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
