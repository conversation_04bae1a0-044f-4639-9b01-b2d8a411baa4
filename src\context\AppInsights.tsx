import { ApplicationInsights } from "@microsoft/applicationinsights-web";
import { ReactPlugin } from "@microsoft/applicationinsights-react-js";
import { CONNECTION_STRING } from "config";

const defaultBrowserHistory = {
	url: "/",
	location: { pathname: "" },
	listen: () => {},
};

let browserHistory: any = defaultBrowserHistory;
if (typeof window !== "undefined") {
	browserHistory = { ...browserHistory, ...window.history };
	browserHistory.location.pathname = browserHistory?.state?.url;
}

var reactPlugin = new ReactPlugin();
var appInsights = new ApplicationInsights({
	config: {
		connectionString: CONNECTION_STRING,
		extensions: [reactPlugin],
		extensionConfig: {
			[reactPlugin.identifier]: { history: browserHistory },
		},
	},
});

if (typeof window !== "undefined" && CONNECTION_STRING !== undefined) {
	appInsights.loadAppInsights();
}

export { appInsights, reactPlugin };
