import { ChevronUpIcon } from "@chakra-ui/icons";
import { Button, Popover, PopoverContent, PopoverTrigger, VStack, Text } from "@chakra-ui/react";
import { LogoutIcon } from "components/Icons";
import { useTranslation } from "next-i18next";
import ProfileImage from "./ProfileImage";
import NextLink from "next/link";
import { useLogout } from "hooks/useLogout";

const ProfileMenu = () => {
	const { t } = useTranslation();
	const logoutHandler = useLogout();

	return (
		<>
			<Popover placement="bottom-end">
				{({ isOpen, onClose }) => (
					<>
						<PopoverTrigger>
							<Button>
								<ProfileImage />
								<ChevronUpIcon
									ms={2}
									transform={isOpen ? "scaleY(1)" : "scaleY(-1)"}
									transition={"transform 150ms"}
								/>
							</Button>
						</PopoverTrigger>
						<PopoverContent maxWidth={"195px"} dir={"auto"}>
							<VStack
								bg={"brand.white.50"}
								borderWidth={"1px"}
								borderColor={"brand.fieldBorder"}
								boxShadow={"0px 7px 9px rgba(0, 0, 0, 0.12)"}
								spacing={"0px"}
								onClick={onClose}
							>
								<Button
									as={NextLink}
									w={"100%"}
									justifyContent={"flex-start"}
									fontSize={"sm"}
									fontWeight={"normal"}
									href="/profile"
								>
									<Text as="span">{t("myProfile")}</Text>
								</Button>
								<Button
									w={"100%"}
									justifyContent={"space-between"}
									fontSize={"sm"}
									fontWeight={"normal"}
									onClick={() => {
										logoutHandler();
									}}
								>
									<Text>{t("logOut")}</Text>
									<LogoutIcon />
								</Button>
							</VStack>
						</PopoverContent>
					</>
				)}
			</Popover>
		</>
	);
};

export default ProfileMenu;
