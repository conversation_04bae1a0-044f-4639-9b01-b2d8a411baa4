import { ChildInDFSCase } from "../calculator";

export class ChildInDFSCalculator {
	public static calculateSiblings(data: ChildInDFSCase) {
		const { personalInformation } = data;
		if (personalInformation.haveSiblings === "1") {
			const numberOfPodSiblings = Number(personalInformation.numberOfPODSiblings);

			let podChildsAllowance = 0;
			let normalChildsAllowance = 0;

			if (personalInformation.isSiblingsPOD === "1") {
				podChildsAllowance = 5000 * numberOfPodSiblings;
			}
			const numberOfSiblings =
				Number(personalInformation.numberOfSiblings) - (numberOfPodSiblings || 0);
			if (numberOfSiblings === 1) {
				normalChildsAllowance = 2400;
			} else if (numberOfSiblings === 2) {
				normalChildsAllowance = 4000;
			} else if (numberOfSiblings === 3) {
				normalChildsAllowance = 5600;
			} else if (numberOfSiblings > 3) {
				normalChildsAllowance = 5600 + 800 * (numberOfSiblings - 3);
			}
			return normalChildsAllowance + podChildsAllowance;
		}
		return 0;
	}
	public static calculateSelfAllowance(data: ChildInDFSCase) {
		return 5000;
	}

	public static calculate(data: ChildInDFSCase) {
		const self = this.calculateSelfAllowance(data);
		const siblings = this.calculateSiblings(data);
		const final = self + siblings - Number(data.personalInformation.totalIncome);
		const isEligble = final > 0;
		return {
			self,
			siblings,
			final,
			eightyPercant: final * 0.8,
			isEligble,
		};
	}
}
