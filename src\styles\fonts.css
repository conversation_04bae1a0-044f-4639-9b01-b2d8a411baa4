@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue-Thin.woff2") format("woff2");
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue-Medium.woff2") format("woff2");
	font-weight: 500;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue-Italic.woff2") format("woff2");
	font-weight: normal;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue.woff2") format("woff2");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue-Bold.woff2") format("woff2");
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Helvetica Neue";
	src: url("/fonts/HelveticaNeue-Light.woff2") format("woff2");
	font-weight: 300;
	font-style: normal;
	font-display: swap;
}

/* these fonts are not working correctly
@font-face {
	font-family: "AXtManal";
	src: url("/fonts/AXtManal.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "AXtManal";
	src: url("/fonts/AXtManalBold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal;
	font-display: swap;
} */

@font-face {
	font-family: "Univers Next Arabic";
	src: url("/fonts/UniversNextArabic-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Univers Next Arabic";
	src: url("/fonts/Univers Next W04 330 Light.ttf") format("truetype");
	font-weight: light;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Univers Next Arabic";
	src: url("/fonts/UniversNextArabic-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

/* @font-face {
	font-family: "Noto Kufi Arabic";
	src: url("/fonts/NotoKufiArabic-VariableFont_wght.ttf") format("truetype");
} */
