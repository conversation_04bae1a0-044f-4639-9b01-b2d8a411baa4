import { But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Grid, GridItem, Text } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { TrashIcon } from "components/Icons";
import ModalDialog from "components/ModalDialog";
import { useFormContext } from "context/FormContext";
import { FieldArray, Form, Formik } from "formik";
import { useTranslation } from "next-i18next";
import React, { useEffect, useRef, useState } from "react";
import { formatAmount } from "utils/formatters";
import * as functions from "./functions";
import { AddIcon } from "@chakra-ui/icons";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	handleAddDeleteFieldArray,
	initialData,
	readOnly = false,
}) {
	const { t } = useTranslation(["forms", "common"]);
	const [isModalShow, setIsModalShow] = useState({
		incomes: false,
		pensions: false,
		tradeLicenses: false,
		RentalIncomes: false,
	});
	const [incomeID, setIncomeID] = useState("");
	const [pensionID, setPensionID] = useState("");
	const [tradeLicenseID, setTradeLicenseID] = useState("");
	const [rentalIncomeID, setRentalIncomeID] = useState("");

	const { lookups } = useFormContext();
	const formikRef: any = useRef();

	const handleDeleteIncomeModal = (id, type) => {
		if (type === "incomes") {
			setIncomeID(id);
		} else if (type === "pensions") {
			setPensionID(id);
		} else if (type === "tradeLicenses") {
			setTradeLicenseID(id);
		} else if (type === "RentalIncomes") {
			setRentalIncomeID(id);
		}
		handleShowHideModal(true, type);
	};
	const handleDeleteIncome = (remove, type) => {
		let selectedID = "";
		if (type === "incomes") {
			selectedID = incomeID;
			remove(incomeID);
		} else if (type === "pensions") {
			selectedID = incomeID;
			remove(pensionID);
		} else if (type === "tradeLicenses") {
			selectedID = tradeLicenseID;
			remove(tradeLicenseID);
		} else if (type === "RentalIncomes") {
			selectedID = rentalIncomeID;
			remove(rentalIncomeID);
		}
		handleAddDeleteFieldArray("delete", type, formKey, null, selectedID);
		handleShowHideModal(false, type);
	};

	const handleShowHideModal = (hideOrShow, type) => {
		setIsModalShow((prev) => ({ ...prev, [type]: hideOrShow }));
	};
	const updateDropdownValues = () => {
		let originalInitialValues = { ...functions.getInitialValues };
		Object.keys(initialData).forEach((key) => {
			if (Array.isArray(initialData[key])) {
				let arrayItem = initialData[key];
				originalInitialValues[key] = initialData[key]
					? JSON.parse(JSON.stringify(initialData[key]))
					: initialData[key];

				arrayItem.forEach((innerObject, innerObjectIndex) => {
					for (let innerKey in innerObject) {
						if (innerKey in lookups) {
							let indexOfItem = lookups[innerKey].findIndex(
								(val) => val.value === innerObject[innerKey]
							);
							if (indexOfItem >= 0)
								originalInitialValues[key][innerObjectIndex][innerKey] =
									lookups[innerKey][indexOfItem];
						}
					}
				});
			} else {
				if (key in lookups) {
					let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				} else {
					originalInitialValues[key] = initialData[key]
						? JSON.parse(JSON.stringify(initialData[key]))
						: initialData[key];
				}
			}
		});
		// // Add an object if its empty
		Object.keys(originalInitialValues).forEach((val) => {
			if (
				Array.isArray(originalInitialValues[val]) &&
				originalInitialValues[val].length === 0 &&
				functions.getInitialValues[val]?.length > 0
			) {
				originalInitialValues[val].push(
					JSON.parse(JSON.stringify(functions.getInitialValues[val][0]))
				);
			}
		});
		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
			innerRef={formikRef}
			validateOnMount
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<FormField
									type="radio"
									label={t("householdHeadContributes")}
									tooltip={t("householdHeadContributesTooltip")}
									name="householdHeadContributes"
									value={formik.values["householdHeadContributes"]}
									options={lookups.Boolean}
									isReadOnly={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent(
											"radio",
											firstArg,
											"householdHeadContributes",
											formik,
											formKey
										);
									}}
								/>
							</GridItem>
							{formik.values["householdHeadContributes"] === "yes" && (
								<FieldArray name="incomes">
									{({ remove, push }) => (
										<>
											<ModalDialog
												isModalShow={isModalShow.incomes}
												handleOnClick={() => {
													handleDeleteIncome(remove, "incomes");
												}}
												handleOnClose={() => {
													handleShowHideModal(false, "incomes");
												}}
												confirmText={t("confirm", { ns: "common" })}
												cancelText={t("cancel", { ns: "common" })}
												imgSrc="/assets/images/deleteIcon.png"
												headerTitle={t("deleteIncome")}
												confirmationMessage={t("deleteIncomeText")}
												undoneAction={t("deleteCantBeUndone")}
											/>
											{formik.values.incomes.length > 0 &&
												formik.values.incomes.map((item, index) => (
													<React.Fragment key={index}>
														{formik.values.incomes.length > 1 && (
															<GridItem colSpan={{ base: 2, md: 2 }}>
																{index < formik.values.incomes.length &&
																	index > 0 &&
																	formik.values.incomes.length > 1 && <Divider mb={6} />}
																<Flex gap={4}>
																	<GridItem colSpan={{ base: 2, md: 2 }}>
																		<Text fontWeight="bold">
																			{t("income")} {formik.values.incomes.length > 1 && index + 1}
																		</Text>
																	</GridItem>

																	{!readOnly && (
																		<Flex
																			alignItems="center"
																			border="1px"
																			borderColor="brand.gray.250"
																			borderRadius="0.42rem"
																			px={1.5}
																			cursor="pointer"
																			onClick={() => handleDeleteIncomeModal(index, "incomes")}
																		>
																			<TrashIcon
																				color="brand.gray.400"
																				h="12px"
																				w="10px"
																				transform="scale(1.1,1.1)"
																			/>
																		</Flex>
																	)}
																</Flex>
															</GridItem>
														)}
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="selectableTags"
																value={item.IncomeTypes}
																options={lookups.IncomeTypes}
																isRequired={true}
																isDisabled={readOnly}
																name={`incomes.${index}.IncomeTypes`}
																label={t("IncomeTypes")}
																tooltip={t("incomeSourceToolTip")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`incomes.${index}.IncomeTypes`]}
																touched={formik.touched[`incomes.${index}.IncomeTypes`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"selectableTags",
																		firstArg,
																		`incomes.${index}.IncomeTypes`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.incomeAmount}
																isRequired={true}
																customFormat={formatAmount}
																isDisabled={readOnly}
																name={`incomes.${index}.incomeAmount`}
																label={t("incomeAmount")}
																tooltip={t("incomeAmountToolTip")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`incomes.${index}.incomeAmount`]}
																touched={formik.touched[`incomes.${index}.incomeAmount`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`incomes.${index}.incomeAmount`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.companyName}
																isRequired={false}
																isDisabled={readOnly}
																name={`incomes.${index}.companyName`}
																label={t("companyName")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`incomes.${index}.companyName`]}
																touched={formik.touched[`incomes.${index}.companyName`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`incomes.${index}.companyName`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
													</React.Fragment>
												))}
											{!readOnly && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<Button
														variant="outline"
														p="2px"
														onClick={() =>
															push({ IncomeTypes: "", incomeAmount: "", companyName: "" })
														}
														w={{ base: "100%", md: "auto" }}
													>
														<AddIcon boxSize={3} mr={3} />
														<Text fontWeight="bold">{t("addMoreIncome")}</Text>
													</Button>
												</GridItem>
											)}
										</>
									)}
								</FieldArray>
							)}

							<GridItem colSpan={{ base: 2, md: 2 }}>
								<FormField
									type="radio"
									label={t("householdHeadPension")}
									tooltip={t("householdHeadPensionTooltip")}
									name="householdHeadPension"
									value={formik.values["householdHeadPension"]}
									options={lookups.Boolean}
									isReadOnly={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent("radio", firstArg, "householdHeadPension", formik, formKey);
									}}
								/>
							</GridItem>
							{formik.values["householdHeadPension"] === "yes" && (
								<FieldArray name="pensions">
									{({ remove, push }) => (
										<>
											<ModalDialog
												isModalShow={isModalShow.pensions}
												handleOnClick={() => {
													handleDeleteIncome(remove, "pensions");
												}}
												handleOnClose={() => {
													handleShowHideModal(false, "pensions");
												}}
												confirmText={t("confirm", { ns: "common" })}
												cancelText={t("cancel", { ns: "common" })}
												imgSrc="/assets/images/deleteIcon.png"
												headerTitle={t("deletePension")}
												confirmationMessage={t("deletePensionText")}
												undoneAction={t("deleteCantBeUndone")}
											/>
											{formik.values.pensions.length > 0 &&
												formik.values.pensions.map((item, index) => (
													<React.Fragment key={index}>
														{formik.values.pensions.length > 1 && (
															<GridItem colSpan={{ base: 2, md: 2 }}>
																{index < formik.values.pensions.length &&
																	index > 0 &&
																	formik.values.pensions.length > 1 && <Divider mb={6} />}
																<Flex gap={4}>
																	<GridItem colSpan={{ base: 2, md: 2 }}>
																		<Text fontWeight="bold">
																			{t("pension")}{" "}
																			{formik.values.pensions.length > 1 && index + 1}
																		</Text>
																	</GridItem>

																	{!readOnly && (
																		<Flex
																			alignItems="center"
																			border="1px"
																			borderColor="brand.gray.250"
																			borderRadius="0.42rem"
																			px={1.5}
																			cursor="pointer"
																			onClick={() => handleDeleteIncomeModal(index, "pensions")}
																		>
																			<TrashIcon
																				color="brand.gray.400"
																				h="12px"
																				w="10px"
																				transform="scale(1.1,1.1)"
																			/>
																		</Flex>
																	)}
																</Flex>
															</GridItem>
														)}
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="selectableTags"
																value={item.PensionType}
																options={lookups.PensionType}
																isRequired={true}
																isDisabled={readOnly}
																name={`pensions.${index}.PensionType`}
																label={t("PensionType")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`pensions.${index}.PensionType`]}
																touched={formik.touched[`pensions.${index}.PensionType`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"selectableTags",
																		firstArg,
																		`pensions.${index}.PensionType`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="selectableTags"
																value={item.PensionAuthority}
																options={lookups.PensionAuthority}
																isRequired={true}
																isDisabled={readOnly}
																name={`pensions.${index}.PensionAuthority`}
																label={t("PensionAuthority")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`pensions.${index}.PensionAuthority`]}
																touched={formik.touched[`pensions.${index}.PensionAuthority`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"selectableTags",
																		firstArg,
																		`pensions.${index}.PensionAuthority`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.pensionAmount}
																isRequired={true}
																customFormat={formatAmount}
																isDisabled={readOnly}
																name={`pensions.${index}.pensionAmount`}
																label={t("pensionAmount")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`pensions.${index}.pensionAmount`]}
																touched={formik.touched[`pensions.${index}.pensionAmount`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`pensions.${index}.pensionAmount`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
													</React.Fragment>
												))}
											{!readOnly && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<Button
														variant="outline"
														p="2px"
														onClick={() =>
															push({ pensionAmount: "", PensionType: "", PensionAuthority: "" })
														}
														w={{ base: "100%", md: "auto" }}
													>
														<AddIcon mr={3} />
														<Text fontWeight="bold">{t("addPension")}</Text>
													</Button>
												</GridItem>
											)}
										</>
									)}
								</FieldArray>
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<FormField
									type="radio"
									label={t("householdHeadTradeLicense")}
									name="householdHeadTradeLicense"
									value={formik.values["householdHeadTradeLicense"]}
									options={lookups.Boolean}
									isReadOnly={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent(
											"radio",
											firstArg,
											"householdHeadTradeLicense",
											formik,
											formKey
										);
									}}
								/>
							</GridItem>
							{formik.values["householdHeadTradeLicense"] === "yes" && (
								<FieldArray name="tradeLicenses">
									{({ remove, push }) => (
										<>
											<ModalDialog
												isModalShow={isModalShow.tradeLicenses}
												handleOnClick={() => {
													handleDeleteIncome(remove, "tradeLicenses");
												}}
												handleOnClose={() => {
													handleShowHideModal(false, "tradeLicenses");
												}}
												confirmText={t("confirm", { ns: "common" })}
												cancelText={t("cancel", { ns: "common" })}
												imgSrc="/assets/images/deleteIcon.png"
												headerTitle={t("deleteTradeLicense")}
												confirmationMessage={t("deleteTradeLicenseText")}
												undoneAction={t("deleteCantBeUndone")}
											/>
											{formik.values.tradeLicenses.length > 0 &&
												formik.values.tradeLicenses.map((item, index) => (
													<React.Fragment key={index}>
														{formik.values.tradeLicenses.length > 1 && (
															<GridItem colSpan={{ base: 2, md: 2 }}>
																{index < formik.values.tradeLicenses.length &&
																	index > 0 &&
																	formik.values.tradeLicenses.length > 1 && <Divider mb={6} />}
																<Flex gap={4}>
																	<GridItem colSpan={{ base: 2, md: 2 }}>
																		<Text fontWeight="bold">
																			{t("tradeLicense")}{" "}
																			{formik.values.tradeLicenses.length > 1 && index + 1}
																		</Text>
																	</GridItem>

																	{!readOnly && (
																		<Flex
																			alignItems="center"
																			border="1px"
																			borderColor="brand.gray.250"
																			borderRadius="0.42rem"
																			px={1.5}
																			cursor="pointer"
																			onClick={() =>
																				handleDeleteIncomeModal(index, "tradeLicenses")
																			}
																		>
																			<TrashIcon
																				color="brand.gray.400"
																				h="12px"
																				w="10px"
																				transform="scale(1.1,1.1)"
																			/>
																		</Flex>
																	)}
																</Flex>
															</GridItem>
														)}
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.tradeLicenseAmount}
																customFormat={formatAmount}
																isRequired={true}
																isDisabled={readOnly}
																name={`tradeLicenses.${index}.tradeLicenseAmount`}
																label={t("tradeLicenseAmount")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`tradeLicenses.${index}.tradeLicenseAmount`]}
																touched={
																	formik.touched[`tradeLicenses.${index}.tradeLicenseAmount`]
																}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`tradeLicenses.${index}.tradeLicenseAmount`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
													</React.Fragment>
												))}
											{!readOnly && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<Button
														variant="outline"
														p="2px"
														onClick={() => push({ tradeLicenseAmount: "" })}
														w={{ base: "100%", md: "auto" }}
													>
														<AddIcon mr={3} />
														<Text fontWeight="bold">{t("addTradeLicense")}</Text>
													</Button>
												</GridItem>
											)}
										</>
									)}
								</FieldArray>
							)}

							<GridItem colSpan={{ base: 2, md: 2 }}>
								<FormField
									type="radio"
									label={t("householdRentalIncomes")}
									name="householdRentalIncomes"
									value={formik.values["householdRentalIncomes"]}
									isReadOnly={readOnly}
									options={lookups.Boolean}
									onChange={(firstArg) => {
										handleChangeEvent("radio", firstArg, "householdRentalIncomes", formik, formKey);
									}}
								/>
							</GridItem>
							{formik.values["householdRentalIncomes"] === "yes" && (
								<FieldArray name="RentalIncomes">
									{({ remove, push }) => (
										<>
											<ModalDialog
												isModalShow={isModalShow.RentalIncomes}
												handleOnClick={() => {
													handleDeleteIncome(remove, "RentalIncomes");
												}}
												handleOnClose={() => {
													handleShowHideModal(false, "RentalIncomes");
												}}
												confirmText={t("confirm", { ns: "common" })}
												cancelText={t("cancel", { ns: "common" })}
												imgSrc="/assets/images/deleteIcon.png"
												headerTitle={t("deleteRentalIncome")}
												confirmationMessage={t("deleteRentalIncomeText")}
												undoneAction={t("deleteCantBeUndone")}
											/>
											{formik.values.RentalIncomes.length > 0 &&
												formik.values.RentalIncomes.map((item, index) => (
													<React.Fragment key={index}>
														{formik.values.RentalIncomes.length > 1 && (
															<GridItem colSpan={{ base: 2, md: 2 }}>
																{index < formik.values.RentalIncomes.length &&
																	index > 0 &&
																	formik.values.RentalIncomes.length > 1 && <Divider mb={6} />}
																<Flex gap={4}>
																	<GridItem colSpan={{ base: 2, md: 2 }}>
																		<Text fontWeight="bold">
																			{t("RentalIncome")}{" "}
																			{formik.values.RentalIncomes.length > 1 && index + 1}
																		</Text>
																	</GridItem>

																	{!readOnly && (
																		<Flex
																			alignItems="center"
																			border="1px"
																			borderColor="brand.gray.250"
																			borderRadius="0.42rem"
																			px={1.5}
																			cursor="pointer"
																			onClick={() =>
																				handleDeleteIncomeModal(index, "RentalIncomes")
																			}
																		>
																			<TrashIcon
																				color="brand.gray.400"
																				h="12px"
																				w="10px"
																				transform="scale(1.1,1.1)"
																			/>
																		</Flex>
																	)}
																</Flex>
															</GridItem>
														)}
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.ContractNo}
																isRequired={true}
																isDisabled={readOnly}
																name={`RentalIncomes.${index}.ContractNo`}
																label={t("ContractNo")}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`RentalIncomes.${index}.ContractNo`]}
																touched={formik.touched[`RentalIncomes.${index}.ContractNo`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`RentalIncomes.${index}.ContractNo`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>

														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="datetime"
																value={item.ContractStartDate}
																isRequired={true}
																name={`RentalIncomes.${index}.ContractStartDate`}
																label={t("ContractStartDate")}
																isDisabled={readOnly}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`RentalIncomes.${index}.ContractStartDate`]}
																touched={formik.touched[`RentalIncomes.${index}.ContractStartDate`]}
																handleChangeEvent={handleChangeEvent}
																onChange={(firstArg: Date) => {
																	if (formik.values.RentalIncomes?.[index]?.ContractEndDate) {
																		formik.setFieldTouched(
																			`RentalIncomes.${index}.ContractEndDate`,
																			undefined,
																			true
																		);
																	}
																	handleChangeEvent(
																		"datetime",
																		firstArg.toISOString(),
																		`RentalIncomes.${index}.ContractStartDate`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="datetime"
																value={item.ContractEndDate}
																isRequired={true}
																name={`RentalIncomes.${index}.ContractEndDate`}
																label={t("ContractEndDate")}
																isDisabled={readOnly}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`RentalIncomes.${index}.ContractEndDate`]}
																touched={formik.touched[`RentalIncomes.${index}.ContractEndDate`]}
																minDate={
																	item.ContractStartDate
																		? new Date(item.ContractStartDate).setDate(
																				new Date(item.ContractStartDate).getDate() + 1
																		  )
																		: new Date()
																}
																handleChangeEvent={handleChangeEvent}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"datetime",
																		firstArg.toISOString(),
																		`RentalIncomes.${index}.ContractEndDate`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>

														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="selectableTags"
																value={item.rentalSource}
																options={lookups.rentalSource}
																isRequired={true}
																name={`RentalIncomes.${index}.rentalSource`}
																label={t("rentalSource")}
																isDisabled={readOnly}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`RentalIncomes.${index}.rentalSource`]}
																touched={formik.touched[`RentalIncomes.${index}.rentalSource`]}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"selectableTags",
																		firstArg,
																		`RentalIncomes.${index}.rentalSource`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
														<GridItem colSpan={{ base: 2, md: 1 }}>
															<FormField
																type="text"
																value={item.RentAmount}
																isRequired={true}
																name={`RentalIncomes.${index}.RentAmount`}
																label={t("RentAmount")}
																isDisabled={readOnly}
																placeholder={t("placeholder", { ns: "common" })}
																error={formik.errors[`RentalIncomes.${index}.RentAmount`]}
																touched={formik.touched[`RentalIncomes.${index}.RentAmount`]}
																customFormat={formatAmount}
																onChange={(firstArg) => {
																	handleChangeEvent(
																		"text",
																		firstArg,
																		`RentalIncomes.${index}.RentAmount`,
																		formik,
																		formKey,
																		true
																	);
																}}
															/>
														</GridItem>
													</React.Fragment>
												))}
											{!readOnly && (
												<GridItem colSpan={{ base: 2, md: 2 }}>
													<Button
														variant="outline"
														p="2px"
														onClick={() =>
															push({
																ContractNo: "",
																ContractStartDate: "",
																ContractEndDate: "",
																rentalSource: "",
																RentAmount: "",
															})
														}
														w={{ base: "100%", md: "auto" }}
													>
														<AddIcon mr={3} />
														<Text fontWeight="bold">{t("addRentalIncome")}</Text>
													</Button>
												</GridItem>
											)}
										</>
									)}
								</FieldArray>
							)}
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
