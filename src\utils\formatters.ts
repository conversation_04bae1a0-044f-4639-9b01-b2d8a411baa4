export const addDollarSign = (value) => "$" + (value || "");

export const formatEmiratesID = (value) => {
	try {
		let arr = [3, 7, 14];
		return addCharactersToString("-", arr, value);
	} catch {
		return value;
	}
};

export const formatLocalNumber = (value) => {
	try {
		let arr = [3, 6];
		if (value.startsWith("+")) {
			arr = [4, 6];
		}

		return addCharactersToString(" ", arr, value);
	} catch (e) {
		return value;
	}
};
export const formatAmount = (value, mfd = 2) => {
	try {
		if (!value) return value;
		let formattedValue = new Intl.NumberFormat("en-AE", {
			minimumFractionDigits: mfd,
			maximumFractionDigits: 0,
		}).format(value);
		if (formattedValue === "NaN") return value;
		return formattedValue;
	} catch (e) {
		return value;
	}
};
// COMM
// Helper functions
function addCharactersToString(character, arr, value) {
	if (!value) value = "";
	if (typeof value === "number") value = value.toString();
	let finalResult = "";
	arr.forEach((charIndex, index) => {
		let startIndex = index === 0 ? 0 : arr[index - 1];
		let endIndex = charIndex;
		finalResult += value.slice(startIndex, endIndex);
		if (value.length > endIndex) finalResult += character;
		if (index === arr.length - 1) finalResult += value.slice(endIndex, value.length);
	});
	return finalResult;
}
