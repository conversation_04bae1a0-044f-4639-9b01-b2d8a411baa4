import { Flex, Text, VStack, Box } from "@chakra-ui/react";
import ServiceRequirements from "components/DLS/ServiceRequirements";
import { AtSignIcon } from "@chakra-ui/icons";
import React from "react";
import { PersonCheckIcon } from "components/Icons";
import { SocialAidContent } from "utils/strapi/socialAid";
import { Note } from "components/DLS/ServiceDescription";
import TickList from "components/Lists/TickList";
import { ITermsList } from "interfaces/StrapiTerms.interface";

const RequiredDocumentsTab = ({
	content,
	topMargin = 8,
	isEidExp,
	isEmirates,
	userEligibleAge = true,
	eligibleInflation = true,
}: {
	content: SocialAidContent;
	topMargin?: number;
	isEidExp?: boolean;
	isEmirates?: boolean;
	userEligibleAge?: boolean;
	eligibleInflation?: boolean;
}) => {
	let termsList: ITermsList[] = [];
	// to hanlde the difference in data strucutre between Topups and Inflation
	if (Array.isArray(content.terms_conditions)) {
		termsList = content.terms_conditions.map((item) => ({
			header: item.header,
			terms: item.terms.map((t) => ({
				val: t.term_value,
				isSub: t.is_sub_term,
			})),
		}));
	} else {
		termsList = [
			{
				header: content.terms_conditions.header,
				terms: content.terms_conditions.terms.map((t) => ({
					val: t.term_value,
					isSub: t.is_sub_term,
				})),
			},
		];
	}

	return (
		<Flex w="full" gap="1.5rem" flexDir={{ base: "column", md: "row" }}>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "70%" }}>
				{termsList.length > 0 &&
					termsList.map(({ header, terms }, idx) => (
						<Details key={`${header}-${idx}`} title={header} Icon={PersonCheckIcon}>
							<Box fontSize={"lg"}>
								<TickList arr={terms} maxW="1200px" translationFile="common" />
							</Box>
						</Details>
					))}
			</VStack>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "30%" }}>
				<Note serviceDetails={content.service_details} />
				<ServiceRequirements
					required_documents={content.required_documents}
					isEidExp={isEidExp}
					isEmirates={isEmirates}
					userEligibleAge={userEligibleAge}
					eligibleInflation={eligibleInflation}
				/>
			</VStack>
		</Flex>
	);
};

function Details({ Icon = AtSignIcon, title, children }) {
	return (
		<VStack align={"start"} bg="brand.white.100" borderRadius={"10px"} p={"2rem"} w="full">
			<Icon w="2rem" h="2rem" color="brand.mainGold" border="px solid black" />
			<Text as="h2" fontWeight={500} fontSize={"1.5rem"}>
				{title}
			</Text>
			{children}
		</VStack>
	);
}

export default RequiredDocumentsTab;
