import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";
import { DIVORCED, EMPLOYEDLOWINCOME, EMPLOYED_ID } from "config";

const getInitialValues = {
	ApplyHousingAllowance: "",
	IsUtilityBillIssuedForFullyOwnedProperty: "",
	LivingSituation: "",
	ReceivingFederalLocalhousingsupport: "",
	ReceivingHousingAllowanceFromEmployer: "",
	FullOwnershipResidentialProperty: "",
	ReceivesHousingSupportFromHusband: "",
};

const getValidationSchema = (t, caseData) => {
	return Yup.object({
		ApplyHousingAllowance: Yup.string().required().label("thisField").nullable(),
		ReceivingFederalLocalhousingsupport: Yup.string().when(["ApplyHousingAllowance"], {
			is: (ApplyHousingAllowance) => {
				return ApplyHousingAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		ReceivingHousingAllowanceFromEmployer: Yup.string().when("ApplyHousingAllowance", {
			is: (ApplyHousingAllowance) => {
				return (
					ApplyHousingAllowance === "yes" &&
					(caseData.socialAidInformation.Category === EMPLOYEDLOWINCOME ||
						caseData.personalInformation.Occupations === EMPLOYED_ID)
				);
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		IsUtilityBillIssuedForFullyOwnedProperty: Yup.string().when(
			["ApplyHousingAllowance", "ReceivingHousingAllowanceFromEmployer"],
			{
				is: (ApplyHousingAllowance, ReceivingHousingAllowanceFromEmployer) => {
					return ApplyHousingAllowance === "yes" && ReceivingHousingAllowanceFromEmployer === "yes";
				},
				then: Yup.string().required().label("thisField").nullable(),
				otherwise: Yup.string().notRequired().nullable(),
			}
		),
		LivingSituation: Yup.object().when(
			[
				"ApplyHousingAllowance",
				"IsUtilityBillIssuedForFullyOwnedProperty",
				"FullOwnershipResidentialProperty",
			],
			{
				is: (
					ApplyHousingAllowance,
					IsUtilityBillIssuedForFullyOwnedProperty,
					FullOwnershipResidentialProperty
				) => {
					return (
						ApplyHousingAllowance === "yes" &&
						(IsUtilityBillIssuedForFullyOwnedProperty === "no" ||
							FullOwnershipResidentialProperty === "no")
					);
				},
				then: Yup.object().shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				}),
				otherwise: Yup.object().notRequired().nullable(),
			}
		),
		ReceivesHousingSupportFromHusband: Yup.string().when("ApplyHousingAllowance", {
			is: (ApplyHousingAllowance) => {
				return (
					ApplyHousingAllowance === "yes" && caseData.socialAidInformation.SubCategory === DIVORCED
				);
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		FullOwnershipResidentialProperty: Yup.string().when(
			["ApplyHousingAllowance", "ReceivingFederalLocalhousingsupport"],
			{
				is: (ApplyHousingAllowance, ReceivingFederalLocalhousingsupport) => {
					return ApplyHousingAllowance === "yes" && ReceivingFederalLocalhousingsupport === "no";
				},
				then: Yup.string().required().label("thisField").nullable(),
				otherwise: Yup.string().notRequired().nullable(),
			}
		),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("Formik Validation", event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
