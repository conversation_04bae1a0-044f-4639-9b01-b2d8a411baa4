import { Box, Text } from "@chakra-ui/react";
import React from "react";

export default function ServiceStep({ title = "", description = "", Icon, w = "100%" }) {
	return (
		<Box mt={{ base: 8, md: 0 }} w={w}>
			{Icon}
			<Text fontSize={"lg"} fontWeight={500} color={"brand.textColor"} mt={6} mb={3}>
				{title}
			</Text>
			<Text
				fontSize={"sm"}
				fontWeight={400}
				width={{ base: "50%", sm: "95%", lg: "100%" }}
				mx={"auto"}
			>
				{description}
			</Text>
		</Box>
	);
}
