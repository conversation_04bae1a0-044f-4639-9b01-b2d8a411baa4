import { FEEDBACK_TITLE } from "config";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<string>>
) {
	const { Name, Email, Description } = req.body;
	if (!Description)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const Title = FEEDBACK_TITLE;
	const BeneficiaryId = await getContactIdFromToken(req);

	const data = await BackendServices.createFeedback(Name, Email, Description, BeneficiaryId);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
