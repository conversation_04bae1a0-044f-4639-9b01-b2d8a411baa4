import { Box, Flex, Show } from "@chakra-ui/react";

import MainLayout from "layouts/MainLayout";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import React, { ReactElement, useEffect } from "react";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";
import Breadcrumbs from "components/Breadcrumbs";
import ApplyForRefund from "pagesComponents/ApplyForRefund";
import { CrmRefund } from "interfaces/CrmRefund.interface";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import useAppToast from "hooks/useAppToast";

import { addLocalLookups } from "utils/helpers";
function ApplyToRefund({
	caseRequest,
	caseId,
	refundInstallments,
	contactId,
	casePendingAmount,
	caseReClaimAmount,
	caseTotalRefundAmount,
	caseAccumilatedAmount,
	currentInstallment,
	InstallmentsRates,
	paymentSuccess,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["common", "tables", "smartservice"]);
	const toast = useAppToast();

	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-myCases"),
			id: "navbar-myCases",
			link: "/my-cases",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-applyToReund"),
			id: "navbar-applyToReund",
			link: "#",
			isCurrentPage: true,
		},
	];
	useEffect(() => {
		if (paymentSuccess === "true") {
			toast({
				status: "info",
				title: t("tables:PaymentDone"),
			});
		}
	}, [paymentSuccess]);
	return (
		<Box pt={{ base: 4, md: 9 }} px={8} pb={8} w="100%" minH={"50vh"}>
			<Box display={{ base: "none", md: "block" }}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Flex
				w="100%"
				py={{ base: 0, md: 4 }}
				mb={{ base: 2, md: 0 }}
				flexDirection={{ base: "column", md: "column" }}
				bg={{ base: "unset", md: "brand.white.50" }}
			>
				<Flex
					mt={{ base: 4, md: 0 }}
					width={{ base: "100%", md: "100%" }}
					height="100%"
					justifyContent="space-between"
				>
					<Show above="md">
						<Box w="100%">
							<ApplyForRefund
								CaseRef={caseRequest?.CaseDetails?.CaseRef || ""}
								caseId={caseId || ""}
								CaseDetails={caseRequest}
								refundInstallments={refundInstallments}
								contactId={contactId || ""}
								casePendingAmount={casePendingAmount}
								caseReClaimAmount={caseReClaimAmount}
								currentInstallment={currentInstallment}
								caseTotalRefundAmount={caseTotalRefundAmount}
								caseAccumilatedAmount={caseAccumilatedAmount}
								InstallmentsRates={InstallmentsRates}
							/>
						</Box>
					</Show>
				</Flex>
			</Flex>
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const caseId = ctx.query.caseId?.toString() || "";
	let paymentSuccess = ctx.query.paymentSuccess;

	const contactId = await getContactIdFromToken(ctx.req);
	const caseRequest = (await BackendServices.getRequest(caseId, contactId))?.Data || null;
	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);
	const refundInstallmentsData =
		(await BackendServices.GetCaseRefundInstallments(caseId))?.Data || null;

	let refundInstallments: CrmRefund[] = [];

	if (refundInstallmentsData)
		refundInstallments = refundInstallmentsData?.listCaseRefundInstallments;

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", [
				"common",
				"tables",
				"forms",
				"smartservice",
			])),
			caseId: caseId || null,
			caseRequest: caseRequest || null,
			refundInstallments: refundInstallments || null,
			contactId: contactId || null,
			casePendingAmount: refundInstallmentsData?.CasePendingAmount || 0,
			caseReClaimAmount: refundInstallmentsData?.CaseReClaimAmount || 0,
			currentInstallment: refundInstallmentsData?.CurrentInstallment || 0,
			caseTotalRefundAmount: refundInstallmentsData?.CaseTotalRefundAmount || 0,
			caseAccumilatedAmount: refundInstallmentsData?.CaseAccumilatedAmount || 0,
			InstallmentsRates: masterData.InstallmentsRates || null,
			paymentSuccess: paymentSuccess || null,
		},
	};
}

ApplyToRefund.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplyToRefund;
