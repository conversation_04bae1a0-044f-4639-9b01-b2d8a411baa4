import { ICrmAppeal } from "interfaces/CrmAppeal.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";
export const config = {
	api: {
		bodyParser: {
			sizeLimit: "8mb",
		},
	},
	// Specifies the maximum allowed duration for this function to execute (in seconds)
};
export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmAppeal>>
) {
	const { Case, listAttachments, Description } = req.body;

	if (!Case) return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const Beneficiary = await getContactIdFromToken(req);

	const data = await BackendServices.createAppealRequest({
		Case,
		listAttachments,
		Description,
		Beneficiary,
	});

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
