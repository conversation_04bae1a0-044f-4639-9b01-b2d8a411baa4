import { Box, Grid, GridItem, ListItem, OrderedList, Text } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState, useRef } from "react";
import { LowIncomeCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import { validationSchema } from "./functions";

export default function FirstStep({
	formData: { personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: LowIncomeCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();
	const toast = useAppToast();
	const empty = {
		gender: "",
		ageGroup: "",
		maritalStatus: "",
		isSpousesPOD: "",
		numberOfPODSpouses: "",
		haveChildren: "",
		numberOfSpouses: "",
		numberOfChildren: "",
		isChildrenPOD: "",
		numberOfPODChildren: "",
	};
	const formikRef: any = useRef();

	const lookups = localizedLookups(locale);
	const handleSelectChange = (fieldName, newValue) => {
		handleChange("selectableTags", newValue, fieldName, formikRef.current, formKey);
	};
	const updateDropdownValues = () => {
		let originalInitialValues = { ...empty };
		Object.keys(personalInformation).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === personalInformation[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];

				handleSelectChange(key, lookups[key][indexOfItem]);
			} else {
				originalInitialValues[key] = personalInformation[key]
					? JSON.parse(JSON.stringify(personalInformation[key]))
					: personalInformation[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(() => updateDropdownValues());
	}, [locale]);
	const { t } = useTranslation("calculator");
	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				formikRef.current = formik!;

				const { gender, maritalStatus, isSpousesPOD, haveChildren, isChildrenPOD } =
					formik.values as any;
				// Handle gender change
				const handleGenderChange = (value) => {
					formik.setFieldValue("gender", value);
					// formik.setFieldValue("maritalStatus", "");
					// formik.setFieldValue("ageGroup", "");
					formik.resetForm();
				};
				// console.log("erros", formik.errors);
				const maleAndMarried = gender.value === "1" && maritalStatus.value === "2";
				const notSingle = maritalStatus.value !== "1";
				const isFemaleAndMarried = gender.value === "2" && maritalStatus.value === "2";
				// if the marital is not single , and is not married female
				const withPotentialChildren =
					!!maritalStatus && !!gender && !isFemaleAndMarried && notSingle;
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || isFemaleAndMarried,
					},
					formKey
				);
				return (
					<Grid
						rowGap={{ base: 6, md: 4 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
							{t("personalInformation")}
						</Text>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("gender")}
								options={getLookUp("gender", locale)}
								placeholder={t("chooseAnOption")}
								name="gender"
								error={formik.errors.gender}
								value={formik.values.gender}
								onChange={(firstArg) => {
									// i added fun this to reset the age and marital status when gender changes
									handleGenderChange(firstArg.value);
									//////////////////////////////////////////////////
									// handleChange("selectableTags", firstArg, "gender", formik, formKey);
									handleSelectChange("gender", firstArg);
									const ifFemaleAndMarried = firstArg.value === "2" && maritalStatus.value === "2";
									if (ifFemaleAndMarried) {
										toast({
											status: "error",
											title: t("enEligble.femaleAndMarried"),
										});
									}
								}}
							/>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("ageGroup")}
								options={getLookUp("ageGroup", locale)}
								placeholder={t("chooseAnOption")}
								name="ageGroup"
								error={formik.errors.ageGroup}
								value={formik.values.ageGroup}
								onChange={(firstArg) => {
									// handleChange("selectableTags", firstArg, "ageGroup", formik, formKey);
									handleSelectChange("ageGroup", firstArg);
								}}
							/>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("maritalStatus")}
								options={getLookUp("maritalStatus", locale)}
								placeholder={t("chooseAnOption")}
								name="maritalStatus"
								error={formik.errors.maritalStatus}
								value={formik.values.maritalStatus}
								onChange={(firstArg) => {
									// handleChange("selectableTags", firstArg, "maritalStatus", formik, formKey);
									handleSelectChange("maritalStatus", firstArg);

									const isFemaleAndMarried = gender?.value === "2" && firstArg?.value === "2";
									if (isFemaleAndMarried) {
										toast({
											status: "error",
											title: t("enEligble.femaleAndMarried"),
										});
									}
								}}
							/>
						</GridItem>
						{maleAndMarried && (
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="text"
										name="numberOfSpouses"
										placeholder={t("writeAnswerHere")}
										label={t("numberOfSpouses")}
										value={formik.values.numberOfSpouses}
										error={formik.errors.numberOfSpouses}
										onChange={(firstArg) => {
											handleChange("text", firstArg, "numberOfSpouses", formik, formKey);
										}}
									/>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("isSpousesPOD")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="isSpousesPOD"
										error={formik.errors.isSpousesPOD}
										value={formik.values.isSpousesPOD}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "isSpousesPOD", formik, formKey);
										}}
									/>
								</GridItem>
								<GridItem colSpan={2}>
									<GrayBox>
										<Text>{t("mocdNote")}</Text>
									</GrayBox>
								</GridItem>
								{isSpousesPOD == "1" && (
									<GridItem colSpan={2} maxW="500px">
										<FormField
											type="text"
											name="numberOfPODSpouses"
											placeholder={t("writeAnswerHere")}
											label={t("numberOfPODSpouses")}
											value={formik.values.numberOfPODSpouses}
											error={formik.errors.numberOfPODSpouses}
											onChange={(firstArg) => {
												handleChange("text", firstArg, "numberOfPODSpouses", formik, formKey);
											}}
										/>
									</GridItem>
								)}
							</>
						)}
						{withPotentialChildren && (
							<>
								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} my={4}>
										{t("emiratiChilds")}
									</Text>
									<GrayBox>
										<Text>{t("childRules-1")}</Text>
										<OrderedList>
											<ListItem>
												<Text>{t("childRules-2")}</Text>
											</ListItem>
											<ListItem>
												<Text>{t("childRules-3")}</Text>
											</ListItem>
										</OrderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("haveChildren")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="haveChildren"
										error={formik.errors.haveChildren}
										value={formik.values.haveChildren}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "haveChildren", formik, formKey);
										}}
									/>
								</GridItem>
								{haveChildren === "1" && (
									<>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												type="text"
												name="numberOfChildren"
												placeholder={t("writeAnswerHere")}
												label={t("numberOfChildren")}
												value={formik.values.numberOfChildren}
												error={formik.errors.numberOfChildren}
												onChange={(firstArg) => {
													handleChange("text", firstArg, "numberOfChildren", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												type="radio"
												label={t("isChildrenPOD")}
												options={getLookUp("boolean", locale)}
												placeholder={t("chooseAnOption")}
												name="isChildrenPOD"
												error={formik.errors.isChildrenPOD}
												value={formik.values.isChildrenPOD}
												onChange={(firstArg) => {
													handleChange("radio", firstArg, "isChildrenPOD", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2}>
											<GrayBox>
												<Text>{t("mocdNote")}</Text>
											</GrayBox>
										</GridItem>
										{isChildrenPOD === "1" && (
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="text"
													name="numberOfPODChildren"
													placeholder={t("writeAnswerHere")}
													label={t("numberOfPODChildren")}
													value={formik.values.numberOfPODChildren}
													error={formik.errors.numberOfPODChildren}
													onChange={(firstArg) => {
														handleChange("text", firstArg, "numberOfPODChildren", formik, formKey);
													}}
												/>
											</GridItem>
										)}
									</>
								)}
							</>
						)}
					</Grid>
				);
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};
