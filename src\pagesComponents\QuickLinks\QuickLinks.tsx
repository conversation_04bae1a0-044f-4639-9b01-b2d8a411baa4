import { Box, Stack } from "@chakra-ui/react";
import BoxHeader from "components/BoxHeader";
import InnerBox from "components/InnerBox";
import React from "react";

const QuickLinks = () => {
	const data = [
		{
			linkTitle: "link 1",
			hyperLink: "https://chakra-ui.com/",
		},
		{
			linkTitle: "link 2",
			hyperLink: "https://chakra-ui.com/",
		},
		{
			linkTitle: "link 3",
			hyperLink: "https://chakra-ui.com/",
		},
	];

	return (
		<Box bg="brand.white.50" py="3" px="4" ml="9" w="full">
			<BoxHeader
				title="Quick Links"
				icon="../assets/images/linkIcon.png"
				width="29px"
				height="29px"
				alt="image not found "
			/>
			{!!data.length &&
				data.map((item, index) => (
					<Box my="1" bg="brand.gray.100" p="3" key={index}>
						<Stack>
							<InnerBox
								headerName={item.linkTitle}
								title={item.hyperLink}
								imgSrc="../assets/images/ExternalLink.png"
								alt="image not found"
								width="36px"
								height="36px"
								isClicable={true}
								lineHeight="1"
							/>
						</Stack>
					</Box>
				))}
		</Box>
	);
};

export default QuickLinks;
