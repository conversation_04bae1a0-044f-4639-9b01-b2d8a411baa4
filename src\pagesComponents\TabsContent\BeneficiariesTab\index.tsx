import { Box, Flex, Grid, GridItem, Text } from "@chakra-ui/react";
import { BeneficiaryIconOne } from "components/Icons";
import ImageModal from "components/ImageModal";
import BeneficiaryBox from "pagesComponents/BeneficiaryBox";
// import TickList from "components/Lists/TickList";
import React, { useState } from "react";
import { AboutContent } from "utils/strapi/about";
import { getImageUrls } from "utils/strapi/helpers";

const BeneficiariesTab = ({ content }: { content: AboutContent }) => {
	const [showImage, setShowImage] = useState(false);
	const [imagePath, setImagePath] = useState("");
	// const { t } = useTranslation("about");
	// const [imageTitle, setImageTitle] = useState("");
	// const benefactorHealthPoints = [t("benefactorHealthPoint1"), t("benefactorHealthPoint2")];
	// const beneficiaryDifficultSituationPoints = [
	// 	t("beneficiaryDifficultSituationPoint1"),
	// 	t("beneficiaryDifficultSituationPoint2"),
	// 	t("beneficiaryDifficultSituationPoint3"),
	// 	// t("beneficiaryDifficultSituationPoint4"),
	// 	// t("beneficiaryDifficultSituationPoint5"),
	// 	t("beneficiaryDifficultSituationPoint6"),
	// ];
	// const imagesLinks = {
	// 	socialSecurityBenefactorsDetails: `/assets/images/aboutUs/${locale}/socialSecurityBenefactorsDetails.jpg`,
	// 	workingBenefeiciariesDetails: `/assets/images/aboutUs/${locale}/lowIncomeWorkersDetails.jpg`,
	// 	workingBenefeiciariesDetails2: `/assets/images/aboutUs/${locale}/lowIncomeWorkersDetails2.jpg`,
	// 	notWorkingOver45Details: `/assets/images/aboutUs/${locale}/notWorkingOver45Details.jpg`,
	// 	notWorkingOver45Details2: `/assets/images/aboutUs/${locale}/notWorkingOver45Details2.jpg`,
	// 	searchingForJobUnder44Details: `/assets/images/aboutUs/${locale}/searchingForJobUnder44Details.jpg`,
	// 	searchingForJobUnder44Details2: `/assets/images/aboutUs/${locale}/searchingForJobUnder44Details2.jpg`,
	// 	specialCasesOtherDetails: `/assets/images/aboutUs/${locale}/specialCasesOtherDetails.jpg`,
	// };

	// const specialCasesImagesLinks = [
	// 	`/assets/images/aboutUs/${locale}/specialCasePeopleOfDetermination.jpg`,
	// 	`/assets/images/aboutUs/${locale}/specialCasesHealthDisablity.jpg`,
	// ];

	const handlePicture = (passedImagePath) => {
		// setShowImage(true);
		// console.log(element);
		setImagePath(passedImagePath);
		setShowImage(true);
	};
	return (
		<Box w="100%">
			<ImageModal
				// imageTitle=""
				width={{ base: "100%", md: "100vh" }}
				isOpen={showImage}
				imagePath={imagePath}
				onClose={() => {
					setShowImage(false);
				}}
			/>
			<Grid
				// rowGap={{ base: 6, md: 6 }}
				columnGap={{ base: 4, md: 8, lg: 8 }}
				rowGap="8"
				templateColumns="repeat(4, 1fr)"
				templateRows="auto"
			>
				{content.benfs.map((ben) => {
					const handleExample = () => {
						handlePicture(getImageUrls(ben.example_image?.url || ""));
					};
					return (
						<GridItem key={ben.id} colSpan={{ base: 4, sm: 2, md: 2, lg: 2, xl: 1 }}>
							<BeneficiaryBox
								mainText={ben.details}
								handleViewDetails={() => {
									handlePicture(getImageUrls(ben.details_image?.url || ""));
								}}
								icon={<BeneficiaryIconOne w={"60px"} h={"60px"} />}
								handleExample={ben.example_image ? handleExample : undefined}
								detailsButton={ben.details_button_text}
								exampleButton={ben.example_button_text || ""}
							/>
						</GridItem>
					);
				})}
			</Grid>
			<Flex
				flexDirection={{ base: "column", md: "column", lg: "row" }}
				justifyContent={"space-between"}
				pt={48}
				pb={36}
			>
				<Box>
					<Text fontSize={"2xl"} color={"brand.mainGold"} fontWeight={500}>
						{content.special_cases.first_text}
					</Text>
					<Text fontSize={"4xl"} color={"brand.textColor"} fontWeight={600} mb={7} mt={3}>
						{content.special_cases.header}
					</Text>
					<Text fontSize={"xl"} color={"brand.textColor"}>
						{content.special_cases.sub_header}
					</Text>
				</Box>
				<Grid
					gap={4}
					templateRows="repeat(2, 1fr)"
					templateColumns="repeat(4, 1fr)"
					w={"100%"}
					ml={{ base: 0, lg: 6 }}
					mt={{ base: 8, lg: 0 }}
				>
					<GridItem
						colSpan={2}
						bg="brand.white.100"
						borderRadius={"10px"}
						p={6}
						display={"flex"}
						flexDirection={"column"}
						justifyContent={"space-between"}
					>
						<Text fontSize={"lg"} color={"brand.textColor"}>
							{content.special_cases.case_1}
						</Text>
						<Box onClick={() => {}}>
							<Text fontSize={"md"} color={"brand.mainGold"}>
								{content.special_cases.learn_more}
							</Text>
						</Box>
					</GridItem>
					<GridItem
						colSpan={2}
						bg="brand.white.100"
						borderRadius={"10px"}
						p={6}
						display={"flex"}
						flexDirection={"column"}
						justifyContent={"space-between"}
					>
						<Text fontSize={"lg"} color={"brand.textColor"}>
							{content.special_cases.case_2}
						</Text>
						<Box onClick={() => {}}>
							<Text fontSize={"md"} color={"brand.mainGold"}>
								{content.special_cases.learn_more}
							</Text>
						</Box>
					</GridItem>
					<GridItem colSpan={4} bg="brand.white.100" borderRadius={"10px"} p={6}>
						<Text fontSize={"lg"} color={"brand.textColor"}>
							{content.special_cases.case_3}
						</Text>
					</GridItem>
				</Grid>
			</Flex>
		</Box>
	);
};

export default BeneficiariesTab;
