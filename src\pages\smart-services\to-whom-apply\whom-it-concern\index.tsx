import { Box, Flex, Heading, HStack, Show, Link, Center } from "@chakra-ui/react";
import Breadcrumbs from "components/Breadcrumbs";
import { LeftArr } from "components/Icons";
import { CUSTOMER_PULSE_SCRIPT_LINK, CUSTOMER_PULSE_WHOM_LINKING_ID } from "config";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import MainLayout from "layouts/MainLayout";
import { GetServerSidePropsContext } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import Script from "next/script";
import RatingModal from "pagesComponents/RatingModal";
import ToWhomItMayConcernForm from "pagesComponents/ToWhomItMayConcernForm";
import { ReactElement } from "react";
import { useTranslation } from "react-i18next";
import { BackendServices } from "services/backend";
import { addLocalLookups, getEmiratesIdFromToken, getLocalizedLookups } from "utils/helpers";

function WhomItConcern({
	userDetails,
	masterData,
	customerPulseScriptLink,
	customerPulseLinkingId,
}) {
	const { t } = useTranslation(["personalInfo", "forms", "common"]);
	const router = useRouter();
	const { locale, query } = router;
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "navbar-howToApply",
			link: "/smart-services/to-whom-apply",
			isCurrentPage: false,
		},
		{
			label: t("common:applying-for-to-whom"),
			id: "howToApplyForWhomItMayConcern",
			link: "#",
			isCurrentPage: true,
		},
	];

	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 8 }} w="100%">
			<Script src={customerPulseScriptLink} strategy="afterInteractive" />
			<Show below={"md"}>
				<HStack my={5} mx={6}>
					<Link onClick={() => {}}>
						<Center>
							<LeftArr
								w={"8px"}
								h={"100%"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
						</Center>
					</Link>

					<Heading
						textAlign="center"
						flexGrow={1}
						size="md"
						fontSize="lg"
						fontWeight="medium"
						p={1}
					>
						{t("applying-for-to-whom", { ns: "common" })}
					</Heading>
				</HStack>
			</Show>
			<Box display={{ base: "none", md: "block" }}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Flex display={{ base: "none", md: "flex" }}>
				<Heading size="md" fontSize="h4" fontWeight="medium" pb={12.5} flexGrow={1}>
					{t("applying-for-to-whom", { ns: "common" })}
				</Heading>
			</Flex>
			<Box>
				<ToWhomItMayConcernForm
					userDetails={userDetails!}
					masterData={masterData!}
					customerPulseLinkingId={customerPulseLinkingId}
				></ToWhomItMayConcernForm>
			</Box>
			<RatingModal isModalShow={false} />
		</Box>
	);
}

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const masterData = addLocalLookups(
		(await BackendServices.getMasterData())?.Data || initialCrmMasterData
	);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const profile = await BackendServices.retrieveContact(emiratesId);
	const userDetails = profile.Data;
	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "personalInfo", "forms"])),
			userDetails,
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			customerPulseScriptLink: CUSTOMER_PULSE_SCRIPT_LINK,
			customerPulseLinkingId: CUSTOMER_PULSE_WHOM_LINKING_ID,
		},
	};
}

WhomItConcern.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default WhomItConcern;
