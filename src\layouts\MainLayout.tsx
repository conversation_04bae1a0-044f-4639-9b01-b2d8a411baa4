import { <PERSON><PERSON>, Flex } from "@chakra-ui/react";
import Header from "components/Header";
import { BackToTop } from "components/Icons";
import { SITE_URL } from "config";
import { useTranslation } from "next-i18next";
import { NextSeo } from "next-seo";
import { useRouter } from "next/router";
import Footer from "pagesComponents/Footer";
import { useState, useEffect } from "react";
import { useQuery } from "react-query";
import { getStrapiGlobal, getStrapiMenus } from "services/frontend";

/**
 *
 * @param maintenanceMode boolean to block navbar links to prevent users from bypassing the block if the website is under maintenance or the request is blocked
 */
const MainLayout = ({
	minimalLayout = false,
	notBlockPage = true,
	children,
	maintenanceMode = false,
}) => {
	const { t } = useTranslation();
	const title = t("title") || "";
	const description = t("description") || "";
	const keywords = t("keywords") || "";
	const [isVisible, setIsVisible] = useState(false);
	const { locale } = useRouter();
	const handleScroll = () => {
		if (window.scrollY > 200) {
			setIsVisible(true);
		} else {
			setIsVisible(false);
		}
	};

	const scrollToTop = () => {
		window.scrollTo({
			top: 0,
			behavior: "smooth",
		});
	};

	useEffect(() => {
		window.addEventListener("scroll", handleScroll);
		return () => {
			window.removeEventListener("scroll", handleScroll);
		};
	}, []);
	const {
		isLoading,
		data,
		status: fetchStatus,
	} = useQuery({
		queryKey: "layoutcontent",
		queryFn: async () => {
			const [headerData, menus] = await Promise.all([
				getStrapiGlobal(locale || "ar"),
				getStrapiMenus(),
			]);
			return { global: headerData, menus };
		},
	});

	return (
		<Flex w="full" alignItems="start" flexDir={"column"} h="full" minH="100vh" bg="#FFF">
			<NextSeo
				title={title}
				description={description}
				additionalMetaTags={[{ property: "keywords", content: keywords }]}
				openGraph={{
					url: SITE_URL,
					title: title,
					description: description,
					site_name: title,
					images: [
						{
							url: "/assets/images/mocdLogo.png",
							alt: title,
						},
					],
				}}
			/>
			<Header
				isMain={!minimalLayout}
				notBlockPage={notBlockPage}
				maintenanceMode={maintenanceMode}
				data={data}
				fetchStatus={fetchStatus}
			/>
			{children}
			{!minimalLayout && <Footer fetchStatus={fetchStatus} data={data} isMain={!minimalLayout} />}
			{isVisible && (
				<Button position={"fixed"} bottom={"48px"} left={"10px"} onClick={scrollToTop} zIndex={100}>
					<BackToTop w={"64px"} h={"64px"} />
				</Button>
			)}
		</Flex>
	);
};

export default MainLayout;
