import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";
import { ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";
import { EMIRATES_ID_REGEX } from "config";

const getInitialValues: {
	RegisteredWithEWE?: ICrmLookupLocalized;
	ReceiveSocialAid?: ICrmLookupLocalized;
	ReceiveInflationAllowance?: ICrmLookupLocalized;
	OwnerEWEBill?: ICrmLookupLocalized;
	EWEBill?: string;
	RelatedEmiratesID?: string;
	EntityReceivedFrom?: ICrmLookupLocalized;
} = {};

const getValidationSchema = () => {
	return Yup.object({
		RegisteredWithEWE: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required("isRequired").label("thisField"),
			})
			.required(),
		OwnerEWEBill: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required(),
			})
			.required(),
		ReceiveSocialAid: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required(),
		ReceiveInflationAllowance: Yup.object().when("ReceiveSocialAid.value", {
			is: "no",
			then: Yup.object()
				.shape({
					label: Yup.string(),
					value: Yup.string().oneOf(["yes", "no"]).required().label("thisField"),
				})
				.required("Required"),
			otherwise: Yup.object().notRequired(),
		}),
		EWEBill: Yup.string()
			.matches(/^\d{12}$/, "eweErrorMessage")
			.required()
			.label("thisField"),
		RelatedEmiratesID: Yup.string().when("OwnerEWEBill.value", {
			is: "yes",
			then: Yup.string().notRequired().nullable(),
			otherwise: Yup.string()
				.matches(EMIRATES_ID_REGEX, "pleaseEnterValidEid")
				.required()
				.label("thisField"),
		}),

		EntityReceivedFrom: Yup.object().when("ReceiveSocialAid.value", {
			is: "yes",
			then: Yup.object().shape({}).required(),
			otherwise: Yup.object().nullable().notRequired(),
		}),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };

Yup.object().shape({
	firstObject: Yup.object().shape({
		value: Yup.string().oneOf(["yes", "no"]).required("Required"),
	}),
	secondObject: Yup.object().when("$firstObject.value", {
		is: "yes",
		then: Yup.string().required("Required"),
		otherwise: Yup.object().notRequired(),
	}),
});
