export interface LandingImage {
	id: number;
	name: string;
	alternativeText: string | null;
	caption: string | null;
	width: number;
	height: number;
	hash: string;
	ext: string;
	mime: string;
	size: number;
	url: string;
	previewUrl: string | null;
	provider: string;
	provider_metadata: any | null;
	createdAt: string;
	updatedAt: string;
}

interface ServiceCard {
	id: number;
	header: string;
	body: string;
	icon: string;
}

interface ServiceDetails {
	id: number;
	header: string;
	application_time_label: string;
	application_time_value: string;
	process_duration_label: string;
	process_duration_value: string;
	process_fees_label: string;
	process_fees_value: string;
}

interface Document {
	id: number;
	text: string;
}

export interface RequiredDocuments {
	id: number;
	header: string;
	apply_button_text: string;
	documents_list: Document[];
	apply_button_link: string;
}

interface ProcessStep {
	id: number;
	text: string;
}

interface TermsAndConditions {
	id: number;
	header: string;
	terms: {
		id: number;
		term_value: string;
		is_sub_term: boolean;
	}[];
}

export interface FarmerServiceContent {
	id: number;
	page_header: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	first_tab_header: string;
	second_tab_header: string;
	process_header: string;
	landing_image: LandingImage;
	service_info: ServiceCard;
	service_provider: ServiceCard;
	aid_application_time: ServiceCard;
	service_details: ServiceDetails;
	required_documents: RequiredDocuments;
	process_list: ProcessStep[];
	terms_conditions: TermsAndConditions;
}
