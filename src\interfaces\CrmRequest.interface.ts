export interface ICrmRequest {
	RequestName: string;
	CreatedDate: Date;
	Contact: Contact;
	Template: Template;
	Status: Status;
	CaseId: string;
	EligibleForAppeal: boolean;
	EligibleForEdit: boolean;
	IsNomiatedInflationCase: boolean;
}

interface Template {
	TemplateId: string;
	TemplateName: string;
	TemplateNameAr: string;
}

interface Contact {
	ContactId: string;
	FirstName: string;
	FirstNameAr?: any;
	LastName: string;
	FullNameAr?: string;
	FullNameEn?: string;
	LastNameAr?: any;
	EmirateID: string;
	Email: string;
	MobileNumber: string;
}
interface ContactInfo {
	Email: string;
	ContactId: string;
	FirstName: string;
	FirstNameAr?: any;
	LastName: string;
	LastNameAr?: any;
	EmirateID: string;
	EmailAddress: string;
	PhoneNumber: string;
}

interface Status {
	Key: string;
	Value: string;
}
