const paymentRates = [
	// {
	// 	label: "25% (4 months)",
	// 	labelAr: "25% (4 شهور)",
	// 	value: "4",
	// },
	// {
	// 	label: "50% (2 months)",
	// 	labelAr: "50% (شهرين)",
	// 	value: "2",
	// },
	// {
	// 	label: "100% (Full Amount)",
	// 	labelAr: "100% (كامل المبلغ)",
	// 	value: "1",
	// },
	{
		label: "5% ",
		labelAr: "5% ",
		value: "1",
	},
	{
		label: "10% ",
		labelAr: "10%",
		value: "2",
	},
	{
		label: "15% ",
		labelAr: "15%",
		value: "1",
	},
	{ label: "20% ", labelAr: "20%", value: "4" },
	{
		label: "25% ",
		labelAr: "25%",
		value: "5",
	},
];

export const lookups = {
	paymentRates,
};

export const getLookUp = (name: keyof typeof lookups, locale = "ar") => {
	return lookups[name].map((lookup) => ({
		label: locale === "ar" ? lookup.labelAr : lookup.label,
		value: lookup.value,
	}));
};
export const localizedLookups = (locale = "ar") => {
	const loc = {} as any;
	for (const key in lookups) {
		loc[key] = lookups[key].map((lookup) => ({
			label: locale === "ar" ? lookup.labelAr : lookup.label,
			value: lookup.value,
		}));
	}
	return loc;
};
