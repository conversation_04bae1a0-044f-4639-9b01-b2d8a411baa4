import {
	Button,
	Checkbox,
	Flex,
	Grid,
	GridItem,
	Link,
	Table,
	TableContainer,
	Tbody,
	Td,
	Text,
	Thead,
	Tooltip,
	Tr,
} from "@chakra-ui/react";
import { Form, Formik } from "formik";
import InflationStripedTable from "pagesComponents/ApplyInflation/InflationStripedTable";
import * as functions from "./functions";
import { useTranslation } from "react-i18next";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useRouter } from "next/router";
import { IChildFamilyMember, IFamilyMember } from "interfaces/InflationForm.interface";
import { useFormContext } from "context/FormContext";
import { useEffect, useState } from "react";
import { getLookupLabel } from "utils/helpers";
import { formatAmount, formatEmiratesID, formatLocalNumber } from "utils/formatters";
import {
	TCS_PDF_FILE_LOCATION_AR,
	TCS_PDF_FILE_LOCATION_EN,
	MilitaryServiceStatusAr,
	MilitaryServiceStatus,
	UtilityProviderEn,
	UtilityProviderAr,
} from "config";
import { ICrmLookup } from "interfaces/CrmMasterData.interface";
import { getCaseUpdateReasonsByCaseId } from "services/frontend";

interface Props {
	onSubmit: any;
	setCurrentStep: any;
	handleStepsIndexes: any;
	documentList: ICrmDocumentList;
	familyMembers: IFamilyMember[];
	caseForm: any;
	handleSetFormikState: any;
	formKey: string;
	hasSSS: boolean;
	childMembers: IChildFamilyMember[];
}

const tableRows = {
	informationForm: ["InflationCategory"],
	personalInformation: [
		"EmiratesID",
		"IDNBackNumber",
		"caseID",
		"PreferredEmail",
		"AlternativeEmail",
		"PreferredPhoneNumber",
		"alternativeNumber",
		"Occupations",
		"Emirates",
		"Area",
		"Center",
		"MaritalStatus",
	],
	inflationInformation: ["ApplyUtilityAllowance", "UtilityProvider", "UtilityAccountNumber"],
};

const formatIDs = {
	EmiratesID: formatEmiratesID,
	PreferredPhoneNumber: formatLocalNumber,
	alternativeNumber: formatLocalNumber,
	incomeAmount: formatAmount,
	pensionAmount: formatAmount,
	tradeLicenseAmount: formatAmount,
	RentAmount: formatAmount,
};

function ReviewDocumentComponent({
	onSubmit,
	setCurrentStep,
	handleStepsIndexes,
	documentList,
	familyMembers,
	caseForm,
	handleSetFormikState,
	formKey,
	childMembers,
	hasSSS = false,
}: Props) {
	const { t } = useTranslation(["forms", "tables", "common"]);
	const { locale, query } = useRouter();
	const { lookups } = useFormContext();

	const [caseUpdateReasons, setCaseUpdateReasons] = useState<ICrmLookup[]>([]);
	let requestId = query.requestId?.toString() ? query.requestId?.toString() : "";
	// Call the function and update state
	const getCaseUpdateReasons = () => {
		getCaseUpdateReasonsByCaseId(requestId).then((response) => {
			if (response.IsSuccess && response.Data) {
				const data = Array.isArray(response.Data) ? response.Data : [response.Data]; // Wrap single object in an array
				setCaseUpdateReasons(data);
			}
		});
	};
	useEffect(() => {
		if (requestId) {
			getCaseUpdateReasons();
		}
	}, []);
	const handleEdit = (stepNumber: number) => {
		setCurrentStep(stepNumber);
		handleStepsIndexes(stepNumber);
	};

	const updateUserData = () => {
		let finalizedIncomeInfoArr: any = {};
		let hideKeysArr: any = []; // This array is used to add keys to hide specific rows

		hideKeysArr.push("IDNBackNumber");

		if (caseForm?.inflationInformation?.ApplyUtilityAllowance === "no") {
			hideKeysArr.push("UtilityAccountNumber");
			hideKeysArr.push("UtilityProvider");
		}

		for (let formKey in tableRows) {
			finalizedIncomeInfoArr[formKey] = [];
			for (let key of tableRows[formKey]) {
				if (!(key in caseForm[formKey])) continue;
				let newItem: any = {
					label: key,
					value:
						getLookupLabel(lookups, key, caseForm?.[formKey]?.[key]) ||
						formatIDs?.[key]?.(caseForm?.[formKey]?.[key] || "-") ||
						caseForm?.[formKey]?.[key] ||
						"-",
				};
				if (key === "LivingSituation") {
					newItem = {
						label: "LivingSituation",
						value:
							getLookupLabel(lookups, "accomadations", caseForm?.[formKey]?.[key]) ||
							formatIDs?.[key]?.(caseForm?.[formKey]?.[key] || "-") ||
							caseForm?.[formKey]?.[key] ||
							"-",
					};
				}
				if (hideKeysArr.length > 0 && hideKeysArr.includes(key)) {
					newItem.hide = true;
				}
				if ((newItem.value === "yes" || newItem.value === "no") && locale === "ar") {
					if (newItem.value === "yes") newItem.value = "نعم";
					if (newItem.value === "no") newItem.value = "لا";
				}
				if (newItem.label === "MilitaryServiceStatus") {
					if (newItem.value) {
						newItem.value =
							locale === "ar"
								? MilitaryServiceStatusAr.filter(
										(item) => item.value === newItem.value.toString()
								  )[0]?.label
								: MilitaryServiceStatus.filter((item) => item.value === newItem.value.toString())[0]
										?.label;
					}
				}
				if (newItem.label === "UtilityProvider") {
					if (newItem.value) {
						newItem.value =
							locale === "ar"
								? UtilityProviderAr.filter((item) => item.value === newItem.value.toString())[0]
										?.label
								: UtilityProviderEn.filter((item) => item.value === newItem.value.toString())[0]
										?.label;
					}
				}

				finalizedIncomeInfoArr[formKey].push(newItem);
			}
		}
		return finalizedIncomeInfoArr;
	};

	const [userData, setUserData] = useState(() => updateUserData());
	const [termsRead, setTermsRead] = useState(false);
	useEffect(() => {
		setUserData(() => updateUserData());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [locale]);

	const documentsTableData = functions.createDocumentsTableData(documentList, locale);
	const familyMembersTablesData = functions.createFamilyMembersTablesData(
		familyMembers,
		childMembers,
		lookups,
		locale,
		formatIDs
	);

	return (
		<Formik
			enableReinitialize
			initialValues={functions.getInitialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || !formik.dirty,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 10, md: 10 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("personalInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(1)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<InflationStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.personalInformation}
									caption={null}
								/>
							</GridItem>
							{caseUpdateReasons.length > 0 ? (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("EditReason")}
										</Text>
									</Flex>
									<TableContainer
										mb={4}
										border="1px"
										borderBottom="0px"
										borderColor="brand.tableBorderColor"
										rounded="lg"
									>
										<Table variant="simple">
											<Thead></Thead>
											<Tbody>
												{caseUpdateReasons.length > 0
													? caseUpdateReasons.map((reason, index) => (
															<Tr key={index}>
																<Td
																	borderColor="brand.tableBorderColor"
																	fontSize="0.875rem"
																	fontWeight="normal"
																	letterSpacing="unset"
																	lineHeight="150%"
																>
																	{locale == "en" ? reason?.Name : reason.NameAR}
																</Td>
															</Tr>
													  ))
													: ""}
											</Tbody>
										</Table>
									</TableContainer>
								</GridItem>
							) : (
								""
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("informationForm")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(0)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<InflationStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.informationForm}
									caption={null}
									SubcatValue={caseForm?.socialAidInformation?.SubCategory}
								/>
							</GridItem>
							{familyMembersTablesData.length > 0 &&
								familyMembersTablesData.map((tableData, idx) => (
									<GridItem colSpan={{ base: 2, md: 2 }} key={idx}>
										<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
											<Text fontSize={"1.25rem"} fontWeight={"bold"}>
												{t("familyMemberInformation")}
											</Text>
											<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
												{t("edit", { ns: "common" })}
											</Button>
										</Flex>
										<InflationStripedTable
											tableBody={tableData.data}
											caption={null}
											sourceOfTranslation="forms"
										/>
									</GridItem>
								))}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("inflationInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(3)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<InflationStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.inflationInformation}
									caption={null}
								/>
							</GridItem>

							{documentsTableData.length > 0 && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("documents")}
										</Text>
										<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(3)}>
											{t("edit", { ns: "common" })}
										</Button>
									</Flex>
									<InflationStripedTable tableBody={documentsTableData} caption={null} />
								</GridItem>
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex>
									<Tooltip label={t("youHaveToReadTerms")} isDisabled={termsRead}>
										<Checkbox
											onChange={() => {
												formik.setFieldValue(
													"termsAndConditions",
													!formik.values.termsAndConditions
												);
											}}
											name="termsAndConditions"
											disabled={!termsRead}
											id="termsAndcond"
										></Checkbox>
									</Tooltip>
									<Text htmlFor="termsandcond" as="label" ms={2.5}>
										{t("readTermsAndConditions", { ns: "tables" })}{" "}
										<Text as="span" color="brand.mainGold" fontSize={"larger"}>
											<Link
												href={locale === "ar" ? TCS_PDF_FILE_LOCATION_AR : TCS_PDF_FILE_LOCATION_EN}
												target={"_blank"}
												onClick={() => setTermsRead(true)}
												fontSize={"xl"}
											>
												{t("acknowledgementAndUndertaking", { ns: "tables" })}
											</Link>
											.
										</Text>
									</Text>
								</Flex>
								<Flex mt={3}>
									<Checkbox
										onChange={() => {
											formik.setFieldValue(
												"confirmAccurateInformation",
												!formik.values.confirmAccurateInformation
											);
										}}
										name="confirmAccurateInformation"
										id="confirmacc"
									></Checkbox>
									<Text as="label" htmlFor="confirmacc" ms={2.5}>
										{t("confirmAccurateInformation", { ns: "tables" })}
									</Text>
								</Flex>
								<Flex mt={3}>
									<Checkbox
										onChange={() => {
											formik.setFieldValue(
												"totalHouseHoldIncomeConfirmation",
												!formik.values.totalHouseHoldIncomeConfirmation
											);
										}}
										name="totalHouseHoldIncomeConfirmation"
										id="confirmacc"
									></Checkbox>
									<Text as="label" htmlFor="confirmacc" ms={2.5}>
										{t("totalHouseHoldIncomeConfirmation", { ns: "tables" })}
									</Text>
								</Flex>
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default ReviewDocumentComponent;
