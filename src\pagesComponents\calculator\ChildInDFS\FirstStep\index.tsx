import { Box, Grid, GridItem, ListItem, OrderedList, Text, UnorderedList } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState, useRef } from "react";
import { ChildInDFSCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import { validationSchema } from "./functions";
import ageWarning from "./customError";

import { formatAmount } from "utils/formatters";
export default function FirstStep({
	formData: { personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: ChildInDFSCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();
	const toast = useAppToast();
	const empty = {
		currentChildSituation: "",
		gender: "",
		ageGroupChild: "",
		qualifiedStudent: "",
		haveSiblings: "",
		isSiblingsPOD: "",
		numberOfPODSiblings: "",
		numberOfSiblings: "",
		totalIncome: "",
	};
	const lookups = localizedLookups(locale);
	const formikRef: any = useRef();
	const handleSelectChange = (fieldName, newValue) => {
		handleChange("selectableTags", newValue, fieldName, formikRef.current, formKey);
	};
	const updateDropdownValues = () => {
		let originalInitialValues = { ...empty };
		Object.keys(personalInformation).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === personalInformation[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				handleSelectChange(key, lookups[key][indexOfItem]);
			} else {
				originalInitialValues[key] = personalInformation[key]
					? JSON.parse(JSON.stringify(personalInformation[key]))
					: personalInformation[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(() => updateDropdownValues());
	}, [locale]);
	const { t } = useTranslation("calculator");
	const [showAgeWarning, setShowAgeWarning] = useState(false);

	const handleAgeChange = (newAge) => {
		if (newAge === "3") {
			setShowAgeWarning(true);
		} else {
			setShowAgeWarning(false);
		}
	};

	const renderAgeWarning = () => {
		if (showAgeWarning) {
			return <div style={ageWarning}>{t("enEligble.ageAbove25")}</div>;
		}
		return null;
	};
	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				const {
					haveSiblings,
					ageGroupChild,
					qualifiedStudent,
					isSiblingsPOD,
					currentChildSituation,
				} = formik.values as any;
				formikRef.current = formik!;

				// const haveNoChildren = haveChildren === "0";
				// const noneOfTheAbove = childAttributes.value === "4";
				const handleGenderChange = (value) => {
					formik.setFieldValue("gender", value);
					// formik.setFieldValue("ageGroup", "");
					formik.resetForm();
				};
				const age25_above = ageGroupChild.value === "3";
				const notStudentAndAge21_25 = qualifiedStudent === "0" && ageGroupChild.value === "2";
				const enEligableConditions = [age25_above, notStudentAndAge21_25];
				const enEligble = enEligableConditions.some((t) => t === true);
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || enEligble,
					},
					formKey
				);
				return (
					<Grid
						rowGap={{ base: 6, md: 4 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
							{t("personalInformation")}
						</Text>

						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("currentChildSituation")}
								options={getLookUp("currentChildSituation", locale)}
								placeholder={t("chooseAnOption")}
								name="currentChildSituation"
								error={formik.errors.currentChildSituation}
								value={formik.values.currentChildSituation}
								onChange={(firstArg) => {
									handleSelectChange("currentChildSituation", firstArg);

									// handleChange(
									// 	"selectableTags",
									// 	firstArg,
									// 	"currentChildSituation",
									// 	formik,
									// 	formKey
									// );
									if (firstArg.value === "3") {
										toast({
											status: "info",
											title: t("unkownParentsDetails"),
										});
									}
								}}
							/>
						</GridItem>

						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("gender")}
								options={getLookUp("gender", locale)}
								placeholder={t("chooseAnOption")}
								name="gender"
								error={formik.errors.gender}
								value={formik.values.gender}
								onChange={(firstArg) => {
									handleGenderChange(firstArg.value);

									handleSelectChange("gender", firstArg);
								}}
							/>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<div>
								<FormField
									type="selectableTags"
									label={t("ageGroup")}
									options={getLookUp("ageGroupChild", locale)}
									placeholder={t("chooseAnOption")}
									error={formik.errors.ageGroupChild}
									value={formik.values.ageGroupChild}
									name="ageGroupChild"
									onChange={(firstArg) => {
										handleAgeChange(firstArg.value);
										// handleChange("selectableTags", firstArg, "ageGroupChild", formik, formKey);
										handleSelectChange("ageGroupChild", firstArg);
									}}
								/>
								{renderAgeWarning()}
							</div>
						</GridItem>

						{ageGroupChild.value === "2" && (
							<>
								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
										{t("qualifiedStudentSect")}
									</Text>
									<GrayBox>
										<UnorderedList>
											<Text>{t("studentRule1")}</Text>

											<UnorderedList>
												<ListItem>
													<Text color="#1B1D21">{t("studentRule2")}</Text>
												</ListItem>
											</UnorderedList>
										</UnorderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("qualifiedStudent")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="qualifiedStudent"
										error={formik.errors.qualifiedStudent}
										value={formik.values.qualifiedStudent}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "qualifiedStudent", formik, formKey);
											if (firstArg === "0") {
												toast({
													status: "error",
													title: t("enEligble.notStudent"),
												});
											}
										}}
									/>
								</GridItem>
							</>
						)}
						{!enEligble && (
							<>
								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} my={4}>
										{t("emiratiSiblings")}
									</Text>
									<GrayBox>
										<Text>{t("childRules-1")}</Text>
										<OrderedList>
											<ListItem>
												<Text>{t("childRules-2")}</Text>
											</ListItem>
											<ListItem>
												<Text>{t("childRules-3")}</Text>
											</ListItem>
										</OrderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("haveSiblings")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="haveSiblings"
										error={formik.errors.haveSiblings}
										value={formik.values.haveSiblings}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "haveSiblings", formik, formKey);
										}}
									/>
								</GridItem>
								{haveSiblings === "1" && (
									<>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												// errorKey="numberOfSiblingsError"
												type="text"
												name="numberOfSiblings"
												placeholder={t("writeAnswerHere")}
												label={t("numberOfSiblings")}
												value={formik.values.numberOfSiblings}
												error={formik.errors.numberOfSiblings}
												onChange={(firstArg) => {
													handleChange("text", firstArg, "numberOfSiblings", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												type="radio"
												label={t("isSiblingsPOD")}
												options={getLookUp("boolean", locale)}
												placeholder={t("chooseAnOption")}
												name="isSiblingsPOD"
												error={formik.errors.isSiblingsPOD}
												value={formik.values.isSiblingsPOD}
												onChange={(firstArg) => {
													handleChange("radio", firstArg, "isSiblingsPOD", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2}>
											<GrayBox>
												<Text>{t("mocdNote")}</Text>
											</GrayBox>
										</GridItem>
										{isSiblingsPOD === "1" && (
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="text"
													name="numberOfPODSiblings"
													placeholder={t("writeAnswerHere")}
													label={t("numberOfPODSiblings")}
													value={formik.values.numberOfPODSiblings}
													error={formik.errors.numberOfPODSiblings}
													onChange={(firstArg) => {
														handleChange("text", firstArg, "numberOfPODSiblings", formik, formKey);
													}}
												/>
											</GridItem>
										)}
									</>
								)}

								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
										{t("incomeInformation")}
									</Text>

									<GrayBox>
										<UnorderedList>
											<Text>{t("incomeRules1")}</Text>

											<UnorderedList>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules2")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules3")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules4")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules5")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules6")}</Text>
												</ListItem>
											</UnorderedList>
										</UnorderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="text"
										label={t("totalIncome")}
										name="totalIncome"
										error={formik.errors.totalIncome}
										placeholder={t("writeAnswerHere")}
										customFormat={formatAmount}
										value={formik.values.totalIncome}
										onChange={(firstArg) => {
											handleChange("text", firstArg, "totalIncome", formik, formKey);
										}}
									/>
								</GridItem>
							</>
						)}
					</Grid>
				);
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};
