import { ICrmGuardianContact } from "interfaces/CrmGuardianContact.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmGuardianContact | null>>
) {
	const { EmiratesId, Code, PhoneNumber } = req.body;
	if (!EmiratesId || !Code || !PhoneNumber)
		return res.status(400).json({ ...errorResponse, Errors: "missing Data" });

	const data = await BackendServices.validateGuardian(EmiratesId, Code, PhoneNumber);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
