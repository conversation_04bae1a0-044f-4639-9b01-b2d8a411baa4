import { <PERSON>, Button, Flex, Show, Text, VStack } from "@chakra-ui/react";
import { CompletedSuccessfully } from "components/Icons";
import html2canvas from "html2canvas";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import SocialAidStripedTable from "pagesComponents/ApplySocialAid/SocialAidStripedTable";
import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { formatEmiratesId, formatUaePhoneNumber } from "utils/helpers";
import { jsPDF } from "jspdf";
import { DownloadIcon } from "@chakra-ui/icons";
// import usePdfDownloader from "pagesComponents/DetailModal/usePdfDownloader";

interface Props {
	data: any;
	userTableData: {
		nameAr: string;
		nameEn: string;
		emiratesId: string;
		phoneNumber: string;
		email: string;
	};
	titles: {
		title: string;
		caseNumberTitle: string;
		caseDateTitle: string;
		body: string;
		tableTitle: string;
	};
	caseNumber: string;
	date: any;
	onDownload: any;
	pageName?: string;
}
// const downloadPdf = usePdfDownloader();

const RequestStatus = forwardRef(
	({ data, userTableData, titles, caseNumber, date, onDownload, pageName = "" }: Props, ref) => {
		const { t } = useTranslation(["common", "tables", "forms"]);
		const { locale } = useRouter();
		const boxRef = useRef(null);
		let userData = [
			{
				label: t("common:name"),
				id: "name",
				value: locale === "en" ? userTableData.nameEn : userTableData.nameAr,
			},
			{
				label: t("common:emiratesID"),
				id: "emiratesID",
				value: formatEmiratesId(userTableData.emiratesId),
			},
			{
				label: t("common:email"),
				id: "email",
				value: userTableData.email,
			},
			{
				label: t("common:mobileNumber"),
				id: "mobileNumber",
				value: formatUaePhoneNumber(userTableData?.phoneNumber),
			},
		];

		useImperativeHandle(ref, () => ({
			downloadPdf() {
				const style = document.createElement("style");
				document.head.appendChild(style);

				// Add styling rules to increase font size and improve readability
				style.sheet?.insertRule("body > div:last-child img { display: inline-block; }");
				style.sheet?.insertRule(".dowmload-button-class { display: none; }");
				style.sheet?.insertRule(
					"body > div p { font-size: 14pt !important; font-family: Arial, sans-serif !important; }"
				);
				style.sheet?.insertRule(
					"body div table tbody tr td { font-size: 14pt !important; font-family: Arial, sans-serif !important; }"
				);
				style.sheet?.insertRule(
					"body div table tbody tr td:first-child { font-weight: bold !important; }"
				);
				style.sheet?.insertRule(".chakra-text { font-size: 14pt !important; }");
				style.sheet?.insertRule(
					"h1, h2, h3, h4, h5, h6 { font-size: 16pt !important; font-weight: bold !important; }"
				);

				const input = boxRef.current;
				if (input != null) {
					html2canvas(input, {
						scale: 3, // Higher quality scaling (increased from 2 to 3)
						useCORS: true,
						logging: false,
						allowTaint: true,
					}).then((canvas) => {
						style.remove();

						// Calculate dimensions based on A4 paper size (210mm × 297mm)
						const pdf = new jsPDF({
							unit: "mm",
							format: [210, 297],
							compress: true,
						});

						// Get canvas dimensions
						const canvasWidth = canvas.width;
						const canvasHeight = canvas.height;

						// Calculate optimal dimensions while maintaining aspect ratio
						const maxWidth = 180; // Leave more margin from edges for better readability
						const maxHeight = 260; // Leave more margin from top/bottom

						let imgWidth = canvasWidth;
						let imgHeight = canvasHeight;

						// Scale if larger than maximum dimensions
						if (canvasWidth > maxWidth || canvasHeight > maxHeight) {
							const scale = Math.min(maxWidth / canvasWidth, maxHeight / canvasHeight);
							imgWidth *= scale;
							imgHeight *= scale;
						}

						// Center the image
						const x = (210 - imgWidth) / 2;
						const y = (297 - imgHeight) / 2;

						// Convert canvas to high-quality image data
						const imgData = canvas.toDataURL("image/png", 1.0);

						// Add image with proper scaling
						pdf.addImage(imgData, "PNG", x, y, imgWidth, imgHeight);

						if (pageName === "complaint") {
							pdf.save(`Inquiry/Suggestion - ${data?.CaseRef || "Details"}.pdf`);
						} else {
							pdf.save(`Case - ${data?.CaseRef || "Details"}.pdf`);
						}
					});
				}
			},
		}));
		const dta = [
			...userData,
			{ id: "s", label: titles.caseNumberTitle, value: caseNumber },
			{ id: "s", label: titles.caseDateTitle, value: date },
		];

		return (
			<Flex
				flexDirection={"column"}
				alignItems={"center"}
				bg="brand.white.50"
				maxW="100vw"
				ref={boxRef}
			>
				<Flex
					width={{ base: "90%", md: "70%" }}
					py={5}
					pb={{ base: 0, md: 5 }}
					flexDirection={"column"}
					alignItems={"center"}
				>
					<CompletedSuccessfully w={"80px"} h={"80px"} />
					<Text
						fontSize={"xl"}
						fontWeight={700}
						color={"brand.textColor"}
						textAlign={"center"}
						mt={5}
						mb={5}
					>
						{titles.title}
					</Text>

					<Text textAlign={"center"} color={"brand.textColor"} fontSize={"md"}>
						{titles.body}
					</Text>
				</Flex>
				<Box px={5} w={"100%"} alignItems={"center"} mt={5}>
					<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
						<Text fontSize={"1.25rem"} fontWeight={"bold"}>
							{titles.tableTitle}
						</Text>
						<Button
							className="dowmload-button-class"
							onClick={onDownload}
							leftIcon={<DownloadIcon mr={2} mt={1} />}
							variant="outline"
						>
							{t("download", { ns: "common" })}
						</Button>
					</Flex>
					<Show above="md">
						<SocialAidStripedTable tableBody={dta} caption={null} />
					</Show>
					<Show below="md">
						<VStack align={"start"}>
							{dta.map((row, index) => {
								if (row.value === "-") return;
								return (
									<Flex
										key={index}
										w="full"
										p={4}
										justifyContent={"space-between"}
										borderBottom="1px solid #BBBCBD"
										_hover={{ cursor: "pointer" }}
									>
										<VStack flex={2} align={"start"}>
											<Text fontSize={"1rem"} fontWeight={"bold"}>
												{row.label}
											</Text>
											<Text fontSize={"0.875rem"}>{row.value}</Text>
										</VStack>
									</Flex>
								);
							})}
						</VStack>
					</Show>
				</Box>
			</Flex>
		);
	}
);

RequestStatus.displayName = "RequestStatus";

export default RequestStatus;
