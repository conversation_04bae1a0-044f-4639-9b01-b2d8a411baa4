import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	householdHeadContributes: "",
	householdHeadPension: "",
	householdHeadTradeLicense: "",
	householdRentalIncomes: "",
	incomes: [
		{
			IncomeTypes: "",
			incomeAmount: "",
			companyName: "",
		},
	],
	pensions: [
		{
			pensionAmount: "",
			PensionType: "",
			PensionAuthority: "",
		},
	],
	tradeLicenses: [
		{
			tradeLicenseAmount: "",
		},
	],
	RentalIncomes: [
		{
			ContractNo: "",
			ContractStartDate: "",
			ContractEndDate: "",
			rentalSource: "",
			RentAmount: "",
		},
	],
};

const getValidationSchema = () => {
	return Yup.object({
		householdHeadContributes: Yup.string().required().label("thisField"),
		incomes: Yup.array().when("householdHeadContributes", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							IncomeTypes: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
							incomeAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							companyName: Yup.string().notRequired().label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
		householdHeadPension: Yup.string().required().label("thisField"),
		pensions: Yup.array().when("householdHeadPension", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							pensionAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							PensionType: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
							PensionAuthority: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string(),
								})
								.required()
								.label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
		householdHeadTradeLicense: Yup.string().required().label("thisField"),
		tradeLicenses: Yup.array().when("householdHeadTradeLicense", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							tradeLicenseAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
						})
						.required()
						.label("thisField")
				),
		}),
		householdRentalIncomes: Yup.string().required().label("thisField"),
		RentalIncomes: Yup.array().when("householdRentalIncomes", {
			is: "yes",
			then: Yup.array()
				.min(1)
				.of(
					Yup.object()
						.shape({
							ContractNo: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							ContractStartDate: Yup.date().required().label("thisField").typeError("mustBeDate"),
							ContractEndDate: Yup.date()
								.required()
								.label("thisField")
								.typeError("mustBeDate")
								.min(Yup.ref("ContractStartDate"), "endDateCantStartBeforeStartDate")
								.test("same_dates_test", "endDateCantStartBeforeStartDate", function (value) {
									if (JSON.stringify(this.parent.ContractStartDate) === JSON.stringify(value))
										return false;
									return true;
								}),
							RentAmount: Yup.number()
								.positive("mustBePositive")
								.required()
								.label("thisField")
								.typeError("mustBeNumber"),
							rentalSource: Yup.object()
								.shape({
									label: Yup.string(),
									value: Yup.string().required(),
								})
								.required()
								.label("thisField"),
						})
						.required()
						.label("thisField")
				),
		}),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
