import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useMemo } from "react";
import { NEXT_AUTH_ENABLED } from "../config";

export const useUserDetails = (loginRequired = true) => {
	const { data: session } = useSession({
		required: NEXT_AUTH_ENABLED && loginRequired,
	});
	const user = useMemo(() => session?.user, [session?.user]);
	const { locale } = useRouter();
	const localeKey = useMemo(() => (locale === "ar" ? "Arabic" : ""), [locale]);

	return {
		...user,
		fullNameEn: !!user
			? `${user?.FirstName || ""} ${user?.MiddleName || ""} ${user?.LastName || ""}`
			: "",
		fullNameAr: !!user
			? `${user?.FirstNameArabic || ""} ${user?.MiddleNameArabic || ""} ${
					user?.LastNameArabic || ""
			  }`
			: "",
		localized: {
			FirstName: user?.["FirstName" + localeKey],
			LastName: user?.["LastName" + localeKey],
			MiddleName: user?.["MiddleName" + localeKey],
		},
	};
};
