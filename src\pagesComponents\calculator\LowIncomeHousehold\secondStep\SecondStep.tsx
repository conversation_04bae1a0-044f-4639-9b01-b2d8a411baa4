import { Box, Grid, GridItem, ListItem, Text, UnorderedList } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState } from "react";
import { LowIncomeCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { validationSchemaCase1, validationSchemaCase2 } from "./functions";
import { formatAmount } from "utils/formatters";

export default function SecondStep({
	formData: { employment, personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: LowIncomeCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();
	const empty = {
		spouseEmployedOrRetired: "",
		ageOfTheOldestEmployedFamilyMember: "",
		totalIncome: "",
	};
	const lookups = localizedLookups(locale);

	const updateDropdownValues = () => {
		let originalInitialValues = { ...empty };
		Object.keys(employment).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === employment[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
			} else {
				originalInitialValues[key] = employment[key]
					? JSON.parse(JSON.stringify(employment[key]))
					: employment[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);
	const maleAndMarried =
		personalInformation.gender === "1" && personalInformation.maritalStatus === "2";
	const validationSchema = maleAndMarried ? validationSchemaCase2 : validationSchemaCase1;
	const { t } = useTranslation("calculator");
	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);

				return (
					<Grid
						rowGap={{ base: 6, md: 4 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						{maleAndMarried && (
							<>
								<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
									{t("employment")}
								</Text>

								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="radio"
										label={t("spouseEmployedOrRetired")}
										options={getLookUp("boolean", locale)}
										placeholder={t("chooseAnOption")}
										name="spouseEmployedOrRetired"
										error={formik.errors.spouseEmployedOrRetired}
										value={formik.values.spouseEmployedOrRetired}
										onChange={(firstArg) => {
											handleChange("radio", firstArg, "spouseEmployedOrRetired", formik, formKey);
										}}
									/>
								</GridItem>
								{formik.values.spouseEmployedOrRetired === "1" && (
									<GridItem colSpan={2} maxW="500px">
										<FormField
											type="selectableTags"
											label={t("ageOfTheOldestEmployedFamilyMember")}
											options={getLookUp("ageGroup", locale)}
											placeholder={t("chooseAnOption")}
											name="ageOfTheOldestEmployedFamilyMember"
											error={formik.errors.ageOfTheOldestEmployedFamilyMember}
											value={formik.values.ageOfTheOldestEmployedFamilyMember}
											onChange={(firstArg) => {
												handleChange(
													"selectableTags",
													firstArg,
													"ageOfTheOldestEmployedFamilyMember",
													formik,
													formKey
												);
											}}
										/>
									</GridItem>
								)}
							</>
						)}
						<GridItem colSpan={2}>
							<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
								{t("incomeInformation")}
							</Text>

							<GrayBox>
								<UnorderedList>
									<Text fontWeight={"bold"}>{t("incomeRules1")}</Text>
									<UnorderedList>
										<ListItem>
											<Text color="#1B1D21">{t("incomeRules2")}</Text>
										</ListItem>
										<ListItem>
											<Text color="#1B1D21">{t("incomeRules3")}</Text>
										</ListItem>
										<ListItem>
											<Text color="#1B1D21">{t("incomeRules4")}</Text>
										</ListItem>
										<ListItem>
											<Text color="#1B1D21">{t("incomeRules5")}</Text>
										</ListItem>
										<ListItem>
											<Text color="#1B1D21">{t("incomeRules6")}</Text>
										</ListItem>
									</UnorderedList>
								</UnorderedList>
							</GrayBox>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="text"
								label={t("totalIncome")}
								name="totalIncome"
								customFormat={formatAmount}
								error={formik.errors.totalIncome}
								placeholder={t("writeAnswerHere")}
								value={formik.values.totalIncome}
								onChange={(firstArg) => {
									handleChange("text", firstArg, "totalIncome", formik, formKey);
								}}
							/>
						</GridItem>
					</Grid>
				);
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};
