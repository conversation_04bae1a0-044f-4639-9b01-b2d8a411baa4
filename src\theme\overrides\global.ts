const globalStyles = {
	styles: {
		global: {
			html: {
				h: "full",
				minH: "full",
				fontSize: { base: "16px", xl: "16px" },
				color: "brand.textColor",
			},
			body: {
				h: "full",
				minH: "full",
				fontSize: { base: "16px", xl: "16px" },
				color: "brand.textColor",
			},
			"*::placeholder": {
				color: "white",
				opacity: "0.6",
			},
			"*, *::before, &::after, *:focus": {
				wordWrap: "break-word",
				boxShadow: "none",
			},
			"input:focus": {
				boxShadow: "none !important",
			},
			"*:focus": {
				outline: "none",
				outlineStyle: "none",
				boxShadow: "none",
				borderColor: "transparent",
			},
		},
	},
};

export default globalStyles;
