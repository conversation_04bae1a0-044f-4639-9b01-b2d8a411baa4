import { LowIncomeCase } from "../calculator";

export class LowIncomeCalculator {
	public static calculateChild(data: LowIncomeCase) {
		const { personalInformation } = data;
		if (personalInformation.haveChildren === "1") {
			const numberOfPodChildren = Number(personalInformation.numberOfPODChildren);

			let podChildsAllowance = 0;
			let normalChildsAllowance = 0;

			if (personalInformation.isChildrenPOD === "1") {
				podChildsAllowance = 5000 * numberOfPodChildren;
			}
			const numberOfChildren =
				Number(personalInformation.numberOfChildren) - (numberOfPodChildren || 0);
			if (numberOfChildren === 1) {
				normalChildsAllowance = 2400;
			} else if (numberOfChildren === 2) {
				normalChildsAllowance = 4000;
			} else if (numberOfChildren === 3) {
				normalChildsAllowance = 5600;
			} else if (numberOfChildren > 3) {
				normalChildsAllowance = 5600 + 800 * (numberOfChildren - 3);
			}
			return normalChildsAllowance + podChildsAllowance;
		}
		return 0;
	}
	public static calculateSelfAllowance(data: LowIncomeCase) {
		let age: any = null;
		if (
			data.personalInformation.maritalStatus === "2" &&
			data.employment.spouseEmployedOrRetired === "1" &&
			data.personalInformation.gender === "1"
		)
			age = data.employment.ageOfTheOldestEmployedFamilyMember;
		else {
			age = data.personalInformation.ageGroup;
		}
		switch (age) {
			case "1":
				return 5000;
			case "2":
				return 7000;
			case "3":
				return 9000;
			case "4":
				return 11000;
			case "5":
				return 13000;
			default:
				return 0;
		}
	}
	public static calculateSpousesAllowance(data: LowIncomeCase) {
		const { personalInformation } = data;
		if (personalInformation.maritalStatus === "2" && personalInformation.gender === "1") {
			const numberOfSpouses = Number(personalInformation.numberOfSpouses);
			const numberOfPODSpouses = Number(personalInformation.numberOfPODSpouses);
			if (personalInformation.isSpousesPOD === "1") {
				let podSpouseAll = 0;
				let nonPodSpouseAll = 0;
				podSpouseAll = numberOfPODSpouses * 5000;
				nonPodSpouseAll = (numberOfSpouses - numberOfPODSpouses) * 3500;
				return podSpouseAll + nonPodSpouseAll;
			} else {
				return 3500 * numberOfSpouses;
			}
		}
		return 0;
	}
	public static calculate(data: LowIncomeCase) {
		const self = this.calculateSelfAllowance(data);
		const child = this.calculateChild(data);
		const spouse = this.calculateSpousesAllowance(data);
		const final = self + child + spouse - Number(data.employment.totalIncome);
		return {
			self,
			child,
			spouse,
			final,
			eightyPercant: final * 0.8,
		};
	}
}
