import * as Yup from "yup";

export const validationSchemaCase1 = Yup.object({
	totalIncome: Yup.number()
		.required()
		.label("thisField")
		.typeError("ThisFieldShouldbeNumber")
		.test({
			name: "no-signs-or-dots",
			message: "PleaseEnteranIntegerNumber",
			test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
		}),
});

export const validationSchemaCase2 = Yup.object({
	spouseEmployedOrRetired: Yup.string().required().label("thisField"),
	ageOfTheOldestEmployedFamilyMember: Yup.object().when("spouseEmployedOrRetired", {
		is: (spouseEmployedOrRetired) => {
			return spouseEmployedOrRetired === "1";
		},
		then: Yup.object()
			.shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			})
			.required(),
		otherwise: Yup.object().nullable().notRequired(),
	}),
	totalIncome: Yup.number()
		.required()
		.typeError("ThisFieldShouldbeNumber")
		.test({
			name: "no-signs-or-dots",
			message: "PleaseEnteranIntegerNumber",
			test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
		})
		.label("thisField"),
});
