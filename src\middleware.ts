import { withAuth } from "next-auth/middleware";
import { NEXT_AUTH_ENABLED } from "config";

export default withAuth({
	callbacks: {
		authorized({ req, token }) {
			if (!NEXT_AUTH_ENABLED) return true;

			if (!token) return false;
			if (token.error) return false;
			// const user = token.user;

			// if (!user) return false;

			return !!token;
		},
	},
});

export const config = {
	/* Add routes that need auth over here */
	matcher: [
		/* Page Routes */
		//"/how-to-apply",
		"/test-service",
		"/smart-services/how-to-apply/apply-socialaid/:path*",
		"/smart-services/to-whom-apply/whom-it-concern",
		"/smart-services/farmer-service/apply-farmer-service/:path*",
		"/smart-services/inflation-service/apply-inflation/:path*",
		"/my-cases",
		"/my-allowance",
		//"/allowance-calculator",
		//"/faq",
		//"/about",
		"/profile",
		//"/complaints",

		/* API Routes */
		"/api/updateProfile",
		"/api/profileImage",
		"/api/document/:path*",
		"/api/notifications/:path*",
		"/api/request/:path*",
		//"/api/complaint/:path*",
	],
};
