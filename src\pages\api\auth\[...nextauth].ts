import {
	NEXT_AUTH_SESSION_MAX_AGE,
	SHOW_MOCK_LOGIN,
	SITE_URL,
	UAEPASS_AUTH_API,
	UAEPASS_CLIENT_ID,
	UAEPASS_CLIENT_SECRET,
	UAEPASS_SIGNOUT_URL,
	UAEPASS_TOKEN_API,
	UAEPASS_USERINFO_API,
} from "config";
import mockUser from "mock/user";
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { BackendServices } from "services/backend";

export default NextAuth({
	providers: [
		CredentialsProvider({
			name: "otp",
			credentials: {
				username: { label: "Username", type: "text" },
				otp: { label: "OTP", type: "password" },
				phone: { label: "Phone", type: "text" },
			},
			async authorize(credentials, req) {
				if (!credentials || !credentials.username || !credentials.otp || !credentials.phone)
					return null;

				if (
					SHOW_MOCK_LOGIN &&
					credentials.username === "MOCK" &&
					credentials.otp === "MOCK" &&
					credentials.phone === "MOCK"
				) {
					const user = mockUser;
					return { ...user, id: user?.EmiratesID || "" };
				}
				const data = await BackendServices.validateOtp(
					credentials.username,
					credentials.otp,
					credentials.phone
				);

				if (!data.IsSuccess || !data.Data) return null;

				const user = data.Data;

				if (user) {
					return { ...user, id: user?.EmiratesID || "", Image: null };
				} else {
					return null;
				}
			},
		}),
		{
			id: "uaepass",
			name: "UAEPASS",
			type: "oauth",
			authorization: {
				url: UAEPASS_AUTH_API,
				params: {
					acr_values: "urn:safelayer:tws:policies:authentication:level:low",
					scope: "urn:uae:digitalid:profile:general",
				},
			},
			token: UAEPASS_TOKEN_API,
			userinfo: UAEPASS_USERINFO_API,
			clientId: UAEPASS_CLIENT_ID,
			clientSecret: UAEPASS_CLIENT_SECRET,
			checks: ["state"],
			profile(profile) {
				return {
					id: profile.uuid,
					email: profile.email,
					name: profile.fullnameEN || profile.fullnameAR,
				};
			},
		},
	],
	callbacks: {
		async signIn({ user, account, profile, email, credentials }) {
			if (!!profile) {
				// oauth flow
				return profile.userType === "SOP2" || profile.userType === "SOP3";
			}
			if (!!credentials) {
				// otp flow
				return true;
			}
			return false;
		},

		async jwt({ token, account, profile, user }) {
			if (account?.type === "oauth" && profile) {
				// Runs only on login
				// token.accessToken = account.access_token;
				token.refreshToken = account.refresh_token;
				const data = await BackendServices.uaepassPostLogin(profile.idn, profile.uuid);
				if (data.IsSuccess && data.Data) {
					token.user = { ...data.Data, Image: null };
				}
				//  else {
				// 	return { ...token, error: "UAE Pass Error" };
				// }
				token.provider = "oauth";
			}

			if (account?.type === "credentials") {
				// Runs only on login
				token.user = {
					...user,
				};
				token.provider = "otp";
			}

			if (!token.user) return { ...token, error: "Invalid State" };

			return token;
		},

		async session({ session, token, user }) {
			// Send properties to the client
			// session.accessToken = token.accessToken;
			// session.error = token.error;
			const userDetails = token.user as Object;
			session.user = { ...session.user, ...userDetails };
			session.provider = token.provider as string;
			return session;
		},
		redirect({ url, baseUrl }) {
			let locale = "en";
			if (url.indexOf("en") > -1) locale = "en";
			else locale = "ar";
			if (url?.indexOf("apply-socialaid") > -1) {
				url = SITE_URL + "/smart-services/how-to-apply/apply-socialaid";
				return url;
			}
			if (url?.indexOf("inflation-service") > -1) {
				url = SITE_URL + "/smart-services/inflation-service/apply-inflation";
				return url;
			}
			if (url?.indexOf("to-whom-apply") > -1) {
				url = SITE_URL + "/smart-services/to-whom-apply/whom-it-concern";
				return url;
			}
			if (url?.indexOf("my-cases") > -1) {
				url = SITE_URL + "/my-cases";
				return url;
			}
			if (url.startsWith("/")) return `${baseUrl}${url}`;
			if (url.startsWith("OauthSignOut/")) {
				const locale = url.split("/")[1];
				const signoutWithRedirectUrl = `${UAEPASS_SIGNOUT_URL}?redirect_uri=${encodeURIComponent(
					`${SITE_URL}/${locale}/home`
				)}`;
				return signoutWithRedirectUrl;
			}
			if (new URL(url).origin === baseUrl) return url;
			return baseUrl;
		},
	},
	pages: {
		signIn: "/login",
		signOut: "/home",
		error: "/home", // Error code passed in query string as ?error=
		verifyRequest: "", // (used for check email message)
		newUser: "",
	},
	session: {
		maxAge: NEXT_AUTH_SESSION_MAX_AGE,
	},
});
