import { Box, Divider, Text } from "@chakra-ui/react";
import { VerticalRectangle } from "components/Icons";
// import OrderedListGoldenNumber from "components/Lists/OrderedListGoldenNumber";
import TickList from "components/Lists/TickList";
import React from "react";
import { useTranslation } from "react-i18next";

const EligibilityCriteria = ({ topMargin = 8 }) => {
	const { t } = useTranslation("common");

	// const additionalEligibilityCriteria = [
	// 	t("additionalEligibilityCriteriaForProgramPoint1"),
	// 	t("additionalEligibilityCriteriaForProgramPoint2"),
	// 	t("additionalEligibilityCriteriaForProgramPoint3"),
	// ];
	// const assetsOwnedByFamilyPoints = [
	// 	t("assetsOwnedByFamilyPoint1"),
	// 	t("assetsOwnedByFamilyPoint2"),
	// 	t("assetsOwnedByFamilyPoint3"),
	// ];

	const eligibilityUnder45Points = [
		{ val: t("eligPoints.eligibilityUnder45Point1"), isSub: false },
		{ val: t("eligPoints.eligibilityUnder45Point2"), isSub: false },
		{ val: t("eligPoints.eligibilityUnder45Point3"), isSub: false },
		{ val: t("eligPoints.eligibilityUnder45Point4"), isSub: false },
		{ val: t("eligPoints.eligibilityUnder45Point5"), isSub: false },
		{ val: t("eligPoints.eligibilityUnder45Point6"), isSub: false },
	];

	// const familyIncomePoints = [t("familyIncomePoint1"), t("familyIncomePoint2")];

	return (
		<Box>
			<Box>
				<Box pb={3}>
					<Box fontSize={"lg"} fontWeight="bold" color={"brand.mainGold"} my={3}>
						1. {t("nationaility")}
						<Text color={"brand.textColor"} mt={2} fontWeight="400">
							{t("nationailityDesc")}
						</Text>
						<Text color={"brand.textColor"} fontWeight="400">
							{t("nationailityDesc2")}
						</Text>
					</Box>
					{/* <Box fontSize={"lg"} fontWeight="bold" color={"brand.mainGold"} my={3}>
						2. {t("ageOfBenefactor")}
						<Text color={"brand.textColor"} mt={2} fontWeight="400">
							{t("ageOfBenefactorDesc")}
						</Text>
					</Box> */}
					<Box fontSize={"lg"} fontWeight="bold" color={"brand.mainGold"} my={3}>
						2. {t("monthlyIncome")}
						<Text color={"brand.textColor"} mt={2} fontWeight="400">
							{t("monthlyIncomeDesc")}
						</Text>
						{/* <Box pl="8" pt="2">
							<OrderedListGoldenNumber arr={familyIncomePoints} />
						</Box> */}
					</Box>
				</Box>
			</Box>
			<Divider borderColor="#BBBCBD" my="4" />
			<Box mt={9}>
				<Text fontSize="2xl" fontWeight="bold">
					<VerticalRectangle color="brand.mainGold" w="6px" mr="10px" mb="4px" />
					{t("eligibilityCriteriaForUnder45Program")}
				</Text>
				<Box fontSize={"lg"}>
					<TickList arr={eligibilityUnder45Points} translationFile="common" />
				</Box>
				{/* <OrderedListGoldenNumber arr={additionalEligibilityCriteria} /> */}
			</Box>
			{/* <Box mt={9}>
				<Text fontSize="2xl" fontWeight="bold">
					{t("assetsOwnedByFamily")}
				</Text>
				<Text fontSize={"lg"} color={"brand.textColor"} py={2}>
					{t("assetsOwnedByFamilyDesc")}
				</Text>
				<OrderedListGoldenNumber arr={assetsOwnedByFamilyPoints} />
			</Box>
			<Box mt={9}>
				<Text fontSize="2xl" fontWeight="bold">
					{t("resdiencyException")}
				</Text>
				<Text fontSize={"lg"} color={"brand.textColor"} py={2}>
					{t("resdiencyExceptionDesc")}
				</Text>
			</Box>
			<Box mt={9}>
				<Text fontSize="2xl" fontWeight="bold">
					{t("ageException")}
				</Text>
				<Text fontSize={"lg"} color={"brand.textColor"} py={2}>
					{t("ageExceptionDesc")}
				</Text>
			</Box> */}
		</Box>
	);
};

export default EligibilityCriteria;
