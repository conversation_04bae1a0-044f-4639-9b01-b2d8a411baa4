import { useCallback, useEffect, useState } from "react";
import useAppToast from "./useAppToast";
import { GenerateCustomerPulseToken } from "services/frontend";
import { useQuery } from "react-query";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/router";
/**
 * custom hook to manage the customer pulse modal pop up
 * @param userId string - user unique id , could be emirate id or any unique id
 * @param linkingId the linking id specified for a service
 * @param onMount if true the customer pulse modal will pop when the component load
 * @returns [isLoading, isSubmitted ,open]
 */
const useCustomerPulse = (userId: string, linkingId: string, onMount = false) => {
	const [submitted, setSubmitted] = useState(false);
	const toast = useAppToast();
	const { t } = useTranslation(["personalInfo", "forms", "common"]);
	const { locale } = useRouter();

	const {
		isRefetching,
		isLoading,
		refetch: generateCustomerPulseToken,
	} = useQuery({
		queryKey: ["generateCustomerPulseToken"],
		queryFn: async () => GenerateCustomerPulseToken(userId, linkingId),
		enabled: false,
		retry: 5,
		retryDelay: 500,
	});

	const isGettingTokenLoading = () => isRefetching || isLoading;

	const open = async () => {
		const { data } = await generateCustomerPulseToken();
		if (data?.IsSuccess) {
			window.CustomerPulse.render(document.body, {
				modal: true,
				token: data.Data,
				lang: locale,
				//allow_close: false, default is true, make it false to prevent the user from closing the survey
			});
			window.CustomerPulse.openModal();
		} else {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
			return;
		}
	};

	const setPulseDone = useCallback(() => {
		setSubmitted(true);
	}, []);

	const setPulseError = useCallback(() => {
		toast({
			title: t("common:genericErrorTitle"),
			description: t("common:genericErrorDescription"),
			status: "error",
		});
	}, []);

	useEffect(() => {
		// these are coming from customer pulse documentation
		window.addEventListener("so-widget-completed", setPulseDone);
		window.addEventListener("so-widget-error", setPulseError);

		if (onMount) {
			open();
		}
		return () => {
			window.removeEventListener("so-widget-error", setPulseError);
			window.removeEventListener("so-widget-completed", setPulseDone);
		};
	}, []);

	return [isGettingTokenLoading(), submitted, open] as const;
};

export default useCustomerPulse;
