import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<boolean>>
) {
	const { idDocument, listAttachments } = req.body;
	if (!idDocument || !listAttachments)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const data = await BackendServices.uploadDocument(idDocument, listAttachments);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}

export const config = {
	api: {
		bodyParser: {
			sizeLimit: "10mb",
		},
	},
};
