import { Box } from "@chakra-ui/react";
import HousingInfoForm from "./HousingInfoForm";

function RequestDetailsForm({
	handleChangeEvent,
	formKey,
	caseData,
	handleSetFormikState,
	initialData,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<HousingInfoForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				caseData={caseData}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
