interface Image {
	id: number;
	name: string;
	alternativeText: string | null;
	caption: string | null;
	width: number;
	height: number;
	hash: string;
	ext: string;
	mime: string;
	size: number;
	url: string;
	previewUrl: string | null;
	provider: string;
	provider_metadata: any | null;
	createdAt: string;
	updatedAt: string;
}

interface Goal {
	id: number;
	goal_title: string;
	goal_icon: string;
	goal_body: string;
}

interface Allowance {
	id: number;
	text: string;
}

interface Beneficiary {
	id: number;
	details: string;
	details_button_text: string;
	example_button_text: string;
	details_image: Image | null;
	example_image: Image | null;
}

interface SpecialCase {
	id: number;
	first_text: string;
	header: string;
	sub_header: string;
	case_1: string;
	case_2: string;
	case_3: string;
	learn_more: string;
}

interface Localization {
	// Add properties relevant to your localization data
}

export interface AboutContent {
	id: number;
	page_header: string;
	page_description: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	goals_header: string;
	about_tab_header: string;
	benf_tab_header: string;
	landing_image: Image;
	goals: Goal[];
	allowances: {
		id: number;
		header: string;
		sub_header: string;
		allowances_list: Allowance[];
		image: Image;
	};
	benfs: Beneficiary[];
	special_cases: SpecialCase;
	localizations: Localization[];
}
