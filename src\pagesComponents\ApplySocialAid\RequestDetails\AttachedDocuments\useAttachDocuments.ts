import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useCallback, useEffect, useState } from "react";
import { useIsMutating, useQuery } from "react-query";
import { getDocumentList } from "services/frontend";

const checkRequiredDocumentNotUploaded = (
	docList: ICrmDocumentList["ListAdditionalDoc"] | ICrmDocumentList["ListPersonalDocs"],
	uploadedStatus: any
) => {
	return (docList || []).some((doc) => {
		return !uploadedStatus[doc.IdDocuments] && !doc.IsOptional;
	});
};

const useAttachDocuments = (requestId: string, isDocumentsUploadStep = false) => {
	const [proceedDisabled, setProceedDisabled] = useState(true);
	const [uploadedStatus, setUploadedStatus] = useState<any>({});
	const isDocumentUploading = useIsMutating(["uploadDocument"]);

	const {
		refetch: callGetDocumentList,
		data: documentList,
		isFetching: getDocumentListLoading,
	} = useQuery(
		["getDocumentList", requestId],
		async () => {
			const data = await getDocumentList(requestId);
			if (data.IsSuccess === false) {
				throw new Error(data.Errors || "");
			}
			return data;
		},
		{
			enabled: false,
			cacheTime: 0,
		}
	);

	useEffect(() => {
		const additionalDocStatus = (documentList?.Data?.ListAdditionalDoc || []).reduce((acc, doc) => {
			return { ...acc, [doc.IdDocuments]: doc.Status.Value === "Uploaded On Portal" };
		}, {});

		const personalDocStatus = (documentList?.Data?.ListPersonalDocs || []).reduce((acc, doc) => {
			return { ...acc, [doc.IdDocuments]: doc.Status.Value === "Uploaded On Portal" };
		}, {});

		setUploadedStatus({
			...additionalDocStatus,
			...personalDocStatus,
		});

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [JSON.stringify(documentList)]);

	const setDocumentStatus = useCallback((id: string, status: boolean) => {
		setUploadedStatus((state) => ({
			...state,
			[id]: status,
		}));
	}, []);

	useEffect(() => {
		setProceedDisabled(
			isDocumentsUploadStep &&
				(checkRequiredDocumentNotUploaded(
					documentList?.Data?.ListPersonalDocs || [],
					uploadedStatus
				) ||
					checkRequiredDocumentNotUploaded(
						documentList?.Data?.ListAdditionalDoc || [],
						uploadedStatus
					))
		);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isDocumentsUploadStep, JSON.stringify(documentList), JSON.stringify(uploadedStatus)]);

	return {
		callGetDocumentList,
		documentList: documentList?.Data,
		setDocumentStatus,
		getDocumentListLoading,
		attachDocumentsStepDisabled: proceedDisabled,
		isDocumentUploading,
	};
};

export default useAttachDocuments;
