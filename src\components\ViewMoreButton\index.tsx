import { Box, Button, Divider, H<PERSON><PERSON>ck, VStack } from "@chakra-ui/react";
import { RightArrow } from "components/Icons";

function ViewMoreButton({ children, clickFunction = () => {}, locale = "en" }) {
	return (
		<VStack w="100%">
			<Divider orientation="horizontal" borderColor="brand.mainGold" />
			<Button variant="viewMore" onClick={clickFunction}>
				<HStack color="brand.mainGold" fontWeight="500">
					<Box mr="10px">{children}</Box>
					<Box m="0" p="0" w="10px">
						<RightArrow
							transform={locale === "ar" ? "scale(-1, 1)" : ""}
							transformOrigin="center"
						/>
					</Box>
				</HStack>
			</Button>
		</VStack>
	);
}

export default ViewMoreButton;
