import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export const config = {
	api: {
		bodyParser: {
			sizeLimit: "8mb",
		},
	},
};

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<any>>
) {
	try {
		const { ResponseUrl, ErrorUrl, TransactionsList, LangId } = req.body;

		const Beneficiary = await getContactIdFromToken(req);

		const data = await BackendServices.PurchaseRequest(
			ResponseUrl,
			ErrorUrl,
			TransactionsList,
			LangId
		);

		if (data) {
			res.status(200).json(data);
		} else {
			res.status(500).json(errorResponse);
		}
	} catch (error) {
		console.error(error);
		res.status(500).json(errorResponse);
	}
}
