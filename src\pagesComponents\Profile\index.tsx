import { Box } from "@chakra-ui/react";
import ProfileCard from "./Card";
import ProfileDetail from "./Detail";

const Profile = ({
	profileData,
	profileImage,
	setProfileImage,
	setProfileImageChanged,
	preferredEmailAddress,
	preferredMobileNumber,
	onPreferredEmailAddressChange,
	onChangePreferredMobileNumber,
	onUpdateProfile,
	preferredEmailAddressError,
	preferredMobileNumberError,
	updateProfileLoading,
}) => {
	return (
		<Box gap={4}>
			<Box maxW="58rem" margin={"auto"}>
				<ProfileCard
					profileImage={profileImage}
					setProfileImage={setProfileImage}
					setProfileImageChanged={setProfileImageChanged}
					profileData={profileData}
				/>
				<ProfileDetail
					profileData={profileData}
					preferredEmailAddress={preferredEmailAddress}
					preferredMobileNumber={preferredMobileNumber}
					onPreferredEmailAddressChange={onPreferredEmailAddressChange}
					onChangePreferredMobileNumber={onChangePreferredMobileNumber}
					onUpdateProfile={onUpdateProfile}
					preferredEmailAddressError={preferredEmailAddressError}
					preferredMobileNumberError={preferredMobileNumberError}
					updateProfileLoading={updateProfileLoading}
				/>
			</Box>
		</Box>
	);
};

export default Profile;
