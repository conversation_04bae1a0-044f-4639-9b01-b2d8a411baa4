import { Box } from "@chakra-ui/react";
import TickList from "components/Lists/TickList";
import React from "react";
import { useTranslation } from "react-i18next";

function TermsAndConditions({
	termsList,
	title,
}: {
	termsList: { val: string; isSub: boolean }[];
	title: string;
}) {
	const { t } = useTranslation();
	return (
		<Box my={9}>
			<Box fontSize={"lg"} fontWeight="bold" my={3}>
				{title}
			</Box>
			<Box fontSize={"lg"}>
				<TickList arr={termsList} maxW="1200px" translationFile="common" />
			</Box>
		</Box>
	);
}

export default TermsAndConditions;
