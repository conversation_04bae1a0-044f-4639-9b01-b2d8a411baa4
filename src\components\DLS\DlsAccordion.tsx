import {
	Accordion,
	AccordionButton,
	AccordionIcon,
	AccordionItem,
	AccordionPanel,
	Box,
	Text,
} from "@chakra-ui/react";
import React from "react";
import { FaqItem } from "utils/strapi/faq";

interface Props {
	FaqList: FaqItem[];
}
function DlsAccordion({ FaqList }: Props) {
	return (
		<Accordion allowToggle rowGap={"20rem"}>
			{FaqList.map((faq, idx) => {
				return (
					<AccordionItem mb="1.5rem" px={""} border="0px" key={idx}>
						{({ isExpanded }) => (
							<>
								<AccordionButton
									bg="brand.white.100"
									_expanded={{
										bg: "white",
										borderTop: "1px solid #BBBCBD",
										borderX: "1px solid #BBBCBD",
										borderRadius: "10px 10px 0 0 ",
										pt: "1.5rem",
									}}
									_hover={{
										bg: "#E7E8EA",
									}}
									borderRadius={"10px"}
									fontSize={"1.25rem"}
									fontWeight={"medium"}
									color="black"
									px={"2.5rem"}
									py="1.5rem"

									//24px 42px
								>
									<Box as="span" flex="1" textAlign="left">
										{faq.question_title}
									</Box>
									<AccordionIcon
										borderRadius={"full"}
										borderColor={"brand.textColor"}
										borderWidth={"1.5px"}
										h={6}
										w={6}
									/>
								</AccordionButton>
								<AccordionPanel
									borderBottom={isExpanded ? "1px solid #BBBCBD" : ""}
									borderX={isExpanded ? "1px solid #BBBCBD" : ""}
									borderRadius={"0px 0px 10px 10px"}
									fontSize={"lg"}
									px={"2.5rem"}
									pb="1.5rem"
								>
									<Text as="p" whiteSpace={"pre-line"}>
										{faq.question_body}
									</Text>
								</AccordionPanel>
							</>
						)}
					</AccordionItem>
				);
			})}
		</Accordion>
	);
}

export default DlsAccordion;
