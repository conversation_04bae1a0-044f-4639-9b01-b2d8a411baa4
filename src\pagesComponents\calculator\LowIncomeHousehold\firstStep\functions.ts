import * as Yup from "yup";

export const validationSchema = Yup.object({
	gender: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	ageGroup: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	maritalStatus: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),

	numberOfSpouses: Yup.number().when(["gender", "maritalStatus"], {
		is: (gender, maritalStatus) => {
			//maleAndMarried
			return gender?.value === "1" && maritalStatus.value === "2";
		},
		then: Yup.number()
			.max(4, "lessThanOrEqual4")
			.min(1)
			.required()
			.label("thisField")
			.positive("PleaseEnteraPositivenumber")
			.test({
				name: "no-signs-or-dots",
				message: "PleaseEnteraPositivenumber",
				test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
			})
			.typeError("PleaseEnteraPositivenumber"),
		otherwise: Yup.number().notRequired().nullable(),
	}),

	isSpousesPOD: Yup.string().when(["gender", "maritalStatus"], {
		is: (gender, maritalStatus) => {
			//maleAndMarried
			return gender?.value === "1" && maritalStatus.value === "2";
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),

	numberOfPODSpouses: Yup.number().when(["gender", "maritalStatus", "isSpousesPOD"], {
		is: (gender, maritalStatus, isSpousesPOD) => {
			//maleAndMarried and isSpoise is true
			return gender?.value === "1" && maritalStatus.value === "2" && isSpousesPOD === "1";
		},
		then: Yup.number()
			.max(4, "lessThanOrEqual4")
			.min(1)
			.required()
			.label("thisField")
			.positive("PleaseEnteraPositivenumber")
			.test({
				name: "no-signs-or-dots",
				message: "PleaseEnteraPositivenumber",
				test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
			})
			.typeError("PleaseEnteraPositivenumber"),
		otherwise: Yup.number().notRequired().nullable(),
	}),

	haveChildren: Yup.string().when(["maritalStatus", "gender"], {
		is: (maritalStatus, gender) => {
			//notSingle && !isFemaleAndMarried
			return (
				!!maritalStatus &&
				!!gender &&
				maritalStatus.value !== "1" &&
				!(gender?.value === "2" && maritalStatus.value === "2")
			);
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),
	numberOfChildren: Yup.number().when(["maritalStatus", "gender", "haveChildren"], {
		is: (maritalStatus, gender, haveChildren) => {
			return (
				!!maritalStatus &&
				!!gender &&
				maritalStatus.value !== "1" &&
				!(gender?.value === "2" && maritalStatus.value === "2") &&
				haveChildren === "1"
			);
		},
		then: Yup.number()
			.integer("isIntegerAndhasOneOrTwoDigits")
			.min(0, "isIntegerAndhasOneOrTwoDigits")
			.max(99, "PleaseEntera1or2-digit")
			.typeError("ThisFieldShouldbeNumber")
			.required(),

		otherwise: Yup.number().notRequired().nullable(),
	}),

	isChildrenPOD: Yup.string().when(["maritalStatus", "gender", "haveChildren"], {
		is: (maritalStatus, gender, haveChildren) => {
			//notSingle && !isFemaleAndMarried
			return (
				!!maritalStatus &&
				!!gender &&
				maritalStatus.value !== "1" &&
				!(gender?.value === "2" && maritalStatus.value === "2") &&
				haveChildren === "1"
			);
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),
	numberOfPODChildren: Yup.number().when(
		["maritalStatus", "gender", "haveChildren", "isChildrenPOD"],
		{
			is: (maritalStatus, gender, haveChildren, isChildrenPOD) => {
				//notSingle && !isFemaleAndMarried
				return (
					!!maritalStatus &&
					!!gender &&
					maritalStatus.value !== "1" &&
					!(gender?.value === "2" && maritalStatus.value === "2") &&
					haveChildren === "1" &&
					isChildrenPOD === "1"
				);
			},
			then: Yup.number()
				.label("thisField")
				.required()
				.test({
					name: "validate-number",
					message: "NumberofPoDChildren",
					test: (value: any) => {
						const isInteger = Number.isInteger(value);
						const stringValue = value !== undefined ? value.toString() : "";
						const hasOneOrTwoDigits = /^[0-9]{1,2}$/.test(stringValue.replace(/,/g, ""));

						return isInteger && hasOneOrTwoDigits;
					},
				})
				.max(Yup.ref("numberOfChildren"), "NumberofPoDChildren")
				.min(1)
				.positive("NumberofPoDChildren")
				.typeError("NumberofPoDChildren"),
			otherwise: Yup.number().notRequired().nullable(),
		}
	),
});
