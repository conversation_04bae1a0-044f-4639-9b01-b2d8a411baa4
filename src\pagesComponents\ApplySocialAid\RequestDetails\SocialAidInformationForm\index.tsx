import { Box } from "@chakra-ui/react";
import SocialAidInformationForm from "./SocialAidInformationForm";

function RequestDetailsForm({
	innerText,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
	isMartialEmpty,
	isMaritalInit,
	userAge = 0,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<SocialAidInformationForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
				isMartialEmpty={isMartialEmpty}
				isMaritalInit={isMaritalInit}
				userAge={userAge}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
