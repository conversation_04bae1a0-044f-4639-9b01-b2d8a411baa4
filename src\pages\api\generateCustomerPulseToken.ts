import {
	CUSTOMER_PULSE_CLIENT_ID,
	CUSTOMER_PULSE_CLIENT_SECRET,
	CUSTOMER_PULSE_GRANT_TYPE,
	CUSTOMER_PULSE_SCOPE,
} from "config";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { customerPulseApi } from "services/api";
import { errorResponse } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<string>>
) {
	const { userId, linkingId } = req.body;
	if (!userId || !linkingId)
		return res.status(400).json({ ...errorResponse, Errors: "user id, or linking id is missing" });

	const oauthBody = {
		client_secret: CUSTOMER_PULSE_CLIENT_SECRET!,
		client_id: CUSTOMER_PULSE_CLIENT_ID!,
		grant_type: CUSTOMER_PULSE_GRANT_TYPE!,
		scope: CUSTOMER_PULSE_SCOPE!,
	};
	// getting the api key to be able to get the customer specific token
	const headerToken = await customerPulseApi.post("/auth/oauth/v2/token", oauthBody, {
		headers: {
			"content-type": "application/x-www-form-urlencoded",
		},
	});
	// getting the customer token
	const customerTokenRequest = await customerPulseApi.post(
		"/Outbound/PMO/Rest/CustomerPulse/V1",
		{
			linking_id: linkingId,
			meta_data: {
				customer: {
					user_id: String(userId),
				},
			},
		},
		{
			headers: {
				Authorization: `Bearer ${headerToken.data.access_token}`,
			},
		}
	);

	const {
		message,
		data: { token },
	} = customerTokenRequest.data;

	if (message === "ok") {
		res.status(200).json({ IsSuccess: true, StatusCode: 200, Data: token });
	} else {
		res.status(500).json(errorResponse);
	}
}
