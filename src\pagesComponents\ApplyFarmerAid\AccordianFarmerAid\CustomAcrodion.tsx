import {
	Accordion,
	AccordionItem,
	AccordionButton,
	Box,
	Heading,
	AccordionPanel,
} from "@chakra-ui/react";
import { ThreeVerticalDots } from "components/Icons";

function CustomAccordion({
	currentStep = 0,
	sectionsArray,
	showIcon = false,
	hideBottom = false,
	doesntNeedIncomeInfo = false,
}) {
	const hideIt = (idx: number) => {
		if (idx !== 2 && idx !== 3) return false;
		return doesntNeedIncomeInfo;
	};
	return (
		<Accordion index={currentStep}>
			{sectionsArray?.map((child, idx) => {
				let pb = hideBottom ? "unset" : 4;
				if (!hideBottom && currentStep === idx) pb = 2;
				return (
					<AccordionItem
						borderColor="transparent"
						bg="white"
						key={idx}
						mt={idx > 0 ? 4 : 0}
						boxShadow="0px 1px 1px rgba(100, 116, 139, 0.06), 0px 1px 2px rgba(100, 116, 139, 0.1)"
						borderRadius="1px"
						hidden={hideIt(idx)}
					>
						{child.title && (
							<AccordionButton py={4} pb={pb} px={6} _hover={{ bg: "white", cursor: "unset" }}>
								<Box flex="1" textAlign="left">
									<Heading size="md" fontSize="lg" fontWeight="bold" flexGrow={1}>
										{child.title}
									</Heading>
								</Box>
								{showIcon && <ThreeVerticalDots transform="scale(0.9)" color="brand.gray.500" />}
							</AccordionButton>
						)}
						{!child.title && <AccordionButton p={0}></AccordionButton>}
						<AccordionPanel pb={6} pt={hideBottom ? "-0.75rem" : 0} px={6}>
							{child.element}
						</AccordionPanel>
					</AccordionItem>
				);
			})}
		</Accordion>
	);
}

export default CustomAccordion;
