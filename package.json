{"name": "mocd-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "docker:run": "docker run -p 3000:3000 mocd-portal", "docker:build": "docker build -f Dockerfile -t mocd-portal ."}, "dependencies": {"@chakra-ui/icon": "^3.2.0", "@chakra-ui/icons": "^2.0.12", "@chakra-ui/react": "^2.4.1", "@chakra-ui/system": "^2.6.2", "@chakra-ui/transition": "^2.1.0", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@microsoft/applicationinsights-react-js": "^3.4.0", "@microsoft/applicationinsights-web": "^2.8.9", "@tanstack/react-table": "^8.7.0", "@types/react-datepicker": "^4.8.0", "axios": "^1.4", "chakra-ui-steps": "^1.8.0", "date-fns": "^2.29.3", "file-saver": "^2.0.5", "formik": "^2.2.9", "framer-motion": "^6.5.1", "html2canvas": "^1.4.1", "i18next": "^22.0.6", "jspdf": "^2.5.1", "next": "^15.3.0", "next-auth": "^4.17.0", "next-i18next": "^13.0.0", "next-seo": "^5.14.1", "nextjs-progressbar": "^0.0.16", "react": "18.2.0", "react-datepicker": "^4.8.0", "react-dom": "18.2.0", "react-i18next": "^12.0.0", "react-icons": "^4.12.0", "react-paginate": "^8.1.4", "react-query": "^3.39.2", "react-select": "^5.7.0", "react-simple-star-rating": "^5.1.7", "react-slick": "^0.29.0", "sass": "^1.56.1", "sheetjs-style": "^0.15.8", "slick-carousel": "^1.8.1", "stylis": "^4.1.3", "stylis-plugin-rtl": "^2.1.1", "yup": "^0.32.11"}, "devDependencies": {"@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "eslint": "^8.28.0", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.2", "lint-staged": "^13.0.4", "prettier": "^2.7.1", "typescript": "^4.9.3"}}