import {
	But<PERSON>,
	Checkbox,
	Flex,
	Grid,
	GridItem,
	Link,
	Table,
	TableContainer,
	Tbody,
	Td,
	Text,
	Thead,
	Tooltip,
	Tr,
} from "@chakra-ui/react";
import { Form, Formik } from "formik";
import SocialAidStripedTable from "pagesComponents/ApplySocialAid/SocialAidStripedTable";
import * as functions from "./functions";
import { useTranslation } from "react-i18next";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useRouter } from "next/router";
import {
	IChildFamilyMember,
	IEducationCase,
	IFamilyMember,
} from "interfaces/SocialAidForm.interface";
import { useFormContext } from "context/FormContext";
import { useEffect, useState } from "react";
import { getLookupLabel } from "utils/helpers";
import { formatAmount, formatEmiratesID, formatLocalNumber } from "utils/formatters";
import {
	BORN_UNKNOWN_PARENTS,
	DIVORCED,
	TCS_PDF_FILE_LOCATION_AR,
	TCS_PDF_FILE_LOCATION_EN,
	UNDER_45_AGE,
	UNEMPLOYED_ID,
	TerminatedAr,
	Terminated,
	MilitaryServiceStatusAr,
	MilitaryServiceStatus,
	SPOUSE_INCAPACITATED_FOREIGNER,
	ChildEligibilityforWomeninDifficultyAr,
	ChildEligibilityforWomeninDifficulty,
	CHILD_IN_DIFFICULT_SITUATION,
	EMPLOYEDLOWINCOME,
	EMPLOYED_ID,
	UtilityProviderAr,
	UtilityProviderEn,
} from "config";
import { getCaseUpdateReasonsByCaseId } from "services/frontend";
import { ICrmLookup } from "interfaces/CrmMasterData.interface";

interface Props {
	onSubmit: any;
	setCurrentStep: any;
	handleStepsIndexes: any;
	documentList: ICrmDocumentList;
	familyMembers: IFamilyMember[];
	caseForm: any;
	handleSetFormikState: any;
	formKey: string;
	hasSSS: boolean;
	childMembers: IChildFamilyMember[];
	educationMembers?: IEducationCase[];
	isEligibleForTopup?: boolean;
}

const tableRows = {
	personalInformation: [
		"EmiratesID",
		"IDNBackNumber",
		"caseID",
		"PreferredEmail",
		"AlternativeEmail",
		"PreferredPhoneNumber",
		"alternativeNumber",
		"Occupations",
		"jobTitle",
		"Emirates",
		"Area",
		"Center",
		"MaritalStatus",
	],
	socialAidInformation: [
		"Category",
		"SubCategory",
		"ChildEligibilityforWomeninDifficulty",
		"IsActiveStudent",
		"GuardianEmiratesID",
		"MilitaryServiceStatus",
		"PursuingHigherEducation",
		"PursuingMilitaryService",
		"ReceivedLocalSupport",
		"Terminated",
		"NumberOfChildren",
		"NumberOfChildrenLessThan25",
	],
	housingInformation: [
		"ApplyHousingAllowance",
		"ReceivingFederalLocalhousingsupport",
		"ReceivingHousingAllowanceFromEmployer",
		"FullOwnershipResidentialProperty",
		"IsUtilityBillIssuedForFullyOwnedProperty",
		"LivingSituation",
		"ReceivesHousingSupportFromHusband",
	],
	inflationInformation: [
		"ApplyInflationAllowance",
		"ApplyUtilityAllowance",
		"UtilityProvider",
		"UtilityAccountNumber",
	],
};

const formatIDs = {
	EmiratesID: formatEmiratesID,
	PreferredPhoneNumber: formatLocalNumber,
	alternativeNumber: formatLocalNumber,
	incomeAmount: formatAmount,
	pensionAmount: formatAmount,
	tradeLicenseAmount: formatAmount,
	RentAmount: formatAmount,
};

function ReviewDocumentComponent({
	onSubmit,
	setCurrentStep,
	handleStepsIndexes,
	documentList,
	familyMembers,
	caseForm,
	handleSetFormikState,
	formKey,
	childMembers,
	educationMembers,
	hasSSS = false,
	isEligibleForTopup = true,
}: Props) {
	const { t } = useTranslation(["forms", "tables", "common"]);
	const { locale, query } = useRouter();
	const { lookups } = useFormContext();

	const [caseUpdateReasons, setCaseUpdateReasons] = useState<ICrmLookup[]>([]);
	let requestId = query.requestId?.toString() ? query.requestId?.toString() : "";
	// Call the function and update state
	const getCaseUpdateReasons = () => {
		getCaseUpdateReasonsByCaseId(requestId).then((response) => {
			if (response.IsSuccess && response.Data) {
				const data = Array.isArray(response.Data) ? response.Data : [response.Data]; // Wrap single object in an array
				setCaseUpdateReasons(data);
			}
		});
	};
	useEffect(() => {
		if (requestId) {
			getCaseUpdateReasons();
		}
	}, []);

	const handleEdit = (stepNumber: number) => {
		setCurrentStep(stepNumber);
		handleStepsIndexes(stepNumber);
	};
	const updateUserData = () => {
		let finalizedIncomeInfoArr: any = {};
		let hideKeysArr: any = []; // This array is used to add keys to hide specific rows
		if (UNEMPLOYED_ID === caseForm?.personalInformation?.Occupations) hideKeysArr.push("jobTitle");
		if (
			caseForm?.socialAidInformation?.SubCategory !== DIVORCED &&
			caseForm?.socialAidInformation?.SubCategory !== SPOUSE_INCAPACITATED_FOREIGNER
		) {
			hideKeysArr.push("ChildEligibilityforWomeninDifficulty");
			hideKeysArr.push("NumberOfChildren");
			hideKeysArr.push("NumberOfChildrenLessThan25");
		}
		if (caseForm?.socialAidInformation?.Category !== CHILD_IN_DIFFICULT_SITUATION) {
			hideKeysArr.push("PursuingHigherEducation");
			hideKeysArr.push("PursuingMilitaryService");
		}

		if (caseForm?.socialAidInformation?.SubCategory !== UNDER_45_AGE) {
			hideKeysArr.push("IsActiveStudent");
			hideKeysArr.push("ReceivedLocalSupport");
			hideKeysArr.push("MilitaryServiceStatus");
			hideKeysArr.push("Terminated");
		}

		if (caseForm?.socialAidInformation?.Category !== BORN_UNKNOWN_PARENTS)
			hideKeysArr.push("GuardianEmiratesID");

		if (
			caseForm?.socialAidInformation?.Category !== EMPLOYEDLOWINCOME &&
			caseForm?.personalInformation?.Occupations !== EMPLOYED_ID
		)
			hideKeysArr.push("ReceivingHousingAllowanceFromEmployer");

		if (caseForm?.housingInformation?.FullOwnershipResidentialProperty !== "yes")
			hideKeysArr.push("IsUtilityBillIssuedForFullyOwnedProperty");

		if (
			caseForm?.housingInformation?.FullOwnershipResidentialProperty === "yes" &&
			caseForm?.housingInformation?.IsUtilityBillIssuedForFullyOwnedProperty === "yes"
		)
			hideKeysArr.push("LivingSituation");

		if (caseForm?.socialAidInformation?.SubCategory !== DIVORCED)
			hideKeysArr.push("ReceivesHousingSupportFromHusband");

		if (caseForm?.inflationInformation?.ApplyUtilityAllowance === "no") {
			hideKeysArr.push("UtilityProvider");
			hideKeysArr.push("UtilityAccountNumber");
		}

		hideKeysArr.push("IDNBackNumber");

		for (let formKey in tableRows) {
			finalizedIncomeInfoArr[formKey] = [];
			for (let key of tableRows[formKey]) {
				if (!(key in caseForm[formKey])) continue;
				let newItem: any = {
					label: key,
					value:
						getLookupLabel(lookups, key, caseForm?.[formKey]?.[key]) ||
						formatIDs?.[key]?.(caseForm?.[formKey]?.[key] || "-") ||
						caseForm?.[formKey]?.[key] ||
						"-",
				};
				if (key === "LivingSituation") {
					newItem = {
						label: "LivingSituation",
						value:
							getLookupLabel(lookups, "accomadations", caseForm?.[formKey]?.[key]) ||
							formatIDs?.[key]?.(caseForm?.[formKey]?.[key] || "-") ||
							caseForm?.[formKey]?.[key] ||
							"-",
					};
				}
				if (hideKeysArr.length > 0 && hideKeysArr.includes(key)) {
					newItem.hide = true;
				}
				if ((newItem.value === "yes" || newItem.value === "no") && locale === "ar") {
					if (newItem.value === "yes") newItem.value = "نعم";
					if (newItem.value === "no") newItem.value = "لا";
				}
				if (newItem.label === "MilitaryServiceStatus") {
					if (newItem.value) {
						newItem.value =
							locale === "ar"
								? MilitaryServiceStatusAr.filter(
										(item) => item.value === newItem.value.toString()
								  )[0]?.label
								: MilitaryServiceStatus.filter((item) => item.value === newItem.value.toString())[0]
										?.label;
					}
				}
				if (newItem.label === "Terminated") {
					if (newItem.value) {
						newItem.value =
							locale === "ar"
								? TerminatedAr.filter((item) => item.value === newItem.value.toString())[0]?.label
								: Terminated.filter((item) => item.value === newItem.value.toString())[0]?.label;
					}
				}
				if (newItem.label === "ChildEligibilityforWomeninDifficulty") {
					if (newItem.value) {
						let arrayValues =
							locale === "ar"
								? ChildEligibilityforWomeninDifficultyAr
								: ChildEligibilityforWomeninDifficulty;
						let finalLabels = "";
						arrayValues.map((item) => {
							if (newItem.value.includes(item.value)) finalLabels += item.label + " , ";
						});
						newItem.value = finalLabels;
					}
				}
				if (newItem.label === "UtilityProvider") {
					if (newItem.value) {
						newItem.value =
							locale === "ar"
								? UtilityProviderAr.filter((item) => item.value === newItem.value.toString())[0]
										?.label
								: UtilityProviderEn.filter((item) => item.value === newItem.value.toString())[0]
										?.label;
					}
				}
				finalizedIncomeInfoArr[formKey].push(newItem);
			}
		}
		return finalizedIncomeInfoArr;
	};

	const [userData, setUserData] = useState(() => updateUserData());
	const [termsRead, setTermsRead] = useState(false);
	useEffect(() => {
		setUserData(() => updateUserData());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [locale]);

	const documentsTableData = functions.createDocumentsTableData(documentList, locale);

	const familyMembersTablesData = functions.createFamilyMembersTablesData(
		familyMembers,
		childMembers,
		lookups,
		locale,
		formatIDs
	);

	const educationTableData = functions.createEducationTablesData(
		familyMembers,
		lookups,
		locale,
		educationMembers
	);

	const incomeTablesData = functions.createIncomeTablesData(
		caseForm["incomeInformation"],
		lookups,
		locale,
		formatIDs
	);

	return (
		<Formik
			enableReinitialize
			initialValues={functions.getInitialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || !formik.dirty,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 10, md: 10 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("personalInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(1)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<SocialAidStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.personalInformation}
									caption={null}
								/>
							</GridItem>
							{caseUpdateReasons.length > 0 ? (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("EditReason")}
										</Text>
									</Flex>
									<TableContainer
										mb={4}
										border="1px"
										borderBottom="0px"
										borderColor="brand.tableBorderColor"
										rounded="lg"
									>
										<Table variant="simple">
											<Thead></Thead>
											<Tbody>
												{caseUpdateReasons.length > 0
													? caseUpdateReasons.map((reason, index) => (
															<Tr key={index}>
																<Td
																	borderColor="brand.tableBorderColor"
																	fontSize="0.875rem"
																	fontWeight="normal"
																	letterSpacing="unset"
																	lineHeight="150%"
																>
																	{locale == "en" ? reason?.Name : reason.NameAR}
																</Td>
															</Tr>
													  ))
													: ""}
											</Tbody>
										</Table>
									</TableContainer>
								</GridItem>
							) : (
								""
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
									<Text fontSize={"1.25rem"} fontWeight={"bold"}>
										{t("socialAidInformation")}
									</Text>
									<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(0)}>
										{t("edit", { ns: "common" })}
									</Button>
								</Flex>
								<SocialAidStripedTable
									sourceOfTranslation="forms"
									tableBody={userData.socialAidInformation}
									caption={null}
									SubcatValue={caseForm?.socialAidInformation?.SubCategory}
								/>
							</GridItem>

							{/* Only show Housing section if eligible and ApplyHousingAllowance is "yes" */}
							{isEligibleForTopup &&
								caseForm?.housingInformation?.ApplyHousingAllowance === "yes" && (
									<GridItem colSpan={{ base: 2, md: 2 }}>
										<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
											<Text fontSize={"1.25rem"} fontWeight={"bold"}>
												{t("housingInformation")}
											</Text>
											<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(3)}>
												{t("edit", { ns: "common" })}
											</Button>
										</Flex>
										<SocialAidStripedTable
											sourceOfTranslation="forms"
											tableBody={userData.housingInformation}
											caption={null}
										/>
									</GridItem>
								)}
							{caseForm?.inflationInformation?.ApplyInflationAllowance === "yes" && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("inflationInformation")}
										</Text>
										<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(5)}>
											{t("edit", { ns: "common" })}
										</Button>
									</Flex>
									<SocialAidStripedTable
										sourceOfTranslation="forms"
										tableBody={userData.inflationInformation}
										caption={null}
									/>
								</GridItem>
							)}
							{caseForm?.housingInformation?.ApplyHousingAllowance === "no" && false && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("housingInformation")}
										</Text>
										<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(3)}>
											{t("edit", { ns: "common" })}
										</Button>
									</Flex>
									<SocialAidStripedTable
										sourceOfTranslation="forms"
										tableBody={[
											{
												label: t("ApplyHousingAllowance"),
												value: t("no", { ns: "common" }),
											},
										]}
										caption={null}
									/>
								</GridItem>
							)}

							{(incomeTablesData.income.length > 0 ||
								incomeTablesData.pension.length > 0 ||
								incomeTablesData.tradeLicense.length > 0 ||
								incomeTablesData.RentalIncomes.length > 0) && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<>
										{incomeTablesData.income.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("income")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.income}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
										{/* {incomeTablesData.pension.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("pension")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.pension}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)} */}
										{incomeTablesData.tradeLicense.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("tradeLicense")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.tradeLicense}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
										{incomeTablesData.RentalIncomes.length > 0 && (
											<>
												<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
													<Text fontSize={"1.25rem"} fontWeight={"bold"}>
														{t("RentalIncomes")}
													</Text>
													<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
														{t("edit", { ns: "common" })}
													</Button>
												</Flex>
												<SocialAidStripedTable
													tableBody={incomeTablesData.RentalIncomes}
													caption={null}
													sourceOfTranslation="forms"
												/>
											</>
										)}
									</>
								</GridItem>
							)}
							{familyMembersTablesData.length > 0 &&
								familyMembersTablesData.map((tableData, idx) => (
									<GridItem colSpan={{ base: 2, md: 2 }} key={idx}>
										<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
											<Text fontSize={"1.25rem"} fontWeight={"bold"}>
												{t("familyMemberInformation")}
											</Text>
											<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(2)}>
												{t("edit", { ns: "common" })}
											</Button>
										</Flex>

										<SocialAidStripedTable
											tableBody={tableData.data.filter((row, index) => {
												const excludeIndices = [6, 7, 8, 9];

												if (
													excludeIndices.includes(index) &&
													tableData.data[5] &&
													tableData.data[5].label === "ApplyEducationAllowance" &&
													(tableData.data[5].value === "No" || tableData.data[5].value === "لا")
												) {
													return false;
												} else {
													return true;
												}
											})}
											caption={null}
											sourceOfTranslation="forms"
										/>
									</GridItem>
								))}
							{/* Only show Education section if eligible */}
							{isEligibleForTopup && educationTableData!.length > 0 && (
								<>
									{educationTableData?.map((tableData, idx) => (
										<GridItem colSpan={{ base: 2, md: 2 }} key={idx}>
											<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
												<Text fontSize={"1.25rem"} fontWeight={"bold"}>
													{t("educationInformation")}
												</Text>
												<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(4)}>
													{t("edit", { ns: "common" })}
												</Button>
											</Flex>

											<SocialAidStripedTable
												tableBody={tableData.data.filter((row, index) => {
													// Check if this is a university field
													const isUniversityField = [
														"universityName",
														"unaccreditedUniversityName",
														"cgpa",
														"creditHours",
													].includes(row.label);

													// Always show university fields if childCompletedSemesterInUniversity is "yes"
													if (isUniversityField) {
														const hasCompletedSemester = tableData.data.some(
															(item) =>
																item.label === "childCompletedSemesterInUniversity" &&
																(item.value === "Yes" || item.value === "نعم")
														);
														return hasCompletedSemester;
													}

													// Original filter logic for other fields
													const excludeIndices = [6, 7, 8, 9];
													if (
														excludeIndices.includes(index) &&
														tableData.data[5] &&
														tableData.data[5].label === "ApplyEducationAllowance" &&
														(tableData.data[5].value === "No" || tableData.data[5].value === "لا")
													) {
														return false;
													} else {
														return true;
													}
												})}
												caption={null}
												sourceOfTranslation="forms"
											/>
										</GridItem>
									))}
								</>
							)}

							{documentsTableData.length > 0 && (
								<GridItem colSpan={{ base: 2, md: 2 }}>
									<Flex mb={4} justifyContent={"space-between"} alignItems={"center"}>
										<Text fontSize={"1.25rem"} fontWeight={"bold"}>
											{t("documents")}
										</Text>
										<Button variant="outline" disabled={hasSSS} onClick={() => handleEdit(6)}>
											{t("edit", { ns: "common" })}
										</Button>
									</Flex>
									<SocialAidStripedTable tableBody={documentsTableData} caption={null} />
								</GridItem>
							)}
							<GridItem colSpan={{ base: 2, md: 2 }}>
								<Flex>
									<Tooltip label={t("youHaveToReadTerms")} isDisabled={termsRead}>
										<Checkbox
											onChange={() => {
												formik.setFieldValue(
													"termsAndConditions",
													!formik.values.termsAndConditions
												);
											}}
											name="termsAndConditions"
											disabled={!termsRead}
											id="termsAndcond"
										></Checkbox>
									</Tooltip>
									<Text htmlFor="termsandcond" as="label" ms={2.5}>
										{t("readTermsAndConditions", { ns: "tables" })}{" "}
										<Text as="span" color="brand.mainGold" fontSize={"larger"}>
											<Link
												href={locale === "ar" ? TCS_PDF_FILE_LOCATION_AR : TCS_PDF_FILE_LOCATION_EN}
												target={"_blank"}
												onClick={() => setTermsRead(true)}
												fontSize={"xl"}
											>
												{t("acknowledgementAndUndertaking", { ns: "tables" })}
											</Link>
											.
										</Text>
									</Text>
								</Flex>
								<Flex mt={3}>
									<Checkbox
										onChange={() => {
											formik.setFieldValue(
												"confirmAccurateInformation",
												!formik.values.confirmAccurateInformation
											);
										}}
										name="confirmAccurateInformation"
										id="confirmacc"
									></Checkbox>
									<Text as="label" htmlFor="confirmacc" ms={2.5}>
										{t("confirmAccurateInformation", { ns: "tables" })}
									</Text>
								</Flex>
							</GridItem>
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default ReviewDocumentComponent;
