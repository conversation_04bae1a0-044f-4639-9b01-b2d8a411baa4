import { formatAmount } from "utils/formatters";
import { LowIncomeCase } from "../calculator";
import { localizedLookups } from "../lookups";

export const lowIncomeTableBuilder = (
	data: LowIncomeCase,
	locale: string = "ar",
	reason: string
) => {
	const { employment, personalInformation } = data;
	const personalInformationData = {
		header: "personalInformation",
		data: [
			{ label: "reasonForApplying", value: convertLookup(reason, "reason", locale) },
			{ label: "gender", value: convertLookup(personalInformation.gender!, "gender", locale) },
			{
				label: "ageGroup",
				value: convertLookup(personalInformation.ageGroup!, "ageGroup", locale),
			},
			{
				label: "maritalStatus",
				value: convertLookup(personalInformation.maritalStatus!, "maritalStatus", locale),
			},
		],
	};
	// add wifes info if the user is  married male
	if (personalInformation.gender === "1" && personalInformation.maritalStatus === "2") {
		const numberOfSpousesData = {
			label: "numberOfSpouses",
			value: String(personalInformation.numberOfSpouses),
		};
		const areSpousePODData = {
			label: "isSpousesPOD",
			value: convertLookup(personalInformation.isSpousesPOD!, "boolean", locale),
		};
		// if male has POD spouses add the number of them to the table
		let spousePODNumber: any = null;
		if (personalInformation.isSpousesPOD === "1") {
			spousePODNumber = {
				label: "numberOfPODSpouses",
				value: String(personalInformation.numberOfPODSpouses),
			};
		}
		personalInformationData.data.push(numberOfSpousesData);
		personalInformationData.data.push(areSpousePODData);
		if (spousePODNumber) {
			personalInformationData.data.push(spousePODNumber);
		}
	}

	// add children info, if there is a potentiol for children
	const notSingle = personalInformation.maritalStatus !== "1";
	const isFemaleAndMarried =
		personalInformation.gender === "2" && personalInformation.maritalStatus === "2";
	// if the marital is not single , and is not married female
	const withPotentialChildren = !isFemaleAndMarried && notSingle;
	if (withPotentialChildren) {
		const haveChildrenData = {
			label: "haveChildren",
			value: convertLookup(personalInformation.haveChildren!, "boolean", locale),
		};
		let numberOfChildren: any | null = null;
		let isChildrenPOD: any = null;
		let childrenPODNumber: any = null;

		personalInformationData.data.push(haveChildrenData);

		if (personalInformation.haveChildren === "1") {
			numberOfChildren = {
				label: "numberOfChildren",
				value: String(personalInformation.numberOfChildren),
			};
			isChildrenPOD = {
				label: "isChildrenPOD",
				value: convertLookup(personalInformation.isChildrenPOD!, "boolean", locale),
			};
			childrenPODNumber = {
				label: "numberOfPODChildren",
				value: String(personalInformation.numberOfPODChildren),
			};

			personalInformationData.data.push(numberOfChildren);
			personalInformationData.data.push(isChildrenPOD);

			if (personalInformation.isChildrenPOD === "1")
				personalInformationData.data.push(childrenPODNumber);
		}
	}
	// add employment info
	const employmentData: typeof personalInformationData = {
		header: "employment",
		data: [],
	};
	if (personalInformation.maritalStatus === "2" && personalInformation.gender === "1") {
		const spouseEmployedOrRetiredData = {
			label: "spouseEmployedOrRetired",
			value: convertLookup(employment.spouseEmployedOrRetired!, "boolean", locale),
		};
		employmentData.data.push(spouseEmployedOrRetiredData);
		if (employment.spouseEmployedOrRetired === "1") {
			const ageOfTheOldestEmployedFamilyMember = {
				label: "ageOfTheOldestEmployedFamilyMember",
				value: convertLookup(
					employment.ageOfTheOldestEmployedFamilyMember!,
					"ageOfTheOldestEmployedFamilyMember",
					locale
				),
			};

			employmentData.data.push(ageOfTheOldestEmployedFamilyMember);
		}
	}
	const incomeData = {
		header: "incomeInformation",
		data: [
			{
				label: "totalIncome",
				value: formatAmount(String(employment.totalIncome), 0),
			},
		],
	};
	return [personalInformationData, employmentData, incomeData];
};

export const convertLookup = (
	lookupValue: string,
	lookupName: string,
	locale: string = "ar"
): string => {
	const lookups = localizedLookups(locale);
	return lookups[lookupName].find((lookup) => lookup.value === lookupValue).label;
};
