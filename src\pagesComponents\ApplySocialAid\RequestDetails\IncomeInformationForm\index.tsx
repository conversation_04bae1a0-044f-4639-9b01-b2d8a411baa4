import { Box } from "@chakra-ui/react";
import SocialAidInformationForm from "./IncomeInformationForm";

function RequestDetailsForm({
	innerText,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	handleAddDeleteFieldArray,
	initialData,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<SocialAidInformationForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				handleAddDeleteFieldArray={handleAddDeleteFieldArray}
				initialData={initialData}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
