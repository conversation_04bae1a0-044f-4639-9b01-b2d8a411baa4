import {
	Box,
	Grid,
	GridItem,
	Image,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	useDisclosure,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import * as functions from "./functions";
import useAppToast from "hooks/useAppToast";
import { CloseIcon } from "@chakra-ui/icons";
import { formatEmiratesID } from "utils/formatters";
import { farmerServiceSocialEntites } from "config";
import Link from "next/link";

function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	EmiratesID,
	readOnly = false,
}) {
	const { t } = useTranslation(["forms", "common"]);
	const { isOpen, onO<PERSON>, onClose } = useDisclosure();
	const { lookups } = useFormContext();
	const toast = useAppToast();
	let [validationSchema] = useState(functions.getValidationSchema());

	const entityStub = farmerServiceSocialEntites.map((ent) => ({
		value: ent.value,
		label: t(ent.label),
	}));

	const [showOther, setShowOther] = useState(
		initialData.RelatedEmiratesID !== "" &&
			initialData.RelatedEmiratesID !== EmiratesID &&
			initialData.RelatedEmiratesID !== undefined
	);
	const [initialValues, setInitialValues] = useState(parseApiToSocialAidInfo());
	function parseApiToSocialAidInfo() {
		const obj: any = {};
		obj.EWEBill = initialData.EWEBill || "";
		if (initialData.RelatedEmiratesID !== "" && initialData.RelatedEmiratesID !== undefined) {
			if (initialData.RelatedEmiratesID === EmiratesID) {
				obj.OwnerEWEBill = lookups.Boolean[0];
			} else {
				obj.OwnerEWEBill = lookups.Boolean[1];
			}
			obj.RelatedEmiratesID = initialData.RelatedEmiratesID;
		}
		if (initialData.RegisteredWithEWE !== undefined) {
			obj.RegisteredWithEWE = initialData.RegisteredWithEWE
				? lookups.Boolean[0]
				: lookups.Boolean[1];
		}
		if (initialData.ReceiveSocialAid !== undefined) {
			obj.ReceiveSocialAid =
				initialData.ReceiveSocialAid === "yes" || initialData.ReceiveSocialAid === true
					? lookups.Boolean[0]
					: lookups.Boolean[1];
		}
		if (initialData.ReceiveInflationAllowance !== undefined) {
			if (initialData.ReceiveInflationAllowance !== "") {
				obj.ReceiveInflationAllowance =
					initialData.ReceiveInflationAllowance === "yes" ||
					initialData.ReceiveInflationAllowance === true
						? lookups.Boolean[0]
						: lookups.Boolean[1];
			}
		}
		if (initialData.EntityReceivedFrom !== undefined) {
			obj.EntityReceivedFrom = entityStub.find(
				(ent) => ent.value === initialData.EntityReceivedFrom
			);
		}
		return obj;
	}

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			validateOnMount
		>
			{(formik) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled:
							!formik.isValid ||
							formik.isSubmitting ||
							formik.values["RegisteredWithEWE"]?.value === "no",
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							{/* is registered ewe field */}
							<GridItem colSpan={{ base: 2, md: 1 }}>
								<FormField
									type="selectableTags"
									isRequired={true}
									name="RegisteredWithEWE"
									options={lookups.Boolean}
									label={t("RegisteredWithEWE")}
									placeholder={""}
									error={formik.errors["RegisteredWithEWE"]}
									touched={formik.touched["RegisteredWithEWE"]}
									value={formik.values.RegisteredWithEWE}
									isDisabled={readOnly}
									onChange={(firstArg) => {
										handleChangeEvent(
											"selectableTags",
											firstArg,
											"RegisteredWithEWE",
											formik,
											formKey
										);
										if (firstArg.value === "no") {
											handleChangeEvent("text", "", "EWEBill", formik, formKey);
											toast({
												status: "error",
												description: t("noEWENoFarmerDesc"),
												title: t("noEWENoFarmerTitle"),
											});
										}
									}}
								/>
							</GridItem>
							{formik.values.RegisteredWithEWE?.value === "yes" && (
								<>
									{/* EWE number field */}
									<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="text"
											value={formik.values["EWEBill"]}
											isRequired={true}
											name="EWEBill"
											label={t("PleaseEnterEWENumber")}
											placeholder={""}
											error={formik.errors["EWEBill"]}
											maxLength={12}
											dir={"auto"}
											textAlign={"left"}
											isDisabled={readOnly}
											extraIcon={{ onClick: onOpen }}
											onChange={(firstArg) => {
												handleChangeEvent("text", firstArg, "EWEBill", formik, formKey);
											}}
										/>
										<Modal isOpen={isOpen} onClose={onClose}>
											<ModalOverlay />
											<ModalContent maxW={"40rem"} pb={"5"}>
												<ModalHeader
													color={"brand.gray.400"}
													textAlign={"center"}
													fontWeight={"normal"}
												>
													{t("HowToKnowEWE")}
												</ModalHeader>
												<ModalCloseButton
													mt={"3"}
													border="2px solid "
													h={"5"}
													w="5"
													borderRadius={"100%"}
													p="3"
												>
													<CloseIcon boxSize={"2.5"} />
												</ModalCloseButton>
												<ModalBody>
													<Link href={"/assets/images/eweexample.png"} target="_blank" locale="">
														<Image
															src="/assets/images/eweexample.png"
															alt="EWE subscription example"
														/>
													</Link>
												</ModalBody>
											</ModalContent>
										</Modal>
									</GridItem>

									{/* emirate id for EWE field */}
									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="selectableTags"
											isRequired={true}
											name="OwnerEWEBill"
											options={lookups.Boolean}
											label={t("ownerEWEBill")}
											placeholder={""}
											error={formik.errors["OwnerEWEBill"]}
											touched={formik.touched["OwnerEWEBill"]}
											value={formik.values.OwnerEWEBill}
											isDisabled={readOnly}
											onChange={(firstArg) => {
												if (firstArg.value === "yes") {
													handleChangeEvent(
														"text",
														{ target: { value: EmiratesID } },
														"RelatedEmiratesID",
														formik,
														formKey
													);
													formik.values["OwnerEWEBill"] = lookups.Boolean[0];
													formik.setFieldValue("RelatedEmiratesID", EmiratesID);
													setShowOther(false);
												} else {
													formik.setFieldValue("RelatedEmiratesID", "");
													formik.values["OwnerEWEBill"] = lookups.Boolean[1];
													handleChangeEvent(
														"text",
														{ target: { value: "" } },
														"RelatedEmiratesID",
														formik,
														formKey
													);
													setShowOther(true);
												}

												// handleChangeEvent(
												// 	"selectableTags",
												// 	firstArg,
												// 	"OwnerEWEBill",
												// 	formik,
												// 	formKey
												// );
											}}
										/>
									</GridItem>
									{/* getting social aid field */}

									<GridItem bg="white" colSpan={{ base: 2, md: 1 }}>
										<Box hidden={!showOther}>
											<FormField
												type="text"
												value={formik.values.RelatedEmiratesID}
												isRequired={showOther}
												name="RelatedEmiratesID"
												label={t("EnterEmirateIdForEWE")}
												placeholder={""}
												error={formik.errors["RelatedEmiratesID"]}
												maxLength={15}
												customFormat={formatEmiratesID}
												dir={"auto"}
												hidden={!showOther}
												isDisabled={readOnly}
												textAlign={"left"}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "RelatedEmiratesID", formik, formKey);
												}}
											/>
										</Box>
									</GridItem>

									<GridItem colSpan={{ base: 2, md: 1 }}>
										<FormField
											type="selectableTags"
											isRequired={true}
											name="ReceiveSocialAid"
											options={lookups.Boolean}
											label={t("ReceiveSocialAid")}
											placeholder={""}
											error={formik.errors["ReceiveSocialAid"]}
											touched={formik.touched["ReceiveSocialAid"]}
											value={formik.values.ReceiveSocialAid}
											isDisabled={readOnly}
											onChange={(firstArg) => {
												if (firstArg.value === "no") {
													formik.setFieldValue("EntityReceivedFrom", {
														value: undefined,
														label: "",
													});
													handleChangeEvent(
														"selectableTags",
														"",
														"EntityReceivedFrom",
														formik,
														formKey
													);
												} else {
													handleChangeEvent(
														"selectableTags",
														"",
														"ReceiveInflationAllowance",
														formik,
														formKey
													);
												}
												handleChangeEvent(
													"selectableTags",
													firstArg,
													"ReceiveSocialAid",
													formik,
													formKey
												);
											}}
										/>
									</GridItem>
									{/*  */}
									<GridItem colSpan={{ base: 2, md: 1 }}>
										{/* entity field */}
										{formik.values.ReceiveSocialAid?.value === "yes" && (
											<FormField
												type="selectableTags"
												isRequired={true}
												name="EntityReceivedFrom"
												options={entityStub}
												label={t("EntityReceivedFrom")}
												placeholder={""}
												value={formik.values["EntityReceivedFrom"]}
												error={formik.errors.EntityReceivedFrom}
												touched={formik.touched.EntityReceivedFrom}
												isDisabled={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"EntityReceivedFrom",
														formik,
														formKey
													);
												}}
											/>
										)}
									</GridItem>
									{formik.values["ReceiveSocialAid"]?.value === "no" && (
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												isRequired={true}
												name="ReceiveInflationAllowance"
												options={lookups.Boolean}
												label={t("ReceiveInflationAllowance")}
												placeholder={""}
												error={formik.errors["ReceiveInflationAllowance"]}
												touched={formik.touched["ReceiveInflationAllowance"]}
												isDisabled={readOnly}
												value={formik.values["ReceiveInflationAllowance"]}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"ReceiveInflationAllowance",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>
									)}

									{/* getting inflation aid field */}
								</>
							)}
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
