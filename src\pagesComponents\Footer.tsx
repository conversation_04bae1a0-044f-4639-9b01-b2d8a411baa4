import React from "react";
import { Center, Text, Flex, Link, Box, Image, Divider, Skeleton } from "@chakra-ui/react";
import NextLink from "next/link";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { AndroidIcon, AppleIcon, ChromeIcon, EdgeIcon, SafariIcon } from "components/Icons";
import { useRouter } from "next/router";
import { HeaderAndFooterDetails, Menu } from "utils/strapi/navbar";
import FooterAccordion from "./FooterAccordion";
import { getImageUrls } from "utils/strapi/helpers";
import { useTranslation } from "react-i18next";
import { BsFillFilePdfFill } from "react-icons/bs";
// import { AndroidIcon, AppleIcon, ChromeIcon, EdgeIcon, SafariIcon } from "components/Icons";
interface Props {
	isMain?: boolean;
	data:
		| {
				global: HeaderAndFooterDetails | null;
				menus: Menu[] | null;
		  }
		| undefined;
	fetchStatus: "idle" | "error" | "loading" | "success";
}
const Footer = ({ data, fetchStatus, isMain = true }: Props) => {
	const { locale } = useRouter();
	const { t } = useTranslation("common");
	if (fetchStatus === "loading") return <Skeleton w="99%" rounded="lg" margin={"auto"} h="5rem" />;
	if (fetchStatus === "success" && !data) {
		return <h1>error</h1>;
	}

	const footerMenu = data?.menus?.find((menu) => menu.slug === "footer")!;
	return (
		<>
			{isMain && (
				<Center
					w={"100%"}
					bg={"brand.gray.100"}
					py={6}
					px={{ base: 6, md: 20 }}
					borderTop="1px"
					borderColor="brand.mainGold"
				>
					<Flex flexDirection={"column"} w={"100%"}>
						<Flex
							w={"100%"}
							alignItems={"start"}
							direction={{ base: "column", md: "row" }}
							justifyContent="space-between"
						>
							{footerMenu.items.map((item) => {
								const localizedText = locale === "ar" ? item.title : item.english_title;
								return (
									<Box key={item.id} w={{ base: "100%", md: "16.6%" }}>
										<FooterAccordion accordionTitle={localizedText}>
											<Flex flexDirection={"column"} flex={1} gap={2.5}>
												{item.children.map((child) => {
													const localizedText = locale === "ar" ? child.title : child.english_title;
													const localeTxt = locale ?? "ar";

													if (localizedText === "<EMAIL>") {
														return (
															<Text key={child.id} color="brand.footerTxtColor" fontSize="sm">
																{localizedText}
															</Text>
														);
													} else {
														return (
															<Link
																key={child.id}
																href={child.url?.replace("ar", localeTxt)}
																target={child.target! || "_blank"}
																onClick={(e) => {
																	if (!child.url) e.preventDefault();
																}}
															>
																<Text color="brand.footerTxtColor" fontSize="sm">
																	{localizedText}
																</Text>
															</Link>
														);
													}
												})}
											</Flex>
										</FooterAccordion>
									</Box>
								);
							})}

							<Box w={{ base: "100%", md: "16.6%" }}>
								<Flex>
									<Link as={NextLink} href={"/"}>
										<Image
											src={getImageUrls(data?.global?.footer?.footer_logo.url || "")}
											alt="logo"
											maxW={{ base: "80%", md: "100%" }}
											maxH={"100%"}
											draggable="false"
										/>
									</Link>
								</Flex>
							</Box>
						</Flex>
					</Flex>
				</Center>
			)}
			<Box bg="brand.gray.100" px={{ base: 6, md: 20 }} pb={0} w="100%">
				<Flex direction="column" bg={"brand.gray.100"} px={4} py={6} pb={0}>
					<Box w={"100%"} bg={"brand.gray.100"} p="0px" pb={{ base: 8, md: 4 }}>
						{/* <Text textAlign={"left"}>
							{t("lastUpdate") +
								" " +
								new Date(data?.global?.updatedAt || "").toLocaleDateString(
									locale === "ar" ? "ar-EG-u-nu-latn" : "EG-u-nu-latn",
									{
										weekday: "long",
										year: "numeric",
										month: "short",
										day: "numeric",
									}
								)}
						</Text> */}
						<Text>{t("websiteInfo")}</Text>
					</Box>
					<Divider orientation="horizontal" borderColor="brand.gray.500" />
					<Flex
						w={"100%"}
						p="0px"
						pt={4}
						direction={{ base: "column", md: "row" }}
						justifyContent="space-between"
					>
						<Flex
							flexDirection={{ base: "column-reverse", md: "row" }}
							columnGap={3.5}
							mb={{ base: 2.5, md: "unset" }}
						>
							<Text
								color="black"
								fontSize="xs"
								noOfLines={{ base: 2, md: 1 }}
								m="0px"
								p="0px"
								py={{ base: 3.5, md: "unset" }}
							>
								{t("copyrightText")}
							</Text>
							<Flex
								columnGap={2}
								rowGap={2}
								textAlign={"left"}
								maxH={{ base: "unset", md: "20px" }}
								direction={{ base: "column", md: "row" }}
								mb={{ base: 2.5, md: "unset" }}
							>
								<Link href={`https://www.mocd.gov.ae/${locale}/disclaimer.aspx`} target={"_blank"}>
									<Text
										color="brand.mainGold"
										fontWeight="bold"
										decoration="underline"
										fontSize="xs"
									>
										{t("disclaimer")}
									</Text>
								</Link>
								<Divider
									mt="3px"
									height="65%"
									opacity={1}
									borderWidth="1px"
									orientation="vertical"
									borderColor="brand.mainGold"
									display={{ base: "none", md: "block" }}
								/>
								<Link
									href={`https://www.mocd.gov.ae/${locale}/privacy-policy.aspx`}
									target={"_blank"}
								>
									<Text
										color="brand.mainGold"
										fontWeight="bold"
										decoration="underline"
										fontSize="xs"
									>
										{t("privacyPolicy")}
									</Text>
								</Link>
								<Divider
									mt="3px"
									height="65%"
									opacity={1}
									borderWidth="1px"
									orientation="vertical"
									borderColor="brand.mainGold"
									display={{ base: "none", md: "block" }}
								/>
								<Link
									href={`https://www.mocd.gov.ae/${locale}/terms-and-conditions.aspx`}
									target={"_blank"}
								>
									<Text
										color="brand.mainGold"
										fontWeight="bold"
										decoration="underline"
										fontSize="xs"
									>
										{t("termsConditions")}
									</Text>
								</Link>
								<Divider
									mt="3px"
									height="65%"
									borderWidth="1px"
									opacity={1}
									orientation="vertical"
									borderColor="brand.mainGold"
									display={{ base: "none", md: "block" }}
								/>
								<Link href={`https://www.mocd.gov.ae/${locale}/copyrights.aspx`} target={"_blank"}>
									<Text
										color="brand.mainGold"
										fontWeight="bold"
										decoration="underline"
										fontSize="xs"
									>
										{t("copyrights")}
									</Text>
								</Link>
							</Flex>
						</Flex>
						<Flex
							columnGap={{ base: 10, sm: 2, md: 10 }}
							ml={{ base: "unset", md: 4 }}
							flexWrap={{ base: "wrap", md: "nowrap" }}
							rowGap={1}
						>
							<Flex columnGap={4} w={{ base: "100%", md: "unset" }}>
								<Text fontWeight={"bold"} fontSize="xs" color={"brand.footer.headerText"}>
									{t("mobileApp")}
								</Text>
								<Flex columnGap={4} flexWrap="wrap">
									<Link
										href={"https://apps.apple.com/ae/app/mocd-connect/id1490376957"}
										target={"_blank"}
									>
										<AppleIcon color="brand.gray.400" mb="10px" />
									</Link>
									<Link
										href={`https://play.google.com/store/apps/details?id=com.mocd.mocdapp?hl=${locale}`}
										target={"_blank"}
									>
										<AndroidIcon color="brand.gray.400" mb="8px" />
									</Link>
								</Flex>
							</Flex>
							<Flex columnGap={4}>
								<Text fontWeight={"bold"} fontSize="xs" color={"brand.footer.headerText"}>
									{t("channelsPrograms")}
								</Text>
								<Flex columnGap={4} flexWrap="wrap">
									<Link
										href={"https://www.google.com/chrome/browser/desktop/index.html"}
										target={"_blank"}
									>
										<ChromeIcon color="brand.gray.400" mb="6px" />
									</Link>
									<Link href={"https://safari.en.softonic.com/download"} target={"_blank"}>
										<EdgeIcon color="brand.gray.400" mb="6px" />
									</Link>
									<Link href={"https://support.apple.com/downloads/safari"} target={"_blank"}>
										<SafariIcon color="brand.gray.400" mb="6px" />
									</Link>
									<Link href={"http://get.adobe.com/reader/"} target={"_blank"}>
										<Box margin="5px 0">
											<BsFillFilePdfFill color="gray" />
										</Box>
									</Link>
								</Flex>
							</Flex>
						</Flex>
					</Flex>
					{/* <Flex
						w={"100%"}
						h={"100%"}
						p="0px"
						py={8}
						direction={{ base: "column", md: "row" }}
						justifyContent="center"
					>
						<Flex
							flexDirection={{ base: "column-reverse", md: "row" }}
							columnGap={3.5}
							mb={{ base: 2.5, md: "unset" }}
						>
							<Text
								color="#76777A"
								fontSize="md"
								noOfLines={{ base: 2, md: 1 }}
								m="0px"
								p="0px"
								py={{ base: 3.5, md: "unset" }}
							>
								{data?.global?.footer.copy_right}
							</Text>
						</Flex>
					</Flex> */}
				</Flex>
			</Box>
		</>
	);
};

export default Footer;
