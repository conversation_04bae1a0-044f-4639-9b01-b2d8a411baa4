import React, { forwardRef, useEffect, useState, useImperativeHandle, useRef } from "react";
import {
	Table,
	Thead,
	Tbody,
	Tr,
	Th,
	Td,
	Flex,
	useBreakpoint,
	Box,
	Text,
	Show,
	VStack,
} from "@chakra-ui/react";
import {
	useReactTable,
	flexRender,
	getCoreRowModel,
	ColumnDef,
	SortingState,
	getSortedRowModel,
	getPaginationRowModel,
} from "@tanstack/react-table";
import { TableSortIcon } from "components/Icons";

import ReactPaginate from "react-paginate";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import { useRouter } from "next/router";
import * as FileSaver from "file-saver";
import XLSX from "sheetjs-style";
import { getFormattedDate } from "utils/helpers";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";
import { DRAFT_STATUS_ID, PENDING_STATUS_ID, SOCIALAID_TEMPLATE_ID } from "config";

type DataTableProps = {
	data: any[];
	columns: ColumnDef<any, any>[];
	Modal: any;
	StatusArabic: any;
};

export const DataTable = forwardRef(
	({ columns, data, Modal, StatusArabic }: DataTableProps, ref) => {
		const { push: routerPush, locale } = useRouter();
		const [sorting, setSorting] = React.useState<SortingState>([]);
		const [isModalShow, setIsModalShow] = useState(false);
		const [modalData, setModalData] = useState<any | null>(null);
		const tableDataRef = useRef(null);
		const toast = useAppToast();
		const { t } = useTranslation(["forms", "common"]);
		useImperativeHandle(ref, () => ({
			downloadExcel() {
				let dataToExport: any = [];
				table.getRowModel().rows.map((row) => {
					let obj = {};
					let amountLocalized = t("tables:amount") || "Amount";
					let requestNumberLocalized = t("tables:requestNumber") || "Request Name";
					let dateLocalized = t("tables:date") || "Date";
					let currencyLocalized = t("tables:aed") || "AED";
					let totalAmount: any = +row.original.TotalAmount || 0;
					totalAmount = totalAmount?.toFixed(2) || "";
					obj[amountLocalized] = "";
					obj[requestNumberLocalized] = "";
					obj[dateLocalized] = "";
					obj[amountLocalized] = totalAmount !== "" ? totalAmount : "0.00";
					if (locale === "en") {
						obj[amountLocalized] = `${currencyLocalized} ${obj[amountLocalized]}`;
					} else {
						obj[amountLocalized] = `${obj[amountLocalized]}${currencyLocalized}`;
					}
					obj[requestNumberLocalized] = row.original.RequestName;
					obj[dateLocalized] = getFormattedDate(row.original.PayDate, "dd MMMM yyyy", locale);
					dataToExport.push(obj);
				});

				const fileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				const fileExtension = ".xlsx";
				const ws = XLSX.utils.json_to_sheet(dataToExport);
				const wb = {
					Sheets: { data: ws },
					SheetNames: ["data"],
					Workbook: { Views: [{ RTL: false }] },
				};
				if (locale === "ar") wb.Workbook.Views[0].RTL = true;
				const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
				const excelData = new Blob([excelBuffer], { type: fileType });
				FileSaver.saveAs(excelData, "My Allowance" + fileExtension);
			},
		}));

		const table = useReactTable({
			columns,
			data: data,
			getCoreRowModel: getCoreRowModel(),
			onSortingChange: setSorting,
			getSortedRowModel: getSortedRowModel(),
			getPaginationRowModel: getPaginationRowModel(),
			state: {
				sorting,
			},
			initialState: {
				pagination: {
					pageSize: 10,
					pageIndex: 0,
				},
			},
		});

		const statusColumn = columns?.find((col) => col?.header === "Status" || "الحالة");
		if (statusColumn) {
			data.sort((a, b) => {
				let statusA, statusB;
				statusA = a?.Status?.Value;
				statusB = b?.Status?.Value;
				return statusA.localeCompare(statusB, locale);
			});
		}

		const handleClick = (rowData: any) => {
			if (rowData?.Status?.Key === DRAFT_STATUS_ID) {
				toast({
					title: t("pleaseWaitLoadDraft", { ns: "common" }),
					status: "info",
				});
				if (rowData?.Template?.TemplateId === SOCIALAID_TEMPLATE_ID)
					routerPush(`/smart-services/how-to-apply/apply-socialaid?requestId=${rowData.CaseId}`);
				else if (rowData?.Template?.TemplateName.includes("Farmer")) {
					routerPush(
						`/smart-services/farmer-service/apply-farmer-service?requestId=${rowData.CaseId}`
					);
				} else if (rowData?.Template?.TemplateName.includes("Inflation")) {
					routerPush(
						`/smart-services/inflation-service/apply-inflation?requestId=${rowData.CaseId}`
					);
				}
			} else if (rowData?.Status.Key === PENDING_STATUS_ID) {
				// this when the request is pending state (it was sent back by agent)
				let targetUrl = "how-to-apply/apply-socialaid";

				if (rowData?.Template?.TemplateName.includes("Inflation")) {
					targetUrl = "inflation-service/apply-inflation";
				} else if (rowData?.Template?.TemplateName.includes("Farmer")) {
					targetUrl = "farmer-service/apply-farmer-service";
				} else {
					targetUrl = "how-to-apply/apply-socialaid";
				}
				toast({
					title: t("pleaseWaitLoadDraft", { ns: "common" }),
					status: "info",
				});
				routerPush(`/smart-services/${targetUrl}?requestId=${rowData.CaseId}&isPending=true`);
			} else {
				setIsModalShow(true);
				setModalData(rowData);
			}
		};

		const breakpoint = useBreakpoint();
		useEffect(() => {
			const allColumns = table.getAllColumns();
			const lastColumn = allColumns[allColumns.length - 1];
			//lastColumn.toggleVisibility(breakpoint !== "base");
		}, [breakpoint]);

		return (
			<>
				<Show above="md">
					<Box
						border="1px solid var(--black-30, #BBBCBD)"
						rounded={"lg"}
						roundedBottom={"none"}
						borderBottom={"none"}
						overflowX={"auto"}
					>
						<Table
							variant="unstyled"
							w="100%"
							bg="white"
							mt={{ base: 4, md: 0 }}
							ref={tableDataRef}
							size={["sm", "md"]}
							sx={{ md: { tableLayout: "fixed" }, base: { tableLayout: "unset" } }}
							rounded={"204"}
						>
							<Thead>
								{table.getHeaderGroups().map((headerGroup) => (
									<Tr key={headerGroup.id}>
										{headerGroup.headers.map((header, idx) => {
											return (
												<Th
													borderBottom={"1px solid #BBBCBD"}
													key={idx}
													onClick={header.column.getToggleSortingHandler()}
												>
													<Flex alignItems={"center"} my="2">
														<Text fontSize={{ md: "1rem", base: "0.875rem" }}>
															{flexRender(header.column.columnDef.header, header.getContext())}
														</Text>
														<Show above="md">
															<TableSortIcon ms="4" />
														</Show>
													</Flex>
												</Th>
											);
										})}
									</Tr>
								))}
							</Thead>

							<Tbody textAlign="start" marginLeft="20px">
								{table.getRowModel().rows.map((row, index) => (
									<React.Fragment key={index}>
										<Tr
											key={row.id}
											pos="relative"
											textAlign="start"
											onClick={() => handleClick(row.original)}
											borderBottom={"1px solid #BBBCBD"}
										>
											{row.getVisibleCells().map((cell) => (
												<Td key={cell.id} color={"black"} padding={0} fontWeight={"normal"}>
													<Text fontSize={{ base: "0.7rem", md: "small" }} as="span">
														{flexRender(cell.column.columnDef.cell, cell.getContext())}
													</Text>
												</Td>
											))}
										</Tr>
									</React.Fragment>
								))}
							</Tbody>
						</Table>
					</Box>
				</Show>
				<Show below="md">
					<VStack align={"start"}>
						{table.getRowModel().rows.map((row, index) => {
							const [reqNum, reqName, date, tag] = row.getVisibleCells();
							return (
								<Flex
									key={index}
									w="full"
									p={4}
									justifyContent={"space-between"}
									borderBottom="1px solid #BBBCBD"
									_hover={{ cursor: "pointer" }}
								>
									<VStack flex={2} align={"start"} w="50%">
										<Text fontSize={"1rem"} fontWeight={"bold"}>
											{flexRender(reqName.column.columnDef.cell, reqName.getContext())}
										</Text>
										<Text fontSize={"0.875rem"}>
											{t("requestNumber")} :{" "}
											<Text
												as="span"
												textColor={"#005DBA"}
												textDecoration={"underline"}
												onClick={() => handleClick(row.original)}
											>
												{String(reqNum.renderValue())}
											</Text>
										</Text>
										<Text fontSize={"0.875rem"}>
											{flexRender(date.column.columnDef.cell, date.getContext())}
										</Text>
									</VStack>
									<Box w="40%" fontSize={"sm"}>
										{" "}
										{flexRender(tag.column.columnDef.cell, tag.getContext())}
									</Box>
								</Flex>
							);
						})}
					</VStack>
				</Show>
				{table.getPageCount() > 1 && (
					<ReactPaginate
						breakLabel="..."
						nextLabel={
							<>
								<Flex marginInlineStart={"1rem"}>
									<Text>{t("next")}</Text>
									<ChevronRightIcon
										h={"24px"}
										w={"24px"}
										transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
									/>
								</Flex>
							</>
						}
						onPageChange={({ selected }) => {
							table.setPageIndex(selected);
						}}
						pageRangeDisplayed={1}
						pageCount={table.getPageCount()}
						initialPage={0}
						previousLabel={
							<Flex marginInlineEnd={"1rem"}>
								<ChevronLeftIcon
									h={"24px"}
									w={"24px"}
									transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
								/>
								<Text>{t("previous")}</Text>
							</Flex>
						}
						className={`pagination pagination-${breakpoint}`}
						// previousLinkClassName="links"
						// nextLinkClassName="links"
						forcePage={table.getState().pagination.pageIndex}
						pageClassName="page-num"
						// breakClassName="break-label"
						activeClassName="active-page"
						// disabledLinkClassName="disable-link"
					/>
				)}
				<Modal
					isModalShow={isModalShow}
					handleOnClose={() => setIsModalShow(false)}
					data={modalData as any}
				/>
			</>
		);
	}
);

DataTable.displayName = "DataTable";
