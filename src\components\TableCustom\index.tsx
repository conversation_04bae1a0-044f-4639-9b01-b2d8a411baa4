import {
	Table,
	TableCaption,
	TableContainer,
	Tbody,
	Td,
	Text,
	Tfoot,
	Th,
	Thead,
	Tr,
} from "@chakra-ui/react";
import StatusPill from "components/StatusPill";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { TableCustomProps } from "./TableCustomProps.ds";

function TableCustom({
	caption = null,
	tableHeaders,
	tableBody,
	hasFooter,
	footerValues,
	tableName,
	mb = 4,
	statusLookup,
}: TableCustomProps) {
	const { t } = useTranslation("tables");
	const router = useRouter();
	const toast = useAppToast();

	const handleClick = (requestId: string) => {
		toast({
			title: t("common:pleaseWaitLoadDraft"),
			status: "info",
		});
		router.push(`/smart-services/how-to-apply/apply-socialaid?requestId=${requestId}`);
	};

	return (
		<>
			<TableContainer mb={mb}>
				<Table variant="simple">
					{caption && <TableCaption>{caption}</TableCaption>}
					<Thead mb={4}>
						<Tr bg="brand.mainBackground" fontWeight="medium">
							{tableHeaders &&
								tableHeaders.map((thObj, idx) => {
									return (
										<Th
											color="brand.tableTextColor"
											fontWeight="medium"
											textTransform="none"
											lineHeight={6}
											fontSize="md"
											px={{ base: 1, md: 2 }}
											py={2}
											key={idx}
											id={thObj.id}
											isNumeric={thObj.type === "number" ? true : false}
										>
											{t(thObj.id)}
										</Th>
									);
								})}
						</Tr>
					</Thead>
					<Tbody>
						{tableBody &&
							tableBody.map((row, rowIdx) => {
								return (
									<Tr key={rowIdx}>
										{row &&
											row
												.filter(
													(_, index) =>
														(tableName === "mycases" && index !== 3) || tableName !== "mycases"
												)
												.map((col, colIdx) => {
													let columnRef = tableHeaders?.[colIdx] || null;
													return (
														<Td
															fontSize="md"
															isNumeric={columnRef?.type === "number" ? true : false}
															fontWeight={columnRef?.isBold ? "bold" : "normal"}
															textDecoration={columnRef?.underlined ? "underline" : "none"}
															pt={rowIdx === 0 ? 4 : 2}
															pb={2}
															px={2}
															key={colIdx}
															lineHeight={6}
															borderBottom="none"
														>
															{colIdx === 1 && tableName === "mycases" ? (
																<StatusPill status={col} statusLookup={statusLookup} />
															) : colIdx === 0 &&
															  tableName === "mycases" &&
															  row[1].Value === "Draft" ? (
																<Text
																	color="brand.blue.300"
																	textDecoration="underline"
																	cursor={"pointer"}
																	onClick={() => handleClick(row[3])}
																>
																	{col}
																</Text>
															) : (
																col
															)}
														</Td>
													);
												})}
									</Tr>
								);
							})}
					</Tbody>
					{hasFooter && (
						<Tfoot>
							<Tr>
								{footerValues &&
									footerValues.map((footerValue, colIdx) => {
										let columnRef = tableHeaders?.[colIdx] || null;
										return (
											<Th key={colIdx} isNumeric={columnRef?.type === "number" ? true : false}>
												{footerValue}
											</Th>
										);
									})}
							</Tr>
						</Tfoot>
					)}
				</Table>
			</TableContainer>
			{tableBody.length === 0 && (
				<Text
					fontSize={"md"}
					fontWeight={"bold"}
					w={"100%"}
					textAlign={"center"}
					mb={{ base: 4, md: 0 }}
				>
					{t("noDataFound")}
				</Text>
			)}
		</>
	);
}

export default TableCustom;
