import { Grid, GridItem, Text } from "@chakra-ui/react";
import <PERSON><PERSON>ield from "components/Form/FormField";
import SectionHeader from "components/SectionHeader";
import { Form, Formik } from "formik";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import * as functions from "./functions";

interface Props {
	onSubmit: any;
	documentList?: ICrmDocumentList;
	setDocumentStatus: any;
}

function AttachedDocumentsForm({ onSubmit, documentList, setDocumentStatus }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();

	return (
		<Formik
			enableReinitialize
			initialValues={functions.getInitialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
		>
			{(formik) => (
				<Form
					onSubmit={(e) => {
						e.preventDefault();
						formik.handleSubmit(e);
					}}
					onChange={(e) => {
						e.preventDefault();
						functions.onChange(e, formik);
					}}
				>
					<Grid
						rowGap={{ base: 6, md: 6 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						<GridItem bg="white" colSpan={2}>
							<SectionHeader title={t("personalDocuments")} innerText={""} textMt={2} textMb={-2} />
						</GridItem>
						{(documentList?.ListPersonalDocs || []).map((document) => (
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }} key={document.IdDocuments}>
								<FormField
									type="file"
									isRequired={!document.IsOptional}
									label={locale === "en" ? document.NameEn : document.NameAr}
									idDocument={document.IdDocuments}
									name={document.IdDocuments}
									allowPdf={true}
									allowImage={true}
									attachment={document.ListAttachments?.[0]}
									onUploadStatusChange={(status) => {
										setDocumentStatus(document.IdDocuments, status);
									}}
								/>
							</GridItem>
						))}
						{(documentList?.ListAdditionalDoc?.length || 0 > 0) && (
							<GridItem bg="white" colSpan={2}>
								<SectionHeader title={t("additionalDocuments")} textMt={2} textMb={-2} mb={4} />
							</GridItem>
						)}
						{(documentList?.ListAdditionalDoc || []).map((document) => (
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }} key={document.IdDocuments}>
								<FormField
									type="file"
									isRequired={!document.IsOptional}
									label={locale === "en" ? document.NameEn : document.NameAr}
									idDocument={document.IdDocuments}
									name={document.IdDocuments}
									allowPdf={true}
									allowImage={true}
									attachment={document.ListAttachments?.[0]}
									onUploadStatusChange={(status) => {
										setDocumentStatus(document.IdDocuments, status);
									}}
								/>
							</GridItem>
						))}
					</Grid>
					{(documentList?.ListAdditionalDoc?.length || 0) === 0 &&
						(documentList?.ListPersonalDocs?.length || 0) === 0 && (
							<Text marginTop={4}>{t("noDocumentsUpload")}</Text>
						)}
				</Form>
			)}
		</Formik>
	);
}

export default AttachedDocumentsForm;
