import ReactPaginate from "react-paginate";
import { faChevronLeft, faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
function Pagination({ pageCount }) {
	return (
		<ReactPaginate
			breakLabel="..."
			nextLabel={<FontAwesomeIcon icon={faChevronRight} />}
			// onPageChange={handlePageClick}
			pageRangeDisplayed={1}
			pageCount={pageCount}
			previousLabel={<FontAwesomeIcon icon={faChevronLeft} />}
			className="pagination"
			previousLinkClassName="links"
			nextLinkClassName="links"
			pageClassName="page-num"
			breakClassName="break-label"
			activeClassName="active-page"
			disabledLinkClassName="disable-link"
		/>
	);
}

export default Pagination;
