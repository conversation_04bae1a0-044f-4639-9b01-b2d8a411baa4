import { Box } from "@chakra-ui/react";
import FamilyMembersInfoForm from "./FamilyMembersInfoForm";

function RequestDetailsForm({
	innerText,
	formKey,
	familyMembers,
	setFamilyMembers,
	childMembers,
	setChildMembers,
	khulasitQaidNumber,
	readOnly = false,
	caseType = 1,
	emiratesId = "",
	maritalStatus = "",
	gender = "",
	subcategory = "",
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions, formKey });
	};
	return (
		<Box>
			<FamilyMembersInfoForm
				onSubmit={onSubmit}
				members={familyMembers}
				setMembers={setFamilyMembers}
				readOnly={readOnly}
				childMembers={childMembers}
				khulasitQaidNumber={khulasitQaidNumber}
				setChildMembers={setChildMembers}
				caseType={caseType}
				emiratesId={emiratesId}
				maritalStatus={maritalStatus}
				gender={gender}
				subcategory={subcategory}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
