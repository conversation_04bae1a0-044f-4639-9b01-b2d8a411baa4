import { Text, HSta<PERSON>, <PERSON><PERSON>, <PERSON>, Flex } from "@chakra-ui/react";
import React from "react";
import { useTranslation } from "next-i18next";
import RatingForm from "./RatingForm";
import { FeedbackRecivedIcon, RatingStarIcon } from "components/Icons";

const StarRating = ({ mt = "6" }) => {
	const { t } = useTranslation();
	const [helpful, setHelpful] = React.useState("");

	return (
		<Box mt={mt}>
			{/* <Flex
				w={"100%"}
				bg={"brand.lightGold"}
				px={4}
				alignItems={"center"}
				justifyContent={"space-between"}
				py={5}
				borderWidth={1}
				borderColor={"brand.ratingBorder"}
				flexDirection={{ base: "column", md: "row" }}
				gap={4}
			>
				<Text fontSize={"18px"} color={"brand.textColor"} fontWeight={700}>
					{String(t("experience-service"))}
				</Text>
				<Rating
					emptyIcon={<EmptyStar w={"48px"} h={"48px"} className={"ratingMainClass"} ms={2} p={2} />}
					fillIcon={<FilledStar w={"48px"} h={"48px"} className={"ratingMainClass"} ms={2} p={2} />}
				/>
			</Flex> */}
			{!helpful && (
				<HStack bg="brand.white.100" w="fit-content" rounded="base" px="2" py="1">
					<Text color={"brand.textColor"}>{String(t("helpful-text"))}</Text>
					<Button onClick={() => setHelpful("yes")} px="0px" ml="0px!important">
						<Text fontWeight="bold" color={"brand.mainGold"}>
							{String(t("yes"))}
						</Text>
					</Button>

					<Text color={"brand.textColor"} ml="0px!important">
						{String(t("or"))}
					</Text>
					<Button onClick={() => setHelpful("no")} ml="-5px!important" px="0px">
						<Text fontWeight="bold" color={"brand.mainGold"}>
							{String(t("no"))}
						</Text>
					</Button>
				</HStack>
			)}
			{helpful === "no" && (
				<Box mt={4}>
					<RatingForm setHelpful={setHelpful} />
				</Box>
			)}
			{helpful === "yes" && (
				<Flex
					mt={4}
					rounded="base"
					px="8"
					py="1"
					bg="brand.white.100"
					w="fit-content"
					alignItems={"center"}
					gap={2}
				>
					<RatingStarIcon boxSize={"1.5rem"} />
					<Text>{t("forms:thankYouForFeedback")}</Text>
				</Flex>
			)}
			{helpful === "submitted" && (
				<Flex
					mt={4}
					rounded="base"
					px="8"
					py="1"
					bg="brand.white.100"
					w="fit-content"
					alignItems={"center"}
					gap={2}
				>
					<FeedbackRecivedIcon boxSize={"1.5rem"} />
					<Text>{t("forms:feedbackRecived")}</Text>
				</Flex>
			)}
		</Box>
	);
};

export default StarRating;
