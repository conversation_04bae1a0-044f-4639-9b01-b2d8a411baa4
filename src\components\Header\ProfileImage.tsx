import { Box, Image, useBreakpointValue } from "@chakra-ui/react";
import { ProfilePictureIcon } from "components/Icons";
import { useQuery } from "react-query";
import { profileImage } from "services/frontend";
import { getBase64DataUri } from "utils/helpers";

const ProfileImage = () => {
	const { data: profileImageResp } = useQuery(["profileImage"], () => profileImage());

	const profileImageSize = useBreakpointValue({
		base: "45px",
		md: "30px",
	});

	return (
		<>
			{profileImageResp?.Data && (
				<Box
					borderWidth={"1px"}
					borderColor={"brand.mainGold"}
					borderRadius="50%"
					width={profileImageSize}
					minWidth={profileImageSize}
					height={profileImageSize}
					overflow="hidden"
				>
					<Image src={getBase64DataUri(profileImageResp?.Data)} alt={"profile image"} />
				</Box>
			)}
			{!profileImageResp?.Data && (
				<ProfilePictureIcon
					borderWidth={"1px"}
					borderColor={"brand.mainGold"}
					borderRadius="50%"
					width={profileImageSize}
					minWidth={profileImageSize}
					height={profileImageSize}
				/>
			)}
		</>
	);
};

export default ProfileImage;
