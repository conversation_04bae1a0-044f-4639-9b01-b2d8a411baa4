import {
	Button,
	Grid,
	Grid<PERSON><PERSON>,
	<PERSON>,
	Flex,
	Text,
	Tbody,
	TableContainer,
	Table,
	Tr,
	Td,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Form, Formik } from "formik";
import { useRouter } from "next/router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import * as functions from "./functions";
import { CompletedSuccessfully } from "components/Icons";
import { getFormattedDate } from "utils/helpers";
import { ICrmContact } from "interfaces/CrmContact.interface";
import {
	ICrmLookup,
	ICrmLookupLocalized,
	ICrmMasterData,
} from "interfaces/CrmMasterData.interface";
import { ToWhomItMayConcernsRequest } from "services/frontend";
import { ICrmToWhomForm } from "interfaces/CrmToWhom.interface";
import { useMutation } from "react-query";
import useAppToast from "hooks/useAppToast";
import { CertificateTypes, EntityAddressed } from "config";
import NextLink from "next/link";
import useCustomerPulse from "hooks/useCustomerPulse";
interface ToWhomItMayConcernFormInterface {
	userDetails: ICrmContact;
	masterData: ICrmMasterData<ICrmLookup>;
	customerPulseLinkingId: string;
}

export default function ToWhomItMayConcernForm({
	userDetails,
	masterData,
	customerPulseLinkingId,
}: ToWhomItMayConcernFormInterface) {
	const [isInitial, setisInitial] = useState(true);
	const [isSubmitted, setisSubmitted] = useState(false);
	const [requestNumber, setrequestNumber] = useState("");
	const [isCustomerPulseLoading, isCustomerPulseSubmitted, openCustomerPulse] = useCustomerPulse(
		userDetails.EmiratesID,
		customerPulseLinkingId
	);
	const { t } = useTranslation(["personalInfo", "forms", "common"]);
	const { locale } = useRouter();
	const toast = useAppToast();

	const getInitialData = () => {
		let initValues = { ...functions.getInitialValues };
		initValues.email = userDetails?.ICAEmail || "";
		initValues.mobileNo = userDetails?.ICAPhoneNumber || "";

		return initValues;
	};
	const [initialValues, setInitialValues] = useState(() => getInitialData());
	const [certificateDisable, setCertificateDisable] = useState(true);
	const [showOther, setShowOther] = useState(false);
	const [showOtherForm, setShowOtherForm] = useState(false);

	const OwnCertificateObj = {
		value: CertificateTypes.Own,
		label: t("Own"),
	};
	const FamilyCertificateObj = {
		value: CertificateTypes.Family,
		label: t("Family"),
	};
	const IcpEntityObject = {
		value: EntityAddressed.ICP,
		label: t("ICP"),
	};
	const OtherEntityObject = {
		value: EntityAddressed.Other,
		label: t("Other"),
	};
	const ToWhomEntityObject = {
		value: EntityAddressed.ToWhom,
		label: t("ToWhom"),
	};
	const MasterObject = masterData?.OtherEntities;
	const [certificateTypeLookup, setcertificateTypeLookup] = useState<Array<ICrmLookupLocalized>>([
		OwnCertificateObj,
		FamilyCertificateObj,
	]);
	const [entityAddressedLookup, setentityAddressedLookup] = useState<Array<ICrmLookupLocalized>>([
		ToWhomEntityObject,
		OtherEntityObject,
	]);

	const { mutateAsync: submitToWhomForm, isLoading: submittingFromLoading } = useMutation({
		mutationFn: (toWhomObject: ICrmToWhomForm) => ToWhomItMayConcernsRequest(toWhomObject),
		onSuccess: (data: any) => {
			setrequestNumber(data?.Data);
		},
	});

	const handleChangeEvent = (type, firstArg, secondArg, formik, isFieldArray = false) => {
		setisInitial(false);
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, isFieldArray);
		}
	};

	const handleTextChange = (event, fieldName, formik, type) => {
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, isFieldArray) => {
		formik.setFieldValue(fieldName, value);
		if (fieldName === "entityAddressed") {
			if (value.value === EntityAddressed.ToWhom) {
				setShowOther(false);
				setShowOtherForm(false);
				//reset field value

				formik.setFieldValue("otherEntity", {
					value: "",
				});

				formik.setFieldValue("typeOfCertificate", {
					value: CertificateTypes.Own,
					label: t("Own"),
				});

				setcertificateTypeLookup([
					{
						value: CertificateTypes.Own,
						label: t("Own"),
					},
				]);
			} else {
				// bug fix
				formik.setFieldValue("otherEntity", {
					value: "f54116ec-22e4-ed11-8847-6045bd6a528f",
				});
				setcertificateTypeLookup([
					{
						value: CertificateTypes.Own,
						label: t("Own"),
					},
				]);
				// reset field value original
				formik.setFieldValue("typeOfCertificate", {
					value: CertificateTypes.Own,
					label: t("Own"),
				});

				value.value === EntityAddressed.Other ? setShowOtherForm(true) : setShowOtherForm(false);
			}
			setCertificateDisable(false);
			formik.validateField("otherEntityAddressed");
		}
		if (fieldName === "otherEntity") {
			if (value.value === "f54116ec-22e4-ed11-8847-6045bd6a528f") {
				setShowOther(true);
				//reset field value
				formik.setFieldValue("typeOfCertificate", {
					value: CertificateTypes.Own,
					label: t("Own"),
				});
				setcertificateTypeLookup([
					{
						value: CertificateTypes.Own,
						label: t("Own"),
					},
				]);
			} else if (value.value === "a515241f-e0e4-ed11-8847-6045bd6a528f") {
				formik.setFieldValue("typeOfCertificate", "");
				setcertificateTypeLookup([
					{
						value: CertificateTypes.Own,
						label: t("Own"),
					},
					{
						value: CertificateTypes.Family,
						label: t("Family"),
					},
				]);

				setShowOther(false);
			} else {
				setShowOther(false);
				// reset field value
				formik.setFieldValue("typeOfCertificate", {
					value: CertificateTypes.Own,
					label: t("Own"),
				});

				setcertificateTypeLookup([
					{
						value: CertificateTypes.Own,
						label: t("Own"),
					},
				]);
			}
		}
	};

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={async (e: any) => {
				// if (!isCustomerPulseSubmitted) {
				// 	openCustomerPulse();
				// } else {
				let formObject: ICrmToWhomForm = {
					EmiratesId: userDetails?.EmiratesID || "",
					Email: e.email,
					MobileNumber: e.mobileNo,
					EntityAddressed: e.entityAddressed.value,
					OtherEntity: e.otherEntity.value,
					TypeOfCertification: e.typeOfCertificate.value,
					OtherEntityAddressed: e.otherEntityAddressed,
				};
				let resp = await submitToWhomForm(formObject);
				if (resp.IsSuccess) {
					setisSubmitted(true);
				} else {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
				}
				// }
			}}
		>
			{(formik) => {
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
							formik.setSubmitting(false);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						{!isSubmitted ? (
							<>
								<Box p={10} bg={"brand.white.50"}>
									<Grid
										rowGap={{ base: 6, md: 6 }}
										columnGap={16}
										templateColumns="repeat(2, 1fr)"
										templateRows="auto"
									>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["email"]}
												isRequired={true}
												name={"email"}
												label={t("PreferredEmailUsed", { ns: "forms" })}
												placeholder={""}
												error={formik.errors[`email`]}
												//touched={formik.touched[`email`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "email", formik);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["mobileNo"]}
												isRequired={true}
												name={"mobileNo"}
												label={t("mobileNoUsed")}
												placeholder={""}
												error={formik.errors[`mobileNo`]}
												//touched={formik.touched[`mobileNo`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "mobileNo", formik);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={formik.values["entityAddressed"]}
												isRequired={true}
												name={"entityAddressed"}
												label={t("entityAddressed")}
												placeholder={""}
												options={entityAddressedLookup}
												error={formik.errors[`entityAddressed`]}
												onChange={(firstArg) => {
													handleChangeEvent("selectableTags", firstArg, "entityAddressed", formik);
													setTimeout(() => formik.setFieldTouched("entityAddressed", true));
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={formik.values["typeOfCertificate"]}
												isRequired={true}
												name="typeOfCertificate"
												label={t("typeOfCertificate")}
												placeholder={""}
												isDisabled={certificateDisable}
												options={certificateTypeLookup}
												error={formik.errors["typeOfCertificate"]}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"typeOfCertificate",
														formik
													);
													setTimeout(() => formik.setFieldTouched("typeOfCertificate", true));
												}}
											/>
										</GridItem>
										{showOtherForm && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="selectableTags"
													value={formik.values["otherEntities"]}
													isRequired={true}
													name="otherEntity"
													label={t("otherEntities")}
													placeholder={""}
													isDisabled={certificateDisable}
													options={MasterObject}
													error={formik.errors["otherEntity"]}
													onChange={(firstArg) => {
														handleChangeEvent("selectableTags", firstArg, "otherEntity", formik);
														setTimeout(() => formik.setFieldTouched("otherEntity", true));
													}}
												/>
											</GridItem>
										)}

										{showOther && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="text"
													value={formik.values["otherEntityAddressed"]}
													isRequired={true}
													name={"otherEntityAddressed"}
													label={t("otherEntityAddressed")}
													placeholder={""}
													error={formik.errors[`otherEntityAddressed`]}
													//touched={formik.touched[`otherEntityAddressed`]}
													onChange={(firstArg) => {
														handleChangeEvent("text", firstArg, "otherEntityAddressed", formik);
													}}
												/>
											</GridItem>
										)}
									</Grid>
								</Box>
								<Box px={10}>
									<Button
										mt={5}
										variant="primary"
										type="submit"
										float={"right"}
										isLoading={submittingFromLoading || isCustomerPulseLoading}
										disabled={
											!formik.isValid ||
											formik.isSubmitting ||
											isInitial ||
											submittingFromLoading ||
											isCustomerPulseLoading
										}
									>
										<Text as="span">
											{isCustomerPulseSubmitted ? t("generate") : t("proceed", { ns: "common" })}
										</Text>
									</Button>
								</Box>
							</>
						) : (
							<Box px={4}>
								<Box p={10}>
									<Flex
										width={{ base: "90%", md: "100%" }}
										py={5}
										pb={{ base: 0, md: 5 }}
										flexDirection={"column"}
										alignItems={"center"}
									>
										<CompletedSuccessfully w={"80px"} h={"80px"} />
										<Text
											fontSize={"xl"}
											fontWeight={700}
											color={"brand.textColor"}
											textAlign={"center"}
											mt={5}
										>
											{t("common:requestReview2")}
										</Text>
										<Text
											mt={4}
											textAlign={"center"}
											color={"brand.textColor"}
											fontSize={"md"}
											display={{ base: "none", md: "block" }}
										>
											{t("common:thanksForApplaying2")}
										</Text>
									</Flex>
								</Box>

								<TableContainer
									border="1px"
									borderBottom="0px"
									borderColor="brand.tableBorderColor"
									rounded="lg"
								>
									<Table variant="simple">
										<Tbody>
											<Tr>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="1rem"
													fontWeight="bold"
													lineHeight="150%"
													letterSpacing="unset"
													w="50%"
												>
													{t("submittedOn")}
												</Td>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="0.875rem"
													fontWeight="normal"
													letterSpacing="unset"
													lineHeight="150%"
												>
													<Text dir={"auto"} w={"min-content"}>
														{getFormattedDate(new Date(), "dd MMMM yyyy", locale)}
													</Text>
												</Td>
											</Tr>
											<Tr>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="1rem"
													fontWeight="bold"
													lineHeight="150%"
													letterSpacing="unset"
													w="50%"
												>
													{t("requestNumber")}
												</Td>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="0.875rem"
													fontWeight="normal"
													letterSpacing="unset"
													lineHeight="150%"
												>
													<Text dir={"auto"} w={"min-content"}>
														{requestNumber}
													</Text>
												</Td>
											</Tr>
										</Tbody>
									</Table>
								</TableContainer>
								<Button
									my={5}
									variant="primary"
									type="submit"
									float={"right"}
									as={NextLink}
									href="/"
								>
									<Text as="span">{t("common:goHome")}</Text>
								</Button>
							</Box>
						)}
					</Form>
				);
			}}
		</Formik>
	);
}
