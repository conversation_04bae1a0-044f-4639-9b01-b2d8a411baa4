import * as Yup from "yup";

export const validationSchema = Yup.object({
	currentSituation: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	ageGroupWomen: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	childAttributes: Yup.object()
		.shape({
			label: Yup.string(),
			value: Yup.string().required().label("thisField"),
		})
		.required(),
	haveChildren: Yup.string().when(["maritalStatus", "gender", "ageGroupPOD"], {
		is: (maritalStatus, gender, ageGroupPOD) => {
			//notSingle && !isFemaleAndMarried
			return (
				!!maritalStatus &&
				!!gender &&
				maritalStatus.value !== "1" &&
				!(gender.value === "2" && maritalStatus.value === "2") &&
				ageGroupPOD.value !== "1"
			);
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),
	numberOfChildren: Yup.number().when("haveChildren", {
		is: (haveChildren) => {
			return haveChildren === "1";
		},
		then: Yup.number()
			.integer("isIntegerAndhasOneOrTwoDigits")
			.min(0, "isIntegerAndhasOneOrTwoDigits")
			.max(99, "PleaseEntera1or2-digit")
			.typeError("ThisFieldShouldbeNumber")
			.required(),

		otherwise: Yup.number().notRequired().nullable(),
	}),
	isChildrenPOD: Yup.string().when("haveChildren", {
		is: (haveChildren) => {
			return haveChildren === "1";
		},
		then: Yup.string().label("thisField").required(),
		otherwise: Yup.string().notRequired().nullable(),
	}),
	numberOfPODChildren: Yup.number().when(["haveChildren", "isChildrenPOD"], {
		is: (haveChildren, isChildrenPOD) => {
			return haveChildren === "1" && isChildrenPOD === "1";
		},
		then: Yup.number()
			.label("thisField")
			.required()
			.test({
				name: "validate-number",
				message: "NumberofPoDChildren",
				test: (value: any) => {
					const isInteger = Number.isInteger(value);
					const stringValue = value !== undefined ? value.toString() : "";
					const hasOneOrTwoDigits = /^[0-9]{1,2}$/.test(stringValue.replace(/,/g, ""));

					return isInteger && hasOneOrTwoDigits;
				},
			})
			.max(Yup.ref("numberOfChildren"), "NumberofPoDChildren")
			.min(1)
			.positive("NumberofPoDChildren")
			.typeError("NumberofPoDChildren"),
		otherwise: Yup.number().notRequired().nullable(),
	}),

	totalIncome: Yup.number()
		.required()
		.label("thisField")
		.typeError("ThisFieldShouldbeNumber")
		.test({
			name: "no-signs-or-dots",
			message: "PleaseEnteranIntegerNumber",
			test: (value: any) => Number.isInteger(value) && /^[0-9]+$/.test(value),
		})
		.required(),
});
