import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	name: "",
	email: "",
	comment: "",
};

const getValidationSchema = () => {
	return Yup.object({
		name: Yup.string().required().label("thisField"),
		email: Yup.string().required().label("thisField"),
		comment: Yup.string().required().label("thisField"),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};
export { getInitialValues, onChange, getValidationSchema };
