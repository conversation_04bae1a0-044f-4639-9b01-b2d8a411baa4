{"complaint": "شكوى", "complaintNumber": "رقم الطلب المرتبط بال{{inquiryType}}", "entityAddressed": "الجهة المعنية", "eweError": "هذا الرقم يحتوي ١٢ خانة", "Family": "عائلية", "generate": "إصدار الشهادة", "ICP": "الهيئة الاتحادية للهوية والجنسية والجمارك وأمن المنافذ", "inquiry": "استفسار", "mobileNo": "رقم الهاتف المفضل", "icpMobileNo": "رقم الهاتف", "noCase": "فارغ", "Other": "جها<PERSON> اخرى", "otherEntities": "الجهات الأُخرى", "otherEntityAddressed": "جهات موجهة أُخرى", "Own": "شخصية", "requestNumber": "رق<PERSON> الطلب", "requestService": "<PERSON><PERSON><PERSON> خدمة", "submittedOn": "تم التقديم", "suggestion": "اقتراح", "thankYou": "شكرًا", "thankYouTitle": "شكرًا", "thisField": "هذا الحقل", "ToWhom": "لمن يهمه الامر (دون تحديد اسم الجهة)", "typeOfCertificate": "نوع الشهادة", "uaeMobileNumberError": "ير<PERSON>ى إدخال رقم هاتف بالصيغة التالية ‎05XXXXXXXX", "uaeIDNumberError": "الرجاء إدخال رقم إماراتي صحيح", "wrongEmailAddress": "عُذراً، يُرجى إدخال بريد إلكتروني صحيح.", "mobileNoUsed": " رقم الهاتف المتحرك (سيتم إرسال الشهادة إلى رقم الهاتف أدناه)", "spacesnotallowed": "هذا الحقل لا يمكن أن يحتوي على مسافات"}