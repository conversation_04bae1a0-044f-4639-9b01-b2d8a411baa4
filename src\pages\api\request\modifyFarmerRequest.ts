import { ALLOWANCE_CATEGORY } from "config";
import { ISocialAidForm } from "interfaces/SocialAidForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ISocialAidForm>>
) {
	const { UpdateType, IdCase, CaseDetails, Index, SubIndex, IdProcessTempalte } = req.body;
	if (!UpdateType || !CaseDetails)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.modifyFarmerRequest({
		UpdateType,
		IdCase,
		CaseDetails,
		IdAllowanceCategory: ALLOWANCE_CATEGORY,
		IdProcessTempalte,
		IdBeneficiary: contactId,
		Index,
		SubIndex,
	});

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
