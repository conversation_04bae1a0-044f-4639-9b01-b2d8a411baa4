import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import React, { ReactElement } from "react";
import { useTranslation } from "react-i18next";
import {
	Box,
	Heading,
	Grid,
	GridItem,
	TabList,
	Tabs,
	TabPanel,
	TabPanels,
	Text,
} from "@chakra-ui/react";
//import { SearchIcon } from "@chakra-ui/icons";
import { ArticleIcon, PeopleGroupIcon, PersonCheckIcon, RequirmentIcom } from "components/Icons";
import DlsAccordion from "components/DLS/DlsAccordion";
import VerticalTablist from "components/DLS/VerticalTablist";
import Breadcrumbs from "components/Breadcrumbs";
import { getStrapiContent } from "services/strapi";
import { FAQPage } from "utils/strapi/faq";
import { InferGetServerSidePropsType } from "next";
// import NewSelectDropDown from "components/NewSelectDropDown";
// import SocialAidTabContent from "./SocialAidTabContent copy";
// import FarmerAidTabContent from "./FarmerAidTabContent";
// import SocialAidTabContent from "./SocialAidTabContent copy";
// import FarmerAidTabContent from "./FarmerAidTabContent";
//import FormField from "components/Form/FormField";

function FAQ({ content }: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation(["faq", "common"]);

	const icons = { ArticleIcon, PeopleGroupIcon, PersonCheckIcon, RequirmentIcom };
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-faq"),
			id: "faq",
			link: "#",
			isCurrentPage: true,
		},
	];
	const Faqs2DList = content.services_faq
		.map((program) => program.tabs)
		.flatMap((s) => s)
		.map((s) => s.faq_items);

	return (
		<Box pt={"2rem"} px={{ base: "4", lg: "6.25rem" }} w="full">
			<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Heading fontSize={"2.5rem"}>{content.page_header}</Heading>

			<Tabs orientation="vertical" variant={"soft-rounded"}>
				<Grid
					templateColumns="repeat(3, 1fr)"
					gap={{ base: "1rem", md: "4.5rem" }}
					w="full"
					pb={20}
				>
					<GridItem textColor={"#1B1D21"} colSpan={{ base: 3, md: 1 }}>
						<TabList w="full">
							{content.services_faq.map((service, idx) => {
								if (idx === 0) {
									return (
										<React.Fragment key={idx}>
											<Text color="#1B1D21" my={6} as="h2" fontSize={"1.875rem"}>
												{service.title}
											</Text>
											<VerticalTablist
												tabItems={service.tabs.map((s) => ({
													tabTitle: s.title,
													Icon: icons[s.icon],
												}))}
											/>
										</React.Fragment>
									);
								}
							})}
						</TabList>
					</GridItem>
					<GridItem w="100%" colSpan={{ base: 3, md: 2 }}>
						<TabPanels>
							{Faqs2DList.map((faqList, idx) => (
								<TabPanel px="0" key={idx}>
									<DlsAccordion FaqList={faqList} />
								</TabPanel>
							))}
						</TabPanels>
					</GridItem>
				</Grid>
			</Tabs>
			{/* <Text> {content.updatedAt} تم التحديث </Text> */}
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	const content = (await getStrapiContent("faq-page?populate=deep,10", ctx.locale)).data as FAQPage;
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "faq"])),
			// Will be passed to the page component as props
			content,
		},
	};
}

FAQ.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default FAQ;
