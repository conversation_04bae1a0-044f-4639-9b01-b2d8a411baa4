import React, { useState } from "react";
import {
	Accordion,
	AccordionItem,
	AccordionButton,
	AccordionPanel,
	AccordionIcon,
	Box,
	Text,
	Link,
} from "@chakra-ui/react";
import ImageModal from "components/ImageModal";
import { URL_REGEX } from "config";

const FaqAccordion = ({ data, title }) => {
	const [showImage, setShowImage] = useState(false);
	const [imagePath, setImagePath] = useState("");
	const [imageTitle, setImageTitle] = useState("");
	return (
		<Box w="full" px={0}>
			<Text fontWeight="700" fontSize="lg" color="textColor" my={3} px="6">
				{title}
			</Text>
			{data.map((item, index) => {
				return (
					<Box key={index} px={{ base: "0", md: "0" }} my="6">
						<Accordion allowMultiple bg="brand.lightGold">
							<AccordionItem px="3" py="2">
								<AccordionButton fontWeight="700" fontSize={{ base: "md", md: "md2" }} gap={4}>
									<Text flex="1" w="full" textAlign={"start"} color={"#000000"}>
										{item.title}
									</Text>
									<AccordionIcon />
								</AccordionButton>
								<AccordionPanel w="full" color={"#000000"}>
									{item.body.split("\n").map((i, key) => {
										let regex = new RegExp(URL_REGEX);
										let result = i.match(regex);
										if (result && result[0] !== null) {
											result = result[0];
											let startIndex = i.search(regex);
											let endIndex = startIndex + result.length;
											return (
												<Box key={key} flexDirection={"row"}>
													<Text fontSize={{ base: "md", md: "md2" }}>
														{i.slice(0, startIndex)}
														<Link
															target={"_blank"}
															color={"#0069c2"}
															textDecoration={"underline"}
															href={"https://" + result}
														>
															{result}
														</Link>

														<Text as={"span"} fontSize={{ base: "md", md: "md2" }}>
															{i.slice(endIndex)}
														</Text>
													</Text>
												</Box>
											);
										} else {
											return (
												<Box key={key}>
													<Text fontSize={{ base: "md", md: "md2" }}>{i}</Text>
												</Box>
											);
										}
									})}
									{item.image && (
										<Link
											color={"red"}
											onClick={() => {
												setShowImage(true);
												setImagePath(item.image);
												setImageTitle(item.imageTitle);
											}}
										>
											{item.imageTitle}
										</Link>
									)}
								</AccordionPanel>
							</AccordionItem>
						</Accordion>
					</Box>
				);
			})}
			<ImageModal
				imageTitle={imageTitle}
				isOpen={showImage}
				imagePath={imagePath}
				onClose={() => {
					setShowImage(false);
				}}
				width={"65vh"}
			/>
		</Box>
	);
};

export default FaqAccordion;
