import React from "react";
import { Heading, VStack } from "@chakra-ui/react";
import PropTypes, { InferProps } from "prop-types";

const SectionTitle = ({
	children,
	paddingBottom = 6,
	paddingY = null,
	fontColor = null,
}: InferProps<typeof SectionTitle.propTypes>) => {
	return (
		<VStack alignItems="self-start" width="full">
			<Heading
				color={fontColor ?? "brand.textColor"}
				fontSize="xl"
				fontWeight={700}
				pb={paddingBottom}
			>
				{children}
			</Heading>
		</VStack>
	);
};

SectionTitle.propTypes = {
	children: PropTypes.any.isRequired,
};

export default SectionTitle;
