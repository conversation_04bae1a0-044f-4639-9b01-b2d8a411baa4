import { ICrmComplaint } from "interfaces/CrmComplaint.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmComplaint>>
) {
	const { ComplaintId, reOpenReason } = req.body;
	if (!ComplaintId)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const data = await BackendServices.appealComplaint(ComplaintId, reOpenReason);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
