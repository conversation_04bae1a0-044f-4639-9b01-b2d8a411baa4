import { Box, Flex, Text, Tabs, Tab<PERSON>ist, Tab<PERSON>ane<PERSON>, Tab, TabPanel, VStack } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { ReactElement } from "react";
import { useTranslation } from "next-i18next";
import ServiceDescription from "components/DLS/ServiceDescription";
import StarRating from "components/StarRating";
import Breadcrumbs from "components/Breadcrumbs";
import { InferGetServerSidePropsType } from "next";
import { getStrapiContent } from "services/strapi";
import { getImageUrls } from "utils/strapi/helpers";
import {
	getContactIdFromToken,
	getIsEmiratesIDExpiryDateFromToken,
	getIsEmiratesNationalityFromToken,
} from "utils/helpers";
import { FarmerServiceContent } from "utils/strapi/farmerService";
import RequiredDocumentsTab from "pagesComponents/TabsContent/RequiredDocumentsTab";
function Topups({
	content,
	isEidExp,
	isEmirates,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	const { t } = useTranslation();

	const breadcrumbsData: any = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "howToApply",
			link: "/smart-services",
			isCurrentPage: false,
		},
		{
			label: t("common:topups"),
			id: "howToApply",
			link: "#",
			isCurrentPage: true,
		},
	];

	return (
		<Box w="100%">
			<Box pos={"relative"}>
				<Flex
					w={{ base: "100%", md: "100%" }}
					minH={"23rem"}
					bgImage={getImageUrls(content.landing_image.url)}
					bgPosition={{ base: "100%", md: "100%" }}
					bgSize="cover"
					bgRepeat={"no-repeat"}
					bgPos={{ base: "top", md: "left" }}
					className="overlay"
					alignItems={"center"}
				>
					<VStack
						width={{ base: "100%", sm: "90%", md: "40%" }}
						marginInlineStart={{ base: 0, md: "6.5rem" }}
						alignItems={"start"}
						zIndex={10}
						px={{ base: "1rem" }}
						w="full"
					>
						<Box display={{ base: "none", md: "block" }} pb={"2rem"}>
							<Breadcrumbs data={breadcrumbsData} isLight />
						</Box>
						<Text color={"brand.white.50"} fontSize={{ base: "2rem", lg: "2.5rem" }}>
							{content.page_header}
						</Text>
					</VStack>
				</Flex>
			</Box>
			<Box>
				<Tabs>
					<TabList px={{ base: "1rem", md: "3rem", lg: "6rem" }}>
						<Tab
							_selected={{
								color: "brand.textColor",
								fontWeight: "700",
								opacity: 1,
								borderColor: "brand.mainGold",
								borderBottomWidth: "4px",
							}}
							px={0}
							py={5}
							me={{ base: 10, md: 15 }}
							opacity={0.5}
							fontSize={{ base: "md", md: "xl" }}
						>
							{content.first_tab_header}
						</Tab>

						<Tab
							_selected={{
								color: "brand.textColor",
								opacity: 1,
								fontWeight: "700",
								borderColor: "brand.mainGold",
								borderBottomWidth: "4px",
							}}
							px={0}
							pb={2.5}
							me={{ base: 10, md: 15 }}
							opacity={0.5}
							fontSize={{ base: "md", md: "xl" }}
						>
							{content.second_tab_header}
						</Tab>
					</TabList>

					<TabPanels
						px={{ base: "1rem", md: "3rem", lg: "6rem" }}
						lineHeight="2.375rem"
						w="full"
						mt={4}
					>
						<TabPanel px={0} w="full">
							<ServiceDescription content={content} isEidExp={isEidExp} isEmirates={isEmirates} />
						</TabPanel>

						<TabPanel px={0}>
							<RequiredDocumentsTab content={content} isEidExp={isEidExp} isEmirates={isEmirates} />
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<Box px={{ base: "1rem", md: "3rem", lg: "6rem" }} pt={0} pb={10}>
				<StarRating mt="10" />
			</Box>
		</Box>
	);
}

export async function getServerSideProps(ctx) {
	const content = await getStrapiContent("/topups-service-page?populate=deep", ctx.locale);

	const expDate = await getIsEmiratesIDExpiryDateFromToken(ctx.req);

	const isEmiratesData = await getIsEmiratesNationalityFromToken(ctx.req);
	const isEmirates = isEmiratesData !== undefined ? isEmiratesData : true;
	const ToDate = new Date();

	let isEidExp = false;
	if (expDate) isEidExp = new Date(expDate).getTime() < ToDate.getTime() ? true : false;
	const contactId = await getContactIdFromToken(ctx.req);

	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common", "buttons", "forms", "tables"])),
			// Will be passed to the page component as props
			content: content.data as FarmerServiceContent,
			isEidExp,
			isEmirates,
		},
	};
}
Topups.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default Topups;
