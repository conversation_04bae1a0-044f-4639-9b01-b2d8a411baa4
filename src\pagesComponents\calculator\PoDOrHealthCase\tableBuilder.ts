import { formatAmount } from "utils/formatters";
import { PODCase } from "../calculator";
import { localizedLookups } from "../lookups";

export const PodTableBuilder = (data: PODCase, locale: string = "ar", reason: string) => {
	const { personalInformation } = data;
	const personalInformationData = {
		header: "personalInformation",
		data: [{ label: "reasonForApplying", value: convertLookup(reason, "reason", locale) }],
	};
	// add wifes info if the user is  married male
	const isPodData = {
		label: "isPod",
		value: convertLookup(personalInformation.isPod!, "boolean", locale),
	};
	personalInformationData.data.push(isPodData);

	if (personalInformation.isPod === "0") {
		const haveHealthDisablityData = {
			label: "haveHealthDisablity",
			value: convertLookup(personalInformation.haveHealthDisablity!, "boolean", locale),
		};
		personalInformationData.data.push(haveHealthDisablityData);
	}

	if (personalInformation.haveHealthDisablity === "1") {
		const values = JSON.parse(personalInformation.healthDisablity!).map((val) =>
			convertLookup(val, "healthDisablity", locale)
		) as any[];
		personalInformationData.data.push({
			label: "healthDisablityHave",
			value: values.join(", "),
		});
	}
	personalInformationData.data.push(
		...[
			{ label: "gender", value: convertLookup(personalInformation.gender!, "gender", locale) },
			{
				label: "ageGroup",
				value: convertLookup(personalInformation.ageGroupPOD!, "ageGroupPOD", locale),
			},
			{
				label: "maritalStatus",
				value: convertLookup(personalInformation.maritalStatus!, "maritalStatus", locale),
			},
		]
	);
	if (
		personalInformation.gender === "1" &&
		personalInformation.maritalStatus === "2" &&
		personalInformation.ageGroupPOD !== "1"
	) {
		const numberOfSpousesData = {
			label: "numberOfSpouses",
			value: String(personalInformation.numberOfSpouses),
		};
		const areSpousePODData = {
			label: "isSpousesPOD",
			value: convertLookup(personalInformation.isSpousesPOD!, "boolean", locale),
		};
		// if male has POD spouses add the number of them to the table
		let spousePODNumber: any = null;
		if (personalInformation.isSpousesPOD === "1") {
			spousePODNumber = {
				label: "numberOfPODSpouses",
				value: String(personalInformation.numberOfPODSpouses),
			};
		}
		personalInformationData.data.push(numberOfSpousesData);
		personalInformationData.data.push(areSpousePODData);
		if (spousePODNumber) {
			personalInformationData.data.push(spousePODNumber);
		}
	}

	// add children info, if there is a potentiol for children
	const notSingle = personalInformation.maritalStatus !== "1";
	const isFemaleAndMarried =
		personalInformation.gender === "2" && personalInformation.maritalStatus === "2";
	// if the marital is not single , and is not married female
	const withPotentialChildren =
		!isFemaleAndMarried && notSingle && personalInformation.ageGroupPOD !== "1";
	if (withPotentialChildren) {
		const haveChildrenData = {
			label: "haveChildren",
			value: convertLookup(personalInformation.haveChildren!, "boolean", locale),
		};
		let numberOfChildren: any | null = null;
		let isChildrenPOD: any = null;
		let childrenPODNumber: any = null;

		personalInformationData.data.push(haveChildrenData);

		if (personalInformation.haveChildren === "1") {
			numberOfChildren = {
				label: "numberOfChildren",
				value: String(personalInformation.numberOfChildren),
			};
			isChildrenPOD = {
				label: "isChildrenPOD",
				value: convertLookup(personalInformation.isChildrenPOD!, "boolean", locale),
			};
			childrenPODNumber = {
				label: "numberOfPODChildren",
				value: String(personalInformation.numberOfPODChildren),
			};

			personalInformationData.data.push(numberOfChildren);
			personalInformationData.data.push(isChildrenPOD);

			if (personalInformation.isChildrenPOD === "1")
				personalInformationData.data.push(childrenPODNumber);
		}
	}

	const incomeData = {
		header: "incomeInformation",
		data: [
			{
				label: "totalIncome",
				value: formatAmount(String(personalInformation.totalIncome), 0),
			},
		],
	};
	return [personalInformationData, incomeData];
};

export const convertLookup = (
	lookupValue: string,
	lookupName: string,
	locale: string = "ar"
): string => {
	const lookups = localizedLookups(locale);
	return lookups[lookupName].find((lookup) => lookup.value === lookupValue).label;
};
