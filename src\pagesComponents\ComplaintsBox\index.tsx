import { Box, <PERSON>ton, Flex, Text } from "@chakra-ui/react";
import { ButtonArrowIcon } from "components/Icons";
import { useRouter } from "next/router";
import React, { ReactElement } from "react";

interface ComplaintsBoxProp {
	icon: ReactElement;
	mainText: string;
	subMainText: string;
	handleViewDetails: () => void;
	handleExample?: () => void;
	detailsButton: string;
	exampleButton: string;
}

export default function ComplaintsBox(props: ComplaintsBoxProp) {
	const { locale } = useRouter();
	return (
		<Box
			className="root"
			borderWidth={1}
			_hover={{
				bg: "linear-gradient(110deg, #E4D5B6 -38.25%, rgba(252, 232, 177, 0.20) 63.16%);",
				borderColor: "brand.mainGold",
			}}
			borderColor="brand.normalBorderColor"
			borderRadius={"10px"}
			p={6}
			h={"100%"}
			maxH={"30rem"}
		>
			<Box w={"fit-content"} h={"65px"}>
				{props.icon}
			</Box>

			<Box py={7}>
				<Text fontWeight={"bold"} w={"100%"} color="brand.black.50" fontSize={"3xl"}>
					{props.mainText}
				</Text>
				<Text my={8} fontWeight={"400"} w={"100%"} color="brand.textColor" fontSize={"lg"}>
					{props.subMainText}
				</Text>
				<Flex flexDirection={"row"} justifyContent={"space-between"} alignItems={"center"}>
					<Button
						variant="outline"
						height={"48px"}
						px={"0px !important"}
						rightIcon={
							<ButtonArrowIcon
								w={"24px"}
								h={"24px"}
								transform={locale === "ar" ? "scale(-1, 1)" : ""}
							/>
						}
						onClick={props.handleViewDetails}
						fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
						color="brand.textColor"
						width={"fit-content"}
					>
						{props.detailsButton}
					</Button>
				</Flex>
			</Box>
		</Box>
	);
}
