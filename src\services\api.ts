import axios from "axios";
import {
	CUSTOMER_PULSE_API,
	STRAPI_API_URL,
	STRAPI_AUTH_TOKEN,
	WRAPPER_API_ENDPOINT_URL,
} from "config";

export const backendApi = axios.create({
	baseURL: WRAPPER_API_ENDPOINT_URL,
	headers: {
		"Content-Type": "application/json",
		Accept: "application/json",
	},
});

export const frontendApi = axios.create({
	baseURL: "/api/",
	headers: {
		"Content-Type": "application/json",
		Accept: "application/json",
	},
});

export const customerPulseApi = axios.create({
	baseURL: CUSTOMER_PULSE_API,
	headers: {
		"Content-Type": "application/json",
		Accept: "application/json",
	},
});

export const strapiApi = axios.create({
	baseURL: STRAPI_API_URL,
	headers: {
		"Content-Type": "application/json",
		Accept: "application/json",
		Authorization: `<PERSON><PERSON> ${STRAPI_AUTH_TOKEN}`,
	},
});
