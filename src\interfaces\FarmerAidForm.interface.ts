import {
	IFamilyMember,
	ListIncomeSourceDetail,
	ListPensionDetail,
	ListRentalDetail,
	ListTradeLicenseDetail,
} from "./SocialAidForm.interface";

export interface IFarmerCaseDetails {
	RegisteredWithEWE?: boolean;
	Alternativenumber?: string;
	EWEBill?: string;
	RelatedEmiratesID?: string;
	AlternativeEmail?: string;
	ReceiveSocialAid?: boolean;
	EntityReceivedFrom?: number;
	ReceiveInflationAllowance?: boolean;
	OwnerEWEBill?: boolean;
	CaseRef?: string;
	IsHouseholdHeadContributeToIncome?: boolean;
	ListIncomeSourceDetails?: ListIncomeSourceDetail[];
	IsHouseholdHeadReceivePensionIncome?: boolean;
	ListPensionDetails?: ListPensionDetail[];
	IshouseholdHeadTradeLicense?: boolean;
	ListTradeLicenseDetails?: ListTradeLicenseDetail[];
	IshouseholdHeadReceiveRentalIncome?: boolean;
	ListRentalDetails?: ListRentalDetail[];
	ListFamilyMember?: IFamilyMember[];
}
export interface IFarmerForm {
	UpdateType?: string;
	IdStatus?: string;
	IdCase?: string;
	CaseDetails?: IFarmerCaseDetails;
	IdProcessTempalte?: string;
	IdAllowanceCategory?: string;
	IdBeneficiary?: string;
	SubmissionTime?: string;
	Index?: number;
	SubIndex?: number;
	ListFamilyMember?: any;
	Mobile?: string;
	Email?: string;
	CaseRef?: string;
}

export interface IFarmerAidFormRequest extends Omit<IFarmerForm, "IdCase"> {
	IdCase?: string;
	Mobile?: string;
	Email?: string;
	CaseRef?: string;
}

export interface IFarmerFormFERequest
	extends Omit<
		IFarmerAidFormRequest,
		"IdBeneficiary" | "IdProcessTempalte" | "IdAllowanceCategory"
	> {}
