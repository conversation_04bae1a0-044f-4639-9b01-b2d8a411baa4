{
	// Set the default
	"editor.formatOnSave": false,
	"editor.defaultFormatter": "esbenp.prettier-vscode",
	// Enable per-language
	"[typescriptreact]": {
		"editor.formatOnSave": true
	},
	"[typescript]": {
		"editor.formatOnSave": true
	},
	"[javascriptreact]": {
		"editor.formatOnSave": true
	},
	"[javascript]": {
		"editor.formatOnSave": true,
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"files.eol": "\n",
	"i18n-ally.localesPaths": ["public/locales"],
	"i18n-ally.keystyle": "nested",
	"i18n-ally.editor.preferEditor": true
}
