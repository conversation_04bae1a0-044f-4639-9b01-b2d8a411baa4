import { useBreakpointValue, useToast } from "@chakra-ui/react";
import ToastNotification, { ToastNotificationProps } from "components/ToastNotification";
import React from "react";

const useAppToast = (Component = ToastNotification) => {
	const toast = useToast();
	const containerWidth = useBreakpointValue({
		base: {
			minWidth: "90%",
			maxWidth: "90%",
		},
		md: {
			minWidth: "50%",
			maxWidth: "50%",
		},
	});

	return (options: ToastNotificationProps) =>
		toast({
			position: "top",
			render: ({ onClose }) => <Component {...options} onClose={onClose} />,
			containerStyle: containerWidth,
		});
};

export default useAppToast;
