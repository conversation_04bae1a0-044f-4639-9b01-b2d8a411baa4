import { Box, Button, Flex, Text } from "@chakra-ui/react";
import React, { ReactElement } from "react";

interface BeneficiaryBoxProp {
	icon: ReactElement;
	mainText: string;
	handleViewDetails: () => void;
	handleExample?: () => void;
	detailsButton: string;
	exampleButton: string;
}

export default function BeneficiaryBox(props: BeneficiaryBoxProp) {
	return (
		<Box borderWidth={1} borderColor="brand.normalBorderColor" borderRadius={"10px"}>
			{props.icon}
			<Box px={5} py={7} h="250px">
				<Text mb={7} fontWeight={"bold"} w={"100%"} color="brand.black.50" fontSize={"xl"} h="60%">
					{props.mainText}
				</Text>
				<Flex flexDirection={"row"} justifyContent={"space-between"} alignItems={"center"} h="20%">
					<Button height={"48px"} variant="secondary" onClick={props.handleViewDetails}>
						<Text fontSize={"lg"} color={"brand.mainGold"}>
							{props.detailsButton}
						</Text>
					</Button>
					{props.handleExample && (
						<Button
							variant="outline"
							onClick={props.handleExample}
							fontSize={{ base: "md", sm: "md", md: "sm", lg: "xl" }}
							width={"fit-content"}
						>
							{props.exampleButton}
						</Button>
					)}
				</Flex>
			</Box>
		</Box>
	);
}
