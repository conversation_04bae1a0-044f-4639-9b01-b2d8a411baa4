// eslint-disable-next-line unused-imports/no-unused-imports
import NextAuth, { DefaultSession } from "next-auth";
import { UaepassProfile } from "interfaces/Uaepass.interface";
import { ICrmContact } from "interfaces/CrmContact.interface";

declare module "next-auth" {
	interface Profile extends UaepassProfile {}

	interface Session {
		provider: string;
		user: DefaultSession["user"] & ICrmContact;
	}
}
