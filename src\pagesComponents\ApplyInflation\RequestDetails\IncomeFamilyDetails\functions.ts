import { getFormattedDate } from "utils/helpers";
import { formatAmount } from "utils/formatters";
import { tempData } from "./datamock";

const createIncomeTablesData = (data: typeof tempData, locale) => {
	return data.ListSalaryIncome.map((income) => [
		{
			label: "مصدر الدخل",
			value: income.IncomeSource,
		},
		{
			label: "نوع الدخل",
			value: income.IncomeType || "-",
		},
		{
			label: "الدخل",
			value: formatAmount(income.Income),
		},
		{
			label: "اسم الفرد",
			value: locale === "ar" ? income.FullNameArabic : income.FullName,
		},
	]);
};
const createPensionTablesData = (data: typeof tempData, locale) => {
	return data.ListPensionIncome.map((income) => [
		{
			label: "نوع معاش التقاعد",
			value: income.PensiontType,
		},
		{
			label: "هيئة التقاعد",
			value: income.PensiontAuthority || "-",
		},
		{
			label: "مبلغ معاش التقاعد الشهري (بالدرهم الإماراتي)",
			value: formatAmount(income.PensiontAmount),
		},
		{
			label: "اسم الفرد",
			value: locale === "ar" ? income.FullNameArabic : income.FullName,
		},
	]);
};
const createTradeTableData = (data: typeof tempData, locale) => {
	return data.ListTradeIncome.map((income) => [
		{
			label: "مبلغ الرخصة التجارية الشهري (بالدرهم الإماراتي)",
			value: formatAmount(income.Income),
		},
		{
			label: "اسم الفرد",
			value: locale === "ar" ? income.FullNameArabic : income.FullName,
		},
	]);
};
const createRentalTableData = (data: typeof tempData, locale) => {
	return data.ListRentalIncome.map((income) => [
		{
			label: "رقم العقد",
			value: income.ContractNo,
		},
		{
			label: "تاريخ بداية العقد",
			value: getFormattedDate(income.ContractStartDate, "dd MMMM yyyy", locale) || "-",
		},
		{
			label: "تاريخ نهاية العقد",
			value: getFormattedDate(income.ContractEndDate, "dd MMMM yyyy", locale) || "-",
		},
		{
			label: "مصدر الإيجار",
			value: income.RentalSource,
		},
		{
			label: "مقدار الإيجار الشهري (درهم إماراتي)",
			value: formatAmount(income.RentAmount),
		},
		{
			label: "اسم الفرد",
			value: locale === "ar" ? income.FullNameArabic : income.FullName,
		},
	]);
};

export {
	createIncomeTablesData,
	createPensionTablesData,
	createTradeTableData,
	createRentalTableData,
};
