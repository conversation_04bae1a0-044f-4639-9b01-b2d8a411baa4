import { Box } from "@chakra-ui/react";
import InflationInfoForm from "./InflationInfoForm";

function RequestDetailsForm({
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
}) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<InflationInfoForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
