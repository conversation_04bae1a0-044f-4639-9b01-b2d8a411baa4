import { TimeIcon } from "@chakra-ui/icons";
import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import React from "react";
import { HeadphoneIcon, HierarchyIcon, CalenderIcon } from "components/Icons";
import ServiceDetails from "components/ServiceDetails";

function ServiceDescription({ documentList, serviceLink }: any) {
	return (
		<Flex w="full" gap="1.5rem" flexDir={{ base: "column", md: "row" }}>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "70%" }}>
				<ServiceDetails
					title={"Service Info"}
					body={
						"This service is a new subsidy for faaskdaskkdaskadsrm owners with limited income, at a value of 8,400 dirhams annually for each beneficiary, which is equivalent to 2,500 kilowatt-hours per month, or the value of consumption, whichever is less, This service is a new subsidy for farm owners with limited income, at a value of 8,400 dirhams annually for each beneficiary, "
					}
					Icon={HeadphoneIcon}
				/>
				<Flex gap="1.5rem" flexDir={{ base: "column", md: "row" }}>
					<ServiceDetails
						Icon={HierarchyIcon}
						title={"Service Provider"}
						body={
							"The Ministry of Community Empowerment in cooperation with the Union Water and Electricity Company."
						}
					/>
					<ServiceDetails
						Icon={CalenderIcon}
						title={"When Will this aid apply?"}
						body={
							"The Ministry of Community Empowerment in cooperation with the Union Water and Electricity Company."
						}
					/>
				</Flex>
			</VStack>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "30%" }}>
				<Note fees={"Free"} process={"5-7 days"} time={"5 mnute"} title={"Note"} />
				{/* <ServiceRequirements documentList={documentList} serviceLink={serviceLink} /> */}
			</VStack>
		</Flex>
	);
}
function Note({ title, time, process, fees }) {
	return (
		<VStack w="full" align={"start"} bg="brand.white.100" borderRadius={"10px"} p={"2rem"}>
			<Text as="h2" fontWeight={500} fontSize={"1.5rem"}>
				{title}
			</Text>
			<VStack align={"start"} spacing={"1rem"} fontSize={"1.125rem"}>
				<HStack>
					<TimeIcon />
					<Text>مدة تقديم الطلب: 5-2 دقائق</Text>
				</HStack>
				<HStack>
					<TimeIcon />
					<Text>مدة انجاز الخدمة: ٢١ يوم</Text>
				</HStack>
				<HStack>
					<TimeIcon />
					<Text>مدة انجاز الخدمة: ٢١ يوم</Text>
				</HStack>
			</VStack>
		</VStack>
	);
}

export default ServiceDescription;
