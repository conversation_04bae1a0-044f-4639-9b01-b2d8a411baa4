import React from "react";
import { Image, Stack, Text, HStack, Button } from "@chakra-ui/react";
import { useTranslation } from "next-i18next";
const Card = ({ imgSrc, date, title, description }) => {
	const { t } = useTranslation("common");
	return (
		<>
			<Image src={imgSrc} alt="Green double couch with wooden legs" w="full" padding="0rem" />
			<Stack mt="6" spacing="3">
				<HStack justifyContent="space-between" mb="1.0625">
					<Text fontSize="fontSizes.md" fontWeight="bold">
						{title}
					</Text>
					<Text color="brand.textColor" fontSize="sm" fontWeight="normal" opacity="0.5">
						{date}
					</Text>
				</HStack>
				<Text
					noOfLines={4}
					paddingRight="1.2rem"
					fontSize="sm"
					fontWeight="normal"
					color="brand.gray.50"
				>
					{description}
				</Text>
				<Button
					justifyContent="flex-start"
					color="brand.mainGold"
					textDecoration="underline"
					px="0rem"
					py="0rem"
					mt={[0, " -0.4375rem !important"]}
					onClick={() => console.log("read more")}
				>
					<Text as="span">{t("readMore")}</Text>
				</Button>
			</Stack>
		</>
	);
};

export default Card;
