import { Box, Table, TableContainer, Tbody, Td, Text, Tr, VStack, Flex } from "@chakra-ui/react";
import { CompletedSuccessfully, NotQualifiedIcon } from "components/Icons";
import { useRouter } from "next/router";
import { WomenIDSCase } from "pagesComponents/calculator/calculator";
import React from "react";
import { useTranslation } from "react-i18next";
import { formatAmount } from "utils/formatters";
import { WomenIDSTableBuilder } from "../tableBuilder";
import { WomenInDFSCalculator } from "pagesComponents/calculator/calculators/womenInDFS";

export default function SecondStep({
	formData,
	reason,
	changeElegable,
}: {
	formData: WomenIDSCase;
	formKey: string;
	reason: string;
	changeElegable: any;
}) {
	const { child, self, eightyPercant, final, isEligble } = WomenInDFSCalculator.calculate(formData);
	const { t } = useTranslation(["calculator"]);
	const { locale } = useRouter();
	const tableData = WomenIDSTableBuilder(formData, locale, reason);
	changeElegable(isEligble);
	return (
		<Box>
			<VStack spacing={4} mt={8}>
				{isEligble ? (
					<>
						<CompletedSuccessfully h="64px" w="64px" />
						<Text fontSize={"2xl"} fontWeight={"bold"} textAlign={"center"}>
							{t("calculator:youAreEligble")}
						</Text>
						<Text
							textAlign={"center"}
							color="#1B1D21B8"
							dangerouslySetInnerHTML={{ __html: t("calculator:eligbleDesc") }}
						></Text>
						<Box>
							<Text textAlign={"center"} fontWeight={"600"} fontSize="lg" color={"brand.mainGold"}>
								{t("calculator:yourExcpectedAll")}
							</Text>
							<Flex align="center">
								<Text fontWeight="bold" fontSize={{ md: "3rem", base: "1rem" }}>
									{t("AED")}
								</Text>
								<Text
									fontWeight="bold"
									fontSize={{ md: "3rem", base: "1rem" }}
									mt={{ md: "3", base: "" }}
									mx="2"
								>
									{eightyPercant < 800
										? "800"
										: ` ${formatAmount(eightyPercant, 0)} - ${formatAmount(final, 0)}`}
								</Text>
							</Flex>
						</Box>
					</>
				) : (
					<>
						<NotQualifiedIcon h="64px" w="64px" />
						<Text fontSize={"2xl"} fontWeight={"bold"} textAlign={"center"}>
							{t("calculator:youAreNotEligble")}
						</Text>
						<Text
							textAlign={"center"}
							color="#1B1D21B8"
							dangerouslySetInnerHTML={{ __html: t("calculator:eligbleDesc") }}
						></Text>
					</>
				)}
			</VStack>
			<Box mt={8}>
				<Text>{t("calculator:allSummary")}</Text>
				<TableContainer
					border="1px"
					borderBottom="0px"
					borderColor="brand.tableBorderColor"
					rounded="lg"
					mt={4}
				>
					<Table variant="simple">
						<Tbody>
							{tableData?.map((data, idx) =>
								data.data.length > 0 ? (
									<React.Fragment key={idx}>
										<Tr>
											<Td
												borderColor="brand.tableBorderColor"
												fontSize="1rem"
												fontWeight="bold"
												lineHeight="150%"
												letterSpacing="unset"
												w="50%"
												colSpan={2}
												textAlign={"center"}
											>
												{t(data.header)}
											</Td>
										</Tr>

										{data.data.map((row, idx) => (
											<Tr key={idx}>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="1rem"
													fontWeight="500"
													lineHeight="150%"
													letterSpacing="unset"
													w="50%"
												>
													<Text>{t(row.label)}</Text>
												</Td>
												<Td
													borderColor="brand.tableBorderColor"
													fontSize="0.875rem"
													fontWeight="normal"
													letterSpacing="unset"
													lineHeight="150%"
												>
													<Text dir={"auto"} w={"min-content"}>
														{row.label === "totalIncome"
															? locale === "en"
																? row.value + " AED"
																: row.value + " درهم إماراتي"
															: row.value}
													</Text>
												</Td>
											</Tr>
										))}
									</React.Fragment>
								) : (
									<></>
								)
							)}
						</Tbody>
					</Table>
				</TableContainer>
			</Box>
		</Box>
	);
}
