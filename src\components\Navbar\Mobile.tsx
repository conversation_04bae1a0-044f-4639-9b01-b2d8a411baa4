import {
	useDisclosure,
	But<PERSON>,
	Drawer,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON><PERSON>nt,
	DrawerHeader,
	Flex,
	DrawerBody,
	Text,
	Divider,
	DrawerCloseButton,
	Accordion,
	AccordionItem,
	AccordionButton,
	AccordionPanel,
	AccordionIcon,
	VStack,
} from "@chakra-ui/react";
import LangSwitcher from "components/LangSwitcher";
import { useLogout } from "hooks/useLogout";
import NextLink from "next/link";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { LANG_SWITCHER_ENABLED } from "config";
import FontResizeDrawer from "./FontResizeDrawer";
import { Menu } from "utils/strapi/navbar";

const NavbarMobile = ({ headerMenu, isMain = true }: { headerMenu: Menu; isMain?: boolean }) => {
	const { t } = useTranslation();

	const { isOpen, onOpen, onClose } = useDisclosure();
	const { push, locale, pathname } = useRouter();
	const subpath = pathname.split("/")[1];
	const logoutHandler = useLogout();

	const finalItems = headerMenu.items.filter((item) => !item.required_auth);
	return (
		<>
			<Button variant={"primary"} onClick={onOpen} pe={0}>
				<Text>{t("menu")}</Text>
			</Button>
			<Drawer onClose={onClose} isOpen={isOpen} size="full" placement={"right"}>
				<DrawerOverlay />
				<DrawerContent>
					<DrawerHeader>
						{/* <Flex w={"100%"}>
							<CloseIcon
								color={"#606164"}
								_hover={{ cursor: "pointer" }}
								onClick={onClose}
								ms={"auto"}
							/>
						</Flex> */}
						<DrawerCloseButton size={"lg"} color={"#606164"} />
					</DrawerHeader>
					<DrawerBody>
						<Flex mt={6} flexDirection={"column"} alignItems="flex-start" gap="6" h={"100%"}>
							{pathname !== "/home" && pathname !== "/login" && (
								<Button
									mt={2}
									as={NextLink}
									href={`/${locale}/login${
										pathname ? `?callbackUrl=${encodeURIComponent(pathname)}` : ""
									}`}
									w="full"
									variant={"primary"}
									onClick={onClose}
								>
									{t("logIn")}
								</Button>
							)}
							{isMain &&
								finalItems.map((route, idx) => {
									const isCurrentPath = `/${subpath}` === route.url ? true : false;
									var fW = isCurrentPath ? 600 : 400;
									const isDropdown = route?.children?.length > 0 || false;
									const localizedTitle = locale === "ar" ? route.title : route.english_title;
									if (isDropdown)
										return (
											<Accordion allowToggle p={0} key={idx}>
												<AccordionItem border="0" p={0}>
													<AccordionButton border="0" p={0} m={0}>
														{localizedTitle}
														<AccordionIcon mx={2} />
													</AccordionButton>

													<AccordionPanel p={0} mt={4}>
														<VStack align={"start"}>
															{route.children.map((child, idx) => {
																const isCurrentPath = `/${subpath}` === child.url ? true : false;
																var fW = isCurrentPath ? 600 : 400;
																const localizedTitle =
																	locale === "ar" ? child.title : child.english_title;
																return (
																	<Button
																		key={idx}
																		fontWeight={fW}
																		as={NextLink}
																		href={child.url!}
																		p={0}
																		onClick={onClose}
																	>
																		{localizedTitle}
																	</Button>
																);
															})}
														</VStack>
													</AccordionPanel>
												</AccordionItem>
											</Accordion>
										);
									return (
										<Button
											fontWeight={fW}
											as={NextLink}
											href={route.url!}
											ps={0}
											key={idx}
											onClick={onClose}
										>
											{localizedTitle}
										</Button>
									);
								})}
							<Divider borderColor={"#BBBCBD"} />
							<FontResizeDrawer />

							{LANG_SWITCHER_ENABLED && (
								<>
									<Divider borderColor={"#BBBCBD"} />
									<LangSwitcher onClose={onClose} />
								</>
							)}

							{/* <FontResize /> */}
						</Flex>
					</DrawerBody>
				</DrawerContent>
			</Drawer>
		</>
	);
};

export default NavbarMobile;
