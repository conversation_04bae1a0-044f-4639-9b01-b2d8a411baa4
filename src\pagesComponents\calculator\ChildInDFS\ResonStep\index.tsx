import { Box, Text } from "@chakra-ui/react";
import React, { useId, useState } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

import { useSteps } from "chakra-ui-steps";

function ResonStep(props) {
	const { t } = useTranslation(["calculator", "common"]);
	const stepManager = useSteps({
		initialStep: 0,
	});
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-myAllowance"),
			id: "navbar-myAllowance",
			link: "#",
			isCurrentPage: true,
		},
	];
	const [reason, setReason] = useState<string>();
	const [selectedValue, setSelectedValue] = useState(null);
	const { locale, push, reload } = useRouter();
	const router = useRouter();

	const id = useId();
	const stepCounter = ["1", "2"].includes(reason || "") ? 3 : 2;
	const progressPercantage = ((stepManager.activeStep + 1) / stepCounter) * 100;

	return (
		<Box maxW="500px" mt={4}>
			<Text color="#1b1d21b8" mb={1} fontWeight={"500"}>
				{t("reasonForApplying")}
			</Text>

			{/* <Select
					styles={customStyles}
					instanceId={id}
					menuPosition={"fixed"}
					isSearchable={false}
					isClearable={false}
					isMulti={false}
					placeholder={t("chooseAnOption")}
					components={{
						IndicatorSeparator: () => null,
					}}
					options={getLookUp("reason", locale)}
					onChange={(e: any) => {
						setReason(e?.value!);
						setSelectedValue(e);
					}}
                    value={getLookUp("reason", locale).find((r) => r.value === reason) as any}
                    /> */}
		</Box>
	);
}
export default ResonStep;
