interface ServiceCard {
	id: number;
	header: string;
	body: string;
	icon: string;
	service_url: string;
	button_text: string;
	background_image: Image;
}

interface AppInfo {
	id: number;
	body: string;
	header: string;
	phone_image: Image;
	appstore_image: Image;
	google_play_image: Image;
}

interface Partner {
	id: number;
	url: string;
	partner_image: Image;
}

interface Image {
	id: number;
	name: string;
	alternativeText: string | null;
	caption: string | null;
	width: number;
	height: number;
	hash: string;
	ext: string;
	mime: string;
	size: number;
	url: string;
	previewUrl: string | null;
	provider: string;
	provider_metadata: any | null;
	createdAt: string;
	updatedAt: string;
}

export interface SmartServicesPage {
	id: number;
	page_header: string;
	page_description: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	cards_header: string;
	services_cards: ServiceCard[];
	app_info: AppInfo;
	partners: Partner[];
	partner_header: string;
	landing_image: Image;
}
