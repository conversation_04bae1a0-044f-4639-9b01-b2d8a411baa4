import { TimeIcon } from "@chakra-ui/icons";
import { Flex, HStack, Text, VStack, Box } from "@chakra-ui/react";
import React from "react";
import ServiceRequirements from "./ServiceRequirements";
import { HeadphoneIcon, HierarchyIcon, CalenderIcon, GroupIcon } from "components/Icons";
import ServiceDetails from "components/ServiceDetails";
import { SocialAidContent } from "utils/strapi/socialAid";

function ServiceDescription({
	content,
	isEidExp,
	isEmirates,
	userEligibleAge = true,
	eligibleInflation = true,
}: {
	content: SocialAidContent;
	isEidExp?: boolean;
	isEmirates?: boolean;
	userEligibleAge?: boolean;
	eligibleInflation?: boolean;
}) {
	const icons = {
		HeadphoneIcon,
		HierarchyIcon,
		CalenderIcon,
		GroupIcon,
	};
	const ServiceInfoIcon = icons[content.service_info.icon];
	const ServiceProviderIcon = icons[content.service_provider.icon];
	const ApplyAidIcon = icons[content.aid_application_time.icon];
	return (
		<Flex w="full" gap="1.5rem" flexDir={{ base: "column", md: "row" }}>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "70%" }}>
				<ServiceDetails
					title={content.service_info.header}
					body={content.service_info.body}
					Icon={ServiceInfoIcon}
				/>
				<Flex gap="1.5rem" flexDir={{ base: "column", md: "row" }} w={{ base: "full" }}>
					<ServiceDetails
						Icon={ServiceProviderIcon}
						title={content.service_provider.header}
						body={content.service_provider.body}
					/>
					<ServiceDetails
						Icon={ApplyAidIcon}
						title={content.aid_application_time.header}
						body={content.aid_application_time.body}
					/>
				</Flex>
				<Box>
					<Text as="h2" fontSize={"3xl"} fontWeight={"bold"}>
						{content.process_header}
					</Text>
					<Flex flexWrap={"wrap"} maxW={{ lg: "100%", base: "100%" }} gap={"1.75rem"} mt={8}>
						{content.process_list.map((step, idx) => (
							<Flex
								key={idx}
								alignItems={"center"}
								justifyContent={"center"}
								w={{ base: "100%", lg: "16rem" }}
								border="1px solid #ADADAD"
								paddingInlineStart={{ md: "1.8rem" }}
								paddingInlineEnd={{ md: "3.5rem" }}
								rounded="lg"
								pos="relative"
								px={{ base: 4 }}
								flexBasis={{ base: "unset", md: "calc(33.33% - 1.75rem)" }}
								py={10}
							>
								<Flex
									top="2"
									right="2"
									w="6"
									h="6"
									rounded={"full"}
									pos={"absolute"}
									bg="brand.mainGold"
									justifyContent={"center"}
									alignItems={"center"}
									color="white"
								>
									<Text textAlign={"center"}>{idx + 1}</Text>
								</Flex>
								<Text>{step.text}</Text>
							</Flex>
						))}
					</Flex>
				</Box>
			</VStack>
			<VStack align={"start"} spacing={"1.5rem"} w={{ base: "full", md: "30%" }}>
				<Note serviceDetails={content.service_details} />
				<ServiceRequirements
					required_documents={content.required_documents}
					isEidExp={isEidExp}
					isEmirates={isEmirates}
					userEligibleAge={userEligibleAge}
					eligibleInflation={eligibleInflation}
				/>
			</VStack>
		</Flex>
	);
}
export function Note({ serviceDetails }: { serviceDetails: SocialAidContent["service_details"] }) {
	return (
		<VStack w="full" align={"start"} bg="brand.white.100" borderRadius={"10px"} p={"2rem"}>
			<Text as="h2" fontWeight={500} fontSize={"1.5rem"}>
				{serviceDetails.header}
			</Text>
			<VStack align={"start"} spacing={"1rem"} fontSize={"1.125rem"}>
				<HStack alignItems={"start"}>
					<TimeIcon mt={3} />
					<Text>
						{serviceDetails.application_time_label} : {serviceDetails.application_time_value}
					</Text>
				</HStack>
				<HStack align={"start"}>
					<TimeIcon mt={3} />
					<Text>
						{serviceDetails.process_duration_label} : {serviceDetails.process_duration_value}
					</Text>
				</HStack>
				<HStack align={"start"}>
					<TimeIcon mt={3} />
					<Text>
						{serviceDetails.process_fees_label} : {serviceDetails.process_fees_value}
					</Text>
				</HStack>
			</VStack>
		</VStack>
	);
}

export default ServiceDescription;
