import { ICrmLookupLocalized, ICrmMasterData } from "interfaces/CrmMasterData.interface";

export interface TableHeadersObject {
	label?: string;
	id: string;
	type: string;
	isBold?: boolean;
	underlined?: boolean;
}

export type TableCustomProps = {
	caption?: string | null;
	tableHeaders: TableHeadersObject[];
	tableBody: any[];
	hasFooter: boolean;
	footerValues: any[];
	statusLookup?: ICrmMasterData<ICrmLookupLocalized>["CaseStatus"];
	tableName?: string;
	mb?: number | string;
};
