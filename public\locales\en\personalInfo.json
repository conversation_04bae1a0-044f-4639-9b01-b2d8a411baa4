{"complaint": "<PERSON><PERSON><PERSON><PERSON>", "complaintNumber": "Request / Case Number ", "entityAddressed": "Entity Addressed", "eweError": "This field must be 12 digits", "Family": "Family", "generate": "Generate", "ICP": "Federal Authority for Identity and Nationality, Customs and Ports Security", "inquiry": "Inquiry", "mobileNo": "Preferred Mobile Number", "icpMobileNo": "Mobile Number", "noCase": "Empty", "Other": "Other", "otherEntities": "Other Entities", "otherEntityAddressed": "Other Entity Adressed", "Own": "Own", "requestNumber": "Case Number", "requestService": "Request Service", "submittedOn": "Submitted On", "suggestion": "Suggestion", "thankYou": "Thank You", "thankYouTitle": "Thank You", "thisField": "This field", "ToWhom": "To Whom It May Concern", "typeOfCertificate": "Type of certificate", "uaeMobileNumberError": "Please provide a UAE number with the following format 05XXXXXXXX", "uaeIDNumberError": "Please enter a valid Emirates ID", "wrongEmailAddress": "Please enter a valid email address.", "mobileNoUsed": "Preferred Mobile Number (the certificate will be sent to this number)", "spacesnotallowed": "This field cannot contain spaces"}