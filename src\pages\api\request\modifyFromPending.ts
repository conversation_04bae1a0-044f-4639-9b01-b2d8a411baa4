import { ALLOWANCE_CATEGORY, PROCESS_TEMPLATE_ID } from "config";
import { ISocialAidForm } from "interfaces/SocialAidForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { BackendServices, errorResponse } from "services/backend";
import { getContactIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ISocialAidForm>>
) {
	const { UpdateType, CaseDetails, IdCase } = req.body;
	if (!UpdateType)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const contactId = await getContactIdFromToken(req);

	const data = await BackendServices.modifyRequestFromPending({
		UpdateType,
		IdCase,
		CaseDetails,
		IdAllowanceCategory: ALLOWANCE_CATEGORY,
		IdProcessTempalte: PROCESS_TEMPLATE_ID,
		IdBeneficiary: contactId,
	});

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
