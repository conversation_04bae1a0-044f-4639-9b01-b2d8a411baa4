import { Box } from "@chakra-ui/react";
import EducationInfoForm from "./EducationInfoForm";

function RequestDetailsForm({
	formKey,
	members,
	setMembers,
	readOnly = false,
	IsEdit,
	isEligibleForTopup = true,
	caseData,
}) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<EducationInfoForm
				onSubmit={onSubmit}
				members={members}
				setMembers={setMembers}
				readOnly={readOnly}
				IsEdit={IsEdit}
				isEligibleForTopup={isEligibleForTopup}
				caseData={caseData}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
