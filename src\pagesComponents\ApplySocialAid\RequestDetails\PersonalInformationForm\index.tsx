import { Box } from "@chakra-ui/react";
import PersonalInfoForm from "./PersonalInfoForm";
function PersonalInformation({
	innerText,
	handleChangeEvent,
	formKey,
	initialData,
	handleSetFormikState,
	isMartialEmpty,
	isMaritalInit,
	readOnly = false,
	reInitialize = "",
	isEdit = false,
}) {
	const onSubmit = async (values: any, actions) => {
		//console.log({ values, actions });
	};
	return (
		<Box>
			<PersonalInfoForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				initialData={initialData}
				isMartialEmpty={isMartialEmpty}
				isMaritalInit={isMaritalInit}
				handleSetFormikState={handleSetFormikState}
				readOnly={readOnly}
				reInitialize={reInitialize}
				isEdit={isEdit}
			/>
		</Box>
	);
}

export default PersonalInformation;
