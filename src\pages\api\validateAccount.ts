import { ICrmAccountData } from "interfaces/CrmAccountData.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<ICrmAccountData | null>>
) {
	const { AccountNumber, Provider, CaseId, ParentCaseId } = req.body;
	if (!AccountNumber || !Provider)
		return res.status(400).json({ ...errorResponse, Errors: "missing Data" });

	const data = await BackendServices.validateAccount(AccountNumber, Provider, CaseId, ParentCaseId);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}
