{"createNewUaePass": "Create new account", "description": "A single trusted digital identity for all citizens.", "didntReceiveOtp": "Didn’t get a code?", "dontHaveUaePass": "Don’t have a UAE PASS account?", "eidPlaceholder": "Emirates ID eg. 784-1990-1234567-8", "loginOr": "OR", "loginToAccess": "Please login to access the page.", "otpCancel": "Cancel", "otpNotification": "Please Note that the OTP will be sent to the phone number attached to that Emirates ID", "otpPlaceholder": "Enter OTP eg. 0950986", "otpResentSuccessDescription": "We have sent the OTP verification code to your mobile number.", "otpResentSuccessTitle": "OTP resent successfully", "otpSubmit": "Confirm", "pleaseEnterValidEid": "Please enter a valid Emirates ID", "pleaseEnterValidOtp": "Please enter a valid OTP", "requestOtp": "Request OTP", "sendOtpAgain": "Click to resend.", "signInWithUaePass": "Sign in with UAE Pass", "title": "Log In", "validateOtpDescription": "The OTP code is sent to {{otpNumber}}", "validateOtpTitle": "Enter OTP Verification", "verificationCode": "Verification code", "Sign_In_UAE_PASS": "Sign In With UAE PASS"}