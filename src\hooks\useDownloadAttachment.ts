import { useTranslation } from "next-i18next";
import { getAttachmentById } from "services/frontend";
import { downloadBase64File } from "utils/helpers";
import useAppToast from "./useAppToast";

const downloadAttachment = (toast: any, t: any) => {
	return async (documentId: string) => {
		if (!documentId) return;
		toast({
			title: t("pleaseWaitDownloadingFile"),
			status: "info",
		});
		const data = await getAttachmentById(documentId);
		if (data.IsSuccess && data.Data && data?.Data?.AttachmentBody) {
			const { FileName, AttachmentBody, MimeType } = data.Data;
			downloadBase64File(MimeType, AttachmentBody, FileName);
		} else {
			toast({
				title: t("genericErrorTitle"),
				description: t("genericErrorDescription"),
				status: "error",
			});
		}
	};
};

const useDownloadAttachment = () => {
	const toast = useAppToast();
	const { t } = useTranslation();
	return downloadAttachment(toast, t);
};

export default useDownloadAttachment;
