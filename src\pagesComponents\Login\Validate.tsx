import { Card } from "@chakra-ui/react";
import ValidateOtp from "pagesComponents/ValidateOtp";

function Validate(props) {
	return (
		<Card
			bg={"brand.white.50"}
			p={{ base: "32px 10px", md: "42px 58px" }}
			position={{ base: "relative", md: "absolute" }}
			top={{ base: "0", md: "15%" }}
			m={{ base: "0px 0px;", md: "0px" }}
			right={{ base: "0", md: "-35%" }}
			borderRadius={"unset"}
			h={{ base: "100vh", md: "unset" }}
		>
			<ValidateOtp {...props} />
		</Card>
	);
}

export default Validate;
