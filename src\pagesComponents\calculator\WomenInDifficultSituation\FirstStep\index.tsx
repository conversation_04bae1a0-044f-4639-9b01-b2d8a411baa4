import { Box, Grid, GridItem, ListItem, OrderedList, Text, UnorderedList } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Formik } from "formik";
import React, { useEffect, useState, useRef } from "react";
import { PODCase } from "../../calculator";
import { getLookUp, localizedLookups } from "../../lookups";
import { useRouter } from "next/router";
import useAppToast from "hooks/useAppToast";
import { useTranslation } from "react-i18next";
import { validationSchema } from "./functions";
import { formatAmount } from "utils/formatters";

export default function FirstStep({
	formData: { personalInformation },
	handleChange,
	handleSetFormikState,
	formKey,
}: {
	formData: PODCase;
	handleChange: any;
	handleSetFormikState: any;
	formKey: string;
}) {
	const { locale } = useRouter();
	const toast = useAppToast();
	const empty = {
		currentSituation: "",
		ageGroupWomen: "",
		haveChildren: "",
		childAttributes: "",
		numberOfChildren: "",
		isChildrenPOD: "",
		numberOfPODChildren: "",
		totalIncome: "",
	};
	const lookups = localizedLookups(locale);
	const formikRef: any = useRef();
	const handleSelectChange = (fieldName, newValue) => {
		handleChange("selectableTags", newValue, fieldName, formikRef.current, formKey);
	};
	const updateDropdownValues = () => {
		let originalInitialValues = { ...empty };
		Object.keys(personalInformation).forEach((key) => {
			if (key in lookups) {
				let indexOfItem = lookups[key].findIndex((val) => val.value === personalInformation[key]);
				if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				handleSelectChange(key, lookups[key][indexOfItem]);
			} else {
				originalInitialValues[key] = personalInformation[key]
					? JSON.parse(JSON.stringify(personalInformation[key]))
					: personalInformation[key];
			}
		});
		return originalInitialValues;
	};
	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(() => updateDropdownValues());
	}, [locale]);
	const { t } = useTranslation("calculator");
	return (
		<Formik
			validateOnMount
			onSubmit={() => {}}
			initialValues={initialValues}
			validationSchema={validationSchema}
		>
			{(formik) => {
				const { haveChildren, isChildrenPOD, childAttributes } = formik.values as any;
				formikRef.current = formik!;

				const haveNoChildren = haveChildren === "0";
				const noneOfTheAbove = childAttributes.value === "4";
				const enEligableConditions = [haveNoChildren, noneOfTheAbove];
				const enEligble = enEligableConditions.some((t) => t === true);
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting || enEligble,
					},
					formKey
				);
				return (
					<Grid
						rowGap={{ base: 6, md: 4 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
							{t("personalInformation")}
						</Text>

						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("currentSituation")}
								options={getLookUp("currentSituation", locale)}
								placeholder={t("chooseAnOption")}
								name="currentSituation"
								error={formik.errors.currentSituation}
								value={formik.values.currentSituation}
								onChange={(firstArg) => {
									handleSelectChange("currentSituation", firstArg);
								}}
							/>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="selectableTags"
								label={t("ageGroup")}
								options={getLookUp("ageGroupWomen", locale)}
								placeholder={t("chooseAnOption")}
								name="ageGroupWomen"
								error={formik.errors.ageGroupWomen}
								value={formik.values.ageGroupWomen}
								onChange={(firstArg) => {
									handleSelectChange("ageGroupWomen", firstArg);
								}}
							/>
						</GridItem>

						<GridItem colSpan={2}>
							<Text fontWeight={"semibold"} my={4}>
								{t("emiratiChilds")}
							</Text>
							<GrayBox>
								<Text>{t("childRules-1")}</Text>
								<OrderedList>
									<ListItem>
										<Text>{t("childRules-2")}</Text>
									</ListItem>
									<ListItem>
										<Text>{t("childRules-3")}</Text>
									</ListItem>
								</OrderedList>
							</GrayBox>
						</GridItem>
						<GridItem colSpan={2} maxW="500px">
							<FormField
								type="radio"
								label={t("haveChildren")}
								options={getLookUp("boolean", locale)}
								placeholder={t("chooseAnOption")}
								name="haveChildren"
								error={formik.errors.haveChildren}
								value={formik.values.haveChildren}
								onChange={(firstArg) => {
									handleChange("radio", firstArg, "haveChildren", formik, formKey);
									if (firstArg === "0") {
										toast({
											status: "error",
											title: t("enEligble.womenWithNoChildAndNone"),
										});
									}
								}}
							/>
						</GridItem>
						{haveChildren === "1" && (
							<>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="selectableTags"
										label={t("childAttributes")}
										options={getLookUp("childAttributes", locale)}
										placeholder={t("chooseAnOption")}
										name="childAttributes"
										error={formik.errors.childAttributes}
										value={formik.values.childAttributes}
										onChange={(firstArg) => {
											handleSelectChange("childAttributes", firstArg);

											if (firstArg.value === "4") {
												toast({
													status: "error",
													title: t("enEligble.womenWithNoChildAndNone"),
												});
											}
										}}
									/>
								</GridItem>
								{childAttributes.value !== "4" && childAttributes && (
									<>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												type="text"
												name="numberOfChildren"
												placeholder={t("writeAnswerHere")}
												label={t("numberOfChildren")}
												value={formik.values.numberOfChildren}
												error={formik.errors.numberOfChildren}
												onChange={(firstArg) => {
													handleChange("text", firstArg, "numberOfChildren", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2} maxW="500px">
											<FormField
												type="radio"
												label={t("isChildrenPOD")}
												options={getLookUp("boolean", locale)}
												placeholder={t("chooseAnOption")}
												name="isChildrenPOD"
												error={formik.errors.isChildrenPOD}
												value={formik.values.isChildrenPOD}
												onChange={(firstArg) => {
													handleChange("radio", firstArg, "isChildrenPOD", formik, formKey);
												}}
											/>
										</GridItem>
										<GridItem colSpan={2}>
											<GrayBox>
												<Text>{t("mocdNote")}</Text>
											</GrayBox>
										</GridItem>
										{isChildrenPOD === "1" && (
											<GridItem colSpan={2} maxW="500px">
												<FormField
													type="text"
													name="numberOfPODChildren"
													placeholder={t("writeAnswerHere")}
													label={t("numberOfPODChildren")}
													value={formik.values.numberOfPODChildren}
													error={formik.errors.numberOfPODChildren}
													onChange={(firstArg) => {
														handleChange("text", firstArg, "numberOfPODChildren", formik, formKey);
													}}
												/>
											</GridItem>
										)}
									</>
								)}
							</>
						)}
						{!enEligble && (
							<>
								<GridItem colSpan={2}>
									<Text fontWeight={"semibold"} fontSize={"lg"} my={2}>
										{t("incomeInformation")}
									</Text>

									<GrayBox>
										<UnorderedList>
											<Text>{t("incomeRules1")}</Text>

											<UnorderedList>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules2")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules3")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules4")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules5")}</Text>
												</ListItem>
												<ListItem>
													<Text color="#1B1D21">{t("incomeRules6")}</Text>
												</ListItem>
											</UnorderedList>
										</UnorderedList>
									</GrayBox>
								</GridItem>
								<GridItem colSpan={2} maxW="500px">
									<FormField
										type="text"
										label={t("totalIncome")}
										name="totalIncome"
										error={formik.errors.totalIncome}
										placeholder={t("writeAnswerHere")}
										customFormat={formatAmount}
										value={formik.values.totalIncome}
										onChange={(firstArg) => {
											handleChange("text", firstArg, "totalIncome", formik, formKey);
										}}
									/>
								</GridItem>
							</>
						)}
					</Grid>
				);
			}}
		</Formik>
	);
}
const GrayBox = ({ children }) => {
	return (
		<Box bg="#F2F2F2" w="full " p={4} rounded="lg" mt={4}>
			{children}
		</Box>
	);
};
