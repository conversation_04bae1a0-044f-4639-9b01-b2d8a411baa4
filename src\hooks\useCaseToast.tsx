import { ICrmRequest } from "interfaces/CrmRequest.interface";
import { useEffect, useState } from "react";
import useHybridAppToast from "./useHybridAppToast";
import { useTranslation } from "react-i18next";
/**
 * hook to show pop up when cases need more info from user
 * @param requests
 * @param predicate
 * @returns
 */

function useCaseToast(requests: ICrmRequest[], predicate: (req: ICrmRequest) => boolean) {
	const toast = useHybridAppToast();
	const { t, i18n } = useTranslation(["common", "tables"]);
	const [hasToastShown, setHasToastShown] = useState(false);

	useEffect(() => {
		const req = requests.find(predicate);
		const currentLanguage = i18n.language;

		if (req && !hasToastShown) {
			toast({
				status: "error",
				title: t("moreInfoRequired"),
				studyingItem: `${t("afterStudying")}`,
				requestNumber: `${req.RequestName}`,
				reason: `${t("missingDocuments")}`,
				caseId: `${req?.CaseId}`,
				type: req?.Template?.TemplateId,
			});

			setHasToastShown(true);
		}
	}, [requests, i18n.language, hasToastShown, toast]);

	return toast;
}

export default useCaseToast;
