import { EmailIcon } from "@chakra-ui/icons";
import {
	Flex,
	Divider,
	Grid,
	FormControl,
	FormLabel,
	Input,
	InputGroup,
	InputLeftElement,
	Button,
	Text,
	InputRightElement,
} from "@chakra-ui/react";
import { UaeFlagIcon } from "components/Icons";
import { useUserDetails } from "hooks/useUserDetails";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { formatUaePhoneNumber, getFormattedDate } from "utils/helpers";

const inputFieldStyles = {
	readOnly: true,
	bg: "#F2F4F8",
	border: "0px",
	color: "#697077",
};

const Detail = ({
	profileData,
	preferredEmailAddress,
	preferredMobileNumber,
	onPreferredEmailAddressChange,
	onChangePreferredMobileNumber,
	onUpdateProfile,
	preferredEmailAddressError,
	preferredMobileNumberError,
	updateProfileLoading,
}) => {
	const { t } = useTranslation("profile");
	const { locale } = useRouter();
	const user = useUserDetails();
	let firstName, lastName, middleName;
	if (locale === "ar") {
		firstName = profileData.FirstNameArabic;
		lastName = profileData.FullNameArabic;
		middleName = profileData.MiddleNameArabic;
	} else {
		firstName = profileData.FirstName;
		lastName = profileData.LastName;
		middleName = profileData.MiddleName;
	}

	return (
		<Flex flex={"1"}>
			<Flex flexDirection={"column"} width={"100%"}>
				<Flex flexDirection={"column"} width={"100%"}>
					<Divider mb="1.5rem" mt="0.5rem" />
					<Text px={{ base: "1rem", md: "2.5rem" }} size="lg" fontWeight="bold" mb="2rem">
						{t("profile:description")}
					</Text>
					<Grid
						rowGap={{ base: 2.5, md: 4 }}
						columnGap={4}
						templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)" }}
						templateRows="auto"
						bg={"brand.white.50"}
						mb={7}
						px={{ base: "1rem", md: "2.5rem" }}
					>
						{/* <FormControl>
							<FormLabel color="#697077">{t("profile:firstName")}</FormLabel>
							<Input type="text" value={firstName} {...inputFieldStyles} />
						</FormControl>
						<FormControl>
							<FormLabel color="#697077">{t("profile:middleName")}</FormLabel>
							<Input type="text" value={middleName} {...inputFieldStyles} />
						</FormControl> */}
						<FormControl>
							<FormLabel color="#697077">{t("profile:fullname")}</FormLabel>
							<Input type="text" value={lastName} {...inputFieldStyles} />
						</FormControl>
						<FormControl>
							<FormLabel color="#697077">{t("profile:dateOfBirth")}</FormLabel>
							<Input
								type="text"
								value={getFormattedDate(profileData?.DateofBirth, "dd/MM/yyyy", locale)}
								{...inputFieldStyles}
							/>
						</FormControl>

						<FormControl>
							<FormLabel color="#697077">{t("profile:emailAddress")}</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<EmailIcon />
								</InputLeftElement>
								<Input type="text" value={profileData?.ICAEmail} {...inputFieldStyles} />
							</InputGroup>
						</FormControl>
						<FormControl>
							<FormLabel color="#697077">{t("profile:mobileNumber")}</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<UaeFlagIcon />
								</InputLeftElement>
								<Input
									type="text"
									value={formatUaePhoneNumber(profileData?.ICAPhoneNumber)}
									{...inputFieldStyles}
									dir="auto"
									textAlign={"left"}
								/>
								<InputRightElement pointerEvents="none" width={20}></InputRightElement>
							</InputGroup>
						</FormControl>
						<FormControl>
							<FormLabel>{t("profile:preferredEmailAddress")}</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<EmailIcon />
								</InputLeftElement>
								<Input
									type="text"
									value={preferredEmailAddress}
									onChange={onPreferredEmailAddressChange}
									placeholder={profileData?.ICAEmail}
									isInvalid={preferredEmailAddressError}
								/>
							</InputGroup>
						</FormControl>
						<FormControl>
							<FormLabel>{t("profile:preferredMobileNumber")}</FormLabel>
							<InputGroup>
								<InputLeftElement pointerEvents="none">
									<UaeFlagIcon />
								</InputLeftElement>
								<Input
									type="tel"
									value={preferredMobileNumber}
									onChange={onChangePreferredMobileNumber}
									placeholder={formatUaePhoneNumber(profileData?.ICAPhoneNumber)}
									maxLength={13}
									dir="auto"
									textAlign={"left"}
									isInvalid={preferredMobileNumberError}
								/>
								<InputRightElement width={20}>
									{/* <Button
										variant="outline"
										border={"1px"}
										borderColor={"brand.mainGold"}
										color={"brand.mainGold"}
										h={"65%"}
										px={2}
										onClick={onValidate}
									>
										<Text fontSize="sm">{t("validate")}</Text>
									</Button> */}
								</InputRightElement>
							</InputGroup>
						</FormControl>
						<FormControl>
							<Button
								mt="1rem"
								variant={"primary"}
								onClick={onUpdateProfile}
								isLoading={updateProfileLoading}
								w="full"
							>
								{t("profile:saveChanges")}
							</Button>
						</FormControl>
					</Grid>
				</Flex>
			</Flex>
			{/* <Modal isOpen={showValidateModal} onClose={onCloseValidateModal} size="xl" isCentered>
				<ModalOverlay />
				<ModalContent>
					<Box px={8} py={16}>
						<ValidateOtp
							otpNumber={undefined}
							onEnterOtp={undefined}
							onValidateOtp={undefined}
							otpError={undefined}
							onRequestOtp={undefined}
							onCancel={onCloseValidateModal}
							signInLoading={undefined}
						/>
					</Box>
				</ModalContent>
			</Modal> */}
		</Flex>
	);
};

export default Detail;
