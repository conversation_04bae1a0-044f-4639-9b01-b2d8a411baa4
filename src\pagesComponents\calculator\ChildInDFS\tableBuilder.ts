import { formatAmount } from "utils/formatters";
import { ChildInDFSCase } from "../calculator";
import { localizedLookups } from "../lookups";

export const ChildTableBuilder = (data: ChildInDFSCase, locale: string = "ar", reason: string) => {
	const { personalInformation } = data;
	const personalInformationData = {
		header: "personalInformation",
		data: [
			{ label: "reasonForApplying", value: convertLookup(reason, "reason", locale) },
			{
				label: "currentChildSituation",
				value: convertLookup(
					personalInformation.currentChildSituation!,
					"currentChildSituation",
					locale
				),
			},
			{
				label: "gender",
				value: convertLookup(personalInformation.gender!, "gender", locale),
			},
			{
				label: "ageGroup",
				value: convertLookup(personalInformation.ageGroupChild!, "ageGroupChild", locale),
			},
		],
	};

	if (personalInformation.ageGroupChild === "2") {
		personalInformationData.data.push({
			label: "qualifiedStudent",
			value: convertLookup(personalInformation.qualifiedStudent!, "boolean", locale),
		});
	}

	const haveSiblingsData = {
		label: "haveSiblings",
		value: convertLookup(personalInformation.haveSiblings!, "boolean", locale),
	};
	let numberOfSiblings: any | null = null;
	let isSiblingPOD: any = null;
	let siblingsPODNumber: any = null;

	personalInformationData.data.push(haveSiblingsData);

	if (personalInformation.haveSiblings === "1") {
		numberOfSiblings = {
			label: "numberOfSiblings",
			value: String(personalInformation.numberOfSiblings),
		};
		isSiblingPOD = {
			label: "isSiblingsPOD",
			value: convertLookup(personalInformation.isSiblingsPOD!, "boolean", locale),
		};
		if (personalInformation.isSiblingsPOD === "1")
			siblingsPODNumber = {
				label: "numberOfPODSiblings",
				value: String(personalInformation.numberOfPODSiblings),
			};

		personalInformationData.data.push(numberOfSiblings);
		personalInformationData.data.push(isSiblingPOD);

		if (siblingsPODNumber) personalInformationData.data.push(siblingsPODNumber);
	}

	const incomeData = {
		header: "incomeInformation",
		data: [
			{
				label: "totalIncome",
				value: formatAmount(String(personalInformation.totalIncome), 0),
			},
		],
	};
	return [personalInformationData, incomeData];
};

export const convertLookup = (
	lookupValue: string,
	lookupName: string,
	locale: string = "ar"
): string => {
	const lookups = localizedLookups(locale);
	return lookups[lookupName].find((lookup) => lookup.value === lookupValue).label;
};
