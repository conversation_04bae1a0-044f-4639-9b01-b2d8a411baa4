import { Flex, Image } from "@chakra-ui/react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import React from "react";
import { useTranslation } from "react-i18next";

function RequestBlocked() {
	const { t } = useTranslation(["common"]);
	const { locale } = useRouter();
	return (
		<Flex w="full" alignItems="start" flexDir={"column"} h="full" minH="100vh" bg="#FFF">
			<Flex
				width={"100%"}
				position={"relative"}
				flexDirection={"column"}
				// bg="url('/assets/images/loginImg.jpg')"
				height={{ base: "91vh", md: "calc(89vh)" }}
				backgroundSize={"cover"}
				justifyContent="center"
				alignItems={"center"}
			>
				<Flex
					as="header"
					flexDirection={{ base: "row", md: "row" }}
					justifyContent="space-between"
					px={6}
					py={{ base: 2, md: 4 }}
					bg="brand.white.50"
					w="full"
					h={{ base: "auto", md: "98px" }}
					dir={"ltr"}
				>
					<Image
						src={"../assets/images/logo-en.png"} //"../assets/images/logo-en.png"
						alt="logomain"
						maxW={{ base: "80%", md: "100%" }}
						maxH={"100%"}
						draggable="false"
					/>
				</Flex>
				<Image
					src="assets/images/under-maint.jpeg"
					alt="this website is under maintenance"
					sizes={"big"}
				/>
			</Flex>
		</Flex>
	);
}

export async function getStaticProps(ctx) {
	return {
		props: {
			...(await serverSideTranslations(ctx.locale, ["common"])),
		},
	};
}

// RequestBlocked.getLayout = function getLayout(page: ReactElement) {
// 	return (
// 		<MainLayout minimalLayout notBlockPage={false} maintenanceMode>
// 			{page}
// 		</MainLayout>
// 	);
// };

export default RequestBlocked;
