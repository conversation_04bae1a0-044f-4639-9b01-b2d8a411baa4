import { IFarmerCaseDetails } from "interfaces/FarmerAidForm.interface";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useRouter } from "next/router";
import { Dispatch, SetStateAction, useState } from "react";
import { useMutation } from "react-query";
import { getFormData, modifyFarmerRequest } from "services/frontend";
import { mapFarmerAidFromCaseForm, mapSocialAidFormToFamilyMembers } from "utils/helpers";

const initialCaseDetails: IFarmerCaseDetails = {
	ReceiveInflationAllowance: false,
	ReceiveSocialAid: false,
	RegisteredWithEWE: false,
	Alternativenumber: "",
	AlternativeEmail: "",
	CaseRef: "",
	IsHouseholdHeadContributeToIncome: false,
	ListIncomeSourceDetails: [],
	IsHouseholdHeadReceivePensionIncome: false,
	ListPensionDetails: [],
	IshouseholdHeadTradeLicense: false,
	ListTradeLicenseDetails: [],
	IshouseholdHeadReceiveRentalIncome: false,
	ListRentalDetails: [],
	ListFamilyMember: [],
};

const useFarmerRequest = (
	caseForm: any,
	setCaseForm: any,
	familyMembers: IFamilyMember[],
	setFamilyMembers: Dispatch<SetStateAction<IFamilyMember[]>>,
	activeIndex: any,
	subIndex: any,
	userDetails: any,
	templateId: string
) => {
	const { push: routerPush, query } = useRouter();

	const [submitLoading, setSubmitLoading] = useState(false);

	const requestId = query.requestId?.toString() || "";

	const { personalInformation, socialAidInformation, incomeInformation } = caseForm;

	const { mutateAsync, isLoading } = useMutation(
		/**
		 * @param clearIncome boolean to check if this request is made to clear the income information,
		 * if the user fills incomer information and goes back to choose that he is receiving social aid or inflation,
		 * the income information will still exist , so this request will be to clear them
		 */
		async ({ submit, clearIncome = false }: { submit: boolean; clearIncome?: boolean }) => {
			if (clearIncome) {
				// clearing income info
				Object.keys(incomeInformation).forEach((key) => {
					delete incomeInformation[key];
				});
			}
			let getDataRequest: any = {
				endpoint: null,
				caseId: requestId,
				beneficiaryId: userDetails?.ContactId || "",
			};
			const body = {
				UpdateType: !requestId ? "CREATE" : submit ? "SUBMIT" : "DRAFT",
				IdCase: requestId || undefined,
				IdProcessTempalte: templateId,
				CaseDetails: {
					...initialCaseDetails,
					CaseRef: personalInformation.caseID || "",
					Alternativenumber: personalInformation.alternativeNumber || "",
					AlternativeEmail: personalInformation.AlternativeEmail || "",
					RegisteredWithEWE:
						socialAidInformation.RegisteredWithEWE === "yes" ||
						socialAidInformation.RegisteredWithEWE === true ||
						false,
					EntityReceivedFrom: socialAidInformation.EntityReceivedFrom || "",
					EWEBill: socialAidInformation.EWEBill || "",
					OwnerEWEBill: socialAidInformation.OwnerEWEBill === "yes",
					ReceiveInflationAllowance:
						socialAidInformation.ReceiveInflationAllowance === "yes" ||
						socialAidInformation.ReceiveInflationAllowance === true ||
						false,
					ReceiveSocialAid:
						socialAidInformation.ReceiveSocialAid === "yes" ||
						socialAidInformation.ReceiveSocialAid === true ||
						false,
					RelatedEmiratesID: socialAidInformation.RelatedEmiratesID || "",
					IsHouseholdHeadContributeToIncome: incomeInformation.householdHeadContributes === "yes",
					ListIncomeSourceDetails:
						incomeInformation.householdHeadContributes === "yes"
							? (incomeInformation.incomes || []).map((i) => ({
									IdIncome: i.IdIncome || undefined,
									IncomeSource: i.IncomeTypes,
									IncomeAmount: i.incomeAmount,
									CompanyName: i.companyName,
							  }))
							: [],

					IsHouseholdHeadReceivePensionIncome: incomeInformation.householdHeadPension === "yes",
					ListPensionDetails:
						incomeInformation.householdHeadPension === "yes"
							? (incomeInformation.pensions || []).map((i) => ({
									IdPension: i.IdPension || undefined,
									PensionType: i.PensionType,
									PensionAuthority: i.PensionAuthority,
									IncomeAmount: i.pensionAmount,
							  }))
							: [],

					IshouseholdHeadTradeLicense: incomeInformation.householdHeadTradeLicense === "yes",
					ListTradeLicenseDetails:
						incomeInformation.householdHeadTradeLicense === "yes"
							? (incomeInformation.tradeLicenses || []).map((i) => ({
									IdTradeLicense: i.IdTradeLicense || undefined,
									IncomeAmount: i.tradeLicenseAmount,
							  }))
							: [],
					IshouseholdHeadReceiveRentalIncome: incomeInformation.householdRentalIncomes === "yes",
					ListRentalDetails:
						incomeInformation.householdRentalIncomes === "yes"
							? (incomeInformation.RentalIncomes || []).map((i) => ({
									IdRental: i.IdRental || undefined,
									ContractNumber: i.ContractNo,
									RentalSource: i.rentalSource,
									ContractStartDate: i.ContractStartDate,
									ContractEndDate: i.ContractEndDate,
									IncomeAmount: i.RentAmount,
							  }))
							: [],
					ListFamilyMember: [
						...(familyMembers || []).map((i) => ({
							...i,
							ListIncomeSourceDetails: i.IsFamilyMemberContributeToIncome
								? i.ListIncomeSourceDetails
								: [],
							ListPensionDetails: i.IsFamilyMemberReceivePensionIncome ? i.ListPensionDetails : [],
							ListTradeLicenseDetails: i.IsFamilyMemberReceiveTradeLicense
								? i.ListTradeLicenseDetails
								: [],
							ListRentalDetails: i.IsFamilyMemberReceiveRentalIncome ? i.ListRentalDetails : [],
						})),
					],
				},
				// if this is a clear request, imitate that the user is in the third step to be able to clear the income info
				Index: clearIncome ? 0 : activeIndex,
				SubIndex: clearIncome ? 2 : subIndex,
			};
			if (activeIndex === 0) {
				if (subIndex === 0) getDataRequest.endpoint = "Request/GetCasePersonalDetails";
				if (subIndex === 1) getDataRequest.endpoint = "Request/GetCaseFamilyHeadListDetails";
				if (subIndex === 2 || subIndex === 3)
					getDataRequest.endpoint = "Request/GetCaseFamilyMembersListDetails";
			}
			if (activeIndex === 2) getDataRequest.endpoint = "Request/GetCaseSummaryDetails";

			const data = await modifyFarmerRequest(body);

			const resp = data.Data;
			if (!resp?.IdCase) return data;
			const query = {
				caseId: getDataRequest.caseId || resp?.IdCase || null,
				endpoint: getDataRequest.endpoint,
			};

			const caseDataRequest = await getFormData(query);
			const caseData = caseDataRequest.Data;
			if (!caseData) return caseData;
			// @ts-ignore
			if (data.Data) data.Data.CaseDetails = caseData;
			if (submit) {
				setSubmitLoading(true);
				return data;
			}
			setCaseForm((state) => mapFarmerAidFromCaseForm(state, data?.Data?.CaseDetails));

			setFamilyMembers(mapSocialAidFormToFamilyMembers(data?.Data?.CaseDetails));

			return data;
		},
		{
			mutationKey: "modifyRequest",
			onSuccess: (resp, obj) => {
				if (resp?.IsSuccess && resp?.Data) {
					if (!requestId)
						routerPush(
							`/smart-services/farmer-service/apply-farmer-service?requestId=${resp?.Data?.IdCase}`,
							undefined,
							{
								shallow: true,
							}
						);
					if (obj.submit) {
						routerPush(
							`/smart-services/farmer-service/apply-farmer-service/review-farmeraid?requestId=${resp?.Data?.IdCase}`
						);
						return;
					}
				}
			},
		}
	);

	return {
		updateRequest: mutateAsync,
		updateRequestLoading: isLoading || submitLoading,
	};
};

export default useFarmerRequest;
