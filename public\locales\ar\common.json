{"3stepsToSignup": "٣ خطوات للتسجيل في برنامج الدعم الاجتماعي", "4stepsToSignup": "٤ خطوات للتسجيل في برنامج الدعم الاجتماعي", "4stepsToSignupFarmer": "٤ خطوات للتسجيل", "5to10mins": "5-2 د<PERSON><PERSON><PERSON><PERSON>", "editReason": "سبب التعديل", "reasonToEdit": "سبب التعديل", "1to2mins": "2-1 دق<PERSON><PERSON>ق", "8to10mins": "10-8 دق<PERSON><PERSON><PERSON>", "aboutMocd": "عن الوزارة", "accessibilityStatement": "بيان إمكانية الوصول", "Additional-info": "المعلومات الإضافية", "additionalEligibilityCriteriaForProgram": "معلومات إضافية عن معايير الأهلية", "additionalEligibilityCriteriaForProgramDesc": "يتضمن دخل الأسرة المكونات التالية:", "additionalEligibilityCriteriaForProgramPoint1": "رات<PERSON> العمل أو الدخل من العمل الخاص (مثل الأعمال التجارية الخاصة، والعمل الحر، وغيرها).", "additionalEligibilityCriteriaForProgramPoint2": "الإيرادات المالية (مثل إيرادات الإيجار والدخل من الأصول العقارية والفوائد وتوزيعات الأرباح والمكاسب المحققة من الأصول المالية، وغيرها).", "additionalEligibilityCriteriaForProgramPoint3": "الدخل الناتج عن الاستحقاقات الاجتماعية الأخرى (مثل المعاش التقاعدي والمساعدات الاجتماعية المحلية).", "afterStudying": "بعد دراسة طلبك رقم:", "ageException": "الإعفاء من شرط السن", "ageExceptionDesc": "في حالات خاصة، قد يتم النظر في إعفاء المتقدم من شرط السن. للتعرف على شروط الحالات الخاصة يمكنكم الاطلاع على قسم الأسئلة الأكثر شيوعاً أو التواصل عبر مركز الإتصال 800623.", "ageOfBenefactor": "سن المستفيد:", "ageOfBenefactorDesc": "بدءًا من 21 عامًا وما فوق (يستثنى من ذلك بعض الحالات الخاصة).", "allStatus": " جميع الحالات", "altLanguage": "English", "AED": "درهم إماراتي", "accreditedUniversities": " (يرجى الرجوع إلى <a target='_blank' href='https://www.caa.ae/Pages/Institutes/All.aspx' download style='text-decoration: underline;'>هذا الرابط</a> للحصول على قائمة الجامعات المعتمدة)", "applayForSocial": "تقديم طلب لبرنامج الدعم الاجتماعي", "application-process": "عملية تقديم الطلب", "application-process-desc": "بمجرد أن يستكمل المتقدم تعبئة وإرسال نموذج الطلب الخاص به، سيقوم الفريق المختص في وزارة تمكين المجتمع بمراجعة الطلب والرد بأسرع وقت ممكن، بعد التأكد من استيفاء المتطلبات والإجراءات، وفقاً لكل حالة.", "applicationSummary": "ملخص الطلب", "applyForFarmer": "التقدم للخدمة", "applyForService": "التقدم للخدمة", "applying-for-farmer-service": "دعم أصحاب المزارع من ذوي الدخل المحدود", "applying-for-social-aid": "تقديم طلب لبرنامج الدعم الاجتماعي", "applying-for-to-whom": "إصدار شهادة لمن يهمه الأمر بشأن الدعم الاجتماعي", "applying-for-to-whom-but": "شهادة لمن يهمه الأمر بشأن الدعم الاجتماعي", "applyingForFarmerAid": "التقدم بطلب للحصول على المساعدة الاجتماعية لمالكي المزارع", "applyingForSocialWelfare": "التقدم بطلب للحصول على الدعم الاجتماعي", "applyingForHousing/EducationAllownce": "التقدم بطلب للحصول على علاوة السكن والتفوق الدراسي للتعليم العالي", "approved": "تمت الموافقة", "archive": "الأرشيف", "assetsOwnedByFamily": "الأصول المملوكة للأسرة", "assetsOwnedByFamilyDesc": "تتضمن الأصول المملوكة للأسرة، على سبيل المثال:", "assetsOwnedByFamilyPoint1": "الودائع المصرفية والممتلكات (مثل الأموال النقدية، العقارات، وغيرها).", "assetsOwnedByFamilyPoint2": "الاستثمارات في العقارات والأسهم والسندات والصكوك والشركات وبراءات الاختراع.", "assetsOwnedByFamilyPoint3": "الدخل من الرخص التجارية.", "back": "العودة إلى الخطوة السابقة", "BacktoHome": "عودة للصفحة الرئيسة", "bank-statement": " صورة عن خلاصة القيد الإماراتية (صادرة خلال الأشهر الثلاثة الماضية)", "cancel": "إلغاء", "YesCustomText": " نعم (مملوك بالكامل) ", "NoCustomText": "لا (مملوك جزئي أو ليس هناك أي عقار مملوك) ", "cantFindAnswer": "تواصل معنا عبر مركز الاتصال", "careers": "الوظائف", "caseNumberUpdated": "رقم الطلب: {{draftId}}", "caseNumber": "رق<PERSON> الطلب", "caseStatus": {"draft": "مسودة", "pending": "في الانتظار", "pendingAllowanceAmountVerification": "في انتظار التحقق من مبلغ العلاوة", "pendingAuditReview": "في انتظار مراجعة الحسابات", "pendingBeneficiaryInformation": "في انتظار معلومات المستفيد", "pendingCommitteeReview": "في انتظار مراجعة اللجنة", "pendingDisbursement": "في انتظار الصرف", "pendingDocumentSubmission": "في انتظار تقديم المستندات", "pendingEligibilityCheck": "في انتظار التحقق من الأهلية", "pendingExceptionCheck": "في انتظار التحقق من الاستثناءات", "pendingFinalReview": "في انتظار المراجعة النهائية", "requestApproved": "تمت الموافقة على الطلب", "requestRejected": "تم رفض الطلب", "submitted": "تم تقديم الطلب", "temporaryQueue": "قائمة المؤقتة", "PendingRefund": "في انتظار استرداد الأموال", "Refunded": "تم استرداد المبلغ", "OverduePayment": "الدفعة المتأخرة", "PaymentCompleted": "الدفعة المكتملة", "PaymentAccumulated": "الدفعة المتراكمة", "PendingConfirmation": "في انتظار التأكيد", "PendingPayment": "في انتظار الدفع", "Stopped": "متوقفة"}, "refund": "تفاصيل  الدفع", "ProceedToPayment": "المتابعة للدفع", "PaidRefundAmount": "مبلغ الاسترداد المدفوع", "TotalRefundAmount": "إجمالي مبلغ الاسترداد", "AccumulatedAmount": "المبلغ المتراكم", "PendingRefundApproved": "مؤهل / معتمد - في انتظار استرداد الأموال", "PendingRefundNotApproved": " غير مؤهل / معتمد - في انتظار استرداد الأموال", "PendingRefund": "في انتظار استرداد الأموال", "RemainingRefundAmount": "المبلغ المسترد المتبقي", "PaymentRatesLabelHeader": "الرجاء تحديد أسعار الدفع المفضلة لديك:", "PaymentRatesLabel": "أسعار الدفعات", "channelsPrograms": "القنوات والبرامج", "children": "- علاوة الأبناء", "clickHere": "اضغط هنا", "commerical-licenses": " التراخيص التجارية التي يتم فيها تسجيل مقدم الطلب كمالكين", "communicateWith": "التواصل مع", "communicateWithLeadership": "التواصل مع القيادة", "complaintReview": "تم تقديم الأستفسار / الاقتراح بنجاح وهي قيد المراجعة", "complaints": "الاستفسارات / الاقتراحات", "complaintSummary": "شكراً لك على تقديم الأستفسار / الاقتراح يمكنك التحقق من حالة هذه الأستفسار / الاقتراح في اي وقت من خلال صفحة الشكاوى \nسنقوم بالتواصل معك لاحقاٍ في حال احتجنا اي معلومات اضافية", "complaintSummaryHead": "ملخص ال{{inquiryType}}", "complaintSummaryTitle": "ملخص ال{{inquiryType}}", "thankYou": "شكرًا", "complaint": "شكوى", "conditions": "المعايير", "ConfirmSelectRefundOption": "هل أنت متأكد أنك تريد متابعة الخيار المحدد", "confirm": "موافق", "contactMocd": "اتصل بنا", "contactUs": "اتصل بنا", "CopyofPassport": " صورة عن جواز السفر (صالحة لمدة ستة أشهر على الأقل)", "CopyoftheFamilyBook": " صورة عن خلاصة القيد الإماراتية (صادرة خلال الأشهر الثلاثة الماضية)", "copyrights": "سياسة النشر", "copyrightText": "© جميع الحقوق محفوظة 2025. وزارة تمكين المجتمع - الامارات العربية المتحدة", "cost": "رسوم الخدمة", "customerHappinessCenters": "مراكز سعادة المتعاملين", "customerHappinessCharter": "ميثاق إسعاد المتعاملين", "delete": "<PERSON><PERSON><PERSON>", "description": "تسعى وزارة تمكين المجتمع لدفع عجلة التنمية الاجتماعية في دولة الإمارات من خلال تحقيق مستهدفات الأجندة الوطنية لرؤية الإمارات 2021، وتعزيز التلاحم المجتمعي والتماسك الأسري، وإيجاد مجتمع مشارك بفعالية في البناء والتطور، وذلك من خلال تبني خطط استراتيجية طويلة الأجل مستمدة من رؤية القيادة الرشيدة للدولة والتوجهات المستقبلية للحكومة، الهادفة لإسعاد المجتمع وترسيخ مكانة الدولة كموطن للسعادة. وتستهدف برامج الوزارة ومبادراتها جميع الفئات المعنية في المجتمع، حيث تركز على تفعيل حقوق الطفل، وتمكين الأسر المنتجة، ودمج أصحاب الهمم في المجتمع، كما تركز على تطوير سياسة الضمان الاجتماعي، وتمكين الفئات الضعيفة من الاندماج في المجتمع وتعزيز استقرار الأسرة الإماراتية وتقوية الروابط بين أفراد المجتمع من خلال الابتكار الدائم بهدف تقديم خدمات وفق أعلى المعايير العالمية في الجودة والكفاءة والشفافية.", "descriptionHeader": "الوصف", "disclaimer": "إخلاء المسؤولية", "documentsRequired": {"documentsRequiredPart1": "بطاقة الهوية الإماراتية (سارية المفعول)", "documentsRequiredPart2": "شهادة الراتب (رب الأسرة /الزوجة)", "documentsRequiredPart3": "سندات العقارات (مثل عقود الإيجار)", "documentsRequiredPart4": "إثبات استمرار التعليم الجامعي للأبناء (في حال الاستحقاق)", "documentsRequiredPart5": "التراخيص التجارية التي يتم فيها تسجيل مقدم الطلب وزوجته كمالك/شريك وأرباح التراخيص التجارية", "documentsRequiredPart6": "مستندات أخرى حسب الحالة"}, "Documentsspecifictoeachcategory": "مستندات خاصة بكل فئة", "download": "تنزيل", "ThisFieldShouldbeNumber": "يجب أن يكون هذا الحقل رقمًا", "DescriptionLimitationMsg": "يجب أن يكون الوصف 700 حرفًا على الأكثر", "draft": "مسودة", "draftUpdateSuccess": "تم حفظ طلبك كمسودة بنجاح. يُمكنك تحديث التفاصيل وتقديمها في أي وقت من 'شاشة ملفاتي'.", "dueDate": "تاريخ الاستحقاق", "edit": "تعديل المعلومات", "eligibility-criteria": "معايير الأهلية", "eligibility-criteria-age": "سن المستفيد", "eligibility-criteria-age-desc": "في حالات خاصة، قد يتم النظر في إعفاء المتقدم من شرط السن حسب كل حالة. وتتضمن هذه الحالات، على سبيل المثال لا الحصر:", "eligibility-criteria-age-item1": "إذا كنت يتيماً", "eligibility-criteria-age-item2": "إذا كنت من الأطفال مجهولي النسب", "eligibility-criteria-age-item3": "إذا كنت من أولاد مسجون بشرط غياب الوالد الآخر (الأم أو الأب)", "eligibility-criteria-age-item4": "إذا كنت من أصحاب الهمم", "eligibility-criteria-age-text": " السن يعتمد على فئة المستفيد", "eligibility-criteria-ageExemption": "الإعفاء من شرط السن", "eligibility-criteria-citizenship": "الجنسية", "eligibility-criteria-citizenship-text": "مواطنو دولة الإمارات العربية المتحدة.", "eligibility-criteria-dec": "يحدد البرنامج أهلية المتقدمين بناءً على معايير رئيسية تشمل دخل الأسرة والأصول التي تملكها والجنسية ومكان ومدة الإقامة والعمر.", "eligibility-criteria-dec2": "للأسر العاملة [نظرة عامة على جدول معايير الأهلية هنا]", "eligibility-criteria-dec3": "للأسر العاطلة عن العمل.", "eligibility-criteria-dec4": "[نظرة عامة على جدول معايير الأهلية هنا]", "eligibility-criteria-dec5": "في بعض الحالات الخاصة، قد يتم إعفاء المتقدمين من معايير معينة.", "eligibility-criteria-dec6": "[نظرة عامة على جدول معايير الأهلية هنا]", "eligibility-criteria-dec7": "في حالات خاصة أخرى، قد يُعفى بعض مقدمي الطلبات المؤهلين من شرط التوظيف، لكنهم يظلون مؤهلين للحصول على علاوات التوظيف.", "eligibility-criteria-dec8": "[نظرة عامة على جدول معايير الأهلية هنا]", "eligibility-criteria-householdAssets": "الأصول المملوكة للأسرة", "eligibility-criteria-householdAssets-desc": "تتضمن الأصول المملوكة للأسرة، على سبيل المثال:", "eligibility-criteria-householdAssets-item1": "الودائع المصرفية والممتلكات (مثل الأموال النقدية، العقارات، وغيرها)", "eligibility-criteria-householdAssets-item2": "الاستثمارات في العقارات والأسهم والسندات والصكوك والشركات وبراءات الاختراع", "eligibility-criteria-householdAssets-item3": "الدخل من الرخص التجارية", "eligibility-criteria-householdAssets-text": "إجمالي الأصول التي تملكها الأسرة يجب أن يكون أقل من الحد الأدنى للأصول", "eligibility-criteria-householdIncome": "دخل الأسرة", "eligibility-criteria-householdIncome-desc": "على سبيل المثال، يتضمن دخل الأسرة التالي:", "eligibility-criteria-householdIncome-item1": "راتب الوظيفة أو الدخل من العمل الحر (مثل المكافآت والعمولات وما إلى ذلك)", "eligibility-criteria-householdIncome-item2": "الإيرادات المالية (مثل الفوائد وأرباح الأسهم وتوزيعات الأرباح والدخل العقاري وما إلى ذلك)", "eligibility-criteria-householdIncome-item3": "الدخل الناتج عن الاستحقاقات الاجتماعية الأخرى (مثل المعاش التقاعدي والمساعدات الاجتماعية المحلية)", "eligibility-criteria-householdIncome-text": "إجمالي دخل الأسرة يجب أن يكون أقل من الحد الأدنى المساعدة المفترضة", "eligibility-criteria-residency": "الإقامة", "eligibility-criteria-residency-text": "الإقامة في الدولة لمواطني دولة الإمارات العربية المتحدة – لن يتم منح أفراد الأسرة الذين لا يقيمون في الدولة علاوة أساسية خاصة بهم", "eligibility-criteria-residencyExemption": "الإعفاء من شرط الإقامة", "eligibility-criteria-residencyExemption-desc": "إذا كان المتقدم بطلب للاستفادة من البرنامج يخضع للعلاج خارج الدولة أو مرافق لمريض، يجب تقديم مستندات إضافية مصدقة ومعتمدة من الجهات الصحية المختصة، مع طلب الإعفاء من شرط الإقامة.", "eligibility-criteria-start": "تعتمد الاشتراطات التي تحتاج إلى استيفائها لتلقي علاوات الدعم الاجتماعي، على وضعك الحالي، وفقاً للمعايير التالية*", "eligibilityCriteria": "معايير الإستحقاق", "eligibilityCriteriaForProgram": "معايير الأهلية للبرنامج", "eligibilityCriteriaForProgramDesc1": "تعتمد كافة الاشتراطات التي تحتاج إلى استيفائها لتلقي علاوات الدعم الاجتماعي على وضعك الحالي، وفقاً للمعايير الخمسة التالية:", "eligibilityCriteriaForProgramDesc2": " دخل الأسرة: مجموع الدخل الصافي الذي يجنيه رب الأسرة والزوجة، بما في ذلك على سبيل المثال: الراتب الذي يتلقاه رب الأسرة، المداخيل من الإيجارات، والربح من التراخيص التجارية، يجب أن يكون أقل من الحد الأدنى للمساعدة المفترضة والتي تشمل العلاوة المطبقة على المستفيد وأفراد الأسرة.", "eligibilityCriteriaForUnder45Program": "معايير الاستحقاق للمتعطل الباحث عن عمل (من عمر 25 إلى 44 عاماً)", "eligPoints": {"eligibilityUnder45Point1": "أن يكون مسجلاً في برنامج نافس لمدة لا تقل عن 3 أشهر", "eligibilityUnder45Point2": "ألا يزيد دخل المتقدم (رب الأسرة/الزوجة) على قيمة المساعدة", "eligibilityUnder45Point3": "ألا يكون (رب الأسرة/الزوجة) مستفيداً من أي برنامج مساعدات  في الدولة", "eligibilityUnder45Point4": "أن يكون قد أنهى الخدمة الوطنية أو معفى منها", "eligibilityUnder45Point5": "أن يكون متمتعاً بحسن السيرة والسلوك", "eligibilityUnder45Point6": "ألا يكون طالباً أو مسجلاً في أي مؤسسة تعليمية نظامية"}, "email": "عنوان البريد الالكترونى", "EmiratesID": "الهوية الاماراتية", "emiratesID": "الهوية الإماراتية", "employeeMail": "البريد الالكتروني للموظفين", "estimated-process-time": "مدة انجاز الخدمة", "experience-service": "كيف كانت تجربتك في استخدام الخدمة؟", "familyIncome": "دخل الأسرة:", "familyIncomeDesc": "مجموع الدخل الصافي الذي يجنيه رب الأسرة والزوجة:", "familyIncomeNoColon": "دخل الأسرة:", "familyIncomePoint1": "الراتب الشهري أو الدخل من العمل الخاص (مثل الأعمال التجارية الخاصة،العمل الحر،الإيرادات المالية مثل عقود الإيجار)", "familyIncomePoint2": "الدخل الناتج عن الاستحقاقات الاجتماعية الأخرى (مثل المعاش التقاعدي والمساعدات الاجتماعية الأخرى في الدولة)", "familyOwnedAssets": "الأصول المملوكة للأسرة:", "familyOwnedAssetsDesc": "القيمة الإجمالية للأصول والممتلكات القيمة التي يمتلكها أفراد الأسرة (كلياً أو جزئياً) يجب أن تكون أقل من الحد الأدنى لقيمة الأصول المحددة للاستفادة من البرنامج.", "Faq": "الأسئلة الشائعة", "faq-pdf": "الأسئلة الشائعة المتوفر بصيغة PDF", "FaqGuide": "هذا الدليل سوف يساعدك على معرفة المزيد عنا وكيف نعمل", "farmerAidBreadTitle": "دعم أصحاب المزارع من ذوي الدخل المحدود", "farmerDocumentsRequired": {"1": "بطاقة الهوية الإماراتية (سارية المفعول).", "2": "شهادة الراتب (رب الأسرة /الزوجة).", "3": "سندات العقارات (مثل عقود الإيجار).", "4": "التراخيص التجارية التي يتم فيها تسجيل مقدم الطلب وزوجته كمالك/شريك وأرباح التراخيص التجارية.", "5": "مستندات أخرى معنية بدخل الأسرة."}, "farmerForthStep": "4. في حال الموافقة على الطلب المقدم سيتم اعلامك من خلال رسالة نصية / بريد إلكتروني وتفعيل الدعم وفق المستحقات", "farmerSecondStep": "2. تعبئة طلب الحصول على دعم بدل الكهرباء للمزارع", "farmerServiceChannel": "يتم فتح باب التسجيل ضمن الموقع الإلكتروني لوزارة تمكين المجتمع للتقديم على دعم أصحاب المزارع.", "farmerServiceDescription": "تعد هذه الخدمة دعماً جديداً لمالكي المزارع من ذوي الدخل المحدود بقيمة 8,400 درهم سنوياً لكل مستفيد، أي ما يعادل 2,500 كيلووات/ساعة شهريّاً، أو قيمة الاستهلاك أيهما أقل، من خلال خصم مباشر من فاتورة الاستهلاك الشهري للمزرعة من الكهرباء في شركة الاتحاد للماء والكهرباء.", "farmerServiceEntity": "وزارة تمكين المجتمع بالتعاون مع شركة الاتحاد للماء والكهرباء.", "farmerServiceTerms": {"0": "أن يكون مالك المزرعة من مواطني دولة الإمارات.", "1": "يطبق الدعم على المزارع المسجلة لدى \"شركة الاتحاد للماء والكهرباء\" فقط.", "2": "أن يكون مالك المزرعة من إحدى الفئات التالية:", "3": "مستفيدي الدعم الاجتماعي الاتحادي أو المحلي.", "4": "ذوي الدخل المحدود (إجمالي دخل الزوجين أقل من 25 ألف).", "5": "سيتم تطبيق دعم الاستهلاك على فاتورة الكهرباء فقط ( واستبعاد رسوم ايجار العداد والرسوم الأخرى ).", "6": "سيتم احتساب الاستهلاك الشهري الفعلي للمستفيدين وسيتم سيتم تخصيص دعم بقيمة 8,400 درهم سنوياً لكل مستفيد ما يعادل 2500 كيلووات/ساعة كحد أقصى شهرياً.", "7": "يتم منح الدعم على أساس شهري ولا يجوز ترحيله من شهر لآخر ولا يمنح بأثر رجعي.", "8": "تقوم وزارة تمكين المجتمع بتحويل المبالغ المترتبة بشكل شهري لحساب شركة الاتحاد للماء والكهرباء، ليتم استقطاع المبلغ من الفاتورة الشهرية.", "9": "يمكن للمستفيد الاطلاع على مبلغ الدعم من خلال فاتورة الاستهلاك الشهري للمزرعة أو عن طريق قنوات الخدمة لشركة الاتحاد للماء والكهرباء.", "10": "سيتم منح المالك مساعدة لمزرعة واحدة فقط ولرقم حساب خاص بعداد واحد فقط في حال كان لدى المزرعة أكثر من حساب.", "11": "يج<PERSON> التأكد ألا يتم استخدام المزرعة لأغراض تجارية.", "12": "إذا كان المالك يتلقى دعماً آخر للكهرباء من الوزارة أو من الجهات المحلية سيتم صرف قيمة الدعم الأعلى.", "13": "ينبغي على مالك المزرعة تسجيل بيانات الهوية الإماراتية لدى شركة الاتحاد للماء والكهرباء، ومن ثم التقديم على الدعم", "14": "سيتم التنسيق بين وزارة تمكين المجتمع وشركة الاتحاد للماء والكهرباء واعتماد قوائم المستحقين بشكل شهري ابتداءً من تاريخ 07 من كل شهر.", "15": "سيتم إيقاف الدعم في حالة إغلاق حساب المزرعة لدى شركة الاتحاد للماء والكهرباء، ولا يتم تفعيل الدعم لأي حساب جديد لنفس المتعامل إلا عن طريق وزارة تمكين المجتمع."}, "farmerServiceTime": "سيتم تطبيق الدعم ابتداءً من شهر يوليو 2023.", "free": "مجانية", "free-service": "خدمة مجانية", "frequentlyAskedQuestions": "الأسئلة الأكثر شيوعاً", "genericErrorDescription": "الرجاء إعادة المحاولة في وقت لاحق", "genericErrorTitle": "<PERSON><PERSON><PERSON>", "goHome": "عودة إلى الصفحة الرئيسة", "googleMaps": "<PERSON><PERSON><PERSON> جوجل", "greeting": "مدحبا", "guidelines": "القواعد الارشادية", "HaveQuestion": "لديك سؤال", "helpful-text": "هل وجدت هذا المحتوى مفيداً؟", "hours": "ساعة", "IncomeSourcesDocument": " سندات ملكية العقارات المملوكة (الأراضي والممتلكات التجارية و/أو العقارات السكنية)", "individual": "- علاوة رب الأسرة", "inProgress": "قيد التنفيذ", "instantly": "فوري", "Inflation": "علاوة بدل التضخم", "applyForHousingEducationTopup": "التقدم للعلاوتين: السكن والتفوق الدراسي للتعليم العالي", "applyForHousingAllowance": "التقدم لعلاوة السكن", "applyForAcademicExcellenceAllowance": "التقدم لعلاوة التفوق الدراسي للتعليم العالي", "actions": "إجراءات", "keywords": "وزارة، اجتماعية، تنمية، تنمية المجتمع، شؤون، تنمية، مالية، حكومة، اتحادية، الإمارات العربية المتحدة، رعاية اجتماعية، الرعاية، الأمن، العامة، عامة، الشعب، الناس، خدمة، خدمات، دعم، مشاركة، اتصال، اشراف، قانون، تنظيم، سياسة، احداث، عائق، معاق، تعليم، نفسية، إعادة تأهيل، جيل، اجيال، المرافق العامة، مرافق عامة، جمعيات، تعاونية، تعاونيات، التنمية الأسرية، تنمية اسرية، الأسر المنتجة، اسر منتجة، حضانة، شي في خاطرك، تعليق، أصحاب الهمم، بطاقة، منح الزواج، منحة الزواج، ضمان اجتماعي، الضمان الاجتماعي، مؤسسات أهلية، نفع عام، صناديق التكافل، كبار المواطنين، استشارة، حماية الطفل، استشارات أسرية،استشارات مالية، برنامج إعداد، أعراس جماعية، عرس جماعي،العرس، الجماعي، تسويق منتج، لغة الاشارة، احتياجات خاصة، توظيف، احتضان طفل، تبني طفل، ابلاغ اساءة، توظيف أصحاب همم، دمج أصحاب الهمم", "landingApply": "للتقديم على الخدمة اضغط هنا", "landingForMoreInfoInflation": "لمزيد من التفاصيل حول علاوات بدل التضخم اضغط هنا", "landingForMoreInformation": "ل<PERSON>زيد من التفاصيل حول البرنامج اضغط هنا", "landingHeader": "مبادرات رئيس الدولة", "landingPageDec": "برنامج شامل ومرن ومتكامل، يقدم الدعم المادي لتعزيز جودة حياة الأفراد والأسر الإماراتية، أطلقته وزارة تمكين المجتمع تنفيذاً لرؤية القيادة الرشيدة وترجمة لتوجّهات حكومة دولة الإمارات، بما يواكب المتطلبات والمستجدات المعيشية، بهدف ضمان جودة حياة أفضل على نطاق الأسرة والمجتمع.", "landingPageDec2": "دعم حكومي مرن ومتغير، يُصرف بشكل شهري للأسر المواطنة، وذلك في ظل ارتفاع الأسعار والتضخم الحاصل على المستوى المحلي والعالمي، ووفقاً للمستجدات الاقتصادية والاجتماعية، بما يمكّن المستفيدين لتلبية الاحتياجات المعيشية والتي تشمل علاوات بدل الوقود، وبدل المواد الغذائية، وبدل الكهرباء والماء.", "landingPageDec3": "وبدأت وزارة تمكين المجتمع، بتطبيق علاوة بدل الوقود في شهر يوليو، تلتها علاوة بدل المواد الغذائية في أغسطس، ثم علاوة بدل الكهرباء والمياه بدءاً من فاتورة استهلاك شهر سبتمبر 2022.", "landingPageLinkText": "ويتم التقديم لعلاوات بدل التضخم عبر الموقع الإلكتروني لوزارة تمكين المجتمع:", "landingPageTitle": "برنامج الدعم الاجتماعي", "landingPageTitle2": "علاوات بدل التضخم", "leadership": "القيادة", "logIn": "تسجيل الدخول", "logOut": "تسجيل الخروج", "makeSureRequiredDocumentsReady": "التأكد من توفر جميع الوثائق المطلوبة قبل البدء بعملية تقديم الطلب", "mins": "دقائق", "missingDocuments": "،تبين أن المستندات المطلوبة غير كاملة، يرجى الضغط على رقم الطلب للمتابعة.", "mobileApp": "تطبيق الوزارة", "mobileNumber": "رقم الهات<PERSON> المحمول", "mocdSiteUrl": "تنمية-المجتمع.امارات", "monthlyIncome": "الدخل الشهري", "monthlyIncomeDesc": "مجموع الرواتب الشهرية والدخل من الرخص التجارية وإيرادات العقارات وأي إيرادات أخرى لكل من الزوج وزوجته.", "more-faq": "ل<PERSON>زيد من المعلومات حول البرنامج، يرجى تحميل ملف ", "moreInfoRequired": "مطلوب معلومات إضافية", "mustBeNumber": "يجب أن تكون القيمة المدخلة رقماً.", "myAllowance": "العلاوة الخاصة بي", "myCases": "ملفاتي", "myProfile": "ملفي", "name": "الاسم", "nationaility": "الجنسية:", "placeholder": "", "nationailityDesc": "مواطنو دولة الإمارات العربية المتحدة المقيمون في الدولة.", "nationailityDesc2": "وفي حال إذا كان المتقدم بطلب لبرنامج الدعم الاجتماعي يخضع للعلاج خارج الدولة أو مرافق لمريض، يجب تقديم مستندات إضافية مصدقة ومعتمدة من الجهات الصحية المختصة.", "navbar-about": "<PERSON>و<PERSON> البرنامج", "navbar-complaints": "الاستفسارات / الاقتراحات", "navbar-faq": "الأسئلة الشائعة", "navbar-home": "الصفحة الرئيسية", "navbar-applyToReund": "الاسترداد", "PayFullAmount": "د<PERSON>ع كامل المبلغ", "navbar-howToApply": "خدمات الدعم الاجتماعي", "navbar-myAllowance": "العلاوة الخاصة بي", "navbar-myCases": "ملفاتي", "newComplaint": "شكوى جديدة", "newInquirie": "جديد", "newRequest": "<PERSON><PERSON><PERSON> جديد", "newsAndUpdatesTitle": "البرامج التعليمية والمبادئ التوجيهية", "nextStep": "تقديم", "no": "لا", "emiratesIdValidationTitle": "حد<PERSON> خطأ ما", "incorrectNationality": "يجب أن لا يكون الزوج من الجنسية الإماراتية.", "emiratesIdValidation": "بطاقة الهوية الإماراتية غير صالحة", "emiratesIdValidationFailedDob": "تاريخ الميلاد غير صحيح", "emiratesIdValidationFailedGender": "الهوية الإماراتية غير صالحة", "noChangesTitle": "لم يتم اكتشاف أي تغييرات", "noChangesDescription": "لم تقم بإجراء أي تعديلات. يرجى إجراء التعديلات قبل المتابعة.", "noNotifications": "لا إشعارات", "notifications": "الإشعارات", "or": "أو", "ourNewsLetter": "بالنشرة الاخبارية", "PaymentFailed": "فشل الدفع", "payment-error-title": "حد<PERSON> خطأ أثناء معالجة دفعتك", "payment-error-subTitle": "ستتم إعادة توجيهك إلى الصفحة الرئيسية قريبًا", "pendingItems": "بن<PERSON><PERSON> قيد الانتظار", "pleaseWaitDownloadingFile": "يرجى الانتظار حتى نقوم بتنزيل ملفك.", "pleaseWaitLoadDraft": "الرجاء الإنتظار حتى يتم تحميل التطبيق لك", "PointofServiceDelivery": "قنوات تقديم الطلب", "PointofServiceDeliveryBody": "- البوابة الإلكترونية لبرنامج الدعم الاجتماعي بوزارة تمكين المجتمع", "privacyPolicy": "سياسة الخصوصية", "problemSolved": "تم حل المشكلة", "proceed": "متابعة", "InvalidNumberOfChildren": "يجب أن تكون القيمة أكبر من صفر أو اكبر من أو تساوي عدد المعايير المحددة", "PleaseEnterNumbergraterThanZero": "الرجاء إدخال رقم أكبر من الصفر", "PleaseEntera1or2-digit": "الرجاء إدخال رقم مكون من خانة  او خانتين", "proceedMocdText": "للاطلاع على المزيد من خدمات الوزارة ", "process": "الإجراءات", "SelectedOption": "الخيار المحدد", "program-details": "تفاصيل البرنامج", "reachUs": "رجاءًا تواصل مع الفريق", "readMore": "قراءة المزيد", "rejected": "مرفو<PERSON>", "remoteAccess": "العمل عن بعد", "request": "ط<PERSON><PERSON>", "request-bloced-desc": "في الوقت الحالي ، لا يمكن الوصول إلى المحتوى إلا من خلال دولة الإمارات العربية المتحدة. ومع ذلك ، فإننا نعمل بجد لتزويدك بخدمات وزارة تمكين المجتمع خارج دولة الإمارات العربية المتحدة قريبًا", "request-bloced-title": "آسف ولكن ليس لديك حق الوصول إلى هذا الموقع!", "requestReview": "تم تقديم طلبك بنجاح، وستتم مراجعته من قبل الإدارة المختصة.", "requestReview2": "تم تقديم طلبك بنجاح.", "required-documents": "الوثائق المطلوبة", "required-documents-des": "تأكد من توفر جميع الوثائق المطلوبة قبل البدء بعملية تقديم الطلب!", "resdiencyException": "الإعفاء من شرط الإقامة", "resdiencyExceptionDesc": "إذا كان المتقدم بطلب لبرنامج الدعم الاجتماعي يخضع للعلاج خارج الدولة أو مرافق لمريض، يجب تقديم مستندات إضافية مصدقة ومعتمدة من الجهات الصحية المختصة، مع طلب الإعفاء من شرط الإقامة.", "residency": "الإقامة:", "residencyDesc": "المقيمين في دولة الامارات العربية المتحدة", "save": "<PERSON><PERSON><PERSON>", "saveAsDraft": "ح<PERSON>ظ كمسودة", "search": "ب<PERSON><PERSON>", "SearchbyKeyword": "بحث بواسطة الكلمة", "Sendusanemail": "800623", "service-description": "وصف الخدمة", "service-nformation": "معلومات عن الخدمة", "serviceBeneficiaries": "يقدم برنامج الدعم الاجتماعي، الدعم الشهري المناسب للأفراد والأسر من مواطني دولة الإمارات، بما يساعدهم على تحقيق متطلبات واحتياجات أسرهم، وتعزيز جودة حياة أفضل. تعرفوا على كيفية التسجيل في البرنامج  من خلال  الفيديو التوضيحي", "ServiceChannel": "قناة تقديم الخدمة", "ServiceDescription": "وصف البرنامج", "ServiceDescriptionBody": "يقدم البرنامج الدعم الاجتماعي اللازم للأفراد والأسر الإماراتية، بهدف ضمان جودة حياة أفضل. ويستفيد من البرنامج كل متقدم يستوفي معايير الأهلية. وسيحصل كل مستفيد على علاوة أساسية تتألف من 5 مكونات:", "ServiceEntity": "جهة تقديم الخدمة", "serviceInformation": "معلومات الخدمة", "serviceTime": " متى سيتم تطبيق الدعم؟", "signupSteps": {"step1desc": "تعبئة البيانات وإرفاق المستندات المطلوبة وإرسال طلب لبرنامج الدعم الاجتماعي بوزارة تمكين المجتمع", "step1title": " إنشاء حساب / تسجيل دخول  في الموقع", "step2desc": "بمجرد استكمال الطلب وإرساله، سيقوم الفريق المختص بوزارة تمكين المجتمع بالمراجعة والرد", "step2title": " تعبئة طلب الحصول على الدعم الاجتماعي وإرفاق المستندات المطلوبة", "step3desc": "في حال الموافقة على الطلب سيتم تفعيل العلاوات للمستفيد من البرنامج حسب فئة العلاوة المستحقة", "step3title": " مراجعة الطلب من قبل الفريق المختص للتأكد من استيفاء متطلبات وشروط الاستحقاق", "step4title": " في حال الموافقة على الطلب المقدم سيتم اعلامك من خلال رسالة نصية / بريد إلكتروني وتفعيل العلاوة وفق المستحقات"}, "sitemap": "خريطة الموقع", "spouse": "- علاوة الزوجة", "startsInJuly": "تبدأ الخدمة في يوليو", "status": "الحالة", "StillHaveQuestions": "ل<PERSON><PERSON>يد من الاستفسار", "submit": "تقديم", "submitted": "مُقَدَّم", "subscribeTo": "الاشتراك", "support": "الدعم", "termsAndConditions": "الأحكام والشروط", "termsConditions": "الشروط والأحكام", "thanksForApplaying": "شكراً لك على التقدم لبرنامج الدعم الاجتماعي. يمكنك التحقق من حالة طلبك في أي وقت، عبر صفحة \"ملفاتي\". سنقوم بالتواصل معك قريباً في حال احتجنا إلى أية معلومات إضافية.", "thanksForApplaying2": " ستتم مراجعة الطلب والرد برسالة عبر البريد الإلكتروني المسجل لدينا.", "thanksForEditingBody": "شكرا لك على تعديل الطلب للحصول خدمة المساعدة الاجتماعية. تم تقديم طلبك للخدمة وهو قيد المراجعة. ستتواصل وزارة تمكين المجتمع قريباً.", "thanksForEditingTitle": "تم تعديل طلبك بنجاح وهو قيد المراجعة", "the-target-audience": "الجمهور المستهدف", "the-target-audience-desc": "جميع المواطنين والمقيمين على أرض الدولة .", "ThisPageCouldNotBeFound": "هذه الصفحة غير موجودة", "ThisPageCouldNotBeFoundDescription": "ربما تمت إزالة الصفحة التي تبحث عنها أو تم تغيير اسمها أو أنها غير متاحة مؤقتًا.", "timeRequiredToComplete": "مدة تقديم الطلب", "title": "وزارة تمكين المجتمع", "tollFreeNumber": "الرقم المجاني 800623", "uaePromiseGuidelines": "نموذج وعد حكومة دولة الامارات", "under-maintenance-desc": "الصفحة التي تحاولون الوصول إليها حاليًا تحت الصيانة وستكون متاحة مرة أخرى في غضون الساعة القادمة", "under-maintenance-desc2": "يمكنكم زيارة موقع وزارة تمكين المجتمع mocd.gov.ae للمعلومات الإضافية ", "under-maintenance-title": "الموقع تحت الصيانة - سنعود اليكم قريبا", "unemployedJob": "- علاوة المتعطلين الباحثين عن عمل ممن فقدوا وظائفهم في القطاع الحكومي وشبه الحكومي", "unemployedOver": "- علاوة من لا عمل لهم فوق سن 45 عاماً", "upto-days": "21 يوماً", "userExperience": "تجربة المستخدمين", "valid-emirates-id": "بطاقة هوية إماراتية (سارية المفعول)", "video-title": "كيفية التقديم لطلب الدعم الاجتماعي", "viewAll": "مشاهدة الكل", "viewLocationIn": "موقعنا", "whistleblower": "الإبلاغ عن المخالفات- مكتب التدقيق الداخلي", "whom-service-desc": "يتم اصدار رسالة لمن يهمه الأمر للأشخاص والجهات وذلك لمعرفة هل الاشخاص المستعلم عنهم يتقاضون المساعدة الاجتماعية من الوزارة أم لا. يتوجب عليك انشاء ملف شخصي للاستفادة من الخدمة.", "yes": "نعم", "you-can-apply": "بإمكانكم التقديم لهذه الخدمة بسرعة!", "textResizer": "تغيير حجم النص", "textResizerDesc": "استخدم شريط التمرير أدناه لزيادة حجم الخط أو تصغيره", "ourPartners": "شركاؤنا", "processSteps": "اجراءات الطلب", "aboutWebsite": "عن الموقع", "menu": "القائمة", "siteMap": "خريط الموقع", "next": "التالي", "previous": "السابق", "allowanceCalculator": "حاسبة الدعم", "pages": "الصفحات", "more": "المزيد", "Beneficiaries": "المستفيدين", "appeal": "تظلم", "reOpen": "إعادة فتح", "Reopened": "إعادة فتح", "InformationProvided": "تم تزويد المعلومات", "appealConfirmation": "هل أنت متأكد من تقديم تظلم ؟ ", "reOpenConfirmation": "هل أنت متأكد من إعادة الفتح ؟ ", "reOpenReson": "الرجاء ادخال سبب لإعادة فتح الحالة ", "sentForAppeal": "تظلم", "cantCreateComplaint": "عُذراً، لا يمكن تقديم الطلب حيث يوجد حاليًا استفسار/شكوى نشطة أخرى بنفس الموضوع بالرقم المرجعي {{caseNum}}", "guardianOtpHeader": "التحقق من رقم الوصي", "otpVerificationSubTitle": " رمز التحقق ارسل الى رقم هاتف الوصي {{otpNumber}}", "complaintSubText": "قدم شكوى بخصوص خدمة حديثة تم استخدامها.", "inquiry": "استفسار", "inquirySubText": "طلب المعلومات المتعلقة بالخدمة التي تهمك.", "suggestion": "اقتراح", "suggestionSubText": "قدم اقتراح حول كيف يمكننا تحسين تجربتك", "thankYouTitle": "شكرًا", "thankYouSubText": "التعبير عن التقدير تجاه الموظف أو الخدمة المستخدمة", "complaintAuthText": "هل تريد الاستمرار كضيف؟", "continue": "متابعة", "lastUpdate": "أخر تحديث للموقع في:", "websiteInfo": "يجب أن تكون دقة الشاشة 1920x1080 لأفضل تصفح للموقع. يدعم الموقع متصفحات ، مايكروسوفت ايدج، فاير فوكس 10.0+، سفاري 3+، جوجل كروم 12.0", "closed": "مغلقه", "myInquiriesSuggestions": "استفساراتي / اقتراحاتي", "expEidDate": "لا يمكنك التقديم باستخدام بطاقة الهوية الإماراتية منتهية الصلاحية", "notEmarati": "يمكن للمواطنين الإماراتيين فقط التقدم لهذا الطلب", "farmerServiceRemoveTitle": "للتقديم بطلب الحصول على ‘دعم أصحاب المزارع‘، يرجى التواصل مع مزود الخدمة شركة ‘الاتحاد للماء والكهرباء‘ من خلال الضغط على ", "DearCustomer": "متعاملينا الأعزاء،", "linkText": "الرابط", "utlityAllowanceError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه. الرجاء إدخال رقم حساب خدمات آخر، أو المتابعة إلى القسم التالي، في حال عدم صحة البيانات الرجاء التواصل مع مزود الخدمة الخاص بحسابك لتعديل البيانات", "notEligibleForInflation": "غير مؤهل للحصول على علاوة بدل التضخم", "InactiveError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه غير فعال. الرجاء إدخال رقم حساب خدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه، في حال عدم صحة البيانات الرجاء التواصل مع مزود الخدمة الخاص بحسابك لتعديل البيانات", "Non-ResidentialError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه غير سكني. الرجاء إدخال رقم حساب خدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه، في حال عدم صحة البيانات الرجاء التواصل مع مزود الخدمة الخاص بحسابك لتعديل البيانات", "Non-EmiratiError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لكونه لغير مواطن. الرجاء إدخال رقم حساب خدمات آخر، أو المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه، في حال عدم صحة البيانات الرجاء التواصل مع مزود الخدمة الخاص بحسابك لتعديل البيانات", "ReceivingUtilityAidError": "حساب الخدمات هذا غير مؤهل للحصول على علاوة بدل الكهرباء والمياه لحصوله على دعم آخر. الرجاء المتابعة إلى القسم التالي دون تقديم طلب للحصول على علاوة بدل الكهرباء والمياه، في حال عدم صحة البيانات الرجاء التواصل مع مزود الخدمة الخاص بحسابك لتعديل البيانات", "inflationEdit": "'يرجى تعديل جميع المعلومات المتعلقة بالتضخم في طلب علاوة بدل التضخم التي تظهر ضمن قسم 'ملفاتي", "topups": "علاوة التفوق الدراسي للتعليم العالي", "info": "ملحوظة", "RecevingUtilityInfo": "حساب الخدمات المرفق يستلم دعم مسبقا", "thisField": "هذا الحقل مطلوب", "downloadTemplate": "تحميل النموذج"}