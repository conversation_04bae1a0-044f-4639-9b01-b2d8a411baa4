import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";
import { getEmiratesIdFromToken } from "utils/helpers";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<string>>
) {
	const emiratesId = await getEmiratesIdFromToken(req);

	const data = await BackendServices.retrieveContact(emiratesId);
	if (data?.IsSuccess) {
		if (data?.Data?.Image) return res.status(200).json({ ...data, Data: data?.Data?.Image });
		return res.status(200).json({ ...data, Data: "" });
	} else {
		res.status(500).json(errorResponse);
	}
}
