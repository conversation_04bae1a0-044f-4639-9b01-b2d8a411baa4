import {
	Grid,
	Grid<PERSON><PERSON>,
	<PERSON>dal,
	ModalBody,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	Button,
	ModalFooter,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { CloseIcon } from "components/Icons";
import { useFormContext } from "context/FormContext";
import { Form, Formik, FormikProps } from "formik";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useRef } from "react";
import { useMemo, useState } from "react";
import { getLookupLabel, getLookupItem } from "utils/helpers";
import * as functions from "./functions";

interface Props {
	onClose: any;
	member: IFamilyMember | null;
	onEditMember: (member: IFamilyMember) => void;
	readOnly?: boolean;
}

function EditFamilyMemberForm({ onClose, member, onEditMember, readOnly }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();
	const [isModalShow, setIsModalShow] = useState({
		incomes: false,
		pensions: false,
		tradeLicenses: false,
		RentalIncomes: false,
	});
	const [incomeID, setIncomeID] = useState("");
	const [pensionID, setPensionID] = useState("");
	const [tradeLicenseID, setTradeLicenseID] = useState("");
	const [rentalIncomeID, setRentalIncomeID] = useState("");
	const formikRef = useRef<FormikProps<any>>(null);

	const handleDeleteIncomeModal = (id, type) => {
		if (type === "incomes") {
			setIncomeID(id);
		} else if (type === "pensions") {
			setPensionID(id);
		} else if (type === "tradeLicenses") {
			setTradeLicenseID(id);
		} else if (type === "RentalIncomes") {
			setRentalIncomeID(id);
		}
		handleShowHideModal(true, type);
	};
	const handleDeleteIncome = (remove, type) => {
		if (type === "incomes") {
			remove(incomeID);
		} else if (type === "pensions") {
			remove(pensionID);
		} else if (type === "tradeLicenses") {
			remove(tradeLicenseID);
		} else if (type === "RentalIncomes") {
			remove(rentalIncomeID);
		}
		handleShowHideModal(false, type);
	};

	const handleShowHideModal = (hideOrShow, type) => {
		setIsModalShow((prev) => ({ ...prev, [type]: hideOrShow }));
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};

	const initialValues = useMemo(
		() => ({
			Occupations: getLookupItem(lookups, "Occupations", member?.Occupations),
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[member?.Id, locale]
	);

	const onSubmit = (it) => {
		member &&
			onEditMember({
				...member,
				Occupations: it.Occupations.value,
				IsInformationUpdated: true,
			});
	};

	return (
		<>
			<Modal
				isOpen={!!member}
				onClose={onClose}
				size={{ base: "full", md: "4xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{locale === "ar" ? member?.FullnameAR : member?.FullnameEN} -{" "}
									{getLookupLabel(lookups, "FamilyRelationship", member?.Relationship)}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
					>
						{(formik) => (
							<>
								<ModalBody>
									<Form
										onSubmit={(e) => {
											e.preventDefault();
											formik.handleSubmit(e);
										}}
										onChange={(e) => {
											e.preventDefault();
											functions.onChange(e, formik);
										}}
									>
										<Grid
											rowGap={{ base: 6, md: 6 }}
											columnGap={6}
											templateColumns="repeat(2, 1fr)"
											templateRows="auto"
										>
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="selectableTags"
													value={formik.values["Occupations"]}
													isRequired={true}
													name="Occupations"
													label={t("Occupations")}
													isDisabled={readOnly}
													placeholder={t("placeholder", { ns: "common" })}
													options={lookups.Occupations}
													touched={formik.touched["Occupations"]}
													error={formik.errors["Occupations"]}
													onChange={(firstArg) => {
														handleChangeEvent("selectableTags", firstArg, "Occupations", formik);
													}}
												/>
											</GridItem>
										</Grid>
									</Form>
								</ModalBody>
								<ModalFooter borderTop="1px solid #BBBCBD">
									<HStack w={"100%"} gap={2} my={4}>
										<Button variant="secondary" w={"100%"} onClick={onClose}>
											{t("common:cancel")}
										</Button>
										<Button
											variant="primary"
											w={"100%"}
											isDisabled={!formik.isValid}
											onClick={formik.submitForm}
										>
											{t("common:save")}
										</Button>
									</HStack>
								</ModalFooter>
							</>
						)}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditFamilyMemberForm;
