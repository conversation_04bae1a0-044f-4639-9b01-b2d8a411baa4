import {
	Modal,
	ModalOverlay,
	ModalContent,
	Modal<PERSON>eader,
	Modal<PERSON>ooter,
	ModalBody,
	Button,
	HStack,
	useDisclosure,
	Text,
	VStack,
	Image,
	Img,
} from "@chakra-ui/react";
import React, { useEffect } from "react";

const DeleteModal = ({
	isModalShow,
	handleOnClose,
	handleOnClick,
	imgSrc,
	headerTitle,
	confirmationMessage,
	undoneAction,
}) => {
	const { isOpen, onOpen, onClose } = useDisclosure();

	const handleOnOpen = () => {
		onOpen();
	};
	useEffect(() => {
		isModalShow ? onOpen() : onClose();
	}, [isModalShow]);
	return (
		<>
			<Modal
				isCentered // onClose={handleOnClose || onClose} // isOpen={onOpen} // motionPreset="slideInBottom"
				closeOnOverlayClick={false}
				onClose={handleOnClose || onClose}
				isOpen={isOpen}
			>
				<ModalOverlay />
				<ModalContent borderRadius="12px">
					<ModalHeader>
						<VStack>
							<Image src={imgSrc} alt="image not found" />
							<Text>{headerTitle}</Text>
						</VStack>
					</ModalHeader>
					<Img
						pos="absolute"
						right="21px"
						top="19px"
						src="../assets/images/closeBtn.png"
						w="18px"
						h="18px"
						cursor="pointer"
						onClick={handleOnClose || onClose}
					/>
					<ModalBody>
						<Text textAlign="center">{confirmationMessage}</Text>
						{undoneAction && <Text textAlign="center"> {undoneAction}</Text>}
					</ModalBody>
					<ModalFooter>
						<HStack w="full">
							<Button
								border="1px solid #B08D44"
								onClick={handleOnClose || onClose}
								w="50%"
								// bg="white"
								padding="12px 33px"
								variant="primary"
								borderRadius="1px"
								color="#B08D44"
							>
								<Text as="span">Cancel</Text>
							</Button>
							<Button
								variant="secondary"
								mr={3}
								onClick={handleOnClick || onOpen}
								w="50%"
								bg="#C42828"
								color="white"
								borderRadius="1px"
								padding="16px 24px"
								boxShadow="0px 0px 1px rgba(100, 116, 139, 0.06),0px 1px 2px rgba(100, 116, 139, 0.1)"
								fontSize="16px"
								fontWeight="500"
								textAlign="center"
							>
								<Text as="span">Delete</Text>
							</Button>
						</HStack>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</>
	);
};

export default DeleteModal;
