export interface ICrmDocumentList {
	ListAdditionalDoc?: Document[];
	ListPersonalDocs?: Document[];
	ListAdditionalDocsForComplaints?: Document[];
}

export interface ICrmAttachment {
	Id?: string;
	MimeType: string;
	FileName: string;
	AttachmentBody: string;
}

export interface IUploadDocumentRequest {
	idDocument: string;
	listAttachments: ICrmAttachment[];
}

interface Status {
	Value: string;
	Key: number;
}

interface Document {
	IdDocuments: string;
	NameEn: string;
	NameAr: string;
	IsOptional: boolean;
	Comments: string;
	Status: Status;
	ListAttachments: ICrmAttachment[];
	IsCreatedFromPortal: boolean;
}
